import type { EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput } from "@/apis/gql/generated/types";
import type { SearchCriteriaFormType } from "../types/search-criteria";

export const convertFormInputToSearchInput = ({
  seikyuYm,
  isAll,
  isDone,
  isNoSetting,
  isSave1,
  isSystemSave,
  isTempSave,
  hokenSbts,
  receSbtCenter,
  receSbtRight,
  isIncludeSingle,
  hokenHoubetu,
  kohi1Houbetu,
  kohi2Houbetu,
  kohi3Houbetu,
  kohi4Houbetu,
  lastRaiinDateFrom,
  lastRaiinDateTo,
  hokensyaNoFrom,
  hokensyaNoTo,
  futansyaNoFromLong,
  futansyaNoToLong,
  tensuFrom,
  tensuTo,
  ptId,
  ptIdFrom,
  ptIdTo,
  name,
  birthDay<PERSON>rom,
  birthDayTo,
  kaId,
  doctorId,
  seikyuKbn,
  isTest<PERSON>atient<PERSON>earch,
  itemList,
  itemQuery,
  byomeiCdList,
  byomeiQuery,
  isOnlySuspectedDisease,
  tokki,
}: SearchCriteriaFormType): EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput => {
  return {
    isAdvanceSearch: true,
    seikyuYm: Number(seikyuYm?.format("YYYYMM") ?? "0"),
    isAll,
    isDone,
    isNoSetting,
    isSave1,
    isSystemSave,
    isTempSave,
    hokenSbts,
    receSbtCenter: typeof receSbtCenter !== "undefined" ? receSbtCenter : -1,
    receSbtRight: typeof receSbtRight !== "undefined" ? receSbtRight : -1,
    isIncludeSingle,
    hokenHoubetu,
    kohi1Houbetu,
    kohi2Houbetu,
    kohi3Houbetu,
    kohi4Houbetu,
    lastRaiinDateFrom:
      typeof lastRaiinDateFrom !== "undefined"
        ? Number(lastRaiinDateFrom.format("YYYYMMDD"))
        : undefined,
    lastRaiinDateTo:
      typeof lastRaiinDateTo !== "undefined"
        ? Number(lastRaiinDateTo.format("YYYYMMDD"))
        : undefined,
    hokensyaNoFrom,
    hokensyaNoTo,
    futansyaNoFromLong: futansyaNoFromLong || undefined,
    futansyaNoToLong: futansyaNoToLong || undefined,
    tensuFrom: tensuFrom || undefined,
    tensuTo: tensuTo || undefined,
    ...(ptId
      ? {
          // 個別検索
          ptId,
          ptSearchOption: 1,
        }
      : !!ptIdFrom || !!ptIdTo
        ? {
            // 範囲検索
            ptIdFrom,
            ptIdTo,
            ptSearchOption: 0,
          }
        : undefined),
    name,
    birthDayFrom:
      typeof birthDayFrom !== "undefined"
        ? Number(birthDayFrom.format("YYYYMMDD"))
        : undefined,
    birthDayTo:
      typeof birthDayTo !== "undefined"
        ? Number(birthDayTo.format("YYYYMMDD"))
        : undefined,
    kaId,
    doctorId,
    seikyuKbnAll: typeof seikyuKbn === "undefined",
    seikyuKbnDenshi: seikyuKbn === 1,
    seikyuKbnPaper: seikyuKbn === 2,
    isTestPatientSearch,
    itemList,
    itemQuery,
    byomeiCdList,
    byomeiQuery,
    isOnlySuspectedDisease,
    tokki,
    limit: 1,
    filterType: 1,
    sortOrder: false,
    sortKey: 0,
  };
};
