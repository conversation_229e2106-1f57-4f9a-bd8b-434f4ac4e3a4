import { useCallback, useEffect } from "react";

import dayjs from "dayjs";
import { useForm } from "react-hook-form";

import { STORAGE_KEYS } from "@/constants/local-storage";
import { setLocalStorage } from "@/utils/local-storage";

import { searchCriteriaDefaultValue } from "../constants/search-criteria";
import { ReceiptClaimCancelledProvider } from "../hooks/useReceiptClaimCancelledProvider";
import { ModalProvider } from "../hooks/useReceiptListModalProviders";
import { SearchProvider } from "../hooks/useReceiptListSearchProvider";
import { SelectRecordProvider } from "../hooks/useReceiptSelectProviders";
import { useReceiptPagination } from "../hooks/useReceiptPagination";
import { PaginationProvider } from "../hooks/useReceiptPaginationProvider";
import { convertFormInputToSearchInput } from "../utils/search-criteria";

import { ReceiptListContent } from "./ReceiptListContent";

import type { ReceiptListConfig } from "@/types/receipt";
import type {
  SearchArgsType,
  SearchCriteriaFormType,
} from "../types/search-criteria";
import type { UseReceiptPaginationReturn } from "../hooks/useReceiptPagination";
import type { FC } from "react";

type Props = {
  config: ReceiptListConfig | undefined;
};

export const ReceiptList: FC<Props> = ({ config }) => {
  const method = useForm<SearchCriteriaFormType>({
    defaultValues: {
      ...searchCriteriaDefaultValue,
      // Config.seikyuYmがある場合のみ上書き
      ...(config?.seikyuYm && {
        seikyuYm: dayjs(String(config.seikyuYm), "YYYYMM"),
      }),
    },
  });

  // Use pagination hook with current form values
  const pagination = useReceiptPagination(method.getValues());

  const search = useCallback(
    async (input: SearchArgsType) => {
      // Update form values
      method.reset(input);

      // Reset pagination and load first page with new criteria
      pagination.reset();
      await pagination.loadFirstPage();

      // Save to local storage for config
      const queryInput = convertFormInputToSearchInput(input);
      setLocalStorage<ReceiptListConfig>(STORAGE_KEYS.RECE_LIST_PAGE_CONFIG, {
        seikyuYm: queryInput.seikyuYm,
        lastDisplayedReceiptList: pagination.allData,
      });
    },
    [method, pagination],
  );

  useEffect(() => {
    // Load first page on mount
    pagination.loadFirstPage();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleRefreshReceiptList = async () => {
    const formInput = method.getValues();
    await search(formInput);
  };

  return (
    <SelectRecordProvider>
      <ModalProvider>
        <SearchProvider
          formMethod={{
            ...method,
            search,
          }}
        >
          <PaginationProvider pagination={pagination}>
            <ReceiptClaimCancelledProvider
              onRefreshReceiptList={handleRefreshReceiptList}
            >
              <ReceiptListContent
                isLoading={pagination.isLoading}
                receiptList={pagination.allData}
              />
            </ReceiptClaimCancelledProvider>
          </PaginationProvider>
        </SearchProvider>
      </ModalProvider>
    </SelectRecordProvider>
  );
};
