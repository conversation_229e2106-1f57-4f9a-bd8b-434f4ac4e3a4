import type dayjs from "dayjs";
import type { EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput } from "@/apis/gql/generated/types";

export type SearchArgsType = SearchCriteriaFormType;

export type SearchCriteriaFormType = TargetDateFilterType &
  ConfirmFilterType &
  ReceiptSbtFilterType &
  HoubetsuNumberFilterType &
  LastAppointmentFilterType &
  HokensyoNumberFilterType &
  FutansyaFilterType &
  TensuFilterType &
  PatientFilterType &
  SeikyuKbnFilterType &
  OthersFilterType &
  ItemListFilter &
  ByomeiListFilter &
  TokkiFilterType &
  PaginationFilterType;

// MEMO: dayjs型で扱いたいのでRequestInput型は参照しない
type TargetDateFilterType = {
  seikyuYm: dayjs.Dayjs | undefined;
};

// TODO: isSave1（「保留1」）は暫定、「保留」に対応するパラメータを追加するか相談
type ConfirmFilterType = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  "isAll" | "isNoSetting" | "isSystemSave" | "isSave1" | "isTempSave" | "isDone"
>;

// MEMO: receSbtLeftはパラメータに存在しない？代わりにhokenSbtsを使用していそう
// receSbtCenter: 単独,２併,３併...
// receSbtRight: 本人,未就学,家族...
type ReceiptSbtFilterType = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  "hokenSbts" | "receSbtCenter" | "receSbtRight"
>;

type HoubetsuNumberFilterType = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  | "isIncludeSingle"
  | "hokenHoubetu"
  | "kohi1Houbetu"
  | "kohi2Houbetu"
  | "kohi3Houbetu"
  | "kohi4Houbetu"
>;

// MEMO: dayjs型で扱いたいのでRequestInput型は参照しない
type LastAppointmentFilterType = {
  lastRaiinDateFrom: dayjs.Dayjs | undefined;
  lastRaiinDateTo: dayjs.Dayjs | undefined;
};

type HokensyoNumberFilterType = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  "hokensyaNoFrom" | "hokensyaNoTo"
>;

type FutansyaFilterType = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  "futansyaNoFromLong" | "futansyaNoToLong"
>;

type TensuFilterType = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  "tensuFrom" | "tensuTo"
>;

type PatientFilterType = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  "ptId" | "ptIdFrom" | "ptIdTo" | "name" | "kaId" | "doctorId"
> & {
  birthDayFrom: dayjs.Dayjs | undefined;
  birthDayTo: dayjs.Dayjs | undefined;
};

// MEMO: seikyuKbnAll,seikyuKbnDenshi,seikyuKbnPaperを統合
type SeikyuKbnFilterType = {
  seikyuKbn: 1 | 2 | undefined; // 1: 電子レセ, 2: 紙レセ
};

type OthersFilterType = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  "isTestPatientSearch"
>;

export type ItemListFilter = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  "itemList" | "itemQuery"
>;

export type ByomeiListFilter = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  "byomeiCdList" | "byomeiQuery" | "isOnlySuspectedDisease"
>;

type TokkiFilterType = Pick<
  EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput,
  "tokki"
>;

// Pagination parameters for infinite scroll
type PaginationFilterType = {
  limit?: number; // Default: 100
  filterType?: number; // 0: すべて, 1: 社保, 2: 国保, 3: その他
  sortKey?: number; // 0: 患者番号, 1: 氏名, 2: レセプト種別, 3: 最終来院日
  sortOrder?: boolean; // true: 昇順, false: 降順
  cursorPtId?: string; // Cursor for pagination - ptId of last record from previous page
  cursorSinYm?: number; // Cursor for pagination - sinYm of last record from previous page
  cursorHokenId?: number; // Cursor for pagination - hokenId of last record from previous page
};
