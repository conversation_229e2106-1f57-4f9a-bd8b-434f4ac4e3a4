import type { SortOrder } from "@/apis/gql/generated/types";

// Reception pagination and search criteria types
export type ReceptionSearchCriteria = {
  // Pagination parameters
  limit: number; // Fixed at 100
  filterType: ReceptionFilterType; // 0:すべて, 1:社保, 2:国保, 3:その他
  sortKey: ReceptionSortKey; // 0:患者番号, 1:氏名, 2:レセプト種別, 3:最終来院日
  sortOrder: boolean; // true:昇順, false:降順

  // Cursor pagination parameters (undefined for first page)
  cursorPtId?: string;
  cursorSinYm?: number;
  cursorHokenId?: number;

  // Date filter
  sinDate: number;
};

// Filter types for reception
export enum ReceptionFilterType {
  ALL = 0, // すべて
  SYAHO = 1, // 社保
  KOKUHO = 2, // 国保
  OTHER = 3, // その他
}

// Sort keys for reception
export enum ReceptionSortKey {
  PATIENT_NUMBER = 0, // 患者番号
  NAME = 1, // 氏名
  RECEIPT_TYPE = 2, // レセプト種別
  LAST_VISIT_DATE = 3, // 最終来院日
}

// Convert reception sort criteria to API format
export const convertReceptionSortToApiFormat = (
  sortKey: ReceptionSortKey,
  sortOrder: boolean,
): { sortField: string; sortOrder: SortOrder } => {
  const sortFieldMap: Record<ReceptionSortKey, string> = {
    [ReceptionSortKey.PATIENT_NUMBER]: "ptNum",
    [ReceptionSortKey.NAME]: "name",
    [ReceptionSortKey.RECEIPT_TYPE]: "receiptType",
    [ReceptionSortKey.LAST_VISIT_DATE]: "lastVisitDate",
  };

  return {
    sortField: sortFieldMap[sortKey],
    sortOrder: sortOrder ? SortOrder.Ascend : SortOrder.Descend,
  };
};

// Default search criteria
export const DEFAULT_RECEPTION_SEARCH_CRITERIA: Omit<
  ReceptionSearchCriteria,
  "sinDate"
> = {
  limit: 1,
  filterType: ReceptionFilterType.ALL,
  sortKey: ReceptionSortKey.PATIENT_NUMBER,
  sortOrder: true, // 昇順
};

// Helper function to create initial search criteria for first page (no cursor)
export const createInitialReceptionSearchCriteria = (
  sinDate: number,
  overrides?: Partial<
    Omit<
      ReceptionSearchCriteria,
      "cursorPtId" | "cursorSinYm" | "cursorHokenId"
    >
  >,
): ReceptionSearchCriteria => {
  return {
    ...DEFAULT_RECEPTION_SEARCH_CRITERIA,
    sinDate,
    ...overrides,
    // First page should not have cursor parameters
    cursorPtId: undefined,
    cursorSinYm: undefined,
    cursorHokenId: undefined,
  };
};

// Helper function to create search criteria (generic)
export const createReceptionSearchCriteria = (
  sinDate: number,
  overrides?: Partial<ReceptionSearchCriteria>,
): ReceptionSearchCriteria => {
  return {
    ...DEFAULT_RECEPTION_SEARCH_CRITERIA,
    sinDate,
    ...overrides,
  };
};

// Helper function to create next page criteria with cursor from last record
export const createNextPageCriteria = (
  currentCriteria: ReceptionSearchCriteria,
  lastRecord: {
    ptId: string;
    sinYm: number;
    hokenId: number;
  },
): ReceptionSearchCriteria => {
  return {
    ...currentCriteria,
    cursorPtId: lastRecord.ptId,
    cursorSinYm: lastRecord.sinYm,
    cursorHokenId: lastRecord.hokenId,
  };
};

// Helper function to check if criteria is for subsequent page (has cursor)
export const isSubsequentPageCriteria = (
  criteria: ReceptionSearchCriteria,
): boolean => {
  return !!(
    criteria.cursorPtId &&
    criteria.cursorSinYm !== undefined &&
    criteria.cursorHokenId !== undefined
  );
};

// Helper function to prepare API variables, excluding undefined cursor values
export const prepareApiVariables = (criteria: ReceptionSearchCriteria) => {
  const baseVariables = {
    sinDate: criteria.sinDate,
    limit: criteria.limit,
    filterType: criteria.filterType,
    sortKey: criteria.sortKey,
    sortOrder: criteria.sortOrder,
  };

  // Only include cursor parameters if they are defined (not first page)
  if (
    criteria.cursorPtId &&
    criteria.cursorSinYm !== undefined &&
    criteria.cursorHokenId !== undefined
  ) {
    return {
      ...baseVariables,
      cursorPtId: criteria.cursorPtId,
      cursorSinYm: criteria.cursorSinYm,
      cursorHokenId: criteria.cursorHokenId,
    };
  }

  // First page - no cursor parameters
  return baseVariables;
};
