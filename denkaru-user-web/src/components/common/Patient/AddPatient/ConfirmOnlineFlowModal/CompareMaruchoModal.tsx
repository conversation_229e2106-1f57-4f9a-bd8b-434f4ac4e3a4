import { useCallback, useMemo, useState } from "react";

import styled from "styled-components";
import { pick } from "lodash";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useUpdateRefNo } from "@/hooks/add-patient/useUpdateRefNo";
import { SystemScreenCode } from "@/constants/confirm-online";

import { usePatientContext } from "../Providers/PatientProvider";

import { CommonTable } from "./CommonTable";
import {
  formatDayjsDate,
  getInsuranceTypeFromXml,
  getPublicExpenseInfo,
  getXmlValue,
} from "./helper";

import type { Dayjs } from "dayjs";
import type {
  DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation,
  DomainModelsPatientInforCheckKohiInfoDifferenceDto,
} from "@/apis/gql/generated/types";
import type { PatientState } from "@/types/patient";

type MaruchoData = {
  hokenEdaNo: string;
  startDate: string;
  endDate: string;
};

type Props = DomainModelsPatientInforCheckKohiInfoDifferenceDto & {
  isOpen: boolean;
  checkDate: Dayjs;
  resultOfQualifications?: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation[];
};

const fieldNames = {
  hokenEdaNo: "自己負担限度額",
  startDate: "有効開始年月日",
  endDate: "有効終了年月日",
};

type StatusUpdate = Partial<
  Record<keyof Omit<typeof fieldNames, "credentials">, boolean>
>;

export const CompareMaruchoModal: React.FC<Props> = ({
  isOpen,
  hokenId = 0,
  seqNo = "0",
  checkDate,
  resultOfQualifications,
  ...props
}) => {
  const {
    resetAllData: resetAllDataCredentials,
    handleOpenModal,
    setSelectedInsurance,
    callbackList,
    handleSetCallback,
    handleCloseModal,
    handleUpdateOnlineMasterData,
    onlineMasterData,
    selectedPatient,
    confirmingType,
  } = usePatientContext();
  const [statusUpdate, setStatusUpdate] = useState<StatusUpdate>();
  const { handleUpdateRefNo } = useUpdateRefNo({
    screenCode: SystemScreenCode.Reception,
  });
  const notUpdate = useMemo(
    () => Object.values(statusUpdate ?? {}).every((val) => !val),
    [statusUpdate],
  );

  const tableData = useMemo((): {
    currentInfo: MaruchoData;
    newInfo: MaruchoData;
    changeStatus: Record<keyof MaruchoData, boolean>;
  } => {
    const remapData = pick(props, ["hokenEdaNo", "startDate", "endDate"]);
    return {
      currentInfo: pick(getPublicExpenseInfo(props, "value"), [
        "hokenEdaNo",
        "startDate",
        "endDate",
      ]),
      newInfo: pick(getPublicExpenseInfo(props, "xmlValue"), [
        "startDate",
        "endDate",
        "hokenEdaNo",
      ]),
      changeStatus: Object.keys(remapData).reduce(
        (acc, key) => {
          acc[key as keyof MaruchoData] =
            !!remapData[key as keyof MaruchoData]?.isMap;
          return acc;
        },
        {} as Record<keyof MaruchoData, boolean>,
      ),
    };
  }, [props]);

  const handleNextStep = useCallback(
    (type: "ADD" | "EDIT") => {
      if (type === "EDIT" && notUpdate) {
        handleCloseModal("COMPARE_MARUCHO");
        if (
          confirmingType &&
          [
            "CONFIRMING_HOKEN_MY_INSURANCE",
            "CONFIRMING_KOHI_MY_INSURANCE",
          ].includes(confirmingType)
        ) {
          handleUpdateRefNo({
            ptId: selectedPatient?.patientID.toString() || "0",
            ptNum: String(selectedPatient?.patientNumber),
            resultOfQualificationConfirmation: resultOfQualifications?.[0],
          });
        }
        if (callbackList.length) {
          const newCallbackList = [...callbackList];
          newCallbackList.shift();
          handleSetCallback([...newCallbackList]);
          return;
        }
      }

      const raw = pick(props, ["hokenEdaNo", "startDate", "endDate"]);
      const publicExpenseData: Partial<MaruchoData> = {};
      Object.entries(raw).forEach(([fieldName, value]) => {
        if (!value.isMap && statusUpdate?.[fieldName as keyof StatusUpdate]) {
          publicExpenseData[fieldName as keyof MaruchoData] = getXmlValue(
            value.xmlValue,
          ) as typeof fieldName;
        } else {
          publicExpenseData[fieldName as keyof MaruchoData] =
            value.value as typeof fieldName;
        }
      });

      let initInsurance: PatientState["selectedInsurance"] = {
        hokenId: type === "ADD" ? 0 : hokenId,
        seqNo: type === "ADD" ? "0" : seqNo,
        checkDate,
        isMarucho: true,
      };

      switch (type) {
        case "ADD":
          initInsurance = {
            ...initInsurance,
            startDate: formatDayjsDate(props.startDate?.xmlValue),
            endDate: formatDayjsDate(props.endDate?.xmlValue),
            insuranceType: getInsuranceTypeFromXml(props.hokenEdaNo?.xmlValue),
          };
          break;
        case "EDIT":
          initInsurance = {
            ...initInsurance,
            startDate: formatDayjsDate(publicExpenseData.startDate),
            endDate: formatDayjsDate(publicExpenseData.endDate),
            insuranceType: getInsuranceTypeFromXml(
              publicExpenseData.hokenEdaNo,
            ),
          };
          break;

        default:
          break;
      }

      if (type === "ADD") {
        handleUpdateOnlineMasterData({
          endDateModels: [
            ...(onlineMasterData?.endDateModels ?? []),
            {
              hokenId: hokenId?.toString(),
              seqNo,
              endDate: raw.endDate?.value,
              pending: true,
              name: props.kohiName,
              startDate: raw.startDate?.value,
            },
          ],
        });
      }
      setSelectedInsurance({ ...initInsurance });
      handleOpenModal("HANDLE_PUBLIC_EXPENSE");
    },
    [
      callbackList,
      checkDate,
      handleCloseModal,
      handleOpenModal,
      handleSetCallback,
      handleUpdateOnlineMasterData,
      hokenId,
      notUpdate,
      onlineMasterData?.endDateModels,
      props,
      seqNo,
      setSelectedInsurance,
      statusUpdate,
    ],
  );

  const onClose = useCallback(() => {
    setSelectedInsurance(null);
    resetAllDataCredentials();
  }, [resetAllDataCredentials, setSelectedInsurance]);

  return (
    <Modal
      title="最新の資格情報があります"
      centered
      isOpen={isOpen}
      width={760}
      onCancel={onClose}
      centerFooterContent
      footer={
        <BoxFooter>
          <Button
            key="cancel"
            shape="round"
            varient="tertiary"
            onClick={onClose}
          >
            キャンセル
          </Button>
          <BoxBtnUpdate>
            <Button varient="inline" onClick={() => handleNextStep("ADD")}>
              新規登録
            </Button>
            <Button
              varient="primary"
              shape="round"
              onClick={() => handleNextStep("EDIT")}
            >
              {notUpdate ? "無視" : "更新"}
            </Button>
          </BoxBtnUpdate>
        </BoxFooter>
      }
    >
      <ModalBodyWrapper>
        <ContentWrapperTitle>
          <p>
            オンライン資格確認の情報から現在の設定に上書きする項目を選択してください
          </p>
          <NameHoken>{props.kohiName ?? ""}</NameHoken>
        </ContentWrapperTitle>
        <ContentWrapper>
          <CommonTable<MaruchoData>
            {...tableData}
            fieldNames={fieldNames}
            sentDataToParent={setStatusUpdate}
            titleColumn={["現在の設定", "資格確認の設定"]}
          />
        </ContentWrapper>
      </ModalBodyWrapper>
    </Modal>
  );
};

const ModalBodyWrapper = styled.div`
  padding: 24px 24px 52px 24px;
`;

const ContentWrapperTitle = styled.div`
  margin-bottom: 20px;
`;

const BoxFooter = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
`;

const ContentWrapper = styled.div``;

const NameHoken = styled.p`
  margin-top: 20px;
  font-size: 16px;
`;

const BoxBtnUpdate = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
`;
