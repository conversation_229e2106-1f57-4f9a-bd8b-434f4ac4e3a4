import { useEffect, useRef, useState } from "react";

import { Flex, Image } from "antd";
import dayjs from "dayjs";
import { Controller, useFormContext } from "react-hook-form";
import styled from "styled-components";
import { type UploadFile } from "antd";

import { DatePickerSuffixClear } from "@/components/ui/DatePicker";
import { ErrorText } from "@/components/ui/ErrorText";
import { SvgIconCalendar } from "@/components/ui/Icon/IconCalendar";
import { InputLabel } from "@/components/ui/InputLabel";
import { Button } from "@/components/ui/NewButton";
import { Pulldown } from "@/components/ui/Pulldown";
import { RenderIf } from "@/utils/common/render-if";
import { useGetApiPatientInforCheckDeleteHokenQuery } from "@/apis/gql/operations/__generated__/insurance";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { TextInput } from "@/components/ui/TextInput";
import { formatYYYYMMDDWithJapaneseEra } from "@/utils/add-patient";
import { SvgIconDeleteWhite } from "@/components/ui/Icon/IconDeleteWhite";
import { PROPS_CONFIRM_DELETE_INSURANCE } from "@/constants/insurance";

import { PatientErrorModal } from "../PatientErrorModal";
import { usePatientContext } from "../Providers/PatientProvider";
import { ValidateKohiPopup } from "../ValidateKohiPopup";

import type { UploadChangeParam } from "antd/lib/upload";
import type {
  Option,
  PublicExpenseSelectData,
  RegistrationPublicExpenseInforForm,
} from "@/types/insurance";

type Props = {
  optionsPublicExpense: Option[];
  publicExpenseSelectData: PublicExpenseSelectData[];
  handleDeleteFile: () => void;
  dataBlob?: UploadChangeParam<UploadFile>;
  selectedPublicExpense: PublicExpenseSelectData;
};

export const DetailPublicExpenseInfo = ({
  optionsPublicExpense,
  publicExpenseSelectData,
  handleDeleteFile,
  dataBlob,
  selectedPublicExpense,
}: Props) => {
  const { handleError } = useErrorHandler();
  const [isOpenConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false);

  const {
    handleOpenModal,
    selectedInsurance,
    selectedPatient: patientInfo,
    confirmingType,
  } = usePatientContext();
  const ptId = patientInfo?.patientID;
  const [deleteErrorContent, setDeleteErrorContent] = useState<{
    message: string;
    messageDetail: string;
  }>();

  const { loading: loadingCheckDelete, refetch: checkDeleteKohi } =
    useGetApiPatientInforCheckDeleteHokenQuery({
      skip: true,
      variables: {
        kohiId: selectedInsurance?.hokenId ?? undefined,
        ptId: Number(ptId),
      },
      onError: (error) => {
        handleError({ error });
      },
    });

  const handleCheckDelete = async () => {
    if (!selectedInsurance || !ptId) return;
    const { data } = await checkDeleteKohi();
    const typeDelete =
      data.getApiPatientInforCheckDeleteHoken?.data?.messageDelete?.typeDelete;
    if (data) {
      if (typeDelete === 0) {
        handleOpenModal("DELETE_PUBLIC_EXPENSE");
        return;
      }
      if (typeDelete === 3) {
        const message =
          data?.getApiPatientInforCheckDeleteHoken?.data?.messageDelete
            ?.message;
        const messageDetail =
          data?.getApiPatientInforCheckDeleteHoken?.data?.messageDelete
            ?.messageDetail;
        setDeleteErrorContent({
          message: message ?? "",
          messageDetail: messageDetail ?? "",
        });
        return;
      }
    }
  };

  const [optionsPublicExpenseFiltered, setOptionsPublicExpenseFiltered] =
    useState<Option<string>[]>([]);

  const {
    control,
    watch,
    setValue,
    clearErrors,
    formState: { errors },
  } = useFormContext<RegistrationPublicExpenseInforForm>();

  const { futansyaNo, insuranceType, startDate, endDate, file } = watch();

  const hasSetInsuranceTypeRef = useRef(false);

  useEffect(() => {
    if (!insuranceType) {
      setValue("insuranceType", "please_select");
    }
  }, [insuranceType, setValue, selectedInsurance]);

  useEffect(() => {
    const splittedFutansyaNo = futansyaNo.substring(0, 2);
    const findedOption = publicExpenseSelectData.filter(
      (item) => item.houbetu === splittedFutansyaNo,
    );
    const findedOptionsPmh: PublicExpenseSelectData[] = findedOption.filter(
      (item) => {
        return (
          item.uniqKey ===
          `${selectedInsurance?.hokenEdaNo}__${selectedInsurance?.hokenNo}`
        );
      },
    );

    const optionList = [
      {
        label: "選択してください",
        value: "please_select",
      },
      ...findedOption.map((item) => ({
        label: item.displayTextMaster || "",
        value: item.uniqKey,
      })),
    ];

    if (!hasSetInsuranceTypeRef.current && confirmingType) {
      if (
        findedOptionsPmh.length &&
        findedOptionsPmh[0]?.uniqKey &&
        selectedInsurance?.isPmh
      ) {
        setValue("insuranceType", findedOptionsPmh[0]?.uniqKey);
        hasSetInsuranceTypeRef.current = true;
      } else if (!selectedInsurance?.isPmh && optionList[1]?.value) {
        setValue("insuranceType", optionList[1]?.value);
      } else if (
        selectedInsurance?.insuranceType &&
        selectedInsurance.isMarucho
      ) {
        setValue("insuranceType", selectedInsurance?.insuranceType);
      } else {
        setValue("insuranceType", "please_select");
      }
    }

    if (!futansyaNo) {
      setOptionsPublicExpenseFiltered(optionsPublicExpense);
      return;
    }
    setOptionsPublicExpenseFiltered(optionList);
  }, [
    confirmingType,
    futansyaNo,
    optionsPublicExpense,
    publicExpenseSelectData,
    selectedInsurance?.gendogaku,
    selectedInsurance?.hokenEdaNo,
    selectedInsurance?.hokenNo,
    selectedInsurance?.insuranceType,
    selectedInsurance?.isMarucho,
    selectedInsurance?.isPmh,
    selectedInsurance?.rate,
    setValue,
  ]);

  const getGendogakuPlaceholder = () => {
    return (
      selectedPublicExpense?.kaiLimitFutan ||
      selectedPublicExpense?.dayLimitFutan ||
      selectedPublicExpense?.monthLimitFutan
    );
  };

  return (
    <Wrapper>
      <RenderIf condition={!!deleteErrorContent}>
        <PatientErrorModal
          isOpen={!!deleteErrorContent}
          title="エ公費情報の削除"
          onClose={() => setDeleteErrorContent(undefined)}
          contentText={deleteErrorContent?.messageDetail}
          headingText={deleteErrorContent?.message}
        />
      </RenderIf>
      <InputArea>
        <InputWrapper>
          <StyledInputLabel label="公費の種類" required />
          <Controller
            control={control}
            name="insuranceType"
            rules={{
              validate: (value) => {
                if (!value || value === "please_select") {
                  return "公費の種類を入力してください。";
                }
                return true;
              },
            }}
            render={({ field, fieldState }) => (
              <div>
                <StyledSelect
                  options={optionsPublicExpenseFiltered}
                  hasError={!!fieldState.error}
                  {...field}
                  onChange={(value) => {
                    setValue("insuranceType", value);
                    setValue("rate", 0);
                    setValue("gendogaku", 0);

                    if (!publicExpenseSelectData) return;
                    const filteredOptions = publicExpenseSelectData.find(
                      (option) => option.uniqKey === value,
                    );
                    if (filteredOptions) {
                      clearErrors("insuranceType");
                    }
                  }}
                />
                {fieldState.error?.message && (
                  <StyledErrorText>{fieldState.error.message}</StyledErrorText>
                )}
              </div>
            )}
          />
        </InputWrapper>
      </InputArea>
      <InputArea>
        <InputWrapper>
          <StyledInputLabel label="負担率" />
          <Controller
            control={control}
            name="rate"
            rules={{
              maxLength: {
                message: "3文字以内で入力してください。",
                value: 3,
              },
            }}
            render={({ field, fieldState }) => (
              <StyledInput
                width="80px"
                {...field}
                hasError={!!fieldState.error}
                value={
                  insuranceType !== "please_select" ? field.value || "" : ""
                }
                placeholder={
                  insuranceType !== "please_select" &&
                  (selectedPublicExpense?.futanRate !== 0 || insuranceType)
                    ? String(selectedPublicExpense?.futanRate)
                    : ""
                }
                onBlur={(e) => {
                  if (/^0+$/.test(e.target.value)) {
                    field.onChange("");
                  }
                }}
                onInput={(e) => {
                  const input = e.target as HTMLInputElement;
                  input.value = input.value
                    .replace(/[^\d０-９]/g, "")
                    .replace(/[０-９]/g, (s) =>
                      String.fromCharCode(s.charCodeAt(0) - 0xfee0),
                    );
                }}
              />
            )}
          />
          <StyledSpan>％</StyledSpan>
          {errors.rate?.message && (
            <StyledErrorText>{errors.rate.message}</StyledErrorText>
          )}
        </InputWrapper>
      </InputArea>
      <InputArea>
        <InputWrapper>
          <StyledInputLabel label="負担上限" />
          <Controller
            control={control}
            name="gendogaku"
            rules={{
              maxLength: {
                message: "6文字以内で入力してください。",
                value: 6,
              },
            }}
            render={({ field, fieldState }) => (
              <StyledInput
                {...field}
                value={
                  insuranceType !== "please_select" ? field.value || "" : ""
                }
                hasError={!!fieldState.error}
                placeholder={
                  insuranceType !== "please_select" &&
                  (getGendogakuPlaceholder() !== 0 || insuranceType)
                    ? String(getGendogakuPlaceholder())
                    : ""
                }
                onBlur={(e) => {
                  if (/^0+$/.test(e.target.value)) {
                    field.onChange("");
                  }
                }}
                onInput={(e) => {
                  const input = e.target as HTMLInputElement;
                  input.value = input.value
                    .replace(/[^\d０-９]/g, "")
                    .replace(/[０-９]/g, (s) =>
                      String.fromCharCode(s.charCodeAt(0) - 0xfee0),
                    );
                }}
              />
            )}
          />
          <StyledSpan>円</StyledSpan>
          <StyledButton
            varient="ordinary"
            width={100}
            disabled={selectedPublicExpense?.isLimitList !== 1}
            onClick={() => handleOpenModal("PUBLIC_EXPENSE_MAX_COST")}
          >
            上限額管理
          </StyledButton>
          {errors.gendogaku?.message && (
            <StyledErrorText>{errors.gendogaku.message}</StyledErrorText>
          )}
        </InputWrapper>
      </InputArea>
      <InputArea>
        <InputWrapper>
          <StyledInputLabel label="取得年月日" />
          <Controller
            control={control}
            name="sikakuDate"
            render={({ field }) => (
              <StyledDatePicker
                suffixIcon={<SvgIconCalendar />}
                allowClear={false}
                placeholder="資格取得年月日を指定"
                format={(value) =>
                  formatYYYYMMDDWithJapaneseEra(dayjs(value).toDate())
                }
                {...field}
              />
            )}
          />
        </InputWrapper>
      </InputArea>
      <InputArea>
        <InputWrapper>
          <StyledInputLabel label="交付年月日" />
          <Controller
            control={control}
            name="kofuDate"
            render={({ field }) => (
              <StyledDatePicker
                suffixIcon={<SvgIconCalendar />}
                allowClear={false}
                placeholder="資格交付年月日を指定"
                format={(value) =>
                  formatYYYYMMDDWithJapaneseEra(dayjs(value).toDate())
                }
                {...field}
              />
            )}
          />
        </InputWrapper>
      </InputArea>
      <InputArea>
        <InputWrapper>
          <StyledInputLabel label="有効期限" />
          <Flex align="center" gap={8}>
            <Controller
              control={control}
              name="startDate"
              rules={{
                validate: (value) => {
                  if (!value || !endDate) return true;
                  if (dayjs(value) > dayjs(endDate)) {
                    return "公費有効終了日は 公費有効開始日以降を入力してください。";
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <StyledDatePicker
                  suffixIcon={<SvgIconCalendar />}
                  allowClear={false}
                  placeholder="開始日を指定"
                  format={(value) =>
                    formatYYYYMMDDWithJapaneseEra(dayjs(value).toDate())
                  }
                  {...field}
                />
              )}
            />{" "}
            〜{" "}
            <Controller
              control={control}
              name="endDate"
              rules={{
                validate: (value) => {
                  if (!value || !startDate) return true;
                  if (dayjs(value) < dayjs(startDate)) {
                    return "公費有効終了日は 公費有効開始日以降を入力してください。";
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <StyledDatePicker
                  suffixIcon={<SvgIconCalendar />}
                  allowClear={false}
                  placeholder="終了日を指定"
                  format={(value) =>
                    formatYYYYMMDDWithJapaneseEra(dayjs(value).toDate())
                  }
                  {...field}
                  value={
                    Number(dayjs(field.value).format("YYYYMMDD")) !== 99999999
                      ? field.value
                      : dayjs(0)
                  }
                />
              )}
            />
          </Flex>
          {(errors.startDate?.message || errors.endDate?.message) && (
            <StyledErrorText>
              {errors.startDate?.message || errors.endDate?.message}
            </StyledErrorText>
          )}
        </InputWrapper>
      </InputArea>
      <InputArea>
        <InputWrapper>
          <StyledInputLabel
            label="特殊番号"
            required={selectedPublicExpense.isTokusyuNoCheck === 1}
          />
          <Controller
            control={control}
            name="tokusyuNo"
            rules={{
              maxLength: {
                message: "20文字以内で入力してください。",
                value: 20,
              },
            }}
            render={({ field, fieldState }) => (
              <div>
                <StyledInput {...field} hasError={!!fieldState.error} />
                {fieldState.error?.message && (
                  <StyledErrorText>{fieldState.error.message}</StyledErrorText>
                )}
              </div>
            )}
          />
        </InputWrapper>
      </InputArea>
      <InputArea>
        <ImageRegistrationButton
          varient="ordinary"
          onClick={() => handleOpenModal("UPLOAD_FILE")}
          disabled={!!file}
        >
          画像登録
        </ImageRegistrationButton>
        <RenderIf condition={!!selectedInsurance?.hokenId}>
          <DeletePublicExpenseInforBtn
            varient="custom"
            disabled={loadingCheckDelete || !!confirmingType}
            onClick={handleCheckDelete}
          >
            公費情報を削除
          </DeletePublicExpenseInforBtn>
        </RenderIf>
      </InputArea>
      <RenderIf condition={!!file}>
        <ImageContainer>
          <Image
            src={file?.fileLink ?? ""}
            width={260}
            height={176.4}
            preview={{
              src: !dataBlob?.file.originFileObj
                ? (file?.fileLink ?? "")
                : dataBlob?.file.originFileObj
                  ? URL.createObjectURL(dataBlob.file.originFileObj)
                  : "",
            }}
          />
          <ButtonDeleteImage
            varient="custom"
            onClick={() => setOpenConfirmDelete(true)}
          >
            <SvgIconDeleteWhite />
          </ButtonDeleteImage>
        </ImageContainer>
      </RenderIf>
      <RenderIf condition={isOpenConfirmDelete}>
        <ValidateKohiPopup
          content={PROPS_CONFIRM_DELETE_INSURANCE.content}
          buttonProps={PROPS_CONFIRM_DELETE_INSURANCE.buttonConfirm}
          onClose={() => setOpenConfirmDelete(false)}
          onConfirm={() => {
            handleDeleteFile();
            setOpenConfirmDelete(false);
          }}
          open={isOpenConfirmDelete}
        />
      </RenderIf>
    </Wrapper>
  );
};

const ImageContainer = styled.div`
  width: 260px;
  height: 176.4px;
  position: relative;
  img {
    object-fit: cover;
  }
`;

const ButtonDeleteImage = styled(Button)`
  width: 36px;
  height: 36px;
  background-color: rgba(0, 0, 0, 0.3);
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 8px;
  border-radius: 0;
`;

const Wrapper = styled.div`
  padding: 20px 24px;
  border-bottom: 1px solid #e2e3e5;

  &:last-of-type {
    border-bottom: none;
  }
`;

const StyledInput = styled(TextInput)`
  width: 140px;
`;

const InputArea = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;

  &:last-of-type {
    margin-bottom: 0;
  }
`;

const StyledInputLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const StyledDatePicker = styled(DatePickerSuffixClear)`
  width: 220px;
`;

const StyledSpan = styled.span`
  margin-left: 8px;
`;

const ImageRegistrationButton = styled(Button)`
  height: 28px;
  padding: 7px 32px;
  width: 120px;
`;

const StyledButton = styled(Button)<{ width?: number }>`
  margin-left: 25px;
  width: ${(props) => (props.width ? `${props.width}px` : "auto")};
  height: 36px;
`;

const InputWrapper = styled.div``;

const DeletePublicExpenseInforBtn = styled(Button)`
  font-size: 14px;
  line-height: 14px;
  font-weight: normal;
  height: auto;
  padding: 0;
  width: auto;
  color: #e74c3c;
  border: none;
`;

const StyledSelect = styled(Pulldown)`
  width: 220px;
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: 6px;
  font-size: 12px;
  word-wrap: break-word;
`;
