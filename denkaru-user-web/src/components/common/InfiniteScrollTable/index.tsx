import React from "react";

import { Skeleton } from "antd";
import { TableVirtuoso } from "react-virtuoso";
import styled, { css } from "styled-components";

import { SortOrder } from "@/apis/gql/generated/types";
import { SvgIconTableSorterDown } from "@/components/ui/Icon/IconTableSorterDown";
import { SvgIconTableSorterUp } from "@/components/ui/Icon/IconTableSorterUp";

import type { ItemProps, TableVirtuosoProps } from "react-virtuoso";
import type { ReactElement, ReactNode } from "react";

/** @deprecated: src/components/ui/Table/index.tsx src/hooks/useInfiniteScroll.ts を使用してください */
export type TableColumn = {
  title: string;
  dataIndex: string;
  width?: string;
  sortable?: boolean;
};

interface Props<D> extends TableVirtuosoProps<D, unknown> {
  data: D[];
  columns: TableColumn[];
  onLoadMore: () => void;
  isLoadingMore: boolean;
  sortOrder?: SortOrder;
  sortField?: TableColumn["dataIndex"];
  onSort?: (
    sortField: TableColumn["dataIndex"],
    sortOrder: SortOrder | null,
  ) => void;
  emptyPlaceholder?: ReactNode;
  onRowClick?: (item: D) => void;
  customTableRow?: (
    props: ItemProps<D> & {
      context?: unknown;
    },
  ) => ReactElement;
  disableField?: keyof D;
}

/** @deprecated: src/components/ui/Table/index.tsx src/hooks/useInfiniteScroll.ts を使用してください */
export const InfiniteScrollTable = <D,>({
  data,
  columns,
  onLoadMore,
  isLoadingMore,
  onSort,
  sortField,
  sortOrder,
  emptyPlaceholder,
  onRowClick,
  customTableRow,
  disableField,
  ...props
}: Props<D>) => {
  const handleSort = (sortField: TableColumn["dataIndex"]) => {
    if (!onSort) return;

    let newSortOrder: SortOrder | null;
    switch (sortOrder) {
      case SortOrder.Descend:
      case null:
      case undefined:
        newSortOrder = SortOrder.Ascend;
        break;
      case SortOrder.Ascend:
        newSortOrder = SortOrder.Descend;
        break;
      default:
        newSortOrder = null;
        break;
    }

    onSort(sortField, newSortOrder);
  };

  return (
    <TableWrapper>
      <TableVirtuoso
        {...props}
        data={data}
        fixedHeaderContent={() => (
          <TableHeader
            columns={columns}
            onSort={handleSort}
            sortOrder={sortOrder}
            sortField={sortField}
          />
        )}
        endReached={onLoadMore}
        components={{
          EmptyPlaceholder: () => (
            <TableEmptyData>{emptyPlaceholder}</TableEmptyData>
          ),
          TableRow: customTableRow
            ? customTableRow
            : ({ item, context, ...props }) => {
                return (
                  <StyledTableRow
                    {...props}
                    $clickable={!!onRowClick}
                    $disabled={!!(disableField && !item[disableField])}
                    onClick={() => {
                      if (!onRowClick) return;
                      onRowClick(item);
                    }}
                  />
                );
              },
        }}
        fixedFooterContent={() => {
          if (isLoadingMore) {
            return (
              <tr>
                <td colSpan={100}>
                  <Skeleton.Button block active size="large" />
                </td>
              </tr>
            );
          }
          return null;
        }}
      />
    </TableWrapper>
  );
};

type TableDataCellProps = {
  children: ReactNode;
  align?: string;
  colSpan?: number;
  className?: string;
  bordered?: boolean;
};

/** @deprecated: src/components/ui/Table/index.tsx src/hooks/useInfiniteScroll.ts を使用してください */
export const TableDataCell: React.FC<TableDataCellProps> = ({
  children,
  align,
  colSpan,
  className,
  bordered,
}) => {
  return (
    <StyledTableDataCell
      colSpan={colSpan}
      $align={align}
      className={className}
      $bordered={bordered}
    >
      {children}
    </StyledTableDataCell>
  );
};

type TableEmptyDataProps = {
  children?: ReactNode;
};

const TableEmptyData = ({
  children = "検索結果がございません。",
}: TableEmptyDataProps) => {
  return (
    <TableBody>
      <TableRow>
        <TableCell colSpan={100}>
          <Wrapper>{children}</Wrapper>
        </TableCell>
      </TableRow>
    </TableBody>
  );
};

type TableHeaderProps = {
  columns: TableColumn[];
  onSort: (sortField: TableColumn["dataIndex"]) => void;
  sortOrder?: SortOrder | null;
  sortField?: TableColumn["dataIndex"] | null;
};

const TableHeader = ({
  columns,
  sortOrder,
  sortField,
  onSort,
}: TableHeaderProps) => {
  return (
    <tr>
      {columns.map((column) => {
        if (column.sortable) {
          return (
            <StyledHeaderCell key={column.dataIndex} width={column.width}>
              <SortableColumnWrapper onClick={() => onSort(column.dataIndex)}>
                <SortableColumnTitle>{column.title}</SortableColumnTitle>
                {sortField === column.dataIndex && sortOrder && (
                  <IconWrapper>
                    <SortIcon sortOrder={sortOrder} />
                  </IconWrapper>
                )}
              </SortableColumnWrapper>
            </StyledHeaderCell>
          );
        }
        return (
          <StyledHeaderCell key={column.dataIndex} width={column.width}>
            <ChildrenWrapper>{column.title}</ChildrenWrapper>
          </StyledHeaderCell>
        );
      })}
    </tr>
  );
};

const SortIcon = ({ sortOrder }: { sortOrder: SortOrder }) => {
  return (
    <SortIconWrapper>
      {sortOrder === "ascend" && <SvgIconTableSorterUp />}
      {sortOrder === "descend" && <SvgIconTableSorterDown />}
    </SortIconWrapper>
  );
};

const SortableColumnTitle = styled.div`
  text-decoration: underline;
  text-align: center;
`;

const IconWrapper = styled.div`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 4px;
`;

const SortableColumnWrapper = styled.div`
  position: relative;
  border-right: solid 1px rgba(0, 0, 0, 0.2);
  cursor: pointer;
`;

const TableBody = styled.tbody`
  height: 160px;
  width: 100%;
  background-color: #fff;
`;

const TableCell = styled.td`
  text-align: center;
`;

const Wrapper = styled.div`
  width: 428px;
  transform: translateX(-50%);
  margin-left: 50%;
`;

const TableRow = styled.tr`
  display: table-row;
  width: 100%;
`;

const StyledTableDataCell = styled.td<{ $align?: string; $bordered?: boolean }>`
  font-weight: normal;
  text-align: ${({ $align }) => ($align ? $align : "left")};
  padding: 12px;
  word-break: break-all;

  ${({ $bordered }) =>
    $bordered &&
    css`
      border-inline-end: 1px solid #e2e3e5;
      border-bottom: 1px solid #e2e3e5;
    `}
`;

const StyledHeaderCell = styled.th<{ width?: string }>`
  font-weight: normal;
  background-color: #e0e6ec;
  height: 40px;
  &:not(:last-child) {
    span {
      border-right: solid 1px rgba(0, 0, 0, 0.2);
    }
  }
  width: ${({ width }) => (width ? width : "unset")};
`;

const ChildrenWrapper = styled.span`
  text-align: center;
  width: 100%;
  display: block;
`;

const TableWrapper = styled.div`
  font-size: 14px;
  table {
    width: 100%;
    font-size: 14px;
  }
  tr:not(:last-child) {
    border-bottom: solid 1px #e2e3e5;
  }
`;

const SortIconWrapper = styled.div`
  width: 12px;
  margin-right: 4px;
`;

const StyledTableRow = styled.tr<{
  $clickable: boolean;
  $disabled: boolean;
}>`
  &:hover {
    background-color: #eaf0f5;
  }
  background-color: ${({ $disabled }) =>
    $disabled ? "rgba(0,0,0,0.3)" : "#fff"};
  cursor: ${({ $clickable }) => ($clickable ? "pointer" : "auto")};
`;
