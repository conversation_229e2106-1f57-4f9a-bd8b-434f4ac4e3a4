import { gql } from "@/apis/gql/apollo-client";

export const GET_KENSA_CENTER_PARTNERSHIP = gql`
  query getApiKensaCenterPartnershipGetKensaCenterPartnership {
    getApiKensaCenterPartnershipGetKensaCenterPartnership {
      data {
        listData {
          startDate
          masterUpdateDate
          hpId
          endDate
          dspCenterName
          centerName
          centerKey
          centerCd
        }
      }
      status
      message
    }
  }
`;

export const CREATE_KENSA_CENTER_PARTNERSHIP = gql`
  mutation postApiKensaCenterPartnershipRegisterKensaCenterPartnership(
    $input: EmrCloudApiRequestsKensaCenterPartnershipRegisterKensaCenterPartnershipRequestInput
  ) {
    postApiKensaCenterPartnershipRegisterKensaCenterPartnership(
      emrCloudApiRequestsKensaCenterPartnershipRegisterKensaCenterPartnershipRequestInput: $input
    ) {
      status
      message
    }
  }
`;

export const UPDATE_KENSA_CENTER_PARTNERSHIP = gql`
  mutation postApiKensaCenterPartnershipUpdateKensaCenterPartnership(
    $input: EmrCloudApiRequestsKensaCenterPartnershipUpdateKensaCenterPartnershipRequestInput
  ) {
    postApiKensaCenterPartnershipUpdateKensaCenterPartnership(
      emrCloudApiRequestsKensaCenterPartnershipUpdateKensaCenterPartnershipRequestInput: $input
    ) {
      status
      message
    }
  }
`;

export const DELETE_KENSA_CENTER_PARTNERSHIP = gql`
  mutation postApiKensaCenterPartnershipUnregisterKensaCenterPartnership(
    $input: EmrCloudApiRequestsKensaCenterPartnershipUnregisterKensaCenterPartnershipRequestInput
  ) {
    postApiKensaCenterPartnershipUnregisterKensaCenterPartnership(
      emrCloudApiRequestsKensaCenterPartnershipUnregisterKensaCenterPartnershipRequestInput: $input
    ) {
      status
      message
    }
  }
`;

export const UPDATE_KENSA_CENTER_PARTNERSHIP_MST_UPDATE_DATE = gql`
  mutation postApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDate {
    postApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDate {
      status
      message
    }
  }
`;
