import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetPortalHospitalNotificationsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetPortalHospitalNotificationsQuery = {
  __typename?: "query_root";
  getPortalHospitalNotifications: {
    __typename?: "GetPortalHospitalNotificationsRes";
    count: number;
    hospitalNotifications?: Array<{
      __typename?: "PortalHospitalNotificationOutput";
      hospitalNotificationInfo: {
        __typename?: "PortalHospitalNotification";
        hospitalNotificationId: number;
        status: number;
        title: string;
        description: string;
        startDate?: string;
        endDate?: string;
        createdTime?: string;
      };
      hospitalNotificationCreateStaff?: {
        __typename?: "NotificationCreateStaffInfo";
        staffId: number;
        staffName: string;
      };
    }>;
  };
};

export type GetPortalHospitalNotificationByIdQueryVariables = Types.Exact<{
  hospitalNotificationId: Types.Scalars["Int"]["input"];
}>;

export type GetPortalHospitalNotificationByIdQuery = {
  __typename?: "query_root";
  getPortalHospitalNotification: {
    __typename?: "GetPortalHospitalNotificationRes";
    hospitalNotification?: {
      __typename?: "PortalHospitalNotificationOutput";
      hospitalNotificationInfo: {
        __typename?: "PortalHospitalNotification";
        hospitalNotificationId: number;
        title: string;
        status: number;
        description: string;
        startDate?: string;
        endDate?: string;
        createdTime?: string;
      };
      hospitalNotificationCreateStaff?: {
        __typename?: "NotificationCreateStaffInfo";
        staffId: number;
        staffName: string;
      };
    };
  };
};

export type CreatePortalHospitalNotificationMutationVariables = Types.Exact<{
  input: Types.CreatePortalHospitalNotificationInput;
}>;

export type CreatePortalHospitalNotificationMutation = {
  __typename?: "mutation_root";
  createPortalHospitalNotification: {
    __typename?: "CreatePortalHospitalNotificationRes";
    hospitalNotificationId: number;
  };
};

export type EditPortalHospitalNotificationMutationVariables = Types.Exact<{
  input: Types.EditPortalHospitalNotificationInput;
}>;

export type EditPortalHospitalNotificationMutation = {
  __typename?: "mutation_root";
  editPortalHospitalNotification: boolean;
};

export type DeletePortalHospitalNotificationMutationVariables = Types.Exact<{
  input: Types.DeletePortalHospitalNotificationInput;
}>;

export type DeletePortalHospitalNotificationMutation = {
  __typename?: "mutation_root";
  deletePortalHospitalNotification: boolean;
};

export const GetPortalHospitalNotificationsDocument = gql`
  query getPortalHospitalNotifications {
    getPortalHospitalNotifications {
      hospitalNotifications {
        hospitalNotificationInfo {
          hospitalNotificationId
          status
          title
          description
          startDate
          endDate
          createdTime
        }
        hospitalNotificationCreateStaff {
          staffId
          staffName
        }
      }
      count
    }
  }
`;

/**
 * __useGetPortalHospitalNotificationsQuery__
 *
 * To run a query within a React component, call `useGetPortalHospitalNotificationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPortalHospitalNotificationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPortalHospitalNotificationsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetPortalHospitalNotificationsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetPortalHospitalNotificationsQuery,
    GetPortalHospitalNotificationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPortalHospitalNotificationsQuery,
    GetPortalHospitalNotificationsQueryVariables
  >(GetPortalHospitalNotificationsDocument, options);
}
export function useGetPortalHospitalNotificationsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPortalHospitalNotificationsQuery,
    GetPortalHospitalNotificationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPortalHospitalNotificationsQuery,
    GetPortalHospitalNotificationsQueryVariables
  >(GetPortalHospitalNotificationsDocument, options);
}
export function useGetPortalHospitalNotificationsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPortalHospitalNotificationsQuery,
    GetPortalHospitalNotificationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPortalHospitalNotificationsQuery,
    GetPortalHospitalNotificationsQueryVariables
  >(GetPortalHospitalNotificationsDocument, options);
}
export type GetPortalHospitalNotificationsQueryHookResult = ReturnType<
  typeof useGetPortalHospitalNotificationsQuery
>;
export type GetPortalHospitalNotificationsLazyQueryHookResult = ReturnType<
  typeof useGetPortalHospitalNotificationsLazyQuery
>;
export type GetPortalHospitalNotificationsSuspenseQueryHookResult = ReturnType<
  typeof useGetPortalHospitalNotificationsSuspenseQuery
>;
export type GetPortalHospitalNotificationsQueryResult = Apollo.QueryResult<
  GetPortalHospitalNotificationsQuery,
  GetPortalHospitalNotificationsQueryVariables
>;
export const GetPortalHospitalNotificationByIdDocument = gql`
  query getPortalHospitalNotificationById($hospitalNotificationId: Int!) {
    getPortalHospitalNotification(
      hospitalNotificationId: $hospitalNotificationId
    ) {
      hospitalNotification {
        hospitalNotificationInfo {
          hospitalNotificationId
          title
          status
          description
          startDate
          endDate
          createdTime
        }
        hospitalNotificationCreateStaff {
          staffId
          staffName
        }
      }
    }
  }
`;

/**
 * __useGetPortalHospitalNotificationByIdQuery__
 *
 * To run a query within a React component, call `useGetPortalHospitalNotificationByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPortalHospitalNotificationByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPortalHospitalNotificationByIdQuery({
 *   variables: {
 *      hospitalNotificationId: // value for 'hospitalNotificationId'
 *   },
 * });
 */
export function useGetPortalHospitalNotificationByIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPortalHospitalNotificationByIdQuery,
    GetPortalHospitalNotificationByIdQueryVariables
  > &
    (
      | {
          variables: GetPortalHospitalNotificationByIdQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPortalHospitalNotificationByIdQuery,
    GetPortalHospitalNotificationByIdQueryVariables
  >(GetPortalHospitalNotificationByIdDocument, options);
}
export function useGetPortalHospitalNotificationByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPortalHospitalNotificationByIdQuery,
    GetPortalHospitalNotificationByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPortalHospitalNotificationByIdQuery,
    GetPortalHospitalNotificationByIdQueryVariables
  >(GetPortalHospitalNotificationByIdDocument, options);
}
export function useGetPortalHospitalNotificationByIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPortalHospitalNotificationByIdQuery,
    GetPortalHospitalNotificationByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPortalHospitalNotificationByIdQuery,
    GetPortalHospitalNotificationByIdQueryVariables
  >(GetPortalHospitalNotificationByIdDocument, options);
}
export type GetPortalHospitalNotificationByIdQueryHookResult = ReturnType<
  typeof useGetPortalHospitalNotificationByIdQuery
>;
export type GetPortalHospitalNotificationByIdLazyQueryHookResult = ReturnType<
  typeof useGetPortalHospitalNotificationByIdLazyQuery
>;
export type GetPortalHospitalNotificationByIdSuspenseQueryHookResult =
  ReturnType<typeof useGetPortalHospitalNotificationByIdSuspenseQuery>;
export type GetPortalHospitalNotificationByIdQueryResult = Apollo.QueryResult<
  GetPortalHospitalNotificationByIdQuery,
  GetPortalHospitalNotificationByIdQueryVariables
>;
export const CreatePortalHospitalNotificationDocument = gql`
  mutation createPortalHospitalNotification(
    $input: CreatePortalHospitalNotificationInput!
  ) {
    createPortalHospitalNotification(input: $input) {
      hospitalNotificationId
    }
  }
`;
export type CreatePortalHospitalNotificationMutationFn =
  Apollo.MutationFunction<
    CreatePortalHospitalNotificationMutation,
    CreatePortalHospitalNotificationMutationVariables
  >;

/**
 * __useCreatePortalHospitalNotificationMutation__
 *
 * To run a mutation, you first call `useCreatePortalHospitalNotificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreatePortalHospitalNotificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createPortalHospitalNotificationMutation, { data, loading, error }] = useCreatePortalHospitalNotificationMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreatePortalHospitalNotificationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreatePortalHospitalNotificationMutation,
    CreatePortalHospitalNotificationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreatePortalHospitalNotificationMutation,
    CreatePortalHospitalNotificationMutationVariables
  >(CreatePortalHospitalNotificationDocument, options);
}
export type CreatePortalHospitalNotificationMutationHookResult = ReturnType<
  typeof useCreatePortalHospitalNotificationMutation
>;
export type CreatePortalHospitalNotificationMutationResult =
  Apollo.MutationResult<CreatePortalHospitalNotificationMutation>;
export type CreatePortalHospitalNotificationMutationOptions =
  Apollo.BaseMutationOptions<
    CreatePortalHospitalNotificationMutation,
    CreatePortalHospitalNotificationMutationVariables
  >;
export const EditPortalHospitalNotificationDocument = gql`
  mutation EditPortalHospitalNotification(
    $input: EditPortalHospitalNotificationInput!
  ) {
    editPortalHospitalNotification(input: $input)
  }
`;
export type EditPortalHospitalNotificationMutationFn = Apollo.MutationFunction<
  EditPortalHospitalNotificationMutation,
  EditPortalHospitalNotificationMutationVariables
>;

/**
 * __useEditPortalHospitalNotificationMutation__
 *
 * To run a mutation, you first call `useEditPortalHospitalNotificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditPortalHospitalNotificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editPortalHospitalNotificationMutation, { data, loading, error }] = useEditPortalHospitalNotificationMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useEditPortalHospitalNotificationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditPortalHospitalNotificationMutation,
    EditPortalHospitalNotificationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditPortalHospitalNotificationMutation,
    EditPortalHospitalNotificationMutationVariables
  >(EditPortalHospitalNotificationDocument, options);
}
export type EditPortalHospitalNotificationMutationHookResult = ReturnType<
  typeof useEditPortalHospitalNotificationMutation
>;
export type EditPortalHospitalNotificationMutationResult =
  Apollo.MutationResult<EditPortalHospitalNotificationMutation>;
export type EditPortalHospitalNotificationMutationOptions =
  Apollo.BaseMutationOptions<
    EditPortalHospitalNotificationMutation,
    EditPortalHospitalNotificationMutationVariables
  >;
export const DeletePortalHospitalNotificationDocument = gql`
  mutation DeletePortalHospitalNotification(
    $input: DeletePortalHospitalNotificationInput!
  ) {
    deletePortalHospitalNotification(input: $input)
  }
`;
export type DeletePortalHospitalNotificationMutationFn =
  Apollo.MutationFunction<
    DeletePortalHospitalNotificationMutation,
    DeletePortalHospitalNotificationMutationVariables
  >;

/**
 * __useDeletePortalHospitalNotificationMutation__
 *
 * To run a mutation, you first call `useDeletePortalHospitalNotificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeletePortalHospitalNotificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deletePortalHospitalNotificationMutation, { data, loading, error }] = useDeletePortalHospitalNotificationMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useDeletePortalHospitalNotificationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeletePortalHospitalNotificationMutation,
    DeletePortalHospitalNotificationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeletePortalHospitalNotificationMutation,
    DeletePortalHospitalNotificationMutationVariables
  >(DeletePortalHospitalNotificationDocument, options);
}
export type DeletePortalHospitalNotificationMutationHookResult = ReturnType<
  typeof useDeletePortalHospitalNotificationMutation
>;
export type DeletePortalHospitalNotificationMutationResult =
  Apollo.MutationResult<DeletePortalHospitalNotificationMutation>;
export type DeletePortalHospitalNotificationMutationOptions =
  Apollo.BaseMutationOptions<
    DeletePortalHospitalNotificationMutation,
    DeletePortalHospitalNotificationMutationVariables
  >;
