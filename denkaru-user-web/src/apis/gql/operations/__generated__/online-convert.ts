import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiOnlineConvertXmlToQcXmlMsgMutationVariables = Types.Exact<{
  payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineConvertXmlToQcXmlMsgRequestInput>;
}>;

export type PostApiOnlineConvertXmlToQcXmlMsgMutation = {
  __typename?: "mutation_root";
  postApiOnlineConvertXmlToQCXmlMsg?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineConvertXmlToQcXmlMsgResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineConvertXmlToQcXmlMsgResponse";
      qcXmlMsgResponse?: {
        __typename?: "DomainModelsOnlineQualificationConfirmationQcXmlMsgResponse";
        messageBody?: {
          __typename?: "DomainModelsOnlineQualificationConfirmationMessageBody";
          processingResultCode?: string;
          processingResultMessage?: string;
          processingResultStatus?: string;
          qualificationValidity?: string;
          qualificationConfirmSearchInfo?: {
            __typename?: "DomainModelsOnlineQualificationConfirmationQualificationConfirmSearchInfo";
            arbitraryIdentifier?: string;
            birthdate?: string;
            insuredBranchNumber?: string;
            insuredCardSymbol?: string;
            insuredIdentificationNumber?: string;
            insurerNumber?: string;
            limitApplicationCertificateRelatedConsFlg?: string;
          };
          resultList?: {
            __typename?: "DomainModelsOnlineQualificationConfirmationResultList";
            resultOfQualificationConfirmation?: Array<{
              __typename?: "DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation";
              address?: string;
              arbitraryIdentifier?: string;
              birthdate?: string;
              diagnosisInfoAvailableTime?: string;
              diagnosisInfoConsFlg?: string;
              diagnosisInfoConsTime?: string;
              insuredBranchNumber?: string;
              insuredCardClassification?: string;
              insuredCardExpirationDate?: string;
              insuredCardSymbol?: string;
              insuredCardValidDate?: string;
              insuredCertificateIssuanceDate?: string;
              insuredIdentificationNumber?: string;
              insuredName?: string;
              insuredPartialContributionRatio?: string;
              insurerName?: string;
              insurerNumber?: string;
              limitApplicationCertificateRelatedConsFlg?: string;
              limitApplicationCertificateRelatedConsTime?: string;
              name?: string;
              nameKana?: string;
              nameOfOther?: string;
              nameOfOtherKana?: string;
              operationInfoAvailableTime?: string;
              operationInfoConsFlg?: string;
              operationInfoConsTime?: string;
              personalFamilyClassification?: string;
              pharmacistsInfoAvailableTime?: string;
              pharmacistsInfoConsFlg?: string;
              pharmacistsInfoConsTime?: string;
              postNumber?: string;
              preschoolClassification?: string;
              reasonOfLoss?: string;
              referenceNumber?: string;
              sex1?: string;
              sex2?: string;
              specificHealthCheckupsInfoConsTime?: string;
              specificHealthCheckupsInfoConsFlg?: string;
              specificHealthCheckupsInfoAvailableTime?: string;
              specificDiseasesCertificateRelatedConsTime?: string;
              specificDiseasesCertificateRelatedConsFlg?: string;
              elderlyRecipientCertificateInfo?: {
                __typename?: "DomainModelsOnlineQualificationConfirmationElderlyRecipientCertificateInfo";
                elderlyRecipientCertificateDate?: string;
                elderlyRecipientContributionRatio?: string;
                elderlyRecipientValidEndDate?: string;
                elderlyRecipientValidStartDate?: string;
              };
              limitApplicationCertificateRelatedInfo?: {
                __typename?: "DomainModelsOnlineQualificationConfirmationLimitApplicationCertificateRelatedInfo";
                limitApplicationCertificateClassification?: string;
                limitApplicationCertificateClassificationFlag?: string;
                limitApplicationCertificateDate?: string;
                limitApplicationCertificateLongTermDate?: string;
                limitApplicationCertificateValidEndDate?: string;
                limitApplicationCertificateValidStartDate?: string;
              };
              publicExpenseResultList?: {
                __typename?: "DomainModelsOnlineQualificationConfirmationPublicExpenseResultList";
                medicalTicketInfo?: Array<{
                  __typename?: "DomainModelsOnlineQualificationConfirmationMedicalTicketInfo";
                  consistencyFlag?: string;
                  designatedMedicalInstitutionCode?: string;
                  designatedMedicalInstitutionFlag?: string;
                  designatedMedicalInstitutionName?: string;
                  districtContactName?: string;
                  handlingContactName?: string;
                  injuryName1?: string;
                  injuryName2?: string;
                  injuryName3?: string;
                  issueNumber?: string;
                  medicalTicketExpirationDate?: string;
                  medicalTicketValidDate?: string;
                  medicalTreatmentMonth?: string;
                  medicalTreatmentType?: string;
                  prescriptionIssuerMedicalInstitutionCode?: string;
                  prescriptionIssuerMedicalInstitutionName?: string;
                  remarks1?: string;
                  remarks2?: string;
                  remarks3?: string;
                  selfPayAmount?: string;
                  singleOrCombinedUse?: string;
                  statusOfElderlyMedicalCare?: string;
                  statusOfInfecton?: string;
                  statusOfPrefecturalExpenses?: string;
                  statusOfSocialInsurance?: string;
                  ticketType?: string;
                }>;
              };
              specificDiseasesCertificateList?: {
                __typename?: "DomainModelsOnlineQualificationConfirmationSpecificDiseasesCertificateList";
                specificDiseasesCertificateInfo?: Array<{
                  __typename?: "DomainModelsOnlineQualificationConfirmationSpecificDiseasesCertificateInfo";
                  specificDiseasesCertificateDate?: string;
                  specificDiseasesDiseaseCategory?: string;
                  specificDiseasesSelfPay?: string;
                  specificDiseasesValidEndDate?: string;
                  specificDiseasesValidStartDate?: string;
                }>;
              };
            }>;
          };
        };
        messageHeader?: {
          __typename?: "DomainModelsOnlineQualificationConfirmationMessageHeader";
          arbitraryFileIdentifier?: string;
          characterCodeIdentifier?: string;
          errorCode?: string;
          errorMessage?: string;
          medicalInstitutionCode?: string;
          processExecutionTime?: string;
          qualificationConfirmationDate?: string;
          referenceClassification?: string;
          segmentOfResult?: string;
        };
      };
    };
  };
};

export const PostApiOnlineConvertXmlToQcXmlMsgDocument = gql`
  mutation postApiOnlineConvertXmlToQCXmlMsg(
    $payload: EmrCloudApiRequestsOnlineConvertXmlToQcXmlMsgRequestInput
  ) {
    postApiOnlineConvertXmlToQCXmlMsg(
      emrCloudApiRequestsOnlineConvertXmlToQcXmlMsgRequestInput: $payload
    ) {
      data {
        qcXmlMsgResponse {
          messageBody {
            processingResultCode
            processingResultMessage
            processingResultStatus
            qualificationValidity
            qualificationConfirmSearchInfo {
              arbitraryIdentifier
              birthdate
              insuredBranchNumber
              insuredCardSymbol
              insuredIdentificationNumber
              insurerNumber
              limitApplicationCertificateRelatedConsFlg
            }
            resultList {
              resultOfQualificationConfirmation {
                elderlyRecipientCertificateInfo {
                  elderlyRecipientCertificateDate
                  elderlyRecipientContributionRatio
                  elderlyRecipientValidEndDate
                  elderlyRecipientValidStartDate
                }
                limitApplicationCertificateRelatedInfo {
                  limitApplicationCertificateClassification
                  limitApplicationCertificateClassificationFlag
                  limitApplicationCertificateDate
                  limitApplicationCertificateLongTermDate
                  limitApplicationCertificateValidEndDate
                  limitApplicationCertificateValidStartDate
                }
                address
                arbitraryIdentifier
                birthdate
                diagnosisInfoAvailableTime
                diagnosisInfoConsFlg
                diagnosisInfoConsTime
                insuredBranchNumber
                insuredCardClassification
                insuredCardExpirationDate
                insuredCardSymbol
                insuredCardValidDate
                insuredCertificateIssuanceDate
                insuredIdentificationNumber
                insuredName
                insuredPartialContributionRatio
                insurerName
                insurerNumber
                limitApplicationCertificateRelatedConsFlg
                limitApplicationCertificateRelatedConsTime
                name
                nameKana
                nameOfOther
                nameOfOtherKana
                operationInfoAvailableTime
                operationInfoConsFlg
                operationInfoConsTime
                personalFamilyClassification
                pharmacistsInfoAvailableTime
                pharmacistsInfoConsFlg
                pharmacistsInfoConsTime
                postNumber
                preschoolClassification
                publicExpenseResultList {
                  medicalTicketInfo {
                    consistencyFlag
                    designatedMedicalInstitutionCode
                    designatedMedicalInstitutionFlag
                    designatedMedicalInstitutionName
                    districtContactName
                    handlingContactName
                    injuryName1
                    injuryName2
                    injuryName3
                    issueNumber
                    medicalTicketExpirationDate
                    medicalTicketValidDate
                    medicalTreatmentMonth
                    medicalTreatmentType
                    prescriptionIssuerMedicalInstitutionCode
                    prescriptionIssuerMedicalInstitutionName
                    remarks1
                    remarks2
                    remarks3
                    selfPayAmount
                    singleOrCombinedUse
                    statusOfElderlyMedicalCare
                    statusOfInfecton
                    statusOfPrefecturalExpenses
                    statusOfSocialInsurance
                    ticketType
                  }
                }
                reasonOfLoss
                referenceNumber
                sex1
                sex2
                specificDiseasesCertificateList {
                  specificDiseasesCertificateInfo {
                    specificDiseasesCertificateDate
                    specificDiseasesDiseaseCategory
                    specificDiseasesSelfPay
                    specificDiseasesValidEndDate
                    specificDiseasesValidStartDate
                  }
                }
                specificHealthCheckupsInfoConsTime
                specificHealthCheckupsInfoConsFlg
                specificHealthCheckupsInfoAvailableTime
                specificDiseasesCertificateRelatedConsTime
                specificDiseasesCertificateRelatedConsFlg
              }
            }
          }
          messageHeader {
            arbitraryFileIdentifier
            characterCodeIdentifier
            errorCode
            errorMessage
            medicalInstitutionCode
            processExecutionTime
            qualificationConfirmationDate
            referenceClassification
            segmentOfResult
          }
        }
      }
    }
  }
`;
export type PostApiOnlineConvertXmlToQcXmlMsgMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineConvertXmlToQcXmlMsgMutation,
    PostApiOnlineConvertXmlToQcXmlMsgMutationVariables
  >;

/**
 * __usePostApiOnlineConvertXmlToQcXmlMsgMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineConvertXmlToQcXmlMsgMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineConvertXmlToQcXmlMsgMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineConvertXmlToQcXmlMsgMutation, { data, loading, error }] = usePostApiOnlineConvertXmlToQcXmlMsgMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineConvertXmlToQcXmlMsgMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineConvertXmlToQcXmlMsgMutation,
    PostApiOnlineConvertXmlToQcXmlMsgMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineConvertXmlToQcXmlMsgMutation,
    PostApiOnlineConvertXmlToQcXmlMsgMutationVariables
  >(PostApiOnlineConvertXmlToQcXmlMsgDocument, options);
}
export type PostApiOnlineConvertXmlToQcXmlMsgMutationHookResult = ReturnType<
  typeof usePostApiOnlineConvertXmlToQcXmlMsgMutation
>;
export type PostApiOnlineConvertXmlToQcXmlMsgMutationResult =
  Apollo.MutationResult<PostApiOnlineConvertXmlToQcXmlMsgMutation>;
export type PostApiOnlineConvertXmlToQcXmlMsgMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineConvertXmlToQcXmlMsgMutation,
    PostApiOnlineConvertXmlToQcXmlMsgMutationVariables
  >;
