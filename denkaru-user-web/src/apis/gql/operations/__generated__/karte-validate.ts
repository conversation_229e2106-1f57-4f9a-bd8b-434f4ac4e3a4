import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiMedicalExaminationOrderRealtimeCheckerMutationVariables =
  Types.Exact<{
    emrCloudApiRequestsMedicalExaminationOrderRealtimeCheckerRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationOrderRealtimeCheckerRequestInput>;
  }>;

export type PostApiMedicalExaminationOrderRealtimeCheckerMutation = {
  __typename?: "mutation_root";
  postApiMedicalExaminationOrderRealtimeChecker?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationOrderRealtimeCheckerResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationOrderRealtimeCheckerResponse";
      status?: number;
      errorInfoModels?: Array<{
        __typename?: "CommonCheckerModelsErrorInfoModel";
        checkingItemCd?: string;
        currentItemCd?: string;
        errorType?: number;
        firstCellContent?: string;
        fourthCellContent?: string;
        highlightColorCode?: string;
        id?: string;
        secondCellContent?: string;
        suggestedContent?: string;
        thridCellContent?: string;
        listLevelInfo?: Array<{
          __typename?: "CommonCheckerModelsLevelInfoModel";
          backgroundCode?: string;
          borderBrushCode?: string;
          caption?: string;
          comment?: string;
          firstItemName?: string;
          isShowLevelButton?: boolean;
          level?: number;
          secondItemName?: string;
          title?: string;
        }>;
      }>;
    };
  };
};

export type PostApiTodayOrdCheckedExpiredMutationVariables = Types.Exact<{
  emrCloudApiRequestsMedicalExaminationCheckedExpiredRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationCheckedExpiredRequestInput>;
}>;

export type PostApiTodayOrdCheckedExpiredMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdCheckedExpired?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationCheckedExpiredResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationCheckedExpiredResponse";
      messages?: Array<{
        __typename?: "UseCaseMedicalExaminationCheckedExpiredCheckedExpiredOutputItem";
        itemCd?: string;
        itemName?: string;
        sinKouiKbn?: number;
      }>;
    };
  };
};

export type PostApiTodayOrdGetInfCheckedItemNameMutationVariables =
  Types.Exact<{
    emrCloudApiRequestsMedicalExaminationCheckedItemNameRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationCheckedItemNameRequestInput>;
  }>;

export type PostApiTodayOrdGetInfCheckedItemNameMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdGetInfCheckedItemName?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationCheckedItemNameResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationCheckedItemNameResponse";
      checkedItemNames?: any;
    };
  };
};

export type PostApiTodayOrdAutoCheckOrderMutationVariables = Types.Exact<{
  emrCloudApiRequestsMedicalExaminationAutoCheckOrderRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationAutoCheckOrderRequestInput>;
}>;

export type PostApiTodayOrdAutoCheckOrderMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdAutoCheckOrder?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationAutoCheckOrderResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationAutoCheckOrderResponse";
      autoCheckOrderItems?: Array<{
        __typename?: "UseCaseMedicalExaminationAutoCheckOrderAutoCheckOrderItem";
        message?: string;
        odrInfDetailPosition?: number;
        odrInfPosition?: number;
        suryo?: number;
        type?: number;
        tenItemMst?: {
          __typename?: "DomainModelsMstItemTenItemModel";
          buiKbn?: number;
          cdEdano?: number;
          cdKbn?: string;
          cdKbnno?: number;
          centerCd?: string;
          centerName?: string;
          cmtCol1?: number;
          cmtCol2?: number;
          cmtCol3?: number;
          cmtCol4?: number;
          cmtColKeta1?: number;
          cmtColKeta2?: number;
          cmtColKeta3?: number;
          cmtColKeta4?: number;
          cnvTermVal?: number;
          cnvUnitName?: string;
          createDate?: string;
          defaultValue?: number;
          drugKbn?: number;
          endDate?: number;
          formattedEndDate?: string;
          formattedStartDate?: string;
          handanGrpKbn?: number;
          ipnCD?: string;
          hpId?: number;
          ipnName?: string;
          ipnNameCd?: string;
          isAdopted?: number;
          isDefault?: boolean;
          isDeleted?: number;
          isGetPriceInYakka?: boolean;
          isKensaMstEmpty?: boolean;
          isNoSearch?: number;
          isNodspRece?: number;
          isSanteiItem?: boolean;
          itemCd?: string;
          itemType?: number;
          jihiSbt?: number;
          kanaName1?: string;
          kanaName2?: string;
          kanaName3?: string;
          kanaName4?: string;
          kanaName5?: string;
          kanaName6?: string;
          kanaName7?: string;
          kasan1?: number;
          kasan2?: number;
          kensaCenterItemCDDisplay?: string;
          kensaItemCd?: string;
          kensaItemSeqNo?: number;
          kensaMstCenterItemCd1?: string;
          kensaMstCenterItemCd2?: string;
          kohatuKbn?: number;
          kohatuKbnDisplay?: string;
          kokuji1?: string;
          kokuji2?: string;
          kouiName?: string;
          kouseisinKbn?: number;
          kouseisinKbnDisplay?: string;
          kubunToDisplay?: string;
          listGenDate?: Array<string>;
          madokuKbn?: number;
          masterSbt?: string;
          maxAge?: string;
          minAge?: string;
          modeStatus?: number;
          name?: string;
          odrTermVal?: number;
          odrUnitName?: string;
          readOnlyStartDate?: string;
          receName?: string;
          rousaiKbn?: number;
          rousaiKbnDisplay?: string;
          santeiItemCd?: string;
          yohoKbn?: number;
          yjCd?: string;
          yakka?: number;
          tenId?: number;
          tenDisplay?: string;
          ten?: number;
          startDate?: number;
          sinKouiKbn?: number;
          senteiRyoyoYakka?: number;
          senteiRyoyoKbn?: number;
          santeigaiKbn?: number;
        };
      }>;
    };
  };
};

export type PostApiTodayOrdChangeAfterAutoCheckOrderMutationVariables =
  Types.Exact<{
    emrCloudApiRequestsMedicalExaminationChangeAfterAutoCheckOrderRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationChangeAfterAutoCheckOrderRequestInput>;
  }>;

export type PostApiTodayOrdChangeAfterAutoCheckOrderMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdChangeAfterAutoCheckOrder?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationChangeAfterAutoCheckOrderResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationChangeAfterAutoCheckOrderResponse";
      odrInfItems?: Array<{
        __typename?: "UseCaseMedicalExaminationChangeAfterAutoCheckOrderChangeAfterAutoCheckOrderItem";
        position?: number;
        odrInfItem?: {
          __typename?: "UseCaseOrdInfsGetListTreesOdrInfItem";
          createDate?: string;
          createId?: number;
          createMachine?: string;
          createName?: string;
          daysCnt?: number;
          groupOdrKouiKbn?: number;
          hokenPid?: number;
          hpId?: number;
          id?: string;
          inoutKbn?: number;
          isDeleted?: number;
          updateName?: string;
          updateMachine?: string;
          updateDate?: string;
          tosekiKbn?: number;
          syohoSbt?: number;
          sortNo?: number;
          sinDate?: number;
          sikyuKbn?: number;
          santeiKbn?: number;
          rpNo?: string;
          rpName?: string;
          rpEdaNo?: string;
          raiinNo?: string;
          ptId?: string;
          odrKouiKbn?: number;
          odrDetails?: Array<{
            __typename?: "UseCaseOrdInfsGetListTreesOdrInfDetailItem";
            alternationIndex?: number;
            bunkatu?: string;
            bunkatuKoui?: number;
            centerItemCd1?: string;
            centerItemCd2?: string;
            cmtCol1?: number;
            cmtCol2?: number;
            cmtCol3?: number;
            cmtCol4?: number;
            cmtColKeta1?: number;
            cmtColKeta2?: number;
            cmtColKeta3?: number;
            cmtColKeta4?: number;
            cmtName?: string;
            cmtOpt?: string;
            cnvTermVal?: number;
            cnvUnitName?: string;
            commentNewline?: number;
            displayItemName?: string;
            drugKbn?: number;
            fontColor?: string;
            handanGrpKbn?: number;
            hasCmtName?: boolean;
            hpId?: number;
            ipnCd?: string;
            ipnName?: string;
            isGetPriceInYakka?: boolean;
            isKensaMstEmpty?: boolean;
            isNodspRece?: number;
            itemCd?: string;
            itemName?: string;
            jissiDate?: string;
            jissiId?: number;
            jissiKbn?: number;
            jissiMachine?: string;
            kasan1?: number;
            kasan2?: number;
            kensaGaichu?: number;
            kikakiUnit?: string;
            kokuji1?: string;
            kohatuKbn?: number;
            kokuji2?: string;
            masterSbt?: string;
            memoItem?: string;
            odrTermVal?: number;
            odrUnitName?: string;
            ptId?: string;
            raiinNo?: string;
            reqCd?: string;
            rikikaRate?: number;
            rikikaUnit?: string;
            rowNo?: number;
            rpEdaNo?: string;
            rpNo?: string;
            sinDate?: number;
            sinKouiKbn?: number;
            suryo?: number;
            syohoKbn?: number;
            syohoLimitKbn?: number;
            ten?: number;
            termVal?: number;
            unitName?: string;
            unitSbt?: number;
            yakka?: number;
            yakkaiUnit?: string;
            yjCd?: string;
            yohoKbn?: number;
            youkaiekiCd?: string;
          }>;
        };
      }>;
    };
  };
};

export type PostApiTodayOrdGetAddedAutoItemMutationVariables = Types.Exact<{
  emrCloudApiRequestsMedicalExaminationGetAddedAutoItemRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationGetAddedAutoItemRequestInput>;
}>;

export type PostApiTodayOrdGetAddedAutoItemMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdGetAddedAutoItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationGetAddedAutoItemResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationGetAddedAutoItemResponse";
      addedAutoItems?: Array<{
        __typename?: "UseCaseMedicalExaminationGetAddedAutoItemAddedAutoItem";
        orderDetailPosition?: number;
        orderPosition?: number;
        addedOrderDetails?: Array<{
          __typename?: "UseCaseMedicalExaminationGetAddedAutoItemAddedOrderDetail";
          id?: string;
          itemCd?: string;
          itemName?: string;
        }>;
      }>;
    };
  };
};

export type PostApiTodayOrdAddAutoItemMutationVariables = Types.Exact<{
  emrCloudApiRequestsMedicalExaminationAddAutoItemRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationAddAutoItemRequestInput>;
}>;

export type PostApiTodayOrdAddAutoItemMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdAddAutoItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationAddAutoItemResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationAddAutoItemResponse";
      odrInfItemInputDatas?: Array<{
        __typename?: "UseCaseOrdInfsGetListTreesOdrInfItem";
        createDate?: string;
        createMachine?: string;
        createId?: number;
        createName?: string;
        daysCnt?: number;
        groupOdrKouiKbn?: number;
        hokenPid?: number;
        hpId?: number;
        id?: string;
        inoutKbn?: number;
        isDeleted?: number;
        odrKouiKbn?: number;
        raiinNo?: string;
        ptId?: string;
        rpEdaNo?: string;
        rpName?: string;
        rpNo?: string;
        sikyuKbn?: number;
        santeiKbn?: number;
        sinDate?: number;
        sortNo?: number;
        tosekiKbn?: number;
        syohoSbt?: number;
        updateDate?: string;
        updateMachine?: string;
        updateName?: string;
        odrDetails?: Array<{
          __typename?: "UseCaseOrdInfsGetListTreesOdrInfDetailItem";
          alternationIndex?: number;
          bunkatu?: string;
          bunkatuKoui?: number;
          centerItemCd2?: string;
          centerItemCd1?: string;
          cmtCol1?: number;
          cmtCol3?: number;
          cmtCol2?: number;
          cmtCol4?: number;
          cmtColKeta1?: number;
          cmtColKeta2?: number;
          cmtColKeta3?: number;
          cmtColKeta4?: number;
          cmtName?: string;
          cmtOpt?: string;
          cnvTermVal?: number;
          cnvUnitName?: string;
          commentNewline?: number;
          displayItemName?: string;
          drugKbn?: number;
          fontColor?: string;
          handanGrpKbn?: number;
          hasCmtName?: boolean;
          hpId?: number;
          ipnCd?: string;
          ipnName?: string;
          isGetPriceInYakka?: boolean;
          isKensaMstEmpty?: boolean;
          isNodspRece?: number;
          itemCd?: string;
          jissiDate?: string;
          itemName?: string;
          jissiId?: number;
          jissiKbn?: number;
          kasan1?: number;
          jissiMachine?: string;
          kasan2?: number;
          kensaGaichu?: number;
          kikakiUnit?: string;
          kohatuKbn?: number;
          kokuji1?: string;
          kokuji2?: string;
          masterSbt?: string;
          memoItem?: string;
          odrTermVal?: number;
          odrUnitName?: string;
          ptId?: string;
          raiinNo?: string;
          reqCd?: string;
          rikikaRate?: number;
          rikikaUnit?: string;
          rowNo?: number;
          rpEdaNo?: string;
          rpNo?: string;
          sinDate?: number;
          sinKouiKbn?: number;
          suryo?: number;
          syohoKbn?: number;
          syohoLimitKbn?: number;
          ten?: number;
          termVal?: number;
          unitName?: string;
          unitSbt?: number;
          yakka?: number;
          yakkaiUnit?: string;
          yjCd?: string;
          yohoKbn?: number;
          youkaiekiCd?: string;
        }>;
      }>;
    };
  };
};

export type PostApiTodayOrdConvertItemMutationVariables = Types.Exact<{
  emrCloudApiRequestsMedicalExaminationConvertItemRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationConvertItemRequestInput>;
}>;

export type PostApiTodayOrdConvertItemMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdConvertItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationConvertItemResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationConvertItemResponse";
      result?: Array<{
        __typename?: "UseCaseOrdInfsGetListTreesOdrInfItem";
        createDate?: string;
        createId?: number;
        createMachine?: string;
        createName?: string;
        daysCnt?: number;
        groupOdrKouiKbn?: number;
        hokenPid?: number;
        hpId?: number;
        id?: string;
        inoutKbn?: number;
        isDeleted?: number;
        odrKouiKbn?: number;
        ptId?: string;
        raiinNo?: string;
        rpEdaNo?: string;
        rpName?: string;
        rpNo?: string;
        santeiKbn?: number;
        sikyuKbn?: number;
        sinDate?: number;
        sortNo?: number;
        syohoSbt?: number;
        tosekiKbn?: number;
        updateDate?: string;
        updateMachine?: string;
        updateName?: string;
        odrDetails?: Array<{
          __typename?: "UseCaseOrdInfsGetListTreesOdrInfDetailItem";
          isSelectiveComment?: boolean;
          alternationIndex?: number;
          bikoComment?: number;
          buiKbn?: number;
          bunkatu?: string;
          bunkatuKoui?: number;
          centerCd?: string;
          centerItemCd1?: string;
          centerItemCd2?: string;
          centerName?: string;
          cmtCol1?: number;
          cmtCol2?: number;
          cmtCol3?: number;
          cmtCol4?: number;
          cmtColKeta2?: number;
          cmtColKeta1?: number;
          cmtColKeta3?: number;
          cmtColKeta4?: number;
          cmtName?: string;
          cmtOpt?: string;
          cnvTermVal?: number;
          cnvUnitName?: string;
          displayItemName?: string;
          commentNewline?: number;
          drugKbn?: number;
          fontColor?: string;
          handanGrpKbn?: number;
          hasCmtName?: boolean;
          hpId?: number;
          ipnCd?: string;
          ipnName?: string;
          isAdopted?: number;
          isGetPriceInYakka?: boolean;
          isKensaMstEmpty?: boolean;
          isNodspRece?: number;
          itemCd?: string;
          itemName?: string;
          jissiDate?: string;
          jissiId?: number;
          jissiKbn?: number;
          jissiMachine?: string;
          kasan1?: number;
          kasan2?: number;
          kensaGaichu?: number;
          kikakiUnit?: string;
          kohatuKbn?: number;
          kokuji1?: string;
          kokuji2?: string;
          masterSbt?: string;
          memoItem?: string;
          odrTermVal?: number;
          odrUnitName?: string;
          ptId?: string;
          raiinNo?: string;
          reqCd?: string;
          rikikaRate?: number;
          rousaiKbn?: number;
          rikikaUnit?: string;
          rowNo?: number;
          rpEdaNo?: string;
          rpNo?: string;
          senteiRyoyoKbn?: number;
          sinDate?: number;
          sinKouiKbn?: number;
          suryo?: number;
          syohoKbn?: number;
          syohoLimitKbn?: number;
          ten?: number;
          termVal?: number;
          unitName?: string;
          unitSbt?: number;
          yakka?: number;
          yakkaiUnit?: string;
          yjCd?: string;
          yohoKbn?: number;
          youkaiekiCd?: string;
        }>;
      }>;
    };
  };
};

export type PostApiTodayOrdGetValidGairaiRihaMutationVariables = Types.Exact<{
  emrCloudApiRequestsMedicalExaminationGetValidGairaiRihaRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationGetValidGairaiRihaRequestInput>;
}>;

export type PostApiTodayOrdGetValidGairaiRihaMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdGetValidGairaiRiha?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationGetValidGairaiRihaResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationGetValidGairaiRihaResponse";
      gairaiRihaItems?: Array<{
        __typename?: "UseCaseMedicalExaminationGetValidGairaiRihaGairaiRihaItem";
        itemName?: string;
        lastDaySanteiRiha?: number;
        rihaItemName?: string;
        type?: number;
      }>;
    };
  };
};

export type PostApiTodayOrdValidateMutationVariables = Types.Exact<{
  emrCloudApiRequestsMedicalExaminationValidationTodayOrdRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationValidationTodayOrdRequestInput>;
}>;

export type PostApiTodayOrdValidateMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdValidate?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationValidationTodayOrdResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationValidationTodayOrdResponse";
      validationKarte?: {
        __typename?: "EmrCloudApiResponsesKarteInfValidationKarteInfResponse";
        status?: number;
        validationMessage?: string;
      };
      validationOdrInfs?: Array<{
        __typename?: "EmrCloudApiResponsesMedicalExaminationValidationTodayOrdItemResponse";
        orderInfDetailPosition?: string;
        orderInfPosition?: string;
        status?: number;
        validationField?: string;
        validationMessage?: string;
        hokenName?: {
          __typename?: "DomainModelsTodayOdrHokenInfo";
          hoKenId?: number;
          hokenName?: string;
        };
        kohiNames?: Array<{
          __typename?: "DomainModelsTodayOdrHokenInfo";
          hoKenId?: number;
          hokenName?: string;
        }>;
      }>;
      validationRaiinInf?: {
        __typename?: "EmrCloudApiResponsesMedicalExaminationRaiinInfItemResponse";
        status?: number;
        validationMessage?: string;
        hokenName?: {
          __typename?: "DomainModelsTodayOdrHokenInfo";
          hoKenId?: number;
          hokenName?: string;
        };
        kohiNames?: Array<{
          __typename?: "DomainModelsTodayOdrHokenInfo";
          hoKenId?: number;
          hokenName?: string;
        }>;
      };
    };
  };
};

export const PostApiMedicalExaminationOrderRealtimeCheckerDocument = gql`
  mutation postApiMedicalExaminationOrderRealtimeChecker(
    $emrCloudApiRequestsMedicalExaminationOrderRealtimeCheckerRequestInput: EmrCloudApiRequestsMedicalExaminationOrderRealtimeCheckerRequestInput
  ) {
    postApiMedicalExaminationOrderRealtimeChecker(
      emrCloudApiRequestsMedicalExaminationOrderRealtimeCheckerRequestInput: $emrCloudApiRequestsMedicalExaminationOrderRealtimeCheckerRequestInput
    ) {
      message
      status
      data {
        status
        errorInfoModels {
          checkingItemCd
          currentItemCd
          errorType
          firstCellContent
          fourthCellContent
          highlightColorCode
          id
          secondCellContent
          suggestedContent
          thridCellContent
          listLevelInfo {
            backgroundCode
            borderBrushCode
            caption
            comment
            firstItemName
            isShowLevelButton
            level
            secondItemName
            title
          }
        }
      }
    }
  }
`;
export type PostApiMedicalExaminationOrderRealtimeCheckerMutationFn =
  Apollo.MutationFunction<
    PostApiMedicalExaminationOrderRealtimeCheckerMutation,
    PostApiMedicalExaminationOrderRealtimeCheckerMutationVariables
  >;

/**
 * __usePostApiMedicalExaminationOrderRealtimeCheckerMutation__
 *
 * To run a mutation, you first call `usePostApiMedicalExaminationOrderRealtimeCheckerMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMedicalExaminationOrderRealtimeCheckerMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMedicalExaminationOrderRealtimeCheckerMutation, { data, loading, error }] = usePostApiMedicalExaminationOrderRealtimeCheckerMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationOrderRealtimeCheckerRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationOrderRealtimeCheckerRequestInput'
 *   },
 * });
 */
export function usePostApiMedicalExaminationOrderRealtimeCheckerMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMedicalExaminationOrderRealtimeCheckerMutation,
    PostApiMedicalExaminationOrderRealtimeCheckerMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMedicalExaminationOrderRealtimeCheckerMutation,
    PostApiMedicalExaminationOrderRealtimeCheckerMutationVariables
  >(PostApiMedicalExaminationOrderRealtimeCheckerDocument, options);
}
export type PostApiMedicalExaminationOrderRealtimeCheckerMutationHookResult =
  ReturnType<typeof usePostApiMedicalExaminationOrderRealtimeCheckerMutation>;
export type PostApiMedicalExaminationOrderRealtimeCheckerMutationResult =
  Apollo.MutationResult<PostApiMedicalExaminationOrderRealtimeCheckerMutation>;
export type PostApiMedicalExaminationOrderRealtimeCheckerMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMedicalExaminationOrderRealtimeCheckerMutation,
    PostApiMedicalExaminationOrderRealtimeCheckerMutationVariables
  >;
export const PostApiTodayOrdCheckedExpiredDocument = gql`
  mutation postApiTodayOrdCheckedExpired(
    $emrCloudApiRequestsMedicalExaminationCheckedExpiredRequestInput: EmrCloudApiRequestsMedicalExaminationCheckedExpiredRequestInput
  ) {
    postApiTodayOrdCheckedExpired(
      emrCloudApiRequestsMedicalExaminationCheckedExpiredRequestInput: $emrCloudApiRequestsMedicalExaminationCheckedExpiredRequestInput
    ) {
      message
      status
      data {
        messages {
          itemCd
          itemName
          sinKouiKbn
        }
      }
    }
  }
`;
export type PostApiTodayOrdCheckedExpiredMutationFn = Apollo.MutationFunction<
  PostApiTodayOrdCheckedExpiredMutation,
  PostApiTodayOrdCheckedExpiredMutationVariables
>;

/**
 * __usePostApiTodayOrdCheckedExpiredMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdCheckedExpiredMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdCheckedExpiredMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdCheckedExpiredMutation, { data, loading, error }] = usePostApiTodayOrdCheckedExpiredMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationCheckedExpiredRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationCheckedExpiredRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdCheckedExpiredMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdCheckedExpiredMutation,
    PostApiTodayOrdCheckedExpiredMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdCheckedExpiredMutation,
    PostApiTodayOrdCheckedExpiredMutationVariables
  >(PostApiTodayOrdCheckedExpiredDocument, options);
}
export type PostApiTodayOrdCheckedExpiredMutationHookResult = ReturnType<
  typeof usePostApiTodayOrdCheckedExpiredMutation
>;
export type PostApiTodayOrdCheckedExpiredMutationResult =
  Apollo.MutationResult<PostApiTodayOrdCheckedExpiredMutation>;
export type PostApiTodayOrdCheckedExpiredMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTodayOrdCheckedExpiredMutation,
    PostApiTodayOrdCheckedExpiredMutationVariables
  >;
export const PostApiTodayOrdGetInfCheckedItemNameDocument = gql`
  mutation postApiTodayOrdGetInfCheckedItemName(
    $emrCloudApiRequestsMedicalExaminationCheckedItemNameRequestInput: EmrCloudApiRequestsMedicalExaminationCheckedItemNameRequestInput
  ) {
    postApiTodayOrdGetInfCheckedItemName(
      emrCloudApiRequestsMedicalExaminationCheckedItemNameRequestInput: $emrCloudApiRequestsMedicalExaminationCheckedItemNameRequestInput
    ) {
      message
      status
      data {
        checkedItemNames
      }
    }
  }
`;
export type PostApiTodayOrdGetInfCheckedItemNameMutationFn =
  Apollo.MutationFunction<
    PostApiTodayOrdGetInfCheckedItemNameMutation,
    PostApiTodayOrdGetInfCheckedItemNameMutationVariables
  >;

/**
 * __usePostApiTodayOrdGetInfCheckedItemNameMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdGetInfCheckedItemNameMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdGetInfCheckedItemNameMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdGetInfCheckedItemNameMutation, { data, loading, error }] = usePostApiTodayOrdGetInfCheckedItemNameMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationCheckedItemNameRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationCheckedItemNameRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdGetInfCheckedItemNameMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdGetInfCheckedItemNameMutation,
    PostApiTodayOrdGetInfCheckedItemNameMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdGetInfCheckedItemNameMutation,
    PostApiTodayOrdGetInfCheckedItemNameMutationVariables
  >(PostApiTodayOrdGetInfCheckedItemNameDocument, options);
}
export type PostApiTodayOrdGetInfCheckedItemNameMutationHookResult = ReturnType<
  typeof usePostApiTodayOrdGetInfCheckedItemNameMutation
>;
export type PostApiTodayOrdGetInfCheckedItemNameMutationResult =
  Apollo.MutationResult<PostApiTodayOrdGetInfCheckedItemNameMutation>;
export type PostApiTodayOrdGetInfCheckedItemNameMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTodayOrdGetInfCheckedItemNameMutation,
    PostApiTodayOrdGetInfCheckedItemNameMutationVariables
  >;
export const PostApiTodayOrdAutoCheckOrderDocument = gql`
  mutation postApiTodayOrdAutoCheckOrder(
    $emrCloudApiRequestsMedicalExaminationAutoCheckOrderRequestInput: EmrCloudApiRequestsMedicalExaminationAutoCheckOrderRequestInput
  ) {
    postApiTodayOrdAutoCheckOrder(
      emrCloudApiRequestsMedicalExaminationAutoCheckOrderRequestInput: $emrCloudApiRequestsMedicalExaminationAutoCheckOrderRequestInput
    ) {
      message
      status
      data {
        autoCheckOrderItems {
          message
          odrInfDetailPosition
          odrInfPosition
          suryo
          type
          tenItemMst {
            buiKbn
            cdEdano
            cdKbn
            cdKbnno
            centerCd
            centerName
            cmtCol1
            cmtCol2
            cmtCol3
            cmtCol4
            cmtColKeta1
            cmtColKeta2
            cmtColKeta3
            cmtColKeta4
            cnvTermVal
            cnvUnitName
            createDate
            defaultValue
            drugKbn
            endDate
            formattedEndDate
            formattedStartDate
            handanGrpKbn
            ipnCD
            hpId
            ipnName
            ipnNameCd
            isAdopted
            isDefault
            isDeleted
            isGetPriceInYakka
            isKensaMstEmpty
            isNoSearch
            isNodspRece
            isSanteiItem
            itemCd
            itemType
            jihiSbt
            kanaName1
            kanaName2
            kanaName3
            kanaName4
            kanaName5
            kanaName6
            kanaName7
            kasan1
            kasan2
            kensaCenterItemCDDisplay
            kensaItemCd
            kensaItemSeqNo
            kensaMstCenterItemCd1
            kensaMstCenterItemCd2
            kohatuKbn
            kohatuKbnDisplay
            kokuji1
            kokuji2
            kouiName
            kouseisinKbn
            kouseisinKbnDisplay
            kubunToDisplay
            listGenDate
            madokuKbn
            masterSbt
            maxAge
            minAge
            modeStatus
            name
            odrTermVal
            odrUnitName
            readOnlyStartDate
            receName
            rousaiKbn
            rousaiKbnDisplay
            santeiItemCd
            yohoKbn
            yjCd
            yakka
            tenId
            tenDisplay
            ten
            startDate
            sinKouiKbn
            senteiRyoyoYakka
            senteiRyoyoKbn
            santeigaiKbn
          }
        }
      }
    }
  }
`;
export type PostApiTodayOrdAutoCheckOrderMutationFn = Apollo.MutationFunction<
  PostApiTodayOrdAutoCheckOrderMutation,
  PostApiTodayOrdAutoCheckOrderMutationVariables
>;

/**
 * __usePostApiTodayOrdAutoCheckOrderMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdAutoCheckOrderMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdAutoCheckOrderMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdAutoCheckOrderMutation, { data, loading, error }] = usePostApiTodayOrdAutoCheckOrderMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationAutoCheckOrderRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationAutoCheckOrderRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdAutoCheckOrderMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdAutoCheckOrderMutation,
    PostApiTodayOrdAutoCheckOrderMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdAutoCheckOrderMutation,
    PostApiTodayOrdAutoCheckOrderMutationVariables
  >(PostApiTodayOrdAutoCheckOrderDocument, options);
}
export type PostApiTodayOrdAutoCheckOrderMutationHookResult = ReturnType<
  typeof usePostApiTodayOrdAutoCheckOrderMutation
>;
export type PostApiTodayOrdAutoCheckOrderMutationResult =
  Apollo.MutationResult<PostApiTodayOrdAutoCheckOrderMutation>;
export type PostApiTodayOrdAutoCheckOrderMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTodayOrdAutoCheckOrderMutation,
    PostApiTodayOrdAutoCheckOrderMutationVariables
  >;
export const PostApiTodayOrdChangeAfterAutoCheckOrderDocument = gql`
  mutation postApiTodayOrdChangeAfterAutoCheckOrder(
    $emrCloudApiRequestsMedicalExaminationChangeAfterAutoCheckOrderRequestInput: EmrCloudApiRequestsMedicalExaminationChangeAfterAutoCheckOrderRequestInput
  ) {
    postApiTodayOrdChangeAfterAutoCheckOrder(
      emrCloudApiRequestsMedicalExaminationChangeAfterAutoCheckOrderRequestInput: $emrCloudApiRequestsMedicalExaminationChangeAfterAutoCheckOrderRequestInput
    ) {
      message
      status
      data {
        odrInfItems {
          odrInfItem {
            createDate
            createId
            createMachine
            createName
            daysCnt
            groupOdrKouiKbn
            hokenPid
            hpId
            id
            inoutKbn
            isDeleted
            updateName
            updateMachine
            updateDate
            tosekiKbn
            syohoSbt
            sortNo
            sinDate
            sikyuKbn
            santeiKbn
            rpNo
            rpName
            rpEdaNo
            raiinNo
            ptId
            odrKouiKbn
            odrDetails {
              alternationIndex
              bunkatu
              bunkatuKoui
              centerItemCd1
              centerItemCd2
              cmtCol1
              cmtCol2
              cmtCol3
              cmtCol4
              cmtColKeta1
              cmtColKeta2
              cmtColKeta3
              cmtColKeta4
              cmtName
              cmtOpt
              cnvTermVal
              cnvUnitName
              commentNewline
              displayItemName
              drugKbn
              fontColor
              handanGrpKbn
              hasCmtName
              hpId
              ipnCd
              ipnName
              isGetPriceInYakka
              isKensaMstEmpty
              isNodspRece
              itemCd
              itemName
              jissiDate
              jissiId
              jissiKbn
              jissiMachine
              kasan1
              kasan2
              kensaGaichu
              kikakiUnit
              kokuji1
              kohatuKbn
              kokuji2
              masterSbt
              memoItem
              odrTermVal
              odrUnitName
              ptId
              raiinNo
              reqCd
              rikikaRate
              rikikaUnit
              rowNo
              rpEdaNo
              rpNo
              sinDate
              sinKouiKbn
              suryo
              syohoKbn
              syohoLimitKbn
              ten
              termVal
              unitName
              unitSbt
              yakka
              yakkaiUnit
              yjCd
              yohoKbn
              youkaiekiCd
            }
          }
          position
        }
      }
    }
  }
`;
export type PostApiTodayOrdChangeAfterAutoCheckOrderMutationFn =
  Apollo.MutationFunction<
    PostApiTodayOrdChangeAfterAutoCheckOrderMutation,
    PostApiTodayOrdChangeAfterAutoCheckOrderMutationVariables
  >;

/**
 * __usePostApiTodayOrdChangeAfterAutoCheckOrderMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdChangeAfterAutoCheckOrderMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdChangeAfterAutoCheckOrderMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdChangeAfterAutoCheckOrderMutation, { data, loading, error }] = usePostApiTodayOrdChangeAfterAutoCheckOrderMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationChangeAfterAutoCheckOrderRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationChangeAfterAutoCheckOrderRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdChangeAfterAutoCheckOrderMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdChangeAfterAutoCheckOrderMutation,
    PostApiTodayOrdChangeAfterAutoCheckOrderMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdChangeAfterAutoCheckOrderMutation,
    PostApiTodayOrdChangeAfterAutoCheckOrderMutationVariables
  >(PostApiTodayOrdChangeAfterAutoCheckOrderDocument, options);
}
export type PostApiTodayOrdChangeAfterAutoCheckOrderMutationHookResult =
  ReturnType<typeof usePostApiTodayOrdChangeAfterAutoCheckOrderMutation>;
export type PostApiTodayOrdChangeAfterAutoCheckOrderMutationResult =
  Apollo.MutationResult<PostApiTodayOrdChangeAfterAutoCheckOrderMutation>;
export type PostApiTodayOrdChangeAfterAutoCheckOrderMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTodayOrdChangeAfterAutoCheckOrderMutation,
    PostApiTodayOrdChangeAfterAutoCheckOrderMutationVariables
  >;
export const PostApiTodayOrdGetAddedAutoItemDocument = gql`
  mutation postApiTodayOrdGetAddedAutoItem(
    $emrCloudApiRequestsMedicalExaminationGetAddedAutoItemRequestInput: EmrCloudApiRequestsMedicalExaminationGetAddedAutoItemRequestInput
  ) {
    postApiTodayOrdGetAddedAutoItem(
      emrCloudApiRequestsMedicalExaminationGetAddedAutoItemRequestInput: $emrCloudApiRequestsMedicalExaminationGetAddedAutoItemRequestInput
    ) {
      message
      status
      data {
        addedAutoItems {
          orderDetailPosition
          orderPosition
          addedOrderDetails {
            id
            itemCd
            itemName
          }
        }
      }
    }
  }
`;
export type PostApiTodayOrdGetAddedAutoItemMutationFn = Apollo.MutationFunction<
  PostApiTodayOrdGetAddedAutoItemMutation,
  PostApiTodayOrdGetAddedAutoItemMutationVariables
>;

/**
 * __usePostApiTodayOrdGetAddedAutoItemMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdGetAddedAutoItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdGetAddedAutoItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdGetAddedAutoItemMutation, { data, loading, error }] = usePostApiTodayOrdGetAddedAutoItemMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationGetAddedAutoItemRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationGetAddedAutoItemRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdGetAddedAutoItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdGetAddedAutoItemMutation,
    PostApiTodayOrdGetAddedAutoItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdGetAddedAutoItemMutation,
    PostApiTodayOrdGetAddedAutoItemMutationVariables
  >(PostApiTodayOrdGetAddedAutoItemDocument, options);
}
export type PostApiTodayOrdGetAddedAutoItemMutationHookResult = ReturnType<
  typeof usePostApiTodayOrdGetAddedAutoItemMutation
>;
export type PostApiTodayOrdGetAddedAutoItemMutationResult =
  Apollo.MutationResult<PostApiTodayOrdGetAddedAutoItemMutation>;
export type PostApiTodayOrdGetAddedAutoItemMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTodayOrdGetAddedAutoItemMutation,
    PostApiTodayOrdGetAddedAutoItemMutationVariables
  >;
export const PostApiTodayOrdAddAutoItemDocument = gql`
  mutation postApiTodayOrdAddAutoItem(
    $emrCloudApiRequestsMedicalExaminationAddAutoItemRequestInput: EmrCloudApiRequestsMedicalExaminationAddAutoItemRequestInput
  ) {
    postApiTodayOrdAddAutoItem(
      emrCloudApiRequestsMedicalExaminationAddAutoItemRequestInput: $emrCloudApiRequestsMedicalExaminationAddAutoItemRequestInput
    ) {
      message
      status
      data {
        odrInfItemInputDatas {
          createDate
          createMachine
          createId
          createName
          daysCnt
          groupOdrKouiKbn
          hokenPid
          hpId
          id
          inoutKbn
          isDeleted
          odrKouiKbn
          raiinNo
          ptId
          rpEdaNo
          rpName
          rpNo
          sikyuKbn
          santeiKbn
          sinDate
          sortNo
          tosekiKbn
          syohoSbt
          updateDate
          updateMachine
          updateName
          odrDetails {
            alternationIndex
            bunkatu
            bunkatuKoui
            centerItemCd2
            centerItemCd1
            cmtCol1
            cmtCol3
            cmtCol2
            cmtCol4
            cmtColKeta1
            cmtColKeta2
            cmtColKeta3
            cmtColKeta4
            cmtName
            cmtOpt
            cnvTermVal
            cnvUnitName
            commentNewline
            displayItemName
            drugKbn
            fontColor
            handanGrpKbn
            hasCmtName
            hpId
            ipnCd
            ipnName
            isGetPriceInYakka
            isKensaMstEmpty
            isNodspRece
            itemCd
            jissiDate
            itemName
            jissiId
            jissiKbn
            kasan1
            jissiMachine
            kasan2
            kensaGaichu
            kikakiUnit
            kohatuKbn
            kokuji1
            kokuji2
            masterSbt
            memoItem
            odrTermVal
            odrUnitName
            ptId
            raiinNo
            reqCd
            rikikaRate
            rikikaUnit
            rowNo
            rpEdaNo
            rpNo
            sinDate
            sinKouiKbn
            suryo
            syohoKbn
            syohoLimitKbn
            ten
            termVal
            unitName
            unitSbt
            yakka
            yakkaiUnit
            yjCd
            yohoKbn
            youkaiekiCd
          }
        }
      }
    }
  }
`;
export type PostApiTodayOrdAddAutoItemMutationFn = Apollo.MutationFunction<
  PostApiTodayOrdAddAutoItemMutation,
  PostApiTodayOrdAddAutoItemMutationVariables
>;

/**
 * __usePostApiTodayOrdAddAutoItemMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdAddAutoItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdAddAutoItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdAddAutoItemMutation, { data, loading, error }] = usePostApiTodayOrdAddAutoItemMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationAddAutoItemRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationAddAutoItemRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdAddAutoItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdAddAutoItemMutation,
    PostApiTodayOrdAddAutoItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdAddAutoItemMutation,
    PostApiTodayOrdAddAutoItemMutationVariables
  >(PostApiTodayOrdAddAutoItemDocument, options);
}
export type PostApiTodayOrdAddAutoItemMutationHookResult = ReturnType<
  typeof usePostApiTodayOrdAddAutoItemMutation
>;
export type PostApiTodayOrdAddAutoItemMutationResult =
  Apollo.MutationResult<PostApiTodayOrdAddAutoItemMutation>;
export type PostApiTodayOrdAddAutoItemMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTodayOrdAddAutoItemMutation,
    PostApiTodayOrdAddAutoItemMutationVariables
  >;
export const PostApiTodayOrdConvertItemDocument = gql`
  mutation postApiTodayOrdConvertItem(
    $emrCloudApiRequestsMedicalExaminationConvertItemRequestInput: EmrCloudApiRequestsMedicalExaminationConvertItemRequestInput = {

    }
  ) {
    postApiTodayOrdConvertItem(
      emrCloudApiRequestsMedicalExaminationConvertItemRequestInput: $emrCloudApiRequestsMedicalExaminationConvertItemRequestInput
    ) {
      data {
        result {
          createDate
          createId
          createMachine
          createName
          daysCnt
          groupOdrKouiKbn
          hokenPid
          hpId
          id
          inoutKbn
          isDeleted
          odrKouiKbn
          ptId
          raiinNo
          rpEdaNo
          rpName
          rpNo
          santeiKbn
          sikyuKbn
          sinDate
          sortNo
          syohoSbt
          tosekiKbn
          updateDate
          updateMachine
          updateName
          odrDetails {
            isSelectiveComment
            alternationIndex
            bikoComment
            buiKbn
            bunkatu
            bunkatuKoui
            centerCd
            centerItemCd1
            centerItemCd2
            centerName
            cmtCol1
            cmtCol2
            cmtCol3
            cmtCol4
            cmtColKeta2
            cmtColKeta1
            cmtColKeta3
            cmtColKeta4
            cmtName
            cmtOpt
            cnvTermVal
            cnvUnitName
            displayItemName
            commentNewline
            drugKbn
            fontColor
            handanGrpKbn
            hasCmtName
            hpId
            ipnCd
            ipnName
            isAdopted
            isGetPriceInYakka
            isKensaMstEmpty
            isNodspRece
            itemCd
            itemName
            jissiDate
            jissiId
            jissiKbn
            jissiMachine
            kasan1
            kasan2
            kensaGaichu
            kikakiUnit
            kohatuKbn
            kokuji1
            kokuji2
            masterSbt
            memoItem
            odrTermVal
            odrUnitName
            ptId
            raiinNo
            reqCd
            rikikaRate
            rousaiKbn
            rikikaUnit
            rowNo
            rpEdaNo
            rpNo
            senteiRyoyoKbn
            sinDate
            sinKouiKbn
            suryo
            syohoKbn
            syohoLimitKbn
            ten
            termVal
            unitName
            unitSbt
            yakka
            yakkaiUnit
            yjCd
            yohoKbn
            youkaiekiCd
          }
        }
      }
    }
  }
`;
export type PostApiTodayOrdConvertItemMutationFn = Apollo.MutationFunction<
  PostApiTodayOrdConvertItemMutation,
  PostApiTodayOrdConvertItemMutationVariables
>;

/**
 * __usePostApiTodayOrdConvertItemMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdConvertItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdConvertItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdConvertItemMutation, { data, loading, error }] = usePostApiTodayOrdConvertItemMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationConvertItemRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationConvertItemRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdConvertItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdConvertItemMutation,
    PostApiTodayOrdConvertItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdConvertItemMutation,
    PostApiTodayOrdConvertItemMutationVariables
  >(PostApiTodayOrdConvertItemDocument, options);
}
export type PostApiTodayOrdConvertItemMutationHookResult = ReturnType<
  typeof usePostApiTodayOrdConvertItemMutation
>;
export type PostApiTodayOrdConvertItemMutationResult =
  Apollo.MutationResult<PostApiTodayOrdConvertItemMutation>;
export type PostApiTodayOrdConvertItemMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTodayOrdConvertItemMutation,
    PostApiTodayOrdConvertItemMutationVariables
  >;
export const PostApiTodayOrdGetValidGairaiRihaDocument = gql`
  mutation postApiTodayOrdGetValidGairaiRiha(
    $emrCloudApiRequestsMedicalExaminationGetValidGairaiRihaRequestInput: EmrCloudApiRequestsMedicalExaminationGetValidGairaiRihaRequestInput
  ) {
    postApiTodayOrdGetValidGairaiRiha(
      emrCloudApiRequestsMedicalExaminationGetValidGairaiRihaRequestInput: $emrCloudApiRequestsMedicalExaminationGetValidGairaiRihaRequestInput
    ) {
      message
      status
      data {
        gairaiRihaItems {
          itemName
          lastDaySanteiRiha
          rihaItemName
          type
        }
      }
    }
  }
`;
export type PostApiTodayOrdGetValidGairaiRihaMutationFn =
  Apollo.MutationFunction<
    PostApiTodayOrdGetValidGairaiRihaMutation,
    PostApiTodayOrdGetValidGairaiRihaMutationVariables
  >;

/**
 * __usePostApiTodayOrdGetValidGairaiRihaMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdGetValidGairaiRihaMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdGetValidGairaiRihaMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdGetValidGairaiRihaMutation, { data, loading, error }] = usePostApiTodayOrdGetValidGairaiRihaMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationGetValidGairaiRihaRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationGetValidGairaiRihaRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdGetValidGairaiRihaMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdGetValidGairaiRihaMutation,
    PostApiTodayOrdGetValidGairaiRihaMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdGetValidGairaiRihaMutation,
    PostApiTodayOrdGetValidGairaiRihaMutationVariables
  >(PostApiTodayOrdGetValidGairaiRihaDocument, options);
}
export type PostApiTodayOrdGetValidGairaiRihaMutationHookResult = ReturnType<
  typeof usePostApiTodayOrdGetValidGairaiRihaMutation
>;
export type PostApiTodayOrdGetValidGairaiRihaMutationResult =
  Apollo.MutationResult<PostApiTodayOrdGetValidGairaiRihaMutation>;
export type PostApiTodayOrdGetValidGairaiRihaMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTodayOrdGetValidGairaiRihaMutation,
    PostApiTodayOrdGetValidGairaiRihaMutationVariables
  >;
export const PostApiTodayOrdValidateDocument = gql`
  mutation postApiTodayOrdValidate(
    $emrCloudApiRequestsMedicalExaminationValidationTodayOrdRequestInput: EmrCloudApiRequestsMedicalExaminationValidationTodayOrdRequestInput
  ) {
    postApiTodayOrdValidate(
      emrCloudApiRequestsMedicalExaminationValidationTodayOrdRequestInput: $emrCloudApiRequestsMedicalExaminationValidationTodayOrdRequestInput
    ) {
      message
      status
      data {
        validationKarte {
          status
          validationMessage
        }
        validationOdrInfs {
          hokenName {
            hoKenId
            hokenName
          }
          kohiNames {
            hoKenId
            hokenName
          }
          orderInfDetailPosition
          orderInfPosition
          status
          validationField
          validationMessage
        }
        validationRaiinInf {
          hokenName {
            hoKenId
            hokenName
          }
          kohiNames {
            hoKenId
            hokenName
          }
          status
          validationMessage
        }
      }
    }
  }
`;
export type PostApiTodayOrdValidateMutationFn = Apollo.MutationFunction<
  PostApiTodayOrdValidateMutation,
  PostApiTodayOrdValidateMutationVariables
>;

/**
 * __usePostApiTodayOrdValidateMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdValidateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdValidateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdValidateMutation, { data, loading, error }] = usePostApiTodayOrdValidateMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationValidationTodayOrdRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationValidationTodayOrdRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdValidateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdValidateMutation,
    PostApiTodayOrdValidateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdValidateMutation,
    PostApiTodayOrdValidateMutationVariables
  >(PostApiTodayOrdValidateDocument, options);
}
export type PostApiTodayOrdValidateMutationHookResult = ReturnType<
  typeof usePostApiTodayOrdValidateMutation
>;
export type PostApiTodayOrdValidateMutationResult =
  Apollo.MutationResult<PostApiTodayOrdValidateMutation>;
export type PostApiTodayOrdValidateMutationOptions = Apollo.BaseMutationOptions<
  PostApiTodayOrdValidateMutation,
  PostApiTodayOrdValidateMutationVariables
>;
