import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type DeleteLabelMutationVariables = Types.Exact<{
  input: Types.DeleteLabelInput;
}>;

export type DeleteLabelMutation = {
  __typename?: "mutation_root";
  deleteLabel: boolean;
};

export type GetLabelQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetLabelQuery = {
  __typename?: "query_root";
  getLabel: Array<{
    __typename?: "Label";
    labelID: number;
    label: string;
    description: string;
    colorCode: string;
    sortOrder: number;
  }>;
};

export type CreateLabelMutationVariables = Types.Exact<{
  input: Types.CreateLabelInput;
}>;

export type CreateLabelMutation = {
  __typename?: "mutation_root";
  createLabel: {
    __typename?: "Label";
    labelID: number;
    label: string;
    description: string;
    colorCode: string;
    sortOrder: number;
  };
};

export type UpdateLabelMutationVariables = Types.Exact<{
  input: Types.UpdateLabelInput;
}>;

export type UpdateLabelMutation = {
  __typename?: "mutation_root";
  updateLabel: {
    __typename?: "Label";
    labelID: number;
    label: string;
    description: string;
    colorCode: string;
    sortOrder: number;
  };
};

export type UpdateLabelItemPositionMutationVariables = Types.Exact<{
  input: Types.UpdateLabelItemPositionInput;
}>;

export type UpdateLabelItemPositionMutation = {
  __typename?: "mutation_root";
  updateLabelItemPosition: Array<{
    __typename?: "Label";
    labelID: number;
    label: string;
    description: string;
    colorCode: string;
    sortOrder: number;
  }>;
};

export const DeleteLabelDocument = gql`
  mutation deleteLabel($input: DeleteLabelInput!) {
    deleteLabel(input: $input)
  }
`;
export type DeleteLabelMutationFn = Apollo.MutationFunction<
  DeleteLabelMutation,
  DeleteLabelMutationVariables
>;

/**
 * __useDeleteLabelMutation__
 *
 * To run a mutation, you first call `useDeleteLabelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteLabelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteLabelMutation, { data, loading, error }] = useDeleteLabelMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useDeleteLabelMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteLabelMutation,
    DeleteLabelMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<DeleteLabelMutation, DeleteLabelMutationVariables>(
    DeleteLabelDocument,
    options,
  );
}
export type DeleteLabelMutationHookResult = ReturnType<
  typeof useDeleteLabelMutation
>;
export type DeleteLabelMutationResult =
  Apollo.MutationResult<DeleteLabelMutation>;
export type DeleteLabelMutationOptions = Apollo.BaseMutationOptions<
  DeleteLabelMutation,
  DeleteLabelMutationVariables
>;
export const GetLabelDocument = gql`
  query getLabel {
    getLabel {
      labelID
      label
      description
      colorCode
      sortOrder
    }
  }
`;

/**
 * __useGetLabelQuery__
 *
 * To run a query within a React component, call `useGetLabelQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLabelQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLabelQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetLabelQuery(
  baseOptions?: Apollo.QueryHookOptions<GetLabelQuery, GetLabelQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetLabelQuery, GetLabelQueryVariables>(
    GetLabelDocument,
    options,
  );
}
export function useGetLabelLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetLabelQuery,
    GetLabelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetLabelQuery, GetLabelQueryVariables>(
    GetLabelDocument,
    options,
  );
}
export function useGetLabelSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetLabelQuery,
    GetLabelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<GetLabelQuery, GetLabelQueryVariables>(
    GetLabelDocument,
    options,
  );
}
export type GetLabelQueryHookResult = ReturnType<typeof useGetLabelQuery>;
export type GetLabelLazyQueryHookResult = ReturnType<
  typeof useGetLabelLazyQuery
>;
export type GetLabelSuspenseQueryHookResult = ReturnType<
  typeof useGetLabelSuspenseQuery
>;
export type GetLabelQueryResult = Apollo.QueryResult<
  GetLabelQuery,
  GetLabelQueryVariables
>;
export const CreateLabelDocument = gql`
  mutation createLabel($input: CreateLabelInput!) {
    createLabel(input: $input) {
      labelID
      label
      description
      colorCode
      sortOrder
    }
  }
`;
export type CreateLabelMutationFn = Apollo.MutationFunction<
  CreateLabelMutation,
  CreateLabelMutationVariables
>;

/**
 * __useCreateLabelMutation__
 *
 * To run a mutation, you first call `useCreateLabelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateLabelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createLabelMutation, { data, loading, error }] = useCreateLabelMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateLabelMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateLabelMutation,
    CreateLabelMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<CreateLabelMutation, CreateLabelMutationVariables>(
    CreateLabelDocument,
    options,
  );
}
export type CreateLabelMutationHookResult = ReturnType<
  typeof useCreateLabelMutation
>;
export type CreateLabelMutationResult =
  Apollo.MutationResult<CreateLabelMutation>;
export type CreateLabelMutationOptions = Apollo.BaseMutationOptions<
  CreateLabelMutation,
  CreateLabelMutationVariables
>;
export const UpdateLabelDocument = gql`
  mutation updateLabel($input: UpdateLabelInput!) {
    updateLabel(input: $input) {
      labelID
      label
      description
      colorCode
      sortOrder
    }
  }
`;
export type UpdateLabelMutationFn = Apollo.MutationFunction<
  UpdateLabelMutation,
  UpdateLabelMutationVariables
>;

/**
 * __useUpdateLabelMutation__
 *
 * To run a mutation, you first call `useUpdateLabelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateLabelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateLabelMutation, { data, loading, error }] = useUpdateLabelMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateLabelMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateLabelMutation,
    UpdateLabelMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<UpdateLabelMutation, UpdateLabelMutationVariables>(
    UpdateLabelDocument,
    options,
  );
}
export type UpdateLabelMutationHookResult = ReturnType<
  typeof useUpdateLabelMutation
>;
export type UpdateLabelMutationResult =
  Apollo.MutationResult<UpdateLabelMutation>;
export type UpdateLabelMutationOptions = Apollo.BaseMutationOptions<
  UpdateLabelMutation,
  UpdateLabelMutationVariables
>;
export const UpdateLabelItemPositionDocument = gql`
  mutation updateLabelItemPosition($input: updateLabelItemPositionInput!) {
    updateLabelItemPosition(input: $input) {
      labelID
      label
      description
      colorCode
      sortOrder
    }
  }
`;
export type UpdateLabelItemPositionMutationFn = Apollo.MutationFunction<
  UpdateLabelItemPositionMutation,
  UpdateLabelItemPositionMutationVariables
>;

/**
 * __useUpdateLabelItemPositionMutation__
 *
 * To run a mutation, you first call `useUpdateLabelItemPositionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateLabelItemPositionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateLabelItemPositionMutation, { data, loading, error }] = useUpdateLabelItemPositionMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateLabelItemPositionMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateLabelItemPositionMutation,
    UpdateLabelItemPositionMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateLabelItemPositionMutation,
    UpdateLabelItemPositionMutationVariables
  >(UpdateLabelItemPositionDocument, options);
}
export type UpdateLabelItemPositionMutationHookResult = ReturnType<
  typeof useUpdateLabelItemPositionMutation
>;
export type UpdateLabelItemPositionMutationResult =
  Apollo.MutationResult<UpdateLabelItemPositionMutation>;
export type UpdateLabelItemPositionMutationOptions = Apollo.BaseMutationOptions<
  UpdateLabelItemPositionMutation,
  UpdateLabelItemPositionMutationVariables
>;
