import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiCheckOpenFormCheckExistTemplateAccountingMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsExportPdFCheckExistTemplateAccountingRequestInput>;
  }>;

export type PostApiCheckOpenFormCheckExistTemplateAccountingMutation = {
  __typename?: "mutation_root";
  postApiCheckOpenFormCheckExistTemplateAccounting: boolean;
};

export const PostApiCheckOpenFormCheckExistTemplateAccountingDocument = gql`
  mutation postApiCheckOpenFormCheckExistTemplateAccounting(
    $input: EmrCloudApiRequestsExportPdFCheckExistTemplateAccountingRequestInput
  ) {
    postApiCheckOpenFormCheckExistTemplateAccounting(
      emrCloudApiRequestsExportPdFCheckExistTemplateAccountingRequestInput: $input
    )
  }
`;
export type PostApiCheckOpenFormCheckExistTemplateAccountingMutationFn =
  Apollo.MutationFunction<
    PostApiCheckOpenFormCheckExistTemplateAccountingMutation,
    PostApiCheckOpenFormCheckExistTemplateAccountingMutationVariables
  >;

/**
 * __usePostApiCheckOpenFormCheckExistTemplateAccountingMutation__
 *
 * To run a mutation, you first call `usePostApiCheckOpenFormCheckExistTemplateAccountingMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiCheckOpenFormCheckExistTemplateAccountingMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiCheckOpenFormCheckExistTemplateAccountingMutation, { data, loading, error }] = usePostApiCheckOpenFormCheckExistTemplateAccountingMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiCheckOpenFormCheckExistTemplateAccountingMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiCheckOpenFormCheckExistTemplateAccountingMutation,
    PostApiCheckOpenFormCheckExistTemplateAccountingMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiCheckOpenFormCheckExistTemplateAccountingMutation,
    PostApiCheckOpenFormCheckExistTemplateAccountingMutationVariables
  >(PostApiCheckOpenFormCheckExistTemplateAccountingDocument, options);
}
export type PostApiCheckOpenFormCheckExistTemplateAccountingMutationHookResult =
  ReturnType<
    typeof usePostApiCheckOpenFormCheckExistTemplateAccountingMutation
  >;
export type PostApiCheckOpenFormCheckExistTemplateAccountingMutationResult =
  Apollo.MutationResult<PostApiCheckOpenFormCheckExistTemplateAccountingMutation>;
export type PostApiCheckOpenFormCheckExistTemplateAccountingMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiCheckOpenFormCheckExistTemplateAccountingMutation,
    PostApiCheckOpenFormCheckExistTemplateAccountingMutationVariables
  >;
