import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiLoggingWriteLogMutationVariables = Types.Exact<{
  emrCloudApiRequestsLoggingWriteLogRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsLoggingWriteLogRequestInput>;
}>;

export type PostApiLoggingWriteLogMutation = {
  __typename?: "mutation_root";
  postApiLoggingWriteLog?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesLoggerWriteLogResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesLoggerWriteLogResponse";
      status?: number;
    };
  };
};

export type PostApiEpsGetPrescriptionIdListMutationVariables = Types.Exact<{
  emrCloudApiRequestsEpsGetPrescriptionIdListRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsEpsGetPrescriptionIdListRequestInput>;
}>;

export type PostApiEpsGetPrescriptionIdListMutation = {
  __typename?: "mutation_root";
  postApiEpsGetPrescriptionIdList?: {
    __typename?: "EmrCloudApiResponsesResponse1GetPrescriptionIdListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "GetPrescriptionIdListResponse";
      prescriptionIds?: Array<string>;
    };
  };
};

export type PostApiEpsUpdatePrescriptionStatusByIdsMutationVariables =
  Types.Exact<{
    emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput>;
  }>;

export type PostApiEpsUpdatePrescriptionStatusByIdsMutation = {
  __typename?: "mutation_root";
  postApiEpsUpdatePrescriptionStatusByIds?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsUpdatePrescriptionStatusByIdsResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsUpdatePrescriptionStatusByIdsResponse";
      isSuccess?: boolean;
    };
  };
};

export type PostApiEpsGetPrescriptionFromCsvDataMutationVariables =
  Types.Exact<{
    emrCloudApiRequestsEpsGetPrescriptionFromCsvRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsEpsGetPrescriptionFromCsvRequestInput>;
  }>;

export type PostApiEpsGetPrescriptionFromCsvDataMutation = {
  __typename?: "mutation_root";
  postApiEpsGetPrescriptionFromCsvData?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpSGetPrescriptionFromCsvResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpSGetPrescriptionFromCsvResponse";
      compositionModels?: Array<{
        __typename?: "DomainModelsEpsCompositionModel";
        rp?: string;
        rpDisplay?: string;
        compositionMedicines?: Array<{
          __typename?: "DomainModelsEpsCompositionMedicineModel";
          drugSupplements?: Array<string>;
          isEmptySingDoseAndDrugSupplement?: boolean;
          pharmaceutical?: string;
          quantity?: string;
          singleDose?: string;
        }>;
        compositionUsages?: Array<{
          __typename?: "DomainModelsEpsCompositionUsageModel";
          dispensing?: string;
          dosageForm?: string;
          numberPerDay?: string;
          usage?: string;
          usageSupplementary?: string;
        }>;
      }>;
      dispensingGroupDetailModels?: Array<{
        __typename?: "DomainModelsEpsDispensingGroupDetailModel";
        data?: string;
        groupTitle?: string;
        item?: string;
      }>;
      longInformationModels?: Array<{
        __typename?: "DomainModelsEpsLongInformationModel";
        header?: string;
        recordedContents?: Array<string>;
      }>;
    };
  };
};

export type PostApiEpsUpdatePrescriptionStatusMutationVariables = Types.Exact<{
  deletedReason?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  seqNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  status?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type PostApiEpsUpdatePrescriptionStatusMutation = {
  __typename?: "mutation_root";
  postApiEpsUpdatePrescriptionStatus?: {
    __typename?: "EmrCloudApiResponsesResponse1UpdatePrescriptionStatusResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "UpdatePrescriptionStatusResponse";
      isSuccess?: boolean;
    };
  };
};

export const PostApiLoggingWriteLogDocument = gql`
  mutation postApiLoggingWriteLog(
    $emrCloudApiRequestsLoggingWriteLogRequestInput: EmrCloudApiRequestsLoggingWriteLogRequestInput
  ) {
    postApiLoggingWriteLog(
      emrCloudApiRequestsLoggingWriteLogRequestInput: $emrCloudApiRequestsLoggingWriteLogRequestInput
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiLoggingWriteLogMutationFn = Apollo.MutationFunction<
  PostApiLoggingWriteLogMutation,
  PostApiLoggingWriteLogMutationVariables
>;

/**
 * __usePostApiLoggingWriteLogMutation__
 *
 * To run a mutation, you first call `usePostApiLoggingWriteLogMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiLoggingWriteLogMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiLoggingWriteLogMutation, { data, loading, error }] = usePostApiLoggingWriteLogMutation({
 *   variables: {
 *      emrCloudApiRequestsLoggingWriteLogRequestInput: // value for 'emrCloudApiRequestsLoggingWriteLogRequestInput'
 *   },
 * });
 */
export function usePostApiLoggingWriteLogMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiLoggingWriteLogMutation,
    PostApiLoggingWriteLogMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiLoggingWriteLogMutation,
    PostApiLoggingWriteLogMutationVariables
  >(PostApiLoggingWriteLogDocument, options);
}
export type PostApiLoggingWriteLogMutationHookResult = ReturnType<
  typeof usePostApiLoggingWriteLogMutation
>;
export type PostApiLoggingWriteLogMutationResult =
  Apollo.MutationResult<PostApiLoggingWriteLogMutation>;
export type PostApiLoggingWriteLogMutationOptions = Apollo.BaseMutationOptions<
  PostApiLoggingWriteLogMutation,
  PostApiLoggingWriteLogMutationVariables
>;
export const PostApiEpsGetPrescriptionIdListDocument = gql`
  mutation postApiEpsGetPrescriptionIdList(
    $emrCloudApiRequestsEpsGetPrescriptionIdListRequestInput: EmrCloudApiRequestsEpsGetPrescriptionIdListRequestInput
  ) {
    postApiEpsGetPrescriptionIdList(
      emrCloudApiRequestsEpsGetPrescriptionIdListRequestInput: $emrCloudApiRequestsEpsGetPrescriptionIdListRequestInput
    ) {
      data {
        prescriptionIds
      }
      message
      status
    }
  }
`;
export type PostApiEpsGetPrescriptionIdListMutationFn = Apollo.MutationFunction<
  PostApiEpsGetPrescriptionIdListMutation,
  PostApiEpsGetPrescriptionIdListMutationVariables
>;

/**
 * __usePostApiEpsGetPrescriptionIdListMutation__
 *
 * To run a mutation, you first call `usePostApiEpsGetPrescriptionIdListMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsGetPrescriptionIdListMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsGetPrescriptionIdListMutation, { data, loading, error }] = usePostApiEpsGetPrescriptionIdListMutation({
 *   variables: {
 *      emrCloudApiRequestsEpsGetPrescriptionIdListRequestInput: // value for 'emrCloudApiRequestsEpsGetPrescriptionIdListRequestInput'
 *   },
 * });
 */
export function usePostApiEpsGetPrescriptionIdListMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsGetPrescriptionIdListMutation,
    PostApiEpsGetPrescriptionIdListMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsGetPrescriptionIdListMutation,
    PostApiEpsGetPrescriptionIdListMutationVariables
  >(PostApiEpsGetPrescriptionIdListDocument, options);
}
export type PostApiEpsGetPrescriptionIdListMutationHookResult = ReturnType<
  typeof usePostApiEpsGetPrescriptionIdListMutation
>;
export type PostApiEpsGetPrescriptionIdListMutationResult =
  Apollo.MutationResult<PostApiEpsGetPrescriptionIdListMutation>;
export type PostApiEpsGetPrescriptionIdListMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsGetPrescriptionIdListMutation,
    PostApiEpsGetPrescriptionIdListMutationVariables
  >;
export const PostApiEpsUpdatePrescriptionStatusByIdsDocument = gql`
  mutation postApiEpsUpdatePrescriptionStatusByIds(
    $emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput: EmrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput
  ) {
    postApiEpsUpdatePrescriptionStatusByIds(
      emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput: $emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput
    ) {
      data {
        isSuccess
      }
      message
      status
    }
  }
`;
export type PostApiEpsUpdatePrescriptionStatusByIdsMutationFn =
  Apollo.MutationFunction<
    PostApiEpsUpdatePrescriptionStatusByIdsMutation,
    PostApiEpsUpdatePrescriptionStatusByIdsMutationVariables
  >;

/**
 * __usePostApiEpsUpdatePrescriptionStatusByIdsMutation__
 *
 * To run a mutation, you first call `usePostApiEpsUpdatePrescriptionStatusByIdsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsUpdatePrescriptionStatusByIdsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsUpdatePrescriptionStatusByIdsMutation, { data, loading, error }] = usePostApiEpsUpdatePrescriptionStatusByIdsMutation({
 *   variables: {
 *      emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput: // value for 'emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput'
 *   },
 * });
 */
export function usePostApiEpsUpdatePrescriptionStatusByIdsMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsUpdatePrescriptionStatusByIdsMutation,
    PostApiEpsUpdatePrescriptionStatusByIdsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsUpdatePrescriptionStatusByIdsMutation,
    PostApiEpsUpdatePrescriptionStatusByIdsMutationVariables
  >(PostApiEpsUpdatePrescriptionStatusByIdsDocument, options);
}
export type PostApiEpsUpdatePrescriptionStatusByIdsMutationHookResult =
  ReturnType<typeof usePostApiEpsUpdatePrescriptionStatusByIdsMutation>;
export type PostApiEpsUpdatePrescriptionStatusByIdsMutationResult =
  Apollo.MutationResult<PostApiEpsUpdatePrescriptionStatusByIdsMutation>;
export type PostApiEpsUpdatePrescriptionStatusByIdsMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsUpdatePrescriptionStatusByIdsMutation,
    PostApiEpsUpdatePrescriptionStatusByIdsMutationVariables
  >;
export const PostApiEpsGetPrescriptionFromCsvDataDocument = gql`
  mutation postApiEpsGetPrescriptionFromCsvData(
    $emrCloudApiRequestsEpsGetPrescriptionFromCsvRequestInput: EmrCloudApiRequestsEpsGetPrescriptionFromCsvRequestInput
  ) {
    postApiEpsGetPrescriptionFromCsvData(
      emrCloudApiRequestsEpsGetPrescriptionFromCsvRequestInput: $emrCloudApiRequestsEpsGetPrescriptionFromCsvRequestInput
    ) {
      data {
        compositionModels {
          rp
          rpDisplay
          compositionMedicines {
            drugSupplements
            isEmptySingDoseAndDrugSupplement
            pharmaceutical
            quantity
            singleDose
          }
          compositionUsages {
            dispensing
            dosageForm
            numberPerDay
            usage
            usageSupplementary
          }
        }
        dispensingGroupDetailModels {
          data
          groupTitle
          item
        }
        longInformationModels {
          header
          recordedContents
        }
      }
      message
      status
    }
  }
`;
export type PostApiEpsGetPrescriptionFromCsvDataMutationFn =
  Apollo.MutationFunction<
    PostApiEpsGetPrescriptionFromCsvDataMutation,
    PostApiEpsGetPrescriptionFromCsvDataMutationVariables
  >;

/**
 * __usePostApiEpsGetPrescriptionFromCsvDataMutation__
 *
 * To run a mutation, you first call `usePostApiEpsGetPrescriptionFromCsvDataMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsGetPrescriptionFromCsvDataMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsGetPrescriptionFromCsvDataMutation, { data, loading, error }] = usePostApiEpsGetPrescriptionFromCsvDataMutation({
 *   variables: {
 *      emrCloudApiRequestsEpsGetPrescriptionFromCsvRequestInput: // value for 'emrCloudApiRequestsEpsGetPrescriptionFromCsvRequestInput'
 *   },
 * });
 */
export function usePostApiEpsGetPrescriptionFromCsvDataMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsGetPrescriptionFromCsvDataMutation,
    PostApiEpsGetPrescriptionFromCsvDataMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsGetPrescriptionFromCsvDataMutation,
    PostApiEpsGetPrescriptionFromCsvDataMutationVariables
  >(PostApiEpsGetPrescriptionFromCsvDataDocument, options);
}
export type PostApiEpsGetPrescriptionFromCsvDataMutationHookResult = ReturnType<
  typeof usePostApiEpsGetPrescriptionFromCsvDataMutation
>;
export type PostApiEpsGetPrescriptionFromCsvDataMutationResult =
  Apollo.MutationResult<PostApiEpsGetPrescriptionFromCsvDataMutation>;
export type PostApiEpsGetPrescriptionFromCsvDataMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsGetPrescriptionFromCsvDataMutation,
    PostApiEpsGetPrescriptionFromCsvDataMutationVariables
  >;
export const PostApiEpsUpdatePrescriptionStatusDocument = gql`
  mutation postApiEpsUpdatePrescriptionStatus(
    $deletedReason: Int
    $ptId: BigInt
    $raiinNo: BigInt
    $seqNo: BigInt
    $status: Int
  ) {
    postApiEpsUpdatePrescriptionStatus(
      emrCloudApiRequestsEpsUpdatePrescriptionStatusRequestInput: {
        deletedReason: $deletedReason
        ptId: $ptId
        raiinNo: $raiinNo
        seqNo: $seqNo
        status: $status
      }
    ) {
      data {
        isSuccess
      }
      message
      status
    }
  }
`;
export type PostApiEpsUpdatePrescriptionStatusMutationFn =
  Apollo.MutationFunction<
    PostApiEpsUpdatePrescriptionStatusMutation,
    PostApiEpsUpdatePrescriptionStatusMutationVariables
  >;

/**
 * __usePostApiEpsUpdatePrescriptionStatusMutation__
 *
 * To run a mutation, you first call `usePostApiEpsUpdatePrescriptionStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsUpdatePrescriptionStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsUpdatePrescriptionStatusMutation, { data, loading, error }] = usePostApiEpsUpdatePrescriptionStatusMutation({
 *   variables: {
 *      deletedReason: // value for 'deletedReason'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      seqNo: // value for 'seqNo'
 *      status: // value for 'status'
 *   },
 * });
 */
export function usePostApiEpsUpdatePrescriptionStatusMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsUpdatePrescriptionStatusMutation,
    PostApiEpsUpdatePrescriptionStatusMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsUpdatePrescriptionStatusMutation,
    PostApiEpsUpdatePrescriptionStatusMutationVariables
  >(PostApiEpsUpdatePrescriptionStatusDocument, options);
}
export type PostApiEpsUpdatePrescriptionStatusMutationHookResult = ReturnType<
  typeof usePostApiEpsUpdatePrescriptionStatusMutation
>;
export type PostApiEpsUpdatePrescriptionStatusMutationResult =
  Apollo.MutationResult<PostApiEpsUpdatePrescriptionStatusMutation>;
export type PostApiEpsUpdatePrescriptionStatusMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsUpdatePrescriptionStatusMutation,
    PostApiEpsUpdatePrescriptionStatusMutationVariables
  >;
