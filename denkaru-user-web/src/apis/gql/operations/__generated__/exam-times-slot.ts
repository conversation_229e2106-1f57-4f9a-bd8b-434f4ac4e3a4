import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetExamTimeSlotsByConditionsQueryVariables = Types.Exact<{
  input: Types.GetExamTimeSlotByConditionsInput;
}>;

export type GetExamTimeSlotsByConditionsQuery = {
  __typename?: "query_root";
  getExamTimeSlotsByConditions: Array<{
    __typename?: "ExamTimeSlot";
    examTimeSlotID: number;
    treatmentType: number;
    examStartDate: string;
    examEndDate: string;
    slotLimitReserveNum: number;
    totalReserveSlots?: number;
    isSuspended: boolean;
    reservableSlots?: number;
    calendar: {
      __typename?: "Calendar";
      calendarID: number;
      reservableSlotSettingType?: number;
      calendarTreatMents?: Array<{
        __typename?: "CalendarTreatment";
        treatmentDepartment?: {
          __typename?: "TreatmentDepartment";
          treatmentDepartmentId: number;
          firstConsultationTime: number;
          nextConsultationTime: number;
        };
      }>;
    };
  }>;
};

export const GetExamTimeSlotsByConditionsDocument = gql`
  query getExamTimeSlotsByConditions(
    $input: GetExamTimeSlotByConditionsInput!
  ) {
    getExamTimeSlotsByConditions(input: $input) {
      examTimeSlotID
      treatmentType
      examStartDate
      examEndDate
      calendar {
        calendarID
        reservableSlotSettingType
        calendarTreatMents {
          treatmentDepartment {
            treatmentDepartmentId
            firstConsultationTime
            nextConsultationTime
          }
        }
      }
      slotLimitReserveNum
      totalReserveSlots
      isSuspended
      reservableSlots
    }
  }
`;

/**
 * __useGetExamTimeSlotsByConditionsQuery__
 *
 * To run a query within a React component, call `useGetExamTimeSlotsByConditionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetExamTimeSlotsByConditionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetExamTimeSlotsByConditionsQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetExamTimeSlotsByConditionsQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetExamTimeSlotsByConditionsQuery,
    GetExamTimeSlotsByConditionsQueryVariables
  > &
    (
      | {
          variables: GetExamTimeSlotsByConditionsQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetExamTimeSlotsByConditionsQuery,
    GetExamTimeSlotsByConditionsQueryVariables
  >(GetExamTimeSlotsByConditionsDocument, options);
}
export function useGetExamTimeSlotsByConditionsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetExamTimeSlotsByConditionsQuery,
    GetExamTimeSlotsByConditionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetExamTimeSlotsByConditionsQuery,
    GetExamTimeSlotsByConditionsQueryVariables
  >(GetExamTimeSlotsByConditionsDocument, options);
}
export function useGetExamTimeSlotsByConditionsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetExamTimeSlotsByConditionsQuery,
    GetExamTimeSlotsByConditionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetExamTimeSlotsByConditionsQuery,
    GetExamTimeSlotsByConditionsQueryVariables
  >(GetExamTimeSlotsByConditionsDocument, options);
}
export type GetExamTimeSlotsByConditionsQueryHookResult = ReturnType<
  typeof useGetExamTimeSlotsByConditionsQuery
>;
export type GetExamTimeSlotsByConditionsLazyQueryHookResult = ReturnType<
  typeof useGetExamTimeSlotsByConditionsLazyQuery
>;
export type GetExamTimeSlotsByConditionsSuspenseQueryHookResult = ReturnType<
  typeof useGetExamTimeSlotsByConditionsSuspenseQuery
>;
export type GetExamTimeSlotsByConditionsQueryResult = Apollo.QueryResult<
  GetExamTimeSlotsByConditionsQuery,
  GetExamTimeSlotsByConditionsQueryVariables
>;
