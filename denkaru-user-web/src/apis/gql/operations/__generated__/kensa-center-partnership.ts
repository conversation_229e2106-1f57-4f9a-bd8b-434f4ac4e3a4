import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiKensaCenterPartnershipGetKensaCenterPartnershipQueryVariables =
  Types.Exact<{ [key: string]: never }>;

export type GetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery = {
  __typename?: "query_root";
  getApiKensaCenterPartnershipGetKensaCenterPartnership?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaCenterPartnershipKensaCenterPartnershipListResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesKensaCenterPartnershipKensaCenterPartnershipListResponse";
      listData?: Array<{
        __typename?: "DomainModelsKensaCenterPartnershipKensaCenterPartnershipModel";
        startDate?: number;
        masterUpdateDate?: string;
        hpId?: number;
        endDate?: number;
        dspCenterName?: string;
        centerName?: string;
        centerKey?: string;
        centerCd?: string;
      }>;
    };
  };
};

export type PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsKensaCenterPartnershipRegisterKensaCenterPartnershipRequestInput>;
  }>;

export type PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation =
  {
    __typename?: "mutation_root";
    postApiKensaCenterPartnershipRegisterKensaCenterPartnership?: {
      __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaCenterPartnershipKensaCenterPartnershipResponse";
      status?: number;
      message?: string;
    };
  };

export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsKensaCenterPartnershipUpdateKensaCenterPartnershipRequestInput>;
  }>;

export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation =
  {
    __typename?: "mutation_root";
    postApiKensaCenterPartnershipUpdateKensaCenterPartnership?: {
      __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaCenterPartnershipKensaCenterPartnershipResponse";
      status?: number;
      message?: string;
    };
  };

export type PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsKensaCenterPartnershipUnregisterKensaCenterPartnershipRequestInput>;
  }>;

export type PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation =
  {
    __typename?: "mutation_root";
    postApiKensaCenterPartnershipUnregisterKensaCenterPartnership?: {
      __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaCenterPartnershipKensaCenterPartnershipResponse";
      status?: number;
      message?: string;
    };
  };

export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutationVariables =
  Types.Exact<{ [key: string]: never }>;

export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation =
  {
    __typename?: "mutation_root";
    postApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDate?: {
      __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaCenterPartnershipKensaCenterPartnershipResponse";
      status?: number;
      message?: string;
    };
  };

export const GetApiKensaCenterPartnershipGetKensaCenterPartnershipDocument = gql`
  query getApiKensaCenterPartnershipGetKensaCenterPartnership {
    getApiKensaCenterPartnershipGetKensaCenterPartnership {
      data {
        listData {
          startDate
          masterUpdateDate
          hpId
          endDate
          dspCenterName
          centerName
          centerKey
          centerCd
        }
      }
      status
      message
    }
  }
`;

/**
 * __useGetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery__
 *
 * To run a query within a React component, call `useGetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery,
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery,
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQueryVariables
  >(GetApiKensaCenterPartnershipGetKensaCenterPartnershipDocument, options);
}
export function useGetApiKensaCenterPartnershipGetKensaCenterPartnershipLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery,
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery,
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQueryVariables
  >(GetApiKensaCenterPartnershipGetKensaCenterPartnershipDocument, options);
}
export function useGetApiKensaCenterPartnershipGetKensaCenterPartnershipSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery,
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery,
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQueryVariables
  >(GetApiKensaCenterPartnershipGetKensaCenterPartnershipDocument, options);
}
export type GetApiKensaCenterPartnershipGetKensaCenterPartnershipQueryHookResult =
  ReturnType<
    typeof useGetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery
  >;
export type GetApiKensaCenterPartnershipGetKensaCenterPartnershipLazyQueryHookResult =
  ReturnType<
    typeof useGetApiKensaCenterPartnershipGetKensaCenterPartnershipLazyQuery
  >;
export type GetApiKensaCenterPartnershipGetKensaCenterPartnershipSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiKensaCenterPartnershipGetKensaCenterPartnershipSuspenseQuery
  >;
export type GetApiKensaCenterPartnershipGetKensaCenterPartnershipQueryResult =
  Apollo.QueryResult<
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQuery,
    GetApiKensaCenterPartnershipGetKensaCenterPartnershipQueryVariables
  >;
export const PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipDocument = gql`
  mutation postApiKensaCenterPartnershipRegisterKensaCenterPartnership(
    $input: EmrCloudApiRequestsKensaCenterPartnershipRegisterKensaCenterPartnershipRequestInput
  ) {
    postApiKensaCenterPartnershipRegisterKensaCenterPartnership(
      emrCloudApiRequestsKensaCenterPartnershipRegisterKensaCenterPartnershipRequestInput: $input
    ) {
      status
      message
    }
  }
`;
export type PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutationFn =
  Apollo.MutationFunction<
    PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutationVariables
  >;

/**
 * __usePostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation__
 *
 * To run a mutation, you first call `usePostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation, { data, loading, error }] = usePostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutationVariables
  >(
    PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipDocument,
    options,
  );
}
export type PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutationHookResult =
  ReturnType<
    typeof usePostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation
  >;
export type PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutationResult =
  Apollo.MutationResult<PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation>;
export type PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipRegisterKensaCenterPartnershipMutationVariables
  >;
export const PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipDocument = gql`
  mutation postApiKensaCenterPartnershipUpdateKensaCenterPartnership(
    $input: EmrCloudApiRequestsKensaCenterPartnershipUpdateKensaCenterPartnershipRequestInput
  ) {
    postApiKensaCenterPartnershipUpdateKensaCenterPartnership(
      emrCloudApiRequestsKensaCenterPartnershipUpdateKensaCenterPartnershipRequestInput: $input
    ) {
      status
      message
    }
  }
`;
export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutationFn =
  Apollo.MutationFunction<
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutationVariables
  >;

/**
 * __usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation__
 *
 * To run a mutation, you first call `usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation, { data, loading, error }] = usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutationVariables
  >(PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipDocument, options);
}
export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutationHookResult =
  ReturnType<
    typeof usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation
  >;
export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutationResult =
  Apollo.MutationResult<PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation>;
export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMutationVariables
  >;
export const PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipDocument = gql`
  mutation postApiKensaCenterPartnershipUnregisterKensaCenterPartnership(
    $input: EmrCloudApiRequestsKensaCenterPartnershipUnregisterKensaCenterPartnershipRequestInput
  ) {
    postApiKensaCenterPartnershipUnregisterKensaCenterPartnership(
      emrCloudApiRequestsKensaCenterPartnershipUnregisterKensaCenterPartnershipRequestInput: $input
    ) {
      status
      message
    }
  }
`;
export type PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutationFn =
  Apollo.MutationFunction<
    PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutationVariables
  >;

/**
 * __usePostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation__
 *
 * To run a mutation, you first call `usePostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation, { data, loading, error }] = usePostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutationVariables
  >(
    PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipDocument,
    options,
  );
}
export type PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutationHookResult =
  ReturnType<
    typeof usePostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation
  >;
export type PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutationResult =
  Apollo.MutationResult<PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation>;
export type PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutation,
    PostApiKensaCenterPartnershipUnregisterKensaCenterPartnershipMutationVariables
  >;
export const PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateDocument = gql`
  mutation postApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDate {
    postApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDate {
      status
      message
    }
  }
`;
export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutationFn =
  Apollo.MutationFunction<
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation,
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutationVariables
  >;

/**
 * __usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation__
 *
 * To run a mutation, you first call `usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation, { data, loading, error }] = usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation({
 *   variables: {
 *   },
 * });
 */
export function usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation,
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation,
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutationVariables
  >(
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateDocument,
    options,
  );
}
export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutationHookResult =
  ReturnType<
    typeof usePostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation
  >;
export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutationResult =
  Apollo.MutationResult<PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation>;
export type PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutation,
    PostApiKensaCenterPartnershipUpdateKensaCenterPartnershipMstUpdateDateMutationVariables
  >;
