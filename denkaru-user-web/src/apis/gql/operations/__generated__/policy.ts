import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetAllRequireAgreeQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetAllRequireAgreeQuery = {
  __typename?: "query_root";
  GetAllRequireAgree?: Array<{
    __typename?: "RequireAgreeInto";
    agreementID: number;
    preRequireAgreeStartDateTime: string;
    requireAgreeStartDateTime: string;
    title: string;
    document: string;
  }>;
};

export type AgreeMutationVariables = Types.Exact<{
  input: Types.AgreeReq;
}>;

export type AgreeMutation = { __typename?: "mutation_root"; agree: boolean };

export const GetAllRequireAgreeDocument = gql`
  query getAllRequireAgree {
    GetAllRequireAgree {
      agreementID
      preRequireAgreeStartDateTime
      requireAgreeStartDateTime
      title
      document
    }
  }
`;

/**
 * __useGetAllRequireAgreeQuery__
 *
 * To run a query within a React component, call `useGetAllRequireAgreeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAllRequireAgreeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAllRequireAgreeQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetAllRequireAgreeQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetAllRequireAgreeQuery,
    GetAllRequireAgreeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetAllRequireAgreeQuery,
    GetAllRequireAgreeQueryVariables
  >(GetAllRequireAgreeDocument, options);
}
export function useGetAllRequireAgreeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetAllRequireAgreeQuery,
    GetAllRequireAgreeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetAllRequireAgreeQuery,
    GetAllRequireAgreeQueryVariables
  >(GetAllRequireAgreeDocument, options);
}
export function useGetAllRequireAgreeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetAllRequireAgreeQuery,
    GetAllRequireAgreeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetAllRequireAgreeQuery,
    GetAllRequireAgreeQueryVariables
  >(GetAllRequireAgreeDocument, options);
}
export type GetAllRequireAgreeQueryHookResult = ReturnType<
  typeof useGetAllRequireAgreeQuery
>;
export type GetAllRequireAgreeLazyQueryHookResult = ReturnType<
  typeof useGetAllRequireAgreeLazyQuery
>;
export type GetAllRequireAgreeSuspenseQueryHookResult = ReturnType<
  typeof useGetAllRequireAgreeSuspenseQuery
>;
export type GetAllRequireAgreeQueryResult = Apollo.QueryResult<
  GetAllRequireAgreeQuery,
  GetAllRequireAgreeQueryVariables
>;
export const AgreeDocument = gql`
  mutation agree($input: agreeReq!) {
    agree(input: $input)
  }
`;
export type AgreeMutationFn = Apollo.MutationFunction<
  AgreeMutation,
  AgreeMutationVariables
>;

/**
 * __useAgreeMutation__
 *
 * To run a mutation, you first call `useAgreeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAgreeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [agreeMutation, { data, loading, error }] = useAgreeMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAgreeMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AgreeMutation,
    AgreeMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<AgreeMutation, AgreeMutationVariables>(
    AgreeDocument,
    options,
  );
}
export type AgreeMutationHookResult = ReturnType<typeof useAgreeMutation>;
export type AgreeMutationResult = Apollo.MutationResult<AgreeMutation>;
export type AgreeMutationOptions = Apollo.BaseMutationOptions<
  AgreeMutation,
  AgreeMutationVariables
>;
