import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PatientChannelsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type PatientChannelsQuery = {
  __typename?: "query_root";
  patientChannels: Array<{
    __typename?: "PatientChannel";
    channelId: string;
    channelName: string;
    patientId: string;
    createdBy: string;
    updatedBy: string;
    createdAt: string;
    updatedAt: string;
    hospitalId: number;
    isUnread: number;
    sendable: number;
    latestPostedMessageAt?: string;
    isDeleted: number;
  }>;
};

export type PatientChannelMembersQueryVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
}>;

export type PatientChannelMembersQuery = {
  __typename?: "query_root";
  patientChannelMembers: Array<{
    __typename?: "ChannelMember";
    memberId: string;
    hasUnread: number;
    isPatient: number;
    memberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  }>;
};

export type EveryPatientMessageSettingQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type EveryPatientMessageSettingQuery = {
  __typename?: "query_root";
  everyPatientMessageSetting: {
    __typename?: "EveryPatientMessageSetting";
    sendable: number;
  };
};

export type PatientMessagesQueryVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
  num: Types.Scalars["Int"]["input"];
  startCursorMsgCreatedTime?: Types.InputMaybe<
    Types.Scalars["String"]["input"]
  >;
  endCursorMsgCreatedTime?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type PatientMessagesQuery = {
  __typename?: "query_root";
  patientMessages: {
    __typename?: "GetMessagesRes";
    pagination?: {
      __typename?: "Pagination";
      currentPage: number;
      lastPage: number;
      num: number;
      hasPreviousMore: number;
      hasNextMore: number;
      startMessageCreatedTime: string;
      endMessageCreatedTime: string;
    };
    messages: Array<{
      __typename?: "Message";
      channelId: number;
      messageId: string;
      messageType: Types.MessageType;
      hospitalId: number;
      isPatient: number;
      content: string;
      postedMember: string;
      createdAt: string;
      updatedAt: string;
      isDeleted: number;
      items: Array<{
        __typename?: "Item";
        itemId: string;
        itemName: string;
        src: string;
        isDeleted: number;
      }>;
      postedMemberInfo: {
        __typename?: "MemberInfo";
        memberName: string;
        status: number;
        staffType: number;
        managerKbn: number;
      };
    }>;
  };
};

export type StaffChannelsQueryVariables = Types.Exact<{ [key: string]: never }>;

export type StaffChannelsQuery = {
  __typename?: "query_root";
  staffChannels: Array<{
    __typename?: "StaffChannel";
    channelId: string;
    channelType: number;
    channelName: string;
    isUnread: number;
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
    hospitalId: number;
    latestPostedMessageAt?: string;
  }>;
};

export type StaffChannelMembersQueryVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
}>;

export type StaffChannelMembersQuery = {
  __typename?: "query_root";
  staffChannelMembers: Array<{
    __typename?: "ChannelMember";
    memberId: string;
    hasUnread: number;
    isPatient: number;
    memberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  }>;
};

export type StaffMessagesQueryVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
  num: Types.Scalars["Int"]["input"];
  startCursorMsgCreatedTime?: Types.InputMaybe<
    Types.Scalars["String"]["input"]
  >;
  endCursorMsgCreatedTime?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type StaffMessagesQuery = {
  __typename?: "query_root";
  staffMessages: {
    __typename?: "GetMessagesRes";
    pagination?: {
      __typename?: "Pagination";
      currentPage: number;
      lastPage: number;
      num: number;
      hasPreviousMore: number;
      hasNextMore: number;
      startMessageCreatedTime: string;
      endMessageCreatedTime: string;
    };
    messages: Array<{
      __typename?: "Message";
      messageId: string;
      messageType: Types.MessageType;
      hospitalId: number;
      isPatient: number;
      content: string;
      postedMember: string;
      channelId: number;
      createdAt: string;
      updatedAt: string;
      isDeleted: number;
      items: Array<{
        __typename?: "Item";
        itemId: string;
        itemName: string;
        src: string;
        isDeleted: number;
      }>;
      postedMemberInfo: {
        __typename?: "MemberInfo";
        memberName: string;
        status: number;
        staffType: number;
        managerKbn: number;
      };
    }>;
  };
};

export type EditStaffMessageMutationVariables = Types.Exact<{
  messageId: Types.Scalars["String"]["input"];
  content: Types.Scalars["String"]["input"];
}>;

export type EditStaffMessageMutation = {
  __typename?: "mutation_root";
  editStaffMessage: {
    __typename?: "Message";
    channelId: number;
    messageId: string;
    messageType: Types.MessageType;
    hospitalId: number;
    isPatient: number;
    content: string;
    postedMember: string;
    createdAt: string;
    updatedAt: string;
    isDeleted: number;
    items: Array<{
      __typename?: "Item";
      itemId: string;
      itemName: string;
      src: string;
      isDeleted: number;
    }>;
    postedMemberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  };
};

export type AddStaffChannelMembersMutationVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
  members:
    | Array<Types.InputMaybe<Types.Scalars["String"]["input"]>>
    | Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type AddStaffChannelMembersMutation = {
  __typename?: "mutation_root";
  addStaffChannelMembers: Array<{
    __typename?: "ChannelMember";
    memberId: string;
    hasUnread: number;
    isPatient: number;
    memberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  }>;
};

export type DeleteStaffChannelMemberMutationVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
  member: Types.Scalars["String"]["input"];
}>;

export type DeleteStaffChannelMemberMutation = {
  __typename?: "mutation_root";
  deleteStaffChannelMember: Array<{
    __typename?: "ChannelMember";
    memberId: string;
    hasUnread: number;
    isPatient: number;
    memberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  }>;
};

export type MarkAsReadForPatientChannelMutationVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
}>;

export type MarkAsReadForPatientChannelMutation = {
  __typename?: "mutation_root";
  markAsReadForPatientChannel: boolean;
};

export type MarkAsReadForStaffChannelMutationVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
}>;

export type MarkAsReadForStaffChannelMutation = {
  __typename?: "mutation_root";
  markAsReadForStaffChannel: boolean;
};

export type DeleteStaffMessageMutationVariables = Types.Exact<{
  messageId: Types.Scalars["String"]["input"];
}>;

export type DeleteStaffMessageMutation = {
  __typename?: "mutation_root";
  deleteStaffMessage: {
    __typename?: "Message";
    channelId: number;
    messageId: string;
    messageType: Types.MessageType;
    hospitalId: number;
    isPatient: number;
    content: string;
    postedMember: string;
    createdAt: string;
    updatedAt: string;
    isDeleted: number;
    items: Array<{
      __typename?: "Item";
      itemId: string;
      itemName: string;
      src: string;
      isDeleted: number;
    }>;
    postedMemberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  };
};

export type CreateStaffChannelMutationVariables = Types.Exact<{
  channelName: Types.Scalars["String"]["input"];
  channelType: Types.Scalars["Int"]["input"];
  members:
    | Array<Types.InputMaybe<Types.Scalars["String"]["input"]>>
    | Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type CreateStaffChannelMutation = {
  __typename?: "mutation_root";
  createStaffChannel?: {
    __typename?: "StaffChannel";
    channelId: string;
    channelName: string;
    createdBy: string;
    updatedBy: string;
    createdAt: string;
    updatedAt: string;
    hospitalId: number;
    isUnread: number;
    latestPostedMessageAt?: string;
  };
};

export type EditStaffChannelMutationVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
  channelName: Types.Scalars["String"]["input"];
}>;

export type EditStaffChannelMutation = {
  __typename?: "mutation_root";
  editStaffChannel: {
    __typename?: "StaffChannel";
    channelId: string;
    channelName: string;
    channelType: number;
    createdBy: string;
    updatedBy: string;
    createdAt: string;
    updatedAt: string;
    hospitalId: number;
    isUnread: number;
    latestPostedMessageAt?: string;
  };
};

export type DeleteStaffChannelMutationVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
}>;

export type DeleteStaffChannelMutation = {
  __typename?: "mutation_root";
  deleteStaffChannel: Array<{ __typename?: "StaffChannel"; channelId: string }>;
};

export type CreatePatientChannelMutationVariables = Types.Exact<{
  patientId: Types.Scalars["String"]["input"];
}>;

export type CreatePatientChannelMutation = {
  __typename?: "mutation_root";
  createPatientChannel?: { __typename?: "PatientChannel"; channelId: string };
};

export type AllowPatientSendMutationVariables = Types.Exact<{
  patientId: Types.Scalars["String"]["input"];
  sendable: Types.Scalars["Int"]["input"];
}>;

export type AllowPatientSendMutation = {
  __typename?: "mutation_root";
  allowPatientSend: boolean;
};

export type AllowEveryPatientSendMutationVariables = Types.Exact<{
  sendable: Types.Scalars["Int"]["input"];
}>;

export type AllowEveryPatientSendMutation = {
  __typename?: "mutation_root";
  allowEveryPatientSend: boolean;
};

export type PostStaffMessageMutationVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
  content: Types.Scalars["String"]["input"];
}>;

export type PostStaffMessageMutation = {
  __typename?: "mutation_root";
  postStaffMessage: {
    __typename?: "Message";
    channelId: number;
    messageId: string;
    messageType: Types.MessageType;
    hospitalId: number;
    isPatient: number;
    content: string;
    postedMember: string;
    createdAt: string;
    updatedAt: string;
    isDeleted: number;
    items: Array<{
      __typename?: "Item";
      itemId: string;
      itemName: string;
      src: string;
      isDeleted: number;
    }>;
    postedMemberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  };
};

export type PostStaffAttachFilesMutationVariables = Types.Exact<{
  input: Types.AttachFilesForm;
}>;

export type PostStaffAttachFilesMutation = {
  __typename?: "mutation_root";
  postStaffAttachFiles: {
    __typename?: "Message";
    channelId: number;
    messageId: string;
    messageType: Types.MessageType;
    hospitalId: number;
    isPatient: number;
    content: string;
    postedMember: string;
    createdAt: string;
    updatedAt: string;
    isDeleted: number;
    items: Array<{
      __typename?: "Item";
      itemId: string;
      itemName: string;
      src: string;
      isDeleted: number;
    }>;
    postedMemberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  };
};

export type PostPatientMessageMutationVariables = Types.Exact<{
  channelId: Types.Scalars["Int"]["input"];
  content: Types.Scalars["String"]["input"];
}>;

export type PostPatientMessageMutation = {
  __typename?: "mutation_root";
  postPatientMessage: {
    __typename?: "Message";
    channelId: number;
    messageId: string;
    messageType: Types.MessageType;
    hospitalId: number;
    isPatient: number;
    content: string;
    postedMember: string;
    createdAt: string;
    updatedAt: string;
    isDeleted: number;
    items: Array<{
      __typename?: "Item";
      itemId: string;
      itemName: string;
      src: string;
      isDeleted: number;
    }>;
    postedMemberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  };
};

export type EditPatientMessageMutationVariables = Types.Exact<{
  messageId: Types.Scalars["String"]["input"];
  content: Types.Scalars["String"]["input"];
}>;

export type EditPatientMessageMutation = {
  __typename?: "mutation_root";
  editPatientMessage: {
    __typename?: "Message";
    channelId: number;
    messageId: string;
    messageType: Types.MessageType;
    hospitalId: number;
    isPatient: number;
    content: string;
    postedMember: string;
    createdAt: string;
    updatedAt: string;
    isDeleted: number;
    items: Array<{
      __typename?: "Item";
      itemId: string;
      itemName: string;
      src: string;
      isDeleted: number;
    }>;
    postedMemberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  };
};

export type DeletePatientMessageMutationVariables = Types.Exact<{
  messageId: Types.Scalars["String"]["input"];
}>;

export type DeletePatientMessageMutation = {
  __typename?: "mutation_root";
  deletePatientMessage: {
    __typename?: "Message";
    channelId: number;
    messageId: string;
    messageType: Types.MessageType;
    hospitalId: number;
    isPatient: number;
    content: string;
    postedMember: string;
    createdAt: string;
    updatedAt: string;
    isDeleted: number;
    items: Array<{
      __typename?: "Item";
      itemId: string;
      itemName: string;
      src: string;
      isDeleted: number;
    }>;
    postedMemberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  };
};

export type PostPatientAttachFilesMutationVariables = Types.Exact<{
  input: Types.AttachFilesForm;
}>;

export type PostPatientAttachFilesMutation = {
  __typename?: "mutation_root";
  postPatientAttachFiles: {
    __typename?: "Message";
    channelId: number;
    messageId: string;
    messageType: Types.MessageType;
    hospitalId: number;
    isPatient: number;
    content: string;
    postedMember: string;
    createdAt: string;
    updatedAt: string;
    isDeleted: number;
    items: Array<{
      __typename?: "Item";
      itemId: string;
      itemName: string;
      src: string;
      isDeleted: number;
    }>;
    postedMemberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  };
};

export type DeletePatientMessageFileMutationVariables = Types.Exact<{
  itemId: Types.Scalars["String"]["input"];
}>;

export type DeletePatientMessageFileMutation = {
  __typename?: "mutation_root";
  deletePatientMessageFile: {
    __typename?: "Message";
    channelId: number;
    messageId: string;
    messageType: Types.MessageType;
    hospitalId: number;
    isPatient: number;
    content: string;
    postedMember: string;
    createdAt: string;
    updatedAt: string;
    isDeleted: number;
    items: Array<{
      __typename?: "Item";
      itemId: string;
      itemName: string;
      src: string;
      isDeleted: number;
    }>;
    postedMemberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  };
};

export type DeleteStaffMessageFileMutationVariables = Types.Exact<{
  itemId: Types.Scalars["String"]["input"];
}>;

export type DeleteStaffMessageFileMutation = {
  __typename?: "mutation_root";
  deleteStaffMessageFile: {
    __typename?: "Message";
    channelId: number;
    messageId: string;
    messageType: Types.MessageType;
    hospitalId: number;
    isPatient: number;
    content: string;
    postedMember: string;
    createdAt: string;
    updatedAt: string;
    isDeleted: number;
    items: Array<{
      __typename?: "Item";
      itemId: string;
      itemName: string;
      src: string;
      isDeleted: number;
    }>;
    postedMemberInfo: {
      __typename?: "MemberInfo";
      memberName: string;
      status: number;
      staffType: number;
      managerKbn: number;
    };
  };
};

export type SearchMessagesInStaffChannelQueryVariables = Types.Exact<{
  input: Types.SearchInChannel;
}>;

export type SearchMessagesInStaffChannelQuery = {
  __typename?: "query_root";
  searchMessagesInStaffChannel: {
    __typename?: "GetMessagesRes";
    messages: Array<{
      __typename?: "Message";
      messageId: string;
      messageType: Types.MessageType;
      hospitalId: number;
      isPatient: number;
      content: string;
      postedMember: string;
      channelId: number;
      createdAt: string;
      updatedAt: string;
      isDeleted: number;
      items: Array<{
        __typename?: "Item";
        itemId: string;
        itemName: string;
        src: string;
        isDeleted: number;
      }>;
      postedMemberInfo: {
        __typename?: "MemberInfo";
        memberName: string;
        status: number;
        staffType: number;
        managerKbn: number;
      };
    }>;
    pagination?: {
      __typename?: "Pagination";
      currentPage: number;
      lastPage: number;
      num: number;
      hasPreviousMore: number;
      hasNextMore: number;
      startMessageCreatedTime: string;
      endMessageCreatedTime: string;
    };
  };
};

export type SearchMessagesInPatientChannelQueryVariables = Types.Exact<{
  input: Types.SearchInChannel;
}>;

export type SearchMessagesInPatientChannelQuery = {
  __typename?: "query_root";
  searchMessagesInPatientChannel: {
    __typename?: "GetMessagesRes";
    messages: Array<{
      __typename?: "Message";
      messageId: string;
      messageType: Types.MessageType;
      hospitalId: number;
      isPatient: number;
      content: string;
      postedMember: string;
      channelId: number;
      createdAt: string;
      updatedAt: string;
      isDeleted: number;
      items: Array<{
        __typename?: "Item";
        itemId: string;
        itemName: string;
        src: string;
        isDeleted: number;
      }>;
      postedMemberInfo: {
        __typename?: "MemberInfo";
        memberName: string;
        status: number;
        staffType: number;
        managerKbn: number;
      };
    }>;
    pagination?: {
      __typename?: "Pagination";
      currentPage: number;
      lastPage: number;
      num: number;
      hasPreviousMore: number;
      hasNextMore: number;
      startMessageCreatedTime: string;
      endMessageCreatedTime: string;
    };
  };
};

export type GetMessagesUploadUrlQueryVariables = Types.Exact<{
  fileNames:
    | Array<Types.InputMaybe<Types.Scalars["String"]["input"]>>
    | Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetMessagesUploadUrlQuery = {
  __typename?: "query_root";
  getMessagesUploadUrl: Array<{
    __typename?: "MessageUrlRes";
    fileName: string;
    s3key: string;
    url: string;
  }>;
};

export type GetMessagesDownloadUrlQueryVariables = Types.Exact<{
  input: Types.SearchInMessagesDownloadUrl;
}>;

export type GetMessagesDownloadUrlQuery = {
  __typename?: "query_root";
  getMessagesDownloadUrl: Array<{
    __typename?: "MessageUrlRes";
    fileName: string;
    s3key: string;
    url: string;
  }>;
};

export type NotifySubscriptionVariables = Types.Exact<{ [key: string]: never }>;

export type NotifySubscription = {
  __typename?: "subscription_root";
  notify: {
    __typename?: "NotifyMessage";
    eventType: string;
    notifyType: string;
    message: {
      __typename?: "Message";
      channelId: number;
      messageId: string;
      messageType: Types.MessageType;
      hospitalId: number;
      isPatient: number;
      content: string;
      postedMember: string;
      createdAt: string;
      updatedAt: string;
      isDeleted: number;
      items: Array<{
        __typename?: "Item";
        itemId: string;
        itemName: string;
        src: string;
        isDeleted: number;
      }>;
      postedMemberInfo: {
        __typename?: "MemberInfo";
        memberName: string;
        status: number;
        staffType: number;
        managerKbn: number;
      };
    };
    channel: {
      __typename?: "NotifyChannel";
      channelId: string;
      channelName: string;
      patientId: string;
      channelType: number;
      hospitalId: number;
      isUnread: number;
      sendable: number;
      notifyTo?: number;
      latestPostedMessageAt?: string;
    };
  };
};

export type AddAuditLogForPatientChannelsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type AddAuditLogForPatientChannelsQuery = {
  __typename?: "query_root";
  addAuditLogForPatientChannels: boolean;
};

export type AddAuditLogForPatientMessagesQueryVariables = Types.Exact<{
  input: Types.GetMessagesReq;
}>;

export type AddAuditLogForPatientMessagesQuery = {
  __typename?: "query_root";
  AddAuditLogForPatientMessages: boolean;
};

export const PatientChannelsDocument = gql`
  query patientChannels {
    patientChannels {
      channelId
      channelName
      patientId
      createdBy
      updatedBy
      createdAt
      updatedAt
      hospitalId
      isUnread
      sendable
      latestPostedMessageAt
      isDeleted
    }
  }
`;

/**
 * __usePatientChannelsQuery__
 *
 * To run a query within a React component, call `usePatientChannelsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePatientChannelsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePatientChannelsQuery({
 *   variables: {
 *   },
 * });
 */
export function usePatientChannelsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    PatientChannelsQuery,
    PatientChannelsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<PatientChannelsQuery, PatientChannelsQueryVariables>(
    PatientChannelsDocument,
    options,
  );
}
export function usePatientChannelsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    PatientChannelsQuery,
    PatientChannelsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    PatientChannelsQuery,
    PatientChannelsQueryVariables
  >(PatientChannelsDocument, options);
}
export function usePatientChannelsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    PatientChannelsQuery,
    PatientChannelsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    PatientChannelsQuery,
    PatientChannelsQueryVariables
  >(PatientChannelsDocument, options);
}
export type PatientChannelsQueryHookResult = ReturnType<
  typeof usePatientChannelsQuery
>;
export type PatientChannelsLazyQueryHookResult = ReturnType<
  typeof usePatientChannelsLazyQuery
>;
export type PatientChannelsSuspenseQueryHookResult = ReturnType<
  typeof usePatientChannelsSuspenseQuery
>;
export type PatientChannelsQueryResult = Apollo.QueryResult<
  PatientChannelsQuery,
  PatientChannelsQueryVariables
>;
export const PatientChannelMembersDocument = gql`
  query patientChannelMembers($channelId: Int!) {
    patientChannelMembers(input: { channelId: $channelId }) {
      memberId
      hasUnread
      isPatient
      memberInfo {
        memberName
        status
        staffType
        managerKbn
      }
    }
  }
`;

/**
 * __usePatientChannelMembersQuery__
 *
 * To run a query within a React component, call `usePatientChannelMembersQuery` and pass it any options that fit your needs.
 * When your component renders, `usePatientChannelMembersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePatientChannelMembersQuery({
 *   variables: {
 *      channelId: // value for 'channelId'
 *   },
 * });
 */
export function usePatientChannelMembersQuery(
  baseOptions: Apollo.QueryHookOptions<
    PatientChannelMembersQuery,
    PatientChannelMembersQueryVariables
  > &
    (
      | { variables: PatientChannelMembersQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    PatientChannelMembersQuery,
    PatientChannelMembersQueryVariables
  >(PatientChannelMembersDocument, options);
}
export function usePatientChannelMembersLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    PatientChannelMembersQuery,
    PatientChannelMembersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    PatientChannelMembersQuery,
    PatientChannelMembersQueryVariables
  >(PatientChannelMembersDocument, options);
}
export function usePatientChannelMembersSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    PatientChannelMembersQuery,
    PatientChannelMembersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    PatientChannelMembersQuery,
    PatientChannelMembersQueryVariables
  >(PatientChannelMembersDocument, options);
}
export type PatientChannelMembersQueryHookResult = ReturnType<
  typeof usePatientChannelMembersQuery
>;
export type PatientChannelMembersLazyQueryHookResult = ReturnType<
  typeof usePatientChannelMembersLazyQuery
>;
export type PatientChannelMembersSuspenseQueryHookResult = ReturnType<
  typeof usePatientChannelMembersSuspenseQuery
>;
export type PatientChannelMembersQueryResult = Apollo.QueryResult<
  PatientChannelMembersQuery,
  PatientChannelMembersQueryVariables
>;
export const EveryPatientMessageSettingDocument = gql`
  query everyPatientMessageSetting {
    everyPatientMessageSetting {
      sendable
    }
  }
`;

/**
 * __useEveryPatientMessageSettingQuery__
 *
 * To run a query within a React component, call `useEveryPatientMessageSettingQuery` and pass it any options that fit your needs.
 * When your component renders, `useEveryPatientMessageSettingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEveryPatientMessageSettingQuery({
 *   variables: {
 *   },
 * });
 */
export function useEveryPatientMessageSettingQuery(
  baseOptions?: Apollo.QueryHookOptions<
    EveryPatientMessageSettingQuery,
    EveryPatientMessageSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    EveryPatientMessageSettingQuery,
    EveryPatientMessageSettingQueryVariables
  >(EveryPatientMessageSettingDocument, options);
}
export function useEveryPatientMessageSettingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    EveryPatientMessageSettingQuery,
    EveryPatientMessageSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    EveryPatientMessageSettingQuery,
    EveryPatientMessageSettingQueryVariables
  >(EveryPatientMessageSettingDocument, options);
}
export function useEveryPatientMessageSettingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    EveryPatientMessageSettingQuery,
    EveryPatientMessageSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    EveryPatientMessageSettingQuery,
    EveryPatientMessageSettingQueryVariables
  >(EveryPatientMessageSettingDocument, options);
}
export type EveryPatientMessageSettingQueryHookResult = ReturnType<
  typeof useEveryPatientMessageSettingQuery
>;
export type EveryPatientMessageSettingLazyQueryHookResult = ReturnType<
  typeof useEveryPatientMessageSettingLazyQuery
>;
export type EveryPatientMessageSettingSuspenseQueryHookResult = ReturnType<
  typeof useEveryPatientMessageSettingSuspenseQuery
>;
export type EveryPatientMessageSettingQueryResult = Apollo.QueryResult<
  EveryPatientMessageSettingQuery,
  EveryPatientMessageSettingQueryVariables
>;
export const PatientMessagesDocument = gql`
  query patientMessages(
    $channelId: Int!
    $num: Int!
    $startCursorMsgCreatedTime: String
    $endCursorMsgCreatedTime: String
  ) {
    patientMessages(
      input: {
        channelId: $channelId
        num: $num
        startCursorMsgCreatedTime: $startCursorMsgCreatedTime
        endCursorMsgCreatedTime: $endCursorMsgCreatedTime
      }
    ) {
      pagination {
        currentPage
        lastPage
        num
        hasPreviousMore
        hasNextMore
        startMessageCreatedTime
        endMessageCreatedTime
      }
      messages {
        channelId
        messageId
        messageType
        hospitalId
        isPatient
        content
        items {
          itemId
          itemName
          src
          isDeleted
        }
        postedMember
        postedMemberInfo {
          memberName
          status
          staffType
          managerKbn
        }
        createdAt
        updatedAt
        isDeleted
      }
    }
  }
`;

/**
 * __usePatientMessagesQuery__
 *
 * To run a query within a React component, call `usePatientMessagesQuery` and pass it any options that fit your needs.
 * When your component renders, `usePatientMessagesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePatientMessagesQuery({
 *   variables: {
 *      channelId: // value for 'channelId'
 *      num: // value for 'num'
 *      startCursorMsgCreatedTime: // value for 'startCursorMsgCreatedTime'
 *      endCursorMsgCreatedTime: // value for 'endCursorMsgCreatedTime'
 *   },
 * });
 */
export function usePatientMessagesQuery(
  baseOptions: Apollo.QueryHookOptions<
    PatientMessagesQuery,
    PatientMessagesQueryVariables
  > &
    (
      | { variables: PatientMessagesQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<PatientMessagesQuery, PatientMessagesQueryVariables>(
    PatientMessagesDocument,
    options,
  );
}
export function usePatientMessagesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    PatientMessagesQuery,
    PatientMessagesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    PatientMessagesQuery,
    PatientMessagesQueryVariables
  >(PatientMessagesDocument, options);
}
export function usePatientMessagesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    PatientMessagesQuery,
    PatientMessagesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    PatientMessagesQuery,
    PatientMessagesQueryVariables
  >(PatientMessagesDocument, options);
}
export type PatientMessagesQueryHookResult = ReturnType<
  typeof usePatientMessagesQuery
>;
export type PatientMessagesLazyQueryHookResult = ReturnType<
  typeof usePatientMessagesLazyQuery
>;
export type PatientMessagesSuspenseQueryHookResult = ReturnType<
  typeof usePatientMessagesSuspenseQuery
>;
export type PatientMessagesQueryResult = Apollo.QueryResult<
  PatientMessagesQuery,
  PatientMessagesQueryVariables
>;
export const StaffChannelsDocument = gql`
  query staffChannels {
    staffChannels {
      channelId
      channelType
      channelName
      isUnread
      createdBy
      createdAt
      updatedBy
      updatedAt
      hospitalId
      isUnread
      latestPostedMessageAt
    }
  }
`;

/**
 * __useStaffChannelsQuery__
 *
 * To run a query within a React component, call `useStaffChannelsQuery` and pass it any options that fit your needs.
 * When your component renders, `useStaffChannelsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useStaffChannelsQuery({
 *   variables: {
 *   },
 * });
 */
export function useStaffChannelsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    StaffChannelsQuery,
    StaffChannelsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<StaffChannelsQuery, StaffChannelsQueryVariables>(
    StaffChannelsDocument,
    options,
  );
}
export function useStaffChannelsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    StaffChannelsQuery,
    StaffChannelsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<StaffChannelsQuery, StaffChannelsQueryVariables>(
    StaffChannelsDocument,
    options,
  );
}
export function useStaffChannelsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    StaffChannelsQuery,
    StaffChannelsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    StaffChannelsQuery,
    StaffChannelsQueryVariables
  >(StaffChannelsDocument, options);
}
export type StaffChannelsQueryHookResult = ReturnType<
  typeof useStaffChannelsQuery
>;
export type StaffChannelsLazyQueryHookResult = ReturnType<
  typeof useStaffChannelsLazyQuery
>;
export type StaffChannelsSuspenseQueryHookResult = ReturnType<
  typeof useStaffChannelsSuspenseQuery
>;
export type StaffChannelsQueryResult = Apollo.QueryResult<
  StaffChannelsQuery,
  StaffChannelsQueryVariables
>;
export const StaffChannelMembersDocument = gql`
  query staffChannelMembers($channelId: Int!) {
    staffChannelMembers(input: { channelId: $channelId }) {
      memberId
      hasUnread
      isPatient
      memberInfo {
        memberName
        status
        staffType
        managerKbn
      }
    }
  }
`;

/**
 * __useStaffChannelMembersQuery__
 *
 * To run a query within a React component, call `useStaffChannelMembersQuery` and pass it any options that fit your needs.
 * When your component renders, `useStaffChannelMembersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useStaffChannelMembersQuery({
 *   variables: {
 *      channelId: // value for 'channelId'
 *   },
 * });
 */
export function useStaffChannelMembersQuery(
  baseOptions: Apollo.QueryHookOptions<
    StaffChannelMembersQuery,
    StaffChannelMembersQueryVariables
  > &
    (
      | { variables: StaffChannelMembersQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    StaffChannelMembersQuery,
    StaffChannelMembersQueryVariables
  >(StaffChannelMembersDocument, options);
}
export function useStaffChannelMembersLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    StaffChannelMembersQuery,
    StaffChannelMembersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    StaffChannelMembersQuery,
    StaffChannelMembersQueryVariables
  >(StaffChannelMembersDocument, options);
}
export function useStaffChannelMembersSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    StaffChannelMembersQuery,
    StaffChannelMembersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    StaffChannelMembersQuery,
    StaffChannelMembersQueryVariables
  >(StaffChannelMembersDocument, options);
}
export type StaffChannelMembersQueryHookResult = ReturnType<
  typeof useStaffChannelMembersQuery
>;
export type StaffChannelMembersLazyQueryHookResult = ReturnType<
  typeof useStaffChannelMembersLazyQuery
>;
export type StaffChannelMembersSuspenseQueryHookResult = ReturnType<
  typeof useStaffChannelMembersSuspenseQuery
>;
export type StaffChannelMembersQueryResult = Apollo.QueryResult<
  StaffChannelMembersQuery,
  StaffChannelMembersQueryVariables
>;
export const StaffMessagesDocument = gql`
  query staffMessages(
    $channelId: Int!
    $num: Int!
    $startCursorMsgCreatedTime: String
    $endCursorMsgCreatedTime: String
  ) {
    staffMessages(
      input: {
        channelId: $channelId
        num: $num
        startCursorMsgCreatedTime: $startCursorMsgCreatedTime
        endCursorMsgCreatedTime: $endCursorMsgCreatedTime
      }
    ) {
      pagination {
        currentPage
        lastPage
        num
        hasPreviousMore
        hasNextMore
        startMessageCreatedTime
        endMessageCreatedTime
      }
      messages {
        messageId
        messageType
        hospitalId
        isPatient
        content
        items {
          itemId
          itemName
          src
          isDeleted
        }
        postedMember
        postedMemberInfo {
          memberName
          status
          staffType
          managerKbn
        }
        channelId
        createdAt
        updatedAt
        isDeleted
      }
    }
  }
`;

/**
 * __useStaffMessagesQuery__
 *
 * To run a query within a React component, call `useStaffMessagesQuery` and pass it any options that fit your needs.
 * When your component renders, `useStaffMessagesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useStaffMessagesQuery({
 *   variables: {
 *      channelId: // value for 'channelId'
 *      num: // value for 'num'
 *      startCursorMsgCreatedTime: // value for 'startCursorMsgCreatedTime'
 *      endCursorMsgCreatedTime: // value for 'endCursorMsgCreatedTime'
 *   },
 * });
 */
export function useStaffMessagesQuery(
  baseOptions: Apollo.QueryHookOptions<
    StaffMessagesQuery,
    StaffMessagesQueryVariables
  > &
    (
      | { variables: StaffMessagesQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<StaffMessagesQuery, StaffMessagesQueryVariables>(
    StaffMessagesDocument,
    options,
  );
}
export function useStaffMessagesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    StaffMessagesQuery,
    StaffMessagesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<StaffMessagesQuery, StaffMessagesQueryVariables>(
    StaffMessagesDocument,
    options,
  );
}
export function useStaffMessagesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    StaffMessagesQuery,
    StaffMessagesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    StaffMessagesQuery,
    StaffMessagesQueryVariables
  >(StaffMessagesDocument, options);
}
export type StaffMessagesQueryHookResult = ReturnType<
  typeof useStaffMessagesQuery
>;
export type StaffMessagesLazyQueryHookResult = ReturnType<
  typeof useStaffMessagesLazyQuery
>;
export type StaffMessagesSuspenseQueryHookResult = ReturnType<
  typeof useStaffMessagesSuspenseQuery
>;
export type StaffMessagesQueryResult = Apollo.QueryResult<
  StaffMessagesQuery,
  StaffMessagesQueryVariables
>;
export const EditStaffMessageDocument = gql`
  mutation editStaffMessage($messageId: String!, $content: String!) {
    editStaffMessage(input: { messageId: $messageId, content: $content }) {
      channelId
      messageId
      messageType
      hospitalId
      isPatient
      content
      items {
        itemId
        itemName
        src
        isDeleted
      }
      postedMember
      postedMemberInfo {
        memberName
        status
        staffType
        managerKbn
      }
      createdAt
      updatedAt
      isDeleted
    }
  }
`;
export type EditStaffMessageMutationFn = Apollo.MutationFunction<
  EditStaffMessageMutation,
  EditStaffMessageMutationVariables
>;

/**
 * __useEditStaffMessageMutation__
 *
 * To run a mutation, you first call `useEditStaffMessageMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditStaffMessageMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editStaffMessageMutation, { data, loading, error }] = useEditStaffMessageMutation({
 *   variables: {
 *      messageId: // value for 'messageId'
 *      content: // value for 'content'
 *   },
 * });
 */
export function useEditStaffMessageMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditStaffMessageMutation,
    EditStaffMessageMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditStaffMessageMutation,
    EditStaffMessageMutationVariables
  >(EditStaffMessageDocument, options);
}
export type EditStaffMessageMutationHookResult = ReturnType<
  typeof useEditStaffMessageMutation
>;
export type EditStaffMessageMutationResult =
  Apollo.MutationResult<EditStaffMessageMutation>;
export type EditStaffMessageMutationOptions = Apollo.BaseMutationOptions<
  EditStaffMessageMutation,
  EditStaffMessageMutationVariables
>;
export const AddStaffChannelMembersDocument = gql`
  mutation addStaffChannelMembers($channelId: Int!, $members: [String]!) {
    addStaffChannelMembers(
      input: { channelId: $channelId, members: $members }
    ) {
      memberId
      hasUnread
      isPatient
      memberInfo {
        memberName
        status
        staffType
        managerKbn
      }
    }
  }
`;
export type AddStaffChannelMembersMutationFn = Apollo.MutationFunction<
  AddStaffChannelMembersMutation,
  AddStaffChannelMembersMutationVariables
>;

/**
 * __useAddStaffChannelMembersMutation__
 *
 * To run a mutation, you first call `useAddStaffChannelMembersMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddStaffChannelMembersMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addStaffChannelMembersMutation, { data, loading, error }] = useAddStaffChannelMembersMutation({
 *   variables: {
 *      channelId: // value for 'channelId'
 *      members: // value for 'members'
 *   },
 * });
 */
export function useAddStaffChannelMembersMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AddStaffChannelMembersMutation,
    AddStaffChannelMembersMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AddStaffChannelMembersMutation,
    AddStaffChannelMembersMutationVariables
  >(AddStaffChannelMembersDocument, options);
}
export type AddStaffChannelMembersMutationHookResult = ReturnType<
  typeof useAddStaffChannelMembersMutation
>;
export type AddStaffChannelMembersMutationResult =
  Apollo.MutationResult<AddStaffChannelMembersMutation>;
export type AddStaffChannelMembersMutationOptions = Apollo.BaseMutationOptions<
  AddStaffChannelMembersMutation,
  AddStaffChannelMembersMutationVariables
>;
export const DeleteStaffChannelMemberDocument = gql`
  mutation deleteStaffChannelMember($channelId: Int!, $member: String!) {
    deleteStaffChannelMember(
      input: { channelId: $channelId, member: $member }
    ) {
      memberId
      hasUnread
      isPatient
      memberInfo {
        memberName
        status
        staffType
        managerKbn
      }
    }
  }
`;
export type DeleteStaffChannelMemberMutationFn = Apollo.MutationFunction<
  DeleteStaffChannelMemberMutation,
  DeleteStaffChannelMemberMutationVariables
>;

/**
 * __useDeleteStaffChannelMemberMutation__
 *
 * To run a mutation, you first call `useDeleteStaffChannelMemberMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteStaffChannelMemberMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteStaffChannelMemberMutation, { data, loading, error }] = useDeleteStaffChannelMemberMutation({
 *   variables: {
 *      channelId: // value for 'channelId'
 *      member: // value for 'member'
 *   },
 * });
 */
export function useDeleteStaffChannelMemberMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteStaffChannelMemberMutation,
    DeleteStaffChannelMemberMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteStaffChannelMemberMutation,
    DeleteStaffChannelMemberMutationVariables
  >(DeleteStaffChannelMemberDocument, options);
}
export type DeleteStaffChannelMemberMutationHookResult = ReturnType<
  typeof useDeleteStaffChannelMemberMutation
>;
export type DeleteStaffChannelMemberMutationResult =
  Apollo.MutationResult<DeleteStaffChannelMemberMutation>;
export type DeleteStaffChannelMemberMutationOptions =
  Apollo.BaseMutationOptions<
    DeleteStaffChannelMemberMutation,
    DeleteStaffChannelMemberMutationVariables
  >;
export const MarkAsReadForPatientChannelDocument = gql`
  mutation markAsReadForPatientChannel($channelId: Int!) {
    markAsReadForPatientChannel(input: { channelId: $channelId })
  }
`;
export type MarkAsReadForPatientChannelMutationFn = Apollo.MutationFunction<
  MarkAsReadForPatientChannelMutation,
  MarkAsReadForPatientChannelMutationVariables
>;

/**
 * __useMarkAsReadForPatientChannelMutation__
 *
 * To run a mutation, you first call `useMarkAsReadForPatientChannelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMarkAsReadForPatientChannelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [markAsReadForPatientChannelMutation, { data, loading, error }] = useMarkAsReadForPatientChannelMutation({
 *   variables: {
 *      channelId: // value for 'channelId'
 *   },
 * });
 */
export function useMarkAsReadForPatientChannelMutation(
  baseOptions?: Apollo.MutationHookOptions<
    MarkAsReadForPatientChannelMutation,
    MarkAsReadForPatientChannelMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    MarkAsReadForPatientChannelMutation,
    MarkAsReadForPatientChannelMutationVariables
  >(MarkAsReadForPatientChannelDocument, options);
}
export type MarkAsReadForPatientChannelMutationHookResult = ReturnType<
  typeof useMarkAsReadForPatientChannelMutation
>;
export type MarkAsReadForPatientChannelMutationResult =
  Apollo.MutationResult<MarkAsReadForPatientChannelMutation>;
export type MarkAsReadForPatientChannelMutationOptions =
  Apollo.BaseMutationOptions<
    MarkAsReadForPatientChannelMutation,
    MarkAsReadForPatientChannelMutationVariables
  >;
export const MarkAsReadForStaffChannelDocument = gql`
  mutation markAsReadForStaffChannel($channelId: Int!) {
    markAsReadForStaffChannel(input: { channelId: $channelId })
  }
`;
export type MarkAsReadForStaffChannelMutationFn = Apollo.MutationFunction<
  MarkAsReadForStaffChannelMutation,
  MarkAsReadForStaffChannelMutationVariables
>;

/**
 * __useMarkAsReadForStaffChannelMutation__
 *
 * To run a mutation, you first call `useMarkAsReadForStaffChannelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMarkAsReadForStaffChannelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [markAsReadForStaffChannelMutation, { data, loading, error }] = useMarkAsReadForStaffChannelMutation({
 *   variables: {
 *      channelId: // value for 'channelId'
 *   },
 * });
 */
export function useMarkAsReadForStaffChannelMutation(
  baseOptions?: Apollo.MutationHookOptions<
    MarkAsReadForStaffChannelMutation,
    MarkAsReadForStaffChannelMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    MarkAsReadForStaffChannelMutation,
    MarkAsReadForStaffChannelMutationVariables
  >(MarkAsReadForStaffChannelDocument, options);
}
export type MarkAsReadForStaffChannelMutationHookResult = ReturnType<
  typeof useMarkAsReadForStaffChannelMutation
>;
export type MarkAsReadForStaffChannelMutationResult =
  Apollo.MutationResult<MarkAsReadForStaffChannelMutation>;
export type MarkAsReadForStaffChannelMutationOptions =
  Apollo.BaseMutationOptions<
    MarkAsReadForStaffChannelMutation,
    MarkAsReadForStaffChannelMutationVariables
  >;
export const DeleteStaffMessageDocument = gql`
  mutation deleteStaffMessage($messageId: String!) {
    deleteStaffMessage(input: { messageId: $messageId }) {
      channelId
      messageId
      messageType
      hospitalId
      isPatient
      content
      items {
        itemId
        itemName
        src
        isDeleted
      }
      postedMember
      postedMemberInfo {
        memberName
        status
        staffType
        managerKbn
      }
      createdAt
      updatedAt
      isDeleted
    }
  }
`;
export type DeleteStaffMessageMutationFn = Apollo.MutationFunction<
  DeleteStaffMessageMutation,
  DeleteStaffMessageMutationVariables
>;

/**
 * __useDeleteStaffMessageMutation__
 *
 * To run a mutation, you first call `useDeleteStaffMessageMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteStaffMessageMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteStaffMessageMutation, { data, loading, error }] = useDeleteStaffMessageMutation({
 *   variables: {
 *      messageId: // value for 'messageId'
 *   },
 * });
 */
export function useDeleteStaffMessageMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteStaffMessageMutation,
    DeleteStaffMessageMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteStaffMessageMutation,
    DeleteStaffMessageMutationVariables
  >(DeleteStaffMessageDocument, options);
}
export type DeleteStaffMessageMutationHookResult = ReturnType<
  typeof useDeleteStaffMessageMutation
>;
export type DeleteStaffMessageMutationResult =
  Apollo.MutationResult<DeleteStaffMessageMutation>;
export type DeleteStaffMessageMutationOptions = Apollo.BaseMutationOptions<
  DeleteStaffMessageMutation,
  DeleteStaffMessageMutationVariables
>;
export const CreateStaffChannelDocument = gql`
  mutation createStaffChannel(
    $channelName: String!
    $channelType: Int!
    $members: [String]!
  ) {
    createStaffChannel(
      input: {
        channelName: $channelName
        channelType: $channelType
        members: $members
      }
    ) {
      channelId
      channelName
      createdBy
      updatedBy
      createdAt
      updatedAt
      hospitalId
      isUnread
      latestPostedMessageAt
    }
  }
`;
export type CreateStaffChannelMutationFn = Apollo.MutationFunction<
  CreateStaffChannelMutation,
  CreateStaffChannelMutationVariables
>;

/**
 * __useCreateStaffChannelMutation__
 *
 * To run a mutation, you first call `useCreateStaffChannelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateStaffChannelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createStaffChannelMutation, { data, loading, error }] = useCreateStaffChannelMutation({
 *   variables: {
 *      channelName: // value for 'channelName'
 *      channelType: // value for 'channelType'
 *      members: // value for 'members'
 *   },
 * });
 */
export function useCreateStaffChannelMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateStaffChannelMutation,
    CreateStaffChannelMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateStaffChannelMutation,
    CreateStaffChannelMutationVariables
  >(CreateStaffChannelDocument, options);
}
export type CreateStaffChannelMutationHookResult = ReturnType<
  typeof useCreateStaffChannelMutation
>;
export type CreateStaffChannelMutationResult =
  Apollo.MutationResult<CreateStaffChannelMutation>;
export type CreateStaffChannelMutationOptions = Apollo.BaseMutationOptions<
  CreateStaffChannelMutation,
  CreateStaffChannelMutationVariables
>;
export const EditStaffChannelDocument = gql`
  mutation editStaffChannel($channelId: Int!, $channelName: String!) {
    editStaffChannel(
      input: { channelId: $channelId, channelName: $channelName }
    ) {
      channelId
      channelName
      channelType
      createdBy
      updatedBy
      createdAt
      updatedAt
      hospitalId
      isUnread
      latestPostedMessageAt
    }
  }
`;
export type EditStaffChannelMutationFn = Apollo.MutationFunction<
  EditStaffChannelMutation,
  EditStaffChannelMutationVariables
>;

/**
 * __useEditStaffChannelMutation__
 *
 * To run a mutation, you first call `useEditStaffChannelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditStaffChannelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editStaffChannelMutation, { data, loading, error }] = useEditStaffChannelMutation({
 *   variables: {
 *      channelId: // value for 'channelId'
 *      channelName: // value for 'channelName'
 *   },
 * });
 */
export function useEditStaffChannelMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditStaffChannelMutation,
    EditStaffChannelMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditStaffChannelMutation,
    EditStaffChannelMutationVariables
  >(EditStaffChannelDocument, options);
}
export type EditStaffChannelMutationHookResult = ReturnType<
  typeof useEditStaffChannelMutation
>;
export type EditStaffChannelMutationResult =
  Apollo.MutationResult<EditStaffChannelMutation>;
export type EditStaffChannelMutationOptions = Apollo.BaseMutationOptions<
  EditStaffChannelMutation,
  EditStaffChannelMutationVariables
>;
export const DeleteStaffChannelDocument = gql`
  mutation deleteStaffChannel($channelId: Int!) {
    deleteStaffChannel(input: { channelId: $channelId }) {
      channelId
    }
  }
`;
export type DeleteStaffChannelMutationFn = Apollo.MutationFunction<
  DeleteStaffChannelMutation,
  DeleteStaffChannelMutationVariables
>;

/**
 * __useDeleteStaffChannelMutation__
 *
 * To run a mutation, you first call `useDeleteStaffChannelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteStaffChannelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteStaffChannelMutation, { data, loading, error }] = useDeleteStaffChannelMutation({
 *   variables: {
 *      channelId: // value for 'channelId'
 *   },
 * });
 */
export function useDeleteStaffChannelMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteStaffChannelMutation,
    DeleteStaffChannelMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteStaffChannelMutation,
    DeleteStaffChannelMutationVariables
  >(DeleteStaffChannelDocument, options);
}
export type DeleteStaffChannelMutationHookResult = ReturnType<
  typeof useDeleteStaffChannelMutation
>;
export type DeleteStaffChannelMutationResult =
  Apollo.MutationResult<DeleteStaffChannelMutation>;
export type DeleteStaffChannelMutationOptions = Apollo.BaseMutationOptions<
  DeleteStaffChannelMutation,
  DeleteStaffChannelMutationVariables
>;
export const CreatePatientChannelDocument = gql`
  mutation createPatientChannel($patientId: String!) {
    createPatientChannel(input: { patientId: $patientId }) {
      channelId
    }
  }
`;
export type CreatePatientChannelMutationFn = Apollo.MutationFunction<
  CreatePatientChannelMutation,
  CreatePatientChannelMutationVariables
>;

/**
 * __useCreatePatientChannelMutation__
 *
 * To run a mutation, you first call `useCreatePatientChannelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreatePatientChannelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createPatientChannelMutation, { data, loading, error }] = useCreatePatientChannelMutation({
 *   variables: {
 *      patientId: // value for 'patientId'
 *   },
 * });
 */
export function useCreatePatientChannelMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreatePatientChannelMutation,
    CreatePatientChannelMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreatePatientChannelMutation,
    CreatePatientChannelMutationVariables
  >(CreatePatientChannelDocument, options);
}
export type CreatePatientChannelMutationHookResult = ReturnType<
  typeof useCreatePatientChannelMutation
>;
export type CreatePatientChannelMutationResult =
  Apollo.MutationResult<CreatePatientChannelMutation>;
export type CreatePatientChannelMutationOptions = Apollo.BaseMutationOptions<
  CreatePatientChannelMutation,
  CreatePatientChannelMutationVariables
>;
export const AllowPatientSendDocument = gql`
  mutation allowPatientSend($patientId: String!, $sendable: Int!) {
    allowPatientSend(input: { patientId: $patientId, sendable: $sendable })
  }
`;
export type AllowPatientSendMutationFn = Apollo.MutationFunction<
  AllowPatientSendMutation,
  AllowPatientSendMutationVariables
>;

/**
 * __useAllowPatientSendMutation__
 *
 * To run a mutation, you first call `useAllowPatientSendMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAllowPatientSendMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [allowPatientSendMutation, { data, loading, error }] = useAllowPatientSendMutation({
 *   variables: {
 *      patientId: // value for 'patientId'
 *      sendable: // value for 'sendable'
 *   },
 * });
 */
export function useAllowPatientSendMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AllowPatientSendMutation,
    AllowPatientSendMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AllowPatientSendMutation,
    AllowPatientSendMutationVariables
  >(AllowPatientSendDocument, options);
}
export type AllowPatientSendMutationHookResult = ReturnType<
  typeof useAllowPatientSendMutation
>;
export type AllowPatientSendMutationResult =
  Apollo.MutationResult<AllowPatientSendMutation>;
export type AllowPatientSendMutationOptions = Apollo.BaseMutationOptions<
  AllowPatientSendMutation,
  AllowPatientSendMutationVariables
>;
export const AllowEveryPatientSendDocument = gql`
  mutation allowEveryPatientSend($sendable: Int!) {
    allowEveryPatientSend(input: { sendable: $sendable })
  }
`;
export type AllowEveryPatientSendMutationFn = Apollo.MutationFunction<
  AllowEveryPatientSendMutation,
  AllowEveryPatientSendMutationVariables
>;

/**
 * __useAllowEveryPatientSendMutation__
 *
 * To run a mutation, you first call `useAllowEveryPatientSendMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAllowEveryPatientSendMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [allowEveryPatientSendMutation, { data, loading, error }] = useAllowEveryPatientSendMutation({
 *   variables: {
 *      sendable: // value for 'sendable'
 *   },
 * });
 */
export function useAllowEveryPatientSendMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AllowEveryPatientSendMutation,
    AllowEveryPatientSendMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AllowEveryPatientSendMutation,
    AllowEveryPatientSendMutationVariables
  >(AllowEveryPatientSendDocument, options);
}
export type AllowEveryPatientSendMutationHookResult = ReturnType<
  typeof useAllowEveryPatientSendMutation
>;
export type AllowEveryPatientSendMutationResult =
  Apollo.MutationResult<AllowEveryPatientSendMutation>;
export type AllowEveryPatientSendMutationOptions = Apollo.BaseMutationOptions<
  AllowEveryPatientSendMutation,
  AllowEveryPatientSendMutationVariables
>;
export const PostStaffMessageDocument = gql`
  mutation postStaffMessage($channelId: Int!, $content: String!) {
    postStaffMessage(input: { channelId: $channelId, content: $content }) {
      channelId
      messageId
      messageType
      hospitalId
      isPatient
      content
      items {
        itemId
        itemName
        src
        isDeleted
      }
      postedMember
      postedMemberInfo {
        memberName
        status
        staffType
        managerKbn
      }
      createdAt
      updatedAt
      isDeleted
    }
  }
`;
export type PostStaffMessageMutationFn = Apollo.MutationFunction<
  PostStaffMessageMutation,
  PostStaffMessageMutationVariables
>;

/**
 * __usePostStaffMessageMutation__
 *
 * To run a mutation, you first call `usePostStaffMessageMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostStaffMessageMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postStaffMessageMutation, { data, loading, error }] = usePostStaffMessageMutation({
 *   variables: {
 *      channelId: // value for 'channelId'
 *      content: // value for 'content'
 *   },
 * });
 */
export function usePostStaffMessageMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostStaffMessageMutation,
    PostStaffMessageMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostStaffMessageMutation,
    PostStaffMessageMutationVariables
  >(PostStaffMessageDocument, options);
}
export type PostStaffMessageMutationHookResult = ReturnType<
  typeof usePostStaffMessageMutation
>;
export type PostStaffMessageMutationResult =
  Apollo.MutationResult<PostStaffMessageMutation>;
export type PostStaffMessageMutationOptions = Apollo.BaseMutationOptions<
  PostStaffMessageMutation,
  PostStaffMessageMutationVariables
>;
export const PostStaffAttachFilesDocument = gql`
  mutation postStaffAttachFiles($input: attachFilesForm!) {
    postStaffAttachFiles(input: $input) {
      channelId
      messageId
      messageType
      hospitalId
      isPatient
      content
      items {
        itemId
        itemName
        src
        isDeleted
      }
      postedMember
      postedMemberInfo {
        memberName
        status
        staffType
        managerKbn
      }
      createdAt
      updatedAt
      isDeleted
    }
  }
`;
export type PostStaffAttachFilesMutationFn = Apollo.MutationFunction<
  PostStaffAttachFilesMutation,
  PostStaffAttachFilesMutationVariables
>;

/**
 * __usePostStaffAttachFilesMutation__
 *
 * To run a mutation, you first call `usePostStaffAttachFilesMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostStaffAttachFilesMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postStaffAttachFilesMutation, { data, loading, error }] = usePostStaffAttachFilesMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostStaffAttachFilesMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostStaffAttachFilesMutation,
    PostStaffAttachFilesMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostStaffAttachFilesMutation,
    PostStaffAttachFilesMutationVariables
  >(PostStaffAttachFilesDocument, options);
}
export type PostStaffAttachFilesMutationHookResult = ReturnType<
  typeof usePostStaffAttachFilesMutation
>;
export type PostStaffAttachFilesMutationResult =
  Apollo.MutationResult<PostStaffAttachFilesMutation>;
export type PostStaffAttachFilesMutationOptions = Apollo.BaseMutationOptions<
  PostStaffAttachFilesMutation,
  PostStaffAttachFilesMutationVariables
>;
export const PostPatientMessageDocument = gql`
  mutation postPatientMessage($channelId: Int!, $content: String!) {
    postPatientMessage(input: { channelId: $channelId, content: $content }) {
      channelId
      messageId
      messageType
      hospitalId
      isPatient
      content
      items {
        itemId
        itemName
        src
        isDeleted
      }
      postedMember
      postedMemberInfo {
        memberName
        status
        staffType
        managerKbn
      }
      createdAt
      updatedAt
      isDeleted
    }
  }
`;
export type PostPatientMessageMutationFn = Apollo.MutationFunction<
  PostPatientMessageMutation,
  PostPatientMessageMutationVariables
>;

/**
 * __usePostPatientMessageMutation__
 *
 * To run a mutation, you first call `usePostPatientMessageMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostPatientMessageMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postPatientMessageMutation, { data, loading, error }] = usePostPatientMessageMutation({
 *   variables: {
 *      channelId: // value for 'channelId'
 *      content: // value for 'content'
 *   },
 * });
 */
export function usePostPatientMessageMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostPatientMessageMutation,
    PostPatientMessageMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostPatientMessageMutation,
    PostPatientMessageMutationVariables
  >(PostPatientMessageDocument, options);
}
export type PostPatientMessageMutationHookResult = ReturnType<
  typeof usePostPatientMessageMutation
>;
export type PostPatientMessageMutationResult =
  Apollo.MutationResult<PostPatientMessageMutation>;
export type PostPatientMessageMutationOptions = Apollo.BaseMutationOptions<
  PostPatientMessageMutation,
  PostPatientMessageMutationVariables
>;
export const EditPatientMessageDocument = gql`
  mutation editPatientMessage($messageId: String!, $content: String!) {
    editPatientMessage(input: { messageId: $messageId, content: $content }) {
      channelId
      messageId
      messageType
      hospitalId
      isPatient
      content
      items {
        itemId
        itemName
        src
        isDeleted
      }
      postedMember
      postedMemberInfo {
        memberName
        status
        staffType
        managerKbn
      }
      createdAt
      updatedAt
      isDeleted
    }
  }
`;
export type EditPatientMessageMutationFn = Apollo.MutationFunction<
  EditPatientMessageMutation,
  EditPatientMessageMutationVariables
>;

/**
 * __useEditPatientMessageMutation__
 *
 * To run a mutation, you first call `useEditPatientMessageMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditPatientMessageMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editPatientMessageMutation, { data, loading, error }] = useEditPatientMessageMutation({
 *   variables: {
 *      messageId: // value for 'messageId'
 *      content: // value for 'content'
 *   },
 * });
 */
export function useEditPatientMessageMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditPatientMessageMutation,
    EditPatientMessageMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditPatientMessageMutation,
    EditPatientMessageMutationVariables
  >(EditPatientMessageDocument, options);
}
export type EditPatientMessageMutationHookResult = ReturnType<
  typeof useEditPatientMessageMutation
>;
export type EditPatientMessageMutationResult =
  Apollo.MutationResult<EditPatientMessageMutation>;
export type EditPatientMessageMutationOptions = Apollo.BaseMutationOptions<
  EditPatientMessageMutation,
  EditPatientMessageMutationVariables
>;
export const DeletePatientMessageDocument = gql`
  mutation deletePatientMessage($messageId: String!) {
    deletePatientMessage(input: { messageId: $messageId }) {
      channelId
      messageId
      messageType
      hospitalId
      isPatient
      content
      items {
        itemId
        itemName
        src
        isDeleted
      }
      postedMember
      postedMemberInfo {
        memberName
        status
        staffType
        managerKbn
      }
      createdAt
      updatedAt
      isDeleted
    }
  }
`;
export type DeletePatientMessageMutationFn = Apollo.MutationFunction<
  DeletePatientMessageMutation,
  DeletePatientMessageMutationVariables
>;

/**
 * __useDeletePatientMessageMutation__
 *
 * To run a mutation, you first call `useDeletePatientMessageMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeletePatientMessageMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deletePatientMessageMutation, { data, loading, error }] = useDeletePatientMessageMutation({
 *   variables: {
 *      messageId: // value for 'messageId'
 *   },
 * });
 */
export function useDeletePatientMessageMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeletePatientMessageMutation,
    DeletePatientMessageMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeletePatientMessageMutation,
    DeletePatientMessageMutationVariables
  >(DeletePatientMessageDocument, options);
}
export type DeletePatientMessageMutationHookResult = ReturnType<
  typeof useDeletePatientMessageMutation
>;
export type DeletePatientMessageMutationResult =
  Apollo.MutationResult<DeletePatientMessageMutation>;
export type DeletePatientMessageMutationOptions = Apollo.BaseMutationOptions<
  DeletePatientMessageMutation,
  DeletePatientMessageMutationVariables
>;
export const PostPatientAttachFilesDocument = gql`
  mutation postPatientAttachFiles($input: attachFilesForm!) {
    postPatientAttachFiles(input: $input) {
      channelId
      messageId
      messageType
      hospitalId
      isPatient
      content
      items {
        itemId
        itemName
        src
        isDeleted
      }
      postedMember
      postedMemberInfo {
        memberName
        status
        staffType
        managerKbn
      }
      createdAt
      updatedAt
      isDeleted
    }
  }
`;
export type PostPatientAttachFilesMutationFn = Apollo.MutationFunction<
  PostPatientAttachFilesMutation,
  PostPatientAttachFilesMutationVariables
>;

/**
 * __usePostPatientAttachFilesMutation__
 *
 * To run a mutation, you first call `usePostPatientAttachFilesMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostPatientAttachFilesMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postPatientAttachFilesMutation, { data, loading, error }] = usePostPatientAttachFilesMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostPatientAttachFilesMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostPatientAttachFilesMutation,
    PostPatientAttachFilesMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostPatientAttachFilesMutation,
    PostPatientAttachFilesMutationVariables
  >(PostPatientAttachFilesDocument, options);
}
export type PostPatientAttachFilesMutationHookResult = ReturnType<
  typeof usePostPatientAttachFilesMutation
>;
export type PostPatientAttachFilesMutationResult =
  Apollo.MutationResult<PostPatientAttachFilesMutation>;
export type PostPatientAttachFilesMutationOptions = Apollo.BaseMutationOptions<
  PostPatientAttachFilesMutation,
  PostPatientAttachFilesMutationVariables
>;
export const DeletePatientMessageFileDocument = gql`
  mutation deletePatientMessageFile($itemId: String!) {
    deletePatientMessageFile(input: { itemId: $itemId }) {
      channelId
      messageId
      messageType
      hospitalId
      isPatient
      content
      items {
        itemId
        itemName
        src
        isDeleted
      }
      postedMember
      postedMemberInfo {
        memberName
        status
        staffType
        managerKbn
      }
      createdAt
      updatedAt
      isDeleted
    }
  }
`;
export type DeletePatientMessageFileMutationFn = Apollo.MutationFunction<
  DeletePatientMessageFileMutation,
  DeletePatientMessageFileMutationVariables
>;

/**
 * __useDeletePatientMessageFileMutation__
 *
 * To run a mutation, you first call `useDeletePatientMessageFileMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeletePatientMessageFileMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deletePatientMessageFileMutation, { data, loading, error }] = useDeletePatientMessageFileMutation({
 *   variables: {
 *      itemId: // value for 'itemId'
 *   },
 * });
 */
export function useDeletePatientMessageFileMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeletePatientMessageFileMutation,
    DeletePatientMessageFileMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeletePatientMessageFileMutation,
    DeletePatientMessageFileMutationVariables
  >(DeletePatientMessageFileDocument, options);
}
export type DeletePatientMessageFileMutationHookResult = ReturnType<
  typeof useDeletePatientMessageFileMutation
>;
export type DeletePatientMessageFileMutationResult =
  Apollo.MutationResult<DeletePatientMessageFileMutation>;
export type DeletePatientMessageFileMutationOptions =
  Apollo.BaseMutationOptions<
    DeletePatientMessageFileMutation,
    DeletePatientMessageFileMutationVariables
  >;
export const DeleteStaffMessageFileDocument = gql`
  mutation deleteStaffMessageFile($itemId: String!) {
    deleteStaffMessageFile(input: { itemId: $itemId }) {
      channelId
      messageId
      messageType
      hospitalId
      isPatient
      content
      items {
        itemId
        itemName
        src
        isDeleted
      }
      postedMember
      postedMemberInfo {
        memberName
        status
        staffType
        managerKbn
      }
      createdAt
      updatedAt
      isDeleted
    }
  }
`;
export type DeleteStaffMessageFileMutationFn = Apollo.MutationFunction<
  DeleteStaffMessageFileMutation,
  DeleteStaffMessageFileMutationVariables
>;

/**
 * __useDeleteStaffMessageFileMutation__
 *
 * To run a mutation, you first call `useDeleteStaffMessageFileMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteStaffMessageFileMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteStaffMessageFileMutation, { data, loading, error }] = useDeleteStaffMessageFileMutation({
 *   variables: {
 *      itemId: // value for 'itemId'
 *   },
 * });
 */
export function useDeleteStaffMessageFileMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteStaffMessageFileMutation,
    DeleteStaffMessageFileMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteStaffMessageFileMutation,
    DeleteStaffMessageFileMutationVariables
  >(DeleteStaffMessageFileDocument, options);
}
export type DeleteStaffMessageFileMutationHookResult = ReturnType<
  typeof useDeleteStaffMessageFileMutation
>;
export type DeleteStaffMessageFileMutationResult =
  Apollo.MutationResult<DeleteStaffMessageFileMutation>;
export type DeleteStaffMessageFileMutationOptions = Apollo.BaseMutationOptions<
  DeleteStaffMessageFileMutation,
  DeleteStaffMessageFileMutationVariables
>;
export const SearchMessagesInStaffChannelDocument = gql`
  query searchMessagesInStaffChannel($input: searchInChannel!) {
    searchMessagesInStaffChannel(input: $input) {
      messages {
        messageId
        messageType
        hospitalId
        isPatient
        content
        items {
          itemId
          itemName
          src
          isDeleted
        }
        postedMember
        postedMemberInfo {
          memberName
          status
          staffType
          managerKbn
        }
        channelId
        createdAt
        updatedAt
        isDeleted
      }
      pagination {
        currentPage
        lastPage
        num
        hasPreviousMore
        hasNextMore
        startMessageCreatedTime
        endMessageCreatedTime
      }
    }
  }
`;

/**
 * __useSearchMessagesInStaffChannelQuery__
 *
 * To run a query within a React component, call `useSearchMessagesInStaffChannelQuery` and pass it any options that fit your needs.
 * When your component renders, `useSearchMessagesInStaffChannelQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useSearchMessagesInStaffChannelQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSearchMessagesInStaffChannelQuery(
  baseOptions: Apollo.QueryHookOptions<
    SearchMessagesInStaffChannelQuery,
    SearchMessagesInStaffChannelQueryVariables
  > &
    (
      | {
          variables: SearchMessagesInStaffChannelQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    SearchMessagesInStaffChannelQuery,
    SearchMessagesInStaffChannelQueryVariables
  >(SearchMessagesInStaffChannelDocument, options);
}
export function useSearchMessagesInStaffChannelLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    SearchMessagesInStaffChannelQuery,
    SearchMessagesInStaffChannelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    SearchMessagesInStaffChannelQuery,
    SearchMessagesInStaffChannelQueryVariables
  >(SearchMessagesInStaffChannelDocument, options);
}
export function useSearchMessagesInStaffChannelSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    SearchMessagesInStaffChannelQuery,
    SearchMessagesInStaffChannelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    SearchMessagesInStaffChannelQuery,
    SearchMessagesInStaffChannelQueryVariables
  >(SearchMessagesInStaffChannelDocument, options);
}
export type SearchMessagesInStaffChannelQueryHookResult = ReturnType<
  typeof useSearchMessagesInStaffChannelQuery
>;
export type SearchMessagesInStaffChannelLazyQueryHookResult = ReturnType<
  typeof useSearchMessagesInStaffChannelLazyQuery
>;
export type SearchMessagesInStaffChannelSuspenseQueryHookResult = ReturnType<
  typeof useSearchMessagesInStaffChannelSuspenseQuery
>;
export type SearchMessagesInStaffChannelQueryResult = Apollo.QueryResult<
  SearchMessagesInStaffChannelQuery,
  SearchMessagesInStaffChannelQueryVariables
>;
export const SearchMessagesInPatientChannelDocument = gql`
  query searchMessagesInPatientChannel($input: searchInChannel!) {
    searchMessagesInPatientChannel(input: $input) {
      messages {
        messageId
        messageType
        hospitalId
        isPatient
        content
        items {
          itemId
          itemName
          src
          isDeleted
        }
        postedMember
        postedMemberInfo {
          memberName
          status
          staffType
          managerKbn
        }
        channelId
        createdAt
        updatedAt
        isDeleted
      }
      pagination {
        currentPage
        lastPage
        num
        hasPreviousMore
        hasNextMore
        startMessageCreatedTime
        endMessageCreatedTime
      }
    }
  }
`;

/**
 * __useSearchMessagesInPatientChannelQuery__
 *
 * To run a query within a React component, call `useSearchMessagesInPatientChannelQuery` and pass it any options that fit your needs.
 * When your component renders, `useSearchMessagesInPatientChannelQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useSearchMessagesInPatientChannelQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSearchMessagesInPatientChannelQuery(
  baseOptions: Apollo.QueryHookOptions<
    SearchMessagesInPatientChannelQuery,
    SearchMessagesInPatientChannelQueryVariables
  > &
    (
      | {
          variables: SearchMessagesInPatientChannelQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    SearchMessagesInPatientChannelQuery,
    SearchMessagesInPatientChannelQueryVariables
  >(SearchMessagesInPatientChannelDocument, options);
}
export function useSearchMessagesInPatientChannelLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    SearchMessagesInPatientChannelQuery,
    SearchMessagesInPatientChannelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    SearchMessagesInPatientChannelQuery,
    SearchMessagesInPatientChannelQueryVariables
  >(SearchMessagesInPatientChannelDocument, options);
}
export function useSearchMessagesInPatientChannelSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    SearchMessagesInPatientChannelQuery,
    SearchMessagesInPatientChannelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    SearchMessagesInPatientChannelQuery,
    SearchMessagesInPatientChannelQueryVariables
  >(SearchMessagesInPatientChannelDocument, options);
}
export type SearchMessagesInPatientChannelQueryHookResult = ReturnType<
  typeof useSearchMessagesInPatientChannelQuery
>;
export type SearchMessagesInPatientChannelLazyQueryHookResult = ReturnType<
  typeof useSearchMessagesInPatientChannelLazyQuery
>;
export type SearchMessagesInPatientChannelSuspenseQueryHookResult = ReturnType<
  typeof useSearchMessagesInPatientChannelSuspenseQuery
>;
export type SearchMessagesInPatientChannelQueryResult = Apollo.QueryResult<
  SearchMessagesInPatientChannelQuery,
  SearchMessagesInPatientChannelQueryVariables
>;
export const GetMessagesUploadUrlDocument = gql`
  query getMessagesUploadUrl($fileNames: [String]!) {
    getMessagesUploadUrl(fileNames: $fileNames) {
      fileName
      s3key
      url
    }
  }
`;

/**
 * __useGetMessagesUploadUrlQuery__
 *
 * To run a query within a React component, call `useGetMessagesUploadUrlQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMessagesUploadUrlQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMessagesUploadUrlQuery({
 *   variables: {
 *      fileNames: // value for 'fileNames'
 *   },
 * });
 */
export function useGetMessagesUploadUrlQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetMessagesUploadUrlQuery,
    GetMessagesUploadUrlQueryVariables
  > &
    (
      | { variables: GetMessagesUploadUrlQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetMessagesUploadUrlQuery,
    GetMessagesUploadUrlQueryVariables
  >(GetMessagesUploadUrlDocument, options);
}
export function useGetMessagesUploadUrlLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetMessagesUploadUrlQuery,
    GetMessagesUploadUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetMessagesUploadUrlQuery,
    GetMessagesUploadUrlQueryVariables
  >(GetMessagesUploadUrlDocument, options);
}
export function useGetMessagesUploadUrlSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetMessagesUploadUrlQuery,
    GetMessagesUploadUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetMessagesUploadUrlQuery,
    GetMessagesUploadUrlQueryVariables
  >(GetMessagesUploadUrlDocument, options);
}
export type GetMessagesUploadUrlQueryHookResult = ReturnType<
  typeof useGetMessagesUploadUrlQuery
>;
export type GetMessagesUploadUrlLazyQueryHookResult = ReturnType<
  typeof useGetMessagesUploadUrlLazyQuery
>;
export type GetMessagesUploadUrlSuspenseQueryHookResult = ReturnType<
  typeof useGetMessagesUploadUrlSuspenseQuery
>;
export type GetMessagesUploadUrlQueryResult = Apollo.QueryResult<
  GetMessagesUploadUrlQuery,
  GetMessagesUploadUrlQueryVariables
>;
export const GetMessagesDownloadUrlDocument = gql`
  query getMessagesDownloadUrl($input: searchInMessagesDownloadUrl!) {
    getMessagesDownloadUrl(input: $input) {
      fileName
      s3key
      url
    }
  }
`;

/**
 * __useGetMessagesDownloadUrlQuery__
 *
 * To run a query within a React component, call `useGetMessagesDownloadUrlQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMessagesDownloadUrlQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMessagesDownloadUrlQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetMessagesDownloadUrlQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetMessagesDownloadUrlQuery,
    GetMessagesDownloadUrlQueryVariables
  > &
    (
      | { variables: GetMessagesDownloadUrlQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetMessagesDownloadUrlQuery,
    GetMessagesDownloadUrlQueryVariables
  >(GetMessagesDownloadUrlDocument, options);
}
export function useGetMessagesDownloadUrlLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetMessagesDownloadUrlQuery,
    GetMessagesDownloadUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetMessagesDownloadUrlQuery,
    GetMessagesDownloadUrlQueryVariables
  >(GetMessagesDownloadUrlDocument, options);
}
export function useGetMessagesDownloadUrlSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetMessagesDownloadUrlQuery,
    GetMessagesDownloadUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetMessagesDownloadUrlQuery,
    GetMessagesDownloadUrlQueryVariables
  >(GetMessagesDownloadUrlDocument, options);
}
export type GetMessagesDownloadUrlQueryHookResult = ReturnType<
  typeof useGetMessagesDownloadUrlQuery
>;
export type GetMessagesDownloadUrlLazyQueryHookResult = ReturnType<
  typeof useGetMessagesDownloadUrlLazyQuery
>;
export type GetMessagesDownloadUrlSuspenseQueryHookResult = ReturnType<
  typeof useGetMessagesDownloadUrlSuspenseQuery
>;
export type GetMessagesDownloadUrlQueryResult = Apollo.QueryResult<
  GetMessagesDownloadUrlQuery,
  GetMessagesDownloadUrlQueryVariables
>;
export const NotifyDocument = gql`
  subscription notify {
    notify {
      eventType
      notifyType
      message {
        channelId
        messageId
        messageType
        hospitalId
        isPatient
        content
        items {
          itemId
          itemName
          src
          isDeleted
        }
        postedMember
        postedMemberInfo {
          memberName
          status
          staffType
          managerKbn
        }
        channelId
        createdAt
        updatedAt
        isDeleted
      }
      channel {
        channelId
        channelName
        patientId
        channelType
        hospitalId
        isUnread
        sendable
        notifyTo
        latestPostedMessageAt
      }
    }
  }
`;

/**
 * __useNotifySubscription__
 *
 * To run a query within a React component, call `useNotifySubscription` and pass it any options that fit your needs.
 * When your component renders, `useNotifySubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useNotifySubscription({
 *   variables: {
 *   },
 * });
 */
export function useNotifySubscription(
  baseOptions?: Apollo.SubscriptionHookOptions<
    NotifySubscription,
    NotifySubscriptionVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSubscription<
    NotifySubscription,
    NotifySubscriptionVariables
  >(NotifyDocument, options);
}
export type NotifySubscriptionHookResult = ReturnType<
  typeof useNotifySubscription
>;
export type NotifySubscriptionResult =
  Apollo.SubscriptionResult<NotifySubscription>;
export const AddAuditLogForPatientChannelsDocument = gql`
  query addAuditLogForPatientChannels {
    addAuditLogForPatientChannels
  }
`;

/**
 * __useAddAuditLogForPatientChannelsQuery__
 *
 * To run a query within a React component, call `useAddAuditLogForPatientChannelsQuery` and pass it any options that fit your needs.
 * When your component renders, `useAddAuditLogForPatientChannelsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAddAuditLogForPatientChannelsQuery({
 *   variables: {
 *   },
 * });
 */
export function useAddAuditLogForPatientChannelsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    AddAuditLogForPatientChannelsQuery,
    AddAuditLogForPatientChannelsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    AddAuditLogForPatientChannelsQuery,
    AddAuditLogForPatientChannelsQueryVariables
  >(AddAuditLogForPatientChannelsDocument, options);
}
export function useAddAuditLogForPatientChannelsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    AddAuditLogForPatientChannelsQuery,
    AddAuditLogForPatientChannelsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    AddAuditLogForPatientChannelsQuery,
    AddAuditLogForPatientChannelsQueryVariables
  >(AddAuditLogForPatientChannelsDocument, options);
}
export function useAddAuditLogForPatientChannelsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    AddAuditLogForPatientChannelsQuery,
    AddAuditLogForPatientChannelsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    AddAuditLogForPatientChannelsQuery,
    AddAuditLogForPatientChannelsQueryVariables
  >(AddAuditLogForPatientChannelsDocument, options);
}
export type AddAuditLogForPatientChannelsQueryHookResult = ReturnType<
  typeof useAddAuditLogForPatientChannelsQuery
>;
export type AddAuditLogForPatientChannelsLazyQueryHookResult = ReturnType<
  typeof useAddAuditLogForPatientChannelsLazyQuery
>;
export type AddAuditLogForPatientChannelsSuspenseQueryHookResult = ReturnType<
  typeof useAddAuditLogForPatientChannelsSuspenseQuery
>;
export type AddAuditLogForPatientChannelsQueryResult = Apollo.QueryResult<
  AddAuditLogForPatientChannelsQuery,
  AddAuditLogForPatientChannelsQueryVariables
>;
export const AddAuditLogForPatientMessagesDocument = gql`
  query AddAuditLogForPatientMessages($input: getMessagesReq!) {
    AddAuditLogForPatientMessages(input: $input)
  }
`;

/**
 * __useAddAuditLogForPatientMessagesQuery__
 *
 * To run a query within a React component, call `useAddAuditLogForPatientMessagesQuery` and pass it any options that fit your needs.
 * When your component renders, `useAddAuditLogForPatientMessagesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAddAuditLogForPatientMessagesQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAddAuditLogForPatientMessagesQuery(
  baseOptions: Apollo.QueryHookOptions<
    AddAuditLogForPatientMessagesQuery,
    AddAuditLogForPatientMessagesQueryVariables
  > &
    (
      | {
          variables: AddAuditLogForPatientMessagesQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    AddAuditLogForPatientMessagesQuery,
    AddAuditLogForPatientMessagesQueryVariables
  >(AddAuditLogForPatientMessagesDocument, options);
}
export function useAddAuditLogForPatientMessagesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    AddAuditLogForPatientMessagesQuery,
    AddAuditLogForPatientMessagesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    AddAuditLogForPatientMessagesQuery,
    AddAuditLogForPatientMessagesQueryVariables
  >(AddAuditLogForPatientMessagesDocument, options);
}
export function useAddAuditLogForPatientMessagesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    AddAuditLogForPatientMessagesQuery,
    AddAuditLogForPatientMessagesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    AddAuditLogForPatientMessagesQuery,
    AddAuditLogForPatientMessagesQueryVariables
  >(AddAuditLogForPatientMessagesDocument, options);
}
export type AddAuditLogForPatientMessagesQueryHookResult = ReturnType<
  typeof useAddAuditLogForPatientMessagesQuery
>;
export type AddAuditLogForPatientMessagesLazyQueryHookResult = ReturnType<
  typeof useAddAuditLogForPatientMessagesLazyQuery
>;
export type AddAuditLogForPatientMessagesSuspenseQueryHookResult = ReturnType<
  typeof useAddAuditLogForPatientMessagesSuspenseQuery
>;
export type AddAuditLogForPatientMessagesQueryResult = Apollo.QueryResult<
  AddAuditLogForPatientMessagesQuery,
  AddAuditLogForPatientMessagesQueryVariables
>;
