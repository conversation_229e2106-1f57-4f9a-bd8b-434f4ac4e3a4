import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiEpsGetPrescriptionInfListQueryVariables = Types.Exact<{
  patientNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  startSinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  endSinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  startDispensingDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  endDispensingDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiEpsGetPrescriptionInfListQuery = {
  __typename?: "query_root";
  getApiEpsGetPrescriptionInfList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpSGetPrescriptionInfListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpSGetPrescriptionInfListResponse";
      prescriptionInfListModel?: {
        __typename?: "DomainModelsEpSPrescriptionInfListModel";
        lastDispensingListEndDate?: string;
        epsPrescriptionModels?: Array<{
          __typename?: "DomainModelsEpsEpsPrescriptionInfModel";
          accessCode?: string;
          createDate?: string;
          bango?: string;
          createId?: number;
          deleteReasonDisplay?: string;
          deletedDate?: string;
          deletedReason?: number;
          dispensingCreateDate?: string;
          dispensingDate?: number;
          dispensingDateDisplay?: string;
          edaNo?: string;
          epsUpdateDateTime?: string;
          hokenDisplay?: string;
          hokensyaNo?: string;
          hpId?: number;
          kohiFutansyaNo?: string;
          kigo?: string;
          issueTypeDisplay?: string;
          kaId?: number;
          issueType?: number;
          kohiJyukyusyaNo?: string;
          messageFlag?: string;
          pharmacyName?: string;
          prescriptionCreateDate?: string;
          prescriptionDeletedDate?: string;
          ptId?: string;
          prescriptionId?: string;
          kaSName?: string;
          tantoName?: string;
          ptName?: string;
          refileCount?: number;
          ptNum?: string;
          ptInf?: {
            __typename?: "DomainModelsPatientInforPatientInforModel";
            kanaName?: string;
            sex?: number;
            ptNum?: string;
            name?: string;
          };
          epsDispensing?: {
            __typename?: "DomainModelsEpsDispensingEpsDispensingModel";
            bango?: string;
            cancelReason?: string;
            createDate?: string;
            createId?: number;
            createMachine?: string;
            dispensingDate?: number;
            dispensingDocument?: string;
            dispensingResultId?: string;
            dispensingTimes?: number;
            edaNo?: string;
            epsUpdateDateTime?: string;
            hokensyaNo?: string;
            hpId?: number;
            id?: string;
            isDeleted?: number;
            kigo?: string;
            kohiFutansyaNo?: string;
            kohiJyukyusyaNo?: string;
            messageFlg?: number;
            prescriptionId?: string;
            ptId?: string;
            receptionPharmacyName?: string;
            resultType?: number;
            updateDate?: string;
            updateId?: number;
            updateMachine?: string;
          };
          epsPrescription?: {
            __typename?: "DomainModelsEpsPrescriptionEpsPrescriptionModel";
            sinDate?: number;
            refileCount?: number;
            issueType?: number;
            status?: number;
            deletedReason?: number;
            resultType?: number;
            accessCode?: string;
            bango?: string;
            createDate?: string;
            createId?: number;
            deleteReasonDisplay?: string;
            deletedDate?: string;
            dispensingDate?: number;
            dispensingDateDisplay?: string;
            edaNo?: string;
            epsUpdateDateTime?: string;
            hokenDisplay?: string;
            hokensyaNo?: string;
            hpId?: number;
            id?: number;
            kaId?: number;
            issueTypeDisplay?: string;
            kaSName?: string;
            kigo?: string;
            kohiFutansyaNo?: string;
            kohiJyukyusyaNo?: string;
            messageFlag?: string;
            pharmacyName?: string;
            prescriptionDocument?: string;
            prescriptionId?: string;
            ptId?: string;
            ptName?: string;
            ptNum?: string;
            ptNumDisplay?: string;
            raiinNo?: string;
            refill?: string;
            resultTypeDisplay?: string;
            seqNo?: string;
            sinDateDisplay?: string;
            statusDisplay?: string;
            tantoId?: number;
            tantoName?: string;
            updateDate?: string;
            updateId?: number;
            epsDispensing?: {
              __typename?: "DomainModelsEpsDispensingEpsDispensingModel";
              cancelReason?: string;
              bango?: string;
              createDate?: string;
              createId?: number;
              createMachine?: string;
              dispensingDate?: number;
              dispensingDocument?: string;
              dispensingResultId?: string;
              dispensingTimes?: number;
              edaNo?: string;
              epsUpdateDateTime?: string;
              hokensyaNo?: string;
              hpId?: number;
              id?: string;
              isDeleted?: number;
              kohiFutansyaNo?: string;
              kigo?: string;
              kohiJyukyusyaNo?: string;
              messageFlg?: number;
              prescriptionId?: string;
              ptId?: string;
              receptionPharmacyName?: string;
              resultType?: number;
              updateDate?: string;
              updateId?: number;
              updateMachine?: string;
            };
            kaInf?: {
              __typename?: "DomainModelsKaKaMstModel";
              id?: string;
              kaId?: number;
              kaName?: string;
              kaSname?: string;
              receKaCd?: string;
              sortNo?: number;
              yousikiKaCd?: string;
            };
          };
        }>;
      };
    };
  };
};

export type GetApiEpsGetPreCheckOldPrescriptionQueryVariables = Types.Exact<{
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiEpsGetPreCheckOldPrescriptionQuery = {
  __typename?: "query_root";
  getApiEpsGetPreCheckOldPrescription?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpSGetPreCheckOldPrescriptionResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpSGetPreCheckOldPrescriptionResponse";
      isPreCheckOldPrescription?: boolean;
      epsPrescriptionModels?: Array<{
        __typename?: "DomainModelsEpsPrescriptionEpsPrescriptionModel";
        updateId?: number;
        bango?: string;
        createDate?: string;
        deleteReasonDisplay?: string;
        accessCode?: string;
        createId?: number;
        deletedDate?: string;
        deletedReason?: number;
        dispensingDate?: number;
        dispensingDateDisplay?: string;
        edaNo?: string;
        raiinNo?: string;
        refileCount?: number;
        refill?: string;
        resultType?: number;
        resultTypeDisplay?: string;
        seqNo?: string;
        sinDate?: number;
        sinDateDisplay?: string;
        status?: number;
        statusDisplay?: string;
        epsDispensing?: {
          __typename?: "DomainModelsEpsDispensingEpsDispensingModel";
          bango?: string;
          cancelReason?: string;
          createDate?: string;
        };
        tantoInf?: {
          __typename?: "DomainModelsUserUserMstModel";
          drName?: string;
          email?: string;
          emailUpdateDate?: string;
          endDate?: number;
        };
      }>;
    };
  };
};

export const GetApiEpsGetPrescriptionInfListDocument = gql`
  query getApiEpsGetPrescriptionInfList(
    $patientNum: BigInt
    $startSinDate: Int
    $endSinDate: Int
    $startDispensingDate: Int
    $endDispensingDate: Int
  ) {
    getApiEpsGetPrescriptionInfList(
      patientNum: $patientNum
      startSinDate: $startSinDate
      startDispensingDate: $startDispensingDate
      endSinDate: $endSinDate
      endDispensingDate: $endDispensingDate
    ) {
      message
      status
      data {
        prescriptionInfListModel {
          lastDispensingListEndDate
          epsPrescriptionModels {
            accessCode
            createDate
            bango
            createId
            deleteReasonDisplay
            deletedDate
            deletedReason
            dispensingCreateDate
            dispensingDate
            dispensingDateDisplay
            edaNo
            epsUpdateDateTime
            hokenDisplay
            hokensyaNo
            hpId
            kohiFutansyaNo
            kigo
            issueTypeDisplay
            kaId
            issueType
            kohiJyukyusyaNo
            messageFlag
            pharmacyName
            prescriptionCreateDate
            prescriptionDeletedDate
            ptId
            prescriptionId
            kaSName
            tantoName
            ptName
            refileCount
            ptNum
            ptInf {
              kanaName
              sex
              ptNum
              name
            }
            epsDispensing {
              bango
              cancelReason
              createDate
              createId
              createMachine
              dispensingDate
              dispensingDocument
              dispensingResultId
              dispensingTimes
              edaNo
              epsUpdateDateTime
              hokensyaNo
              hpId
              id
              isDeleted
              kigo
              kohiFutansyaNo
              kohiJyukyusyaNo
              messageFlg
              prescriptionId
              ptId
              receptionPharmacyName
              resultType
              updateDate
              updateId
              updateMachine
            }
            epsPrescription {
              sinDate
              refileCount
              issueType
              status
              deletedReason
              resultType
              accessCode
              bango
              createDate
              createId
              deleteReasonDisplay
              deletedDate
              dispensingDate
              dispensingDateDisplay
              edaNo
              epsUpdateDateTime
              hokenDisplay
              hokensyaNo
              hpId
              id
              kaId
              issueTypeDisplay
              kaSName
              kigo
              kohiFutansyaNo
              kohiJyukyusyaNo
              messageFlag
              pharmacyName
              prescriptionDocument
              prescriptionId
              ptId
              ptName
              ptNum
              ptNumDisplay
              raiinNo
              refill
              resultTypeDisplay
              seqNo
              sinDateDisplay
              statusDisplay
              tantoId
              tantoName
              resultType
              messageFlag
              updateDate
              updateId
              epsDispensing {
                cancelReason
                bango
                createDate
                createId
                createMachine
                dispensingDate
                dispensingDocument
                dispensingResultId
                dispensingTimes
                edaNo
                epsUpdateDateTime
                hokensyaNo
                hpId
                id
                isDeleted
                kohiFutansyaNo
                kigo
                kohiJyukyusyaNo
                messageFlg
                prescriptionId
                ptId
                receptionPharmacyName
                resultType
                updateDate
                updateId
                updateMachine
              }
              kaInf {
                id
                kaId
                kaName
                kaSname
                receKaCd
                sortNo
                yousikiKaCd
              }
            }
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiEpsGetPrescriptionInfListQuery__
 *
 * To run a query within a React component, call `useGetApiEpsGetPrescriptionInfListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiEpsGetPrescriptionInfListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiEpsGetPrescriptionInfListQuery({
 *   variables: {
 *      patientNum: // value for 'patientNum'
 *      startSinDate: // value for 'startSinDate'
 *      endSinDate: // value for 'endSinDate'
 *      startDispensingDate: // value for 'startDispensingDate'
 *      endDispensingDate: // value for 'endDispensingDate'
 *   },
 * });
 */
export function useGetApiEpsGetPrescriptionInfListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiEpsGetPrescriptionInfListQuery,
    GetApiEpsGetPrescriptionInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiEpsGetPrescriptionInfListQuery,
    GetApiEpsGetPrescriptionInfListQueryVariables
  >(GetApiEpsGetPrescriptionInfListDocument, options);
}
export function useGetApiEpsGetPrescriptionInfListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiEpsGetPrescriptionInfListQuery,
    GetApiEpsGetPrescriptionInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiEpsGetPrescriptionInfListQuery,
    GetApiEpsGetPrescriptionInfListQueryVariables
  >(GetApiEpsGetPrescriptionInfListDocument, options);
}
export function useGetApiEpsGetPrescriptionInfListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiEpsGetPrescriptionInfListQuery,
    GetApiEpsGetPrescriptionInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiEpsGetPrescriptionInfListQuery,
    GetApiEpsGetPrescriptionInfListQueryVariables
  >(GetApiEpsGetPrescriptionInfListDocument, options);
}
export type GetApiEpsGetPrescriptionInfListQueryHookResult = ReturnType<
  typeof useGetApiEpsGetPrescriptionInfListQuery
>;
export type GetApiEpsGetPrescriptionInfListLazyQueryHookResult = ReturnType<
  typeof useGetApiEpsGetPrescriptionInfListLazyQuery
>;
export type GetApiEpsGetPrescriptionInfListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiEpsGetPrescriptionInfListSuspenseQuery
>;
export type GetApiEpsGetPrescriptionInfListQueryResult = Apollo.QueryResult<
  GetApiEpsGetPrescriptionInfListQuery,
  GetApiEpsGetPrescriptionInfListQueryVariables
>;
export const GetApiEpsGetPreCheckOldPrescriptionDocument = gql`
  query getApiEpsGetPreCheckOldPrescription(
    $sinDate: Int
    $raiinNo: BigInt
    $ptId: BigInt
  ) {
    getApiEpsGetPreCheckOldPrescription(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        epsPrescriptionModels {
          updateId
          bango
          createDate
          deleteReasonDisplay
          accessCode
          createId
          deletedDate
          deletedReason
          dispensingDate
          dispensingDateDisplay
          edaNo
          epsDispensing {
            bango
            cancelReason
            createDate
          }
          raiinNo
          refileCount
          refill
          resultType
          resultTypeDisplay
          seqNo
          sinDate
          sinDateDisplay
          status
          statusDisplay
          tantoInf {
            drName
            email
            emailUpdateDate
            endDate
          }
        }
        isPreCheckOldPrescription
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiEpsGetPreCheckOldPrescriptionQuery__
 *
 * To run a query within a React component, call `useGetApiEpsGetPreCheckOldPrescriptionQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiEpsGetPreCheckOldPrescriptionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiEpsGetPreCheckOldPrescriptionQuery({
 *   variables: {
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiEpsGetPreCheckOldPrescriptionQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiEpsGetPreCheckOldPrescriptionQuery,
    GetApiEpsGetPreCheckOldPrescriptionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiEpsGetPreCheckOldPrescriptionQuery,
    GetApiEpsGetPreCheckOldPrescriptionQueryVariables
  >(GetApiEpsGetPreCheckOldPrescriptionDocument, options);
}
export function useGetApiEpsGetPreCheckOldPrescriptionLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiEpsGetPreCheckOldPrescriptionQuery,
    GetApiEpsGetPreCheckOldPrescriptionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiEpsGetPreCheckOldPrescriptionQuery,
    GetApiEpsGetPreCheckOldPrescriptionQueryVariables
  >(GetApiEpsGetPreCheckOldPrescriptionDocument, options);
}
export function useGetApiEpsGetPreCheckOldPrescriptionSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiEpsGetPreCheckOldPrescriptionQuery,
    GetApiEpsGetPreCheckOldPrescriptionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiEpsGetPreCheckOldPrescriptionQuery,
    GetApiEpsGetPreCheckOldPrescriptionQueryVariables
  >(GetApiEpsGetPreCheckOldPrescriptionDocument, options);
}
export type GetApiEpsGetPreCheckOldPrescriptionQueryHookResult = ReturnType<
  typeof useGetApiEpsGetPreCheckOldPrescriptionQuery
>;
export type GetApiEpsGetPreCheckOldPrescriptionLazyQueryHookResult = ReturnType<
  typeof useGetApiEpsGetPreCheckOldPrescriptionLazyQuery
>;
export type GetApiEpsGetPreCheckOldPrescriptionSuspenseQueryHookResult =
  ReturnType<typeof useGetApiEpsGetPreCheckOldPrescriptionSuspenseQuery>;
export type GetApiEpsGetPreCheckOldPrescriptionQueryResult = Apollo.QueryResult<
  GetApiEpsGetPreCheckOldPrescriptionQuery,
  GetApiEpsGetPreCheckOldPrescriptionQueryVariables
>;
