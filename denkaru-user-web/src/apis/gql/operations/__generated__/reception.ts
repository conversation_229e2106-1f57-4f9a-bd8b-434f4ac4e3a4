import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiRaiinStatusMstGetListQueryVariables = Types.Exact<{
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiRaiinStatusMstGetListQuery = {
  __typename?: "query_root";
  getApiRaiinStatusMstGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesRaiinStatusMstGetRaiinStatusMstCountListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesRaiinStatusMstGetRaiinStatusMstCountListResponse";
      data?: Array<{
        __typename?: "DomainModelsRaiinStatusMstRaiinStatusCountListDto";
        statusKbn?: number;
        statusName?: string;
        countStatus?: number;
      }>;
    };
  };
};

export type GetApiRaiinFilterGetListMstQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiRaiinFilterGetListMstQuery = {
  __typename?: "query_root";
  getApiRaiinFilterGetListMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesRaiinFilterGetRaiinFilterMstListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesRaiinFilterGetRaiinFilterMstListResponse";
      filterMsts?: Array<{
        __typename?: "DomainModelsRaiinFilterMstRaiinFilterMstModel";
        doctorIds?: Array<number>;
        filterId?: number;
        filterName?: string;
        kaIds?: Array<number>;
        selectKbn?: number;
        shortcut?: string;
        sortNo?: number;
        statusIds?: Array<number>;
        treatmentIds?: Array<number>;
        labelIds?: Array<number>;
      }>;
    };
  };
};

export type PutApiVisitingUpdateStaticCellMutationVariables = Types.Exact<{
  cellName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  cellValue?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  grpIds?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars["Int"]["input"]>>
    | Types.InputMaybe<Types.Scalars["Int"]["input"]>
  >;
}>;

export type PutApiVisitingUpdateStaticCellMutation = {
  __typename?: "mutation_root";
  putApiVisitingUpdateStaticCell?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionUpdateReceptionStaticCellResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionUpdateReceptionStaticCellResponse";
      success?: boolean;
    };
  };
};

export type PostApiRaiinFilterSaveListMstMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsRaiinFilterSaveRaiinFilterMstListRequestInput>;
}>;

export type PostApiRaiinFilterSaveListMstMutation = {
  __typename?: "mutation_root";
  postApiRaiinFilterSaveListMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesRaiinFilterSaveRaiinFilterMstListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesRaiinFilterSaveRaiinFilterMstListResponse";
      success?: boolean;
      filterIds?: Array<number>;
    };
  };
};

export type GetVisitingGetListQueryVariables = Types.Exact<{
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetVisitingGetListQuery = {
  __typename?: "query_root";
  getApiVisitingGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionGetReceptionListForViewResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionGetReceptionListForViewResponse";
      receptionInfos?: Array<{
        __typename?: "DomainModelsReceptionReceptionForViewDto";
        uketukeNo?: number;
        uketukeTime?: string;
        sinDate?: number;
        yoyakuTime?: string;
        status?: number;
        ptNum?: string;
        kanaName?: string;
        name?: string;
        sex?: string;
        age?: number;
        birthday?: number;
        kaId?: number;
        tantoId?: number;
        isDeleted?: number;
        kaName?: string;
        ptMemo?: string;
        monshinStatus?: number;
        portalPtId?: number;
        ptId?: string;
        raiinNo?: string;
        referenceNo?: string;
        statusKbnUpdateTime?: string;
        statusMstKbn?: number;
        tantoName?: string;
        karteEditionRaiinNo?: string;
        raiinMemo?: string;
        treatmentDepartmentId?: number;
        yoyakuEndTime?: string;
        hasUnread?: number;
        meetingStatus?: number;
        meetingStatusName?: string;
        reverseName?: string;
        reverseType?: number;
        hokenName?: string;
        odrId?: string;
        onlineConfirmationDate?: number;
        typeAlert?: number;
        errorConfirmOnlineCode?: string;
        errorConfirmOnlineMessage?: string;
        portalCustomerId?: number;
        onlineConfirmationId?: string;
        sortNoStatusMst?: number;
        reverseId?: number;
        reverseDetailId?: number;
        labels?: Array<{
          __typename?: "DomainModelsReceptionLabel";
          colorCd?: string;
          grpId?: number;
          hpId?: number;
          name?: string;
          sortNo?: number;
        }>;
      }>;
    };
  };
};

export type GetTreatmentDepartmentListQueryVariables = Types.Exact<{
  isDeleted?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetTreatmentDepartmentListQuery = {
  __typename?: "query_root";
  getApiTreatmentDepartmentGetTreatmentDepartmentList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesTreatmentDepartmentGetTreatmentDepartmentListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesTreatmentDepartmentGetTreatmentDepartmentListResponse";
      treatmentStatusList?: Array<{
        __typename?: "DomainModelsTreatmentDepartmentTreatmentDepartmentModel";
        treatmentDepartmentId?: number;
        title?: string;
        treatmentDepartmentStatus?: number;
        isDeleted?: number;
      }>;
    };
  };
};

export type GetApiLabelMstGetListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiLabelMstGetListQuery = {
  __typename?: "query_root";
  getApiLabelMstGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesLabelMstGetListLabelMstResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesLabelMstGetListLabelMstResponse";
      labels?: Array<{
        __typename?: "DomainModelsLabelMstLabelMstModel";
        colorCd?: string;
        grpId?: number;
        grpName?: string;
        sortNo?: number;
      }>;
    };
  };
};

export type GetApiReceptionGetQueryVariables = Types.Exact<{
  flag?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiReceptionGetQuery = {
  __typename?: "query_root";
  getApiReceptionGet?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionGetReceptionResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionGetReceptionResponse";
      reception?: {
        __typename?: "DomainModelsReceptionReceptionDto";
        canCombine?: number;
        canEditPrescription?: boolean;
        comment?: string;
        confirmationType?: number;
        departmentSName?: string;
        hokenPid?: number;
        hpId?: number;
        infoConsFlg?: string;
        isYoyaku?: number;
        jikanKbn?: number;
        isLinkCard?: boolean;
        kaId?: number;
        kaikeiId?: number;
        kaikeiTime?: string;
        oyaRaiinNo?: string;
        patternName?: string;
        hasMessage?: boolean;
        paymentMethodCd?: number;
        personNumber?: number;
        prescriptionDeliInfo?: string;
        prescriptionIssueType?: number;
        prescriptionName?: string;
        ptId?: string;
        ptKanaName?: string;
        ptName?: string;
        raiinBinding?: string;
        raiinNo?: string;
        santeiKbn?: number;
        sex?: number;
        sinDate?: number;
        sinEndTime?: string;
        sinryoKbn?: number;
        sinStartTime?: string;
        status?: number;
        syosaisinKbn?: number;
        tantoId?: number;
        treatmentDepartmentId?: number;
        uketukeId?: number;
        uketukeNo?: number;
        uketukeSbt?: number;
        uketukeTime?: string;
        yoyakuEndTime?: string;
        yoyakuId?: number;
        yoyakuTime?: string;
        kaSname?: string;
        tantoName?: string;
        printEpsReference?: number;
        reserveDetailId?: number;
        karteEditionRaiinNo?: string;
        reserveTypeName?: string;
        hokenName?: string;
        prescriptionReceiveMethod?: number;
        labels?: Array<{
          __typename?: "DomainModelsReceptionLabel";
          colorCd?: string;
          grpId?: number;
          hpId?: number;
          name?: string;
          sortNo?: number;
        }>;
        hokenPatternModel?: {
          __typename?: "DomainModelsReceptionSameVisitHokenPatternModel";
          hokenPid?: number;
          kohi1Id?: number;
          kohi2Id?: number;
          kohi3Id?: number;
          kohi4Id?: number;
          hokenId?: number;
        };
        portalCustomerPharmacy?: {
          __typename?: "DomainModelsPortalCustomerPharmacyPortalCustomerPharmacyModel";
          pharmacyName?: string;
          pharmacyStoreName?: string;
          phoneNumber?: string;
          faxNumber?: string;
          postCode?: string;
          address1?: string;
          address2?: string;
        };
      };
    };
  };
};

export type GetApiReceptionGetReceptionCombineQueryVariables = Types.Exact<{
  hokenPid?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isCombined?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceptionGetReceptionCombineQuery = {
  __typename?: "query_root";
  getApiReceptionGetReceptionCombine?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionGetReceptionCombineResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionGetReceptionCombineResponse";
      receptionCombines?: Array<{
        __typename?: "DomainModelsReceptionReceptionCombineModel";
        hpId?: number;
        kaId?: number;
        kaName?: string;
        oyaRaiinNo?: string;
        ptId?: string;
        ptName?: string;
        tantoId?: number;
        tantoName?: string;
        raiinNo?: string;
        treatmentDepartmentId?: number;
        treatmentDepartmentName?: string;
        uketukeNo?: number;
      }>;
    };
  };
};

export type PutApiReceptionCombineSplitBillMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsReceptionCombineSplitBillRequestInput>;
}>;

export type PutApiReceptionCombineSplitBillMutation = {
  __typename?: "mutation_root";
  putApiReceptionCombineSplitBill?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionCombineSplitBillResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionCombineSplitBillResponse";
      isSuccess?: boolean;
    };
  };
};

export type GetApiPatientInforInsuranceListByPtIdQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiPatientInforInsuranceListByPtIdQuery = {
  __typename?: "query_root";
  getApiPatientInforInsuranceListByPtId?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceGetInsuranceListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceGetInsuranceListResponse";
      data?: {
        __typename?: "EmrCloudApiResponsesInsurancePatientInsuranceDto";
        listInsurance?: Array<{
          __typename?: "EmrCloudApiResponsesInsurancePatternDto";
          hokenKbn?: number;
          hokenMemo?: string;
          hokenName?: string;
          hokenPid?: number;
          hokenSbtCd?: number;
          hpId?: number;
          isAddNew?: boolean;
          isDeleted?: number;
          isEmptyKohi1?: boolean;
          isEmptyKohi2?: boolean;
          isEmptyKohi3?: boolean;
          isEmptyKohi4?: boolean;
          hokenInf?: {
            __typename?: "EmrCloudApiResponsesInsuranceHokenInfDto";
            hokenEdaNo?: number;
            hokenId?: number;
            hokenKbn?: number;
            houbetu?: string;
            jibaiHokenName?: string;
            seqNo?: string;
          };
          kohi1?: {
            __typename?: "EmrCloudApiResponsesInsuranceKohiInfDto";
            houbetu?: string;
            hokenMstModel?: {
              __typename?: "EmrCloudApiResponsesInsuranceHokenMstDto";
              hokenNameCd?: string;
            };
          };
          kohi2?: {
            __typename?: "EmrCloudApiResponsesInsuranceKohiInfDto";
            houbetu?: string;
            hokenMstModel?: {
              __typename?: "EmrCloudApiResponsesInsuranceHokenMstDto";
              hokenNameCd?: string;
            };
          };
          kohi3?: {
            __typename?: "EmrCloudApiResponsesInsuranceKohiInfDto";
            houbetu?: string;
            hokenMstModel?: {
              __typename?: "EmrCloudApiResponsesInsuranceHokenMstDto";
              hokenNameCd?: string;
            };
          };
          kohi4?: {
            __typename?: "EmrCloudApiResponsesInsuranceKohiInfDto";
            houbetu?: string;
            hokenMstModel?: {
              __typename?: "EmrCloudApiResponsesInsuranceHokenMstDto";
              hokenNameCd?: string;
            };
          };
        }>;
      };
    };
  };
};

export type GetDefaultValueAddReceptionQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  requestFrom?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isContiFiltered?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isInMonthFiltered?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isLastVisit?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  birthDay?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  uketukeTime?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetDefaultValueAddReceptionQuery = {
  __typename?: "query_root";
  getApiDiseasesGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDiseasesGetPtDiseaseListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesDiseasesGetPtDiseaseListResponse";
      diseaseList?: Array<{
        __typename?: "DomainModelsDiseasesPtDiseaseModel";
        tenkiKbn?: number;
        startDate?: number;
        sinDate?: number;
        tenkiDate?: number;
      }>;
    };
  };
  getApiReceptionGetLastRaiinInfs?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionGetLastRaiinInfsResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionGetLastRaiinInfsResponse";
      data?: Array<{
        __typename?: "DomainModelsReceptionReceptionModel";
        sinDate?: number;
        tantoId?: number;
      }>;
    };
  };
  getApiReceptionGetDefaultSelectedTime?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionGetDefaultSelectedTimeResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionGetDefaultSelectedTimeResponse";
      data?: {
        __typename?: "UseCaseReceptionGetDefaultSelectedTimeDefaultSelectedTimeModel";
        jikanKbnDefault?: number;
        isPatientChildren?: boolean;
      };
    };
  };
  getApiReceptionGetDefaultPrescription?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionGetDefaultPrescriptionResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionGetDefaultPrescriptionResponse";
      prescriptionIssueType?: number;
      prinEpsReference?: number;
    };
  };
};

export type PostApiReceptionInsertMutationVariables = Types.Exact<{
  diseases?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.DomainModelsReceptionDiseaseDtoInput>>
    | Types.InputMaybe<Types.DomainModelsReceptionDiseaseDtoInput>
  >;
  insurances?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.DomainModelsReceptionInsuranceDtoInput>>
    | Types.InputMaybe<Types.DomainModelsReceptionInsuranceDtoInput>
  >;
  kubunInfs?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.DomainModelsReceptionRaiinKbnInfDtoInput>>
    | Types.InputMaybe<Types.DomainModelsReceptionRaiinKbnInfDtoInput>
  >;
  reception?: Types.InputMaybe<Types.DomainModelsReceptionReceptionUpsertItemInput>;
  receptionComment?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type PostApiReceptionInsertMutation = {
  __typename?: "mutation_root";
  postApiReceptionInsert?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionInsertReceptionResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionInsertReceptionResponse";
      raiinNo?: string;
    };
  };
};

export type PostApiReceptionUpdateMutationVariables = Types.Exact<{
  diseases?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.DomainModelsReceptionDiseaseDtoInput>>
    | Types.InputMaybe<Types.DomainModelsReceptionDiseaseDtoInput>
  >;
  insurances?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.DomainModelsReceptionInsuranceDtoInput>>
    | Types.InputMaybe<Types.DomainModelsReceptionInsuranceDtoInput>
  >;
  kubunInfs?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.DomainModelsReceptionRaiinKbnInfDtoInput>>
    | Types.InputMaybe<Types.DomainModelsReceptionRaiinKbnInfDtoInput>
  >;
  reception?: Types.InputMaybe<Types.DomainModelsReceptionReceptionUpsertItemInput>;
  receptionComment?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type PostApiReceptionUpdateMutation = {
  __typename?: "mutation_root";
  postApiReceptionUpdate?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionUpdateReceptionResponse";
    message?: string;
    status?: number;
  };
};

export type PostApiPatientInforSaveHokenCheckMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsPatientInforSaveHokenCheckRequestInput>;
}>;

export type PostApiPatientInforSaveHokenCheckMutation = {
  __typename?: "mutation_root";
  postApiPatientInforSaveHokenCheck?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforSaveHokenCheckResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforSaveHokenCheckResponse";
      status?: number;
    };
  };
};

export type GetApiPdfCreatorReceiptReportBase64QueryVariables = Types.Exact<{
  isCalculateProcess?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  printType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNoList?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars["BigInt"]["input"]>>
    | Types.InputMaybe<Types.Scalars["BigInt"]["input"]>
  >;
}>;

export type GetApiPdfCreatorReceiptReportBase64Query = {
  __typename?: "query_root";
  getApiPdfCreatorReceiptReportBase64?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPdfCreatorReportBase64Response";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPdfCreatorReportBase64Response";
      content?: string;
      fileName?: string;
    };
  };
};

export type GetApiPdfCreatorInDrugBase64QueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiPdfCreatorInDrugBase64Query = {
  __typename?: "query_root";
  getApiPdfCreatorInDrugBase64?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPdfCreatorReportBase64Response";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPdfCreatorReportBase64Response";
      content?: string;
      fileName?: string;
    };
  };
};

export type GetApiPdfCreatorOutDrugBase64QueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiPdfCreatorOutDrugBase64Query = {
  __typename?: "query_root";
  getApiPdfCreatorOutDrugBase64?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPdfCreatorReportBase64Response";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPdfCreatorReportBase64Response";
      content?: string;
      fileName?: string;
    };
  };
};

export type PostApiPatientInforCheckDrawerLedgerDataExistedMutationVariables =
  Types.Exact<{
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type PostApiPatientInforCheckDrawerLedgerDataExistedMutation = {
  __typename?: "mutation_root";
  postApiPatientInforCheckDrawerLedgerDataExisted?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforCheckDrawerLedgerDataExistedResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforCheckDrawerLedgerDataExistedResponse";
      status?: number;
      drawerLedgerDataExisted?: {
        __typename?: "DomainModelsPatientInforCheckDrawerLedgerDataExistedModel";
        existedDetailData?: boolean;
        existedReceiptData?: boolean;
        existedOutOrder?: boolean;
        existedInOrder?: boolean;
        existedSijisenCo?: boolean;
        existedDrugInfo?: boolean;
      };
    };
  };
};

export type GetApiReceptionGetListFileQueryVariables = Types.Exact<{
  categoryCd?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  fileName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiReceptionGetListFileQuery = {
  __typename?: "query_root";
  getApiReceptionGetListFile?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionGetPatientFileListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionGetPatientFileListResponse";
      data?: Array<{
        __typename?: "EmrCloudApiResponsesReceptionGetPatientFileDtO";
        categoryCd?: number;
        sinDate?: number;
        memo?: string;
        hpId?: number;
        getDate?: number;
        fileLink?: string;
        fileId?: number;
        dspFileName?: string;
      }>;
    };
  };
};

export type PutApiReceptionDeletePatientFileMutationVariables = Types.Exact<{
  categoryCd?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  fileId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type PutApiReceptionDeletePatientFileMutation = {
  __typename?: "mutation_root";
  putApiReceptionDeletePatientFile?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionDeletePatientFileResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionDeletePatientFileResponse";
      isSuccess?: boolean;
    };
  };
};

export type Subscription_SubscriptionSubscriptionVariables = Types.Exact<{
  limit?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  order_by?: Types.InputMaybe<
    | Array<Types.Subscription_Subscription_Order_By>
    | Types.Subscription_Subscription_Order_By
  >;
  where?: Types.InputMaybe<Types.Subscription_Subscription_Bool_Exp>;
}>;

export type Subscription_SubscriptionSubscription = {
  __typename?: "subscription_root";
  subscription_subscription: Array<{
    __typename?: "subscription_subscription";
    function_code?: string;
    text: string;
    id: string;
  }>;
};

export type GetApiInsuranceMstGetPublicExpenseComboboxQueryVariables =
  Types.Exact<{
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiInsuranceMstGetPublicExpenseComboboxQuery = {
  __typename?: "query_root";
  getApiInsuranceMstGetPublicExpenseCombobox?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceMstPublicExpenseComboboxResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceMstPublicExpenseComboboxResponse";
      publicExpenseComboboxModel?: {
        __typename?: "DomainModelsInsuranceMstPublicExpenseComboboxModel";
        hokenMstAllData?: Array<{
          __typename?: "DomainModelsInsuranceMstHokenMstModel";
          displayTextMaster?: string;
          endDate?: number;
          futanRate?: number;
          hokenEdaNo?: number;
          hokenName?: string;
          hokenNameCd?: string;
          hokenNo?: number;
          houbetu?: string;
          selectedValueMaster?: string;
          startDate?: number;
          kaiLimitFutan?: number;
          dayLimitFutan?: number;
          monthLimitFutan?: number;
          isTokusyuNoCheck?: number;
          isOtherPrefValid?: number;
          isLimitList?: number;
          isJyukyusyaNoCheck?: number;
          isFutansyaNoCheck?: number;
          jyuKyuCheckDigit?: number;
          isLimitListSum?: number;
          futanKbn?: number;
          hokenSName?: string;
          hokenSbtKbn?: number;
          hokenKohiKbn?: number;
          calcSpKbn?: number;
          prefNo?: number;
          ageEnd?: number;
          ageStart?: number;
          checkDigit?: number;
        }>;
      };
    };
  };
};

export type PostApiPatientInforSaveKohiMutationVariables = Types.Exact<{
  isConfirmOnline?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  kohi?: Types.InputMaybe<Types.EmrCloudApiResponsesInsuranceKohiInfDtoInput>;
  limitList?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.DomainModelsMaxMoneyLimitListModelInput>>
    | Types.InputMaybe<Types.DomainModelsMaxMoneyLimitListModelInput>
  >;
  onlineConfirmationHistory?: Types.InputMaybe<Types.DomainModelsOnlineOnlineConfirmHisDtoInput>;
  patientInfo?: Types.InputMaybe<
    | Array<
        Types.InputMaybe<Types.DomainModelsPatientInforPatientInforConfirmOnlineDtoInput>
      >
    | Types.InputMaybe<Types.DomainModelsPatientInforPatientInforConfirmOnlineDtoInput>
  >;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  endDateModel?: Types.InputMaybe<Types.DomainModelsInsuranceEndDateModelInput>;
  ptKyuseiModel?: Types.InputMaybe<Types.DomainModelsPatientInforPtKyuseiModelInput>;
}>;

export type PostApiPatientInforSaveKohiMutation = {
  __typename?: "mutation_root";
  postApiPatientInforSaveKohi?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceSaveKohiResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceSaveKohiResponse";
      kohiId?: number;
      status?: boolean;
    };
  };
};

export type GetApiPatientInforGetMaxMoneyByPtIdQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiPatientInforGetMaxMoneyByPtIdQuery = {
  __typename?: "query_root";
  getApiPatientInforGetMaxMoneyByPtId?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMaxMoneyGetMaxMoneyByPtIdResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMaxMoneyGetMaxMoneyByPtIdResponse";
      data?: Array<{
        __typename?: "DomainModelsMaxMoneyLimitListModel";
        biko?: string;
        code?: string;
        futanGaku?: number;
        hokenPid?: number;
        id?: string;
        isDeleted?: number;
        kohiId?: number;
        raiinNo?: string;
        seqNo?: number;
        sinDate?: number;
        sinDateD?: number;
        sinDateM?: number;
        sinDateY?: number;
        sort?: number;
        sortKey?: string;
        totalGaku?: number;
        totalMoney?: number;
      }>;
    };
  };
};

export type GetApiReceptionGetMaxMoneyDataQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  hokenKohiId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceptionGetMaxMoneyDataQuery = {
  __typename?: "query_root";
  getApiReceptionGetMaxMoneyData?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMaxMoneyGetMaxMoneyResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMaxMoneyGetMaxMoneyResponse";
      data?: {
        __typename?: "DomainModelsMaxMoneyMaxMoneyModel";
        gendoGaku?: number;
        rate?: number;
      };
    };
  };
};

export type GetApiVisitingGetListMappingMemberQueryVariables = Types.Exact<{
  birthday?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kanaName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  modelNum?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  name?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  portalCustomerId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  ptNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  status?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiVisitingGetListMappingMemberQuery = {
  __typename?: "query_root";
  getApiVisitingGetListMappingMember?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMappingMemberGetMappingModelResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMappingMemberGetMappingModelResponse";
      mappingMemberModels?: Array<{
        __typename?: "DomainModelsReceptionMappingMemberModel";
        birthday?: number;
        isBirthDayEqual?: boolean;
        isKanaNameEqual?: boolean;
        isNameEqual?: boolean;
        isSexEqual?: boolean;
        kanaName?: string;
        name?: string;
        onlineConfirmationHistoryId?: number;
        portalCustomerId?: number;
        ptId?: string;
        ptNum?: string;
        raiinNo?: string;
        sex?: number;
      }>;
    };
  };
};

export type PostApiVisitingUpdateMappingMemberMutationVariables = Types.Exact<{
  aiChartPtId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  hospitalArrivalStatus?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  onlineConfirmHisId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  portalPtId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  userId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type PostApiVisitingUpdateMappingMemberMutation = {
  __typename?: "mutation_root";
  postApiVisitingUpdateMappingMember?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMappingMemberUpdateMappingMemberResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMappingMemberUpdateMappingMemberResponse";
      updateMappingModel?: {
        __typename?: "DomainModelsConfirmOnlineUpdateMappingModelDto";
        deletedRaiinNoList?: Array<string>;
        isContinue?: boolean;
        isSucces?: boolean;
      };
    };
  };
};

export type GetApiReceptionGetLastRaiinInfsQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isLastVisit?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiReceptionGetLastRaiinInfsQuery = {
  __typename?: "query_root";
  getApiReceptionGetLastRaiinInfs?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionGetLastRaiinInfsResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionGetLastRaiinInfsResponse";
      data?: Array<{
        __typename?: "DomainModelsReceptionReceptionModel";
        hokenId?: number;
        hokenKbn?: number;
        hokenKbnName?: string;
        hokenPid?: number;
        houbetu?: string;
        hokensyaNo?: string;
        hpId?: number;
        isDeleted?: boolean;
        isYoyaku?: number;
        jikanKbn?: number;
        kaId?: number;
        kaSname?: string;
        kaikeiId?: number;
        kaikeiTime?: string;
        oyaRaiinNo?: string;
        ptId?: string;
        raiinNo?: string;
        sName?: string;
        santeiKbn?: number;
        sinDate?: number;
        sinEndTime?: string;
        sinStartTime?: string;
        status?: number;
        syosaisinKbn?: number;
        tantoId?: number;
        uketukeId?: number;
        uketukeNo?: number;
        uketukeTime?: string;
        yoyakuId?: number;
        yoyakuTime?: string;
        comment?: string;
      }>;
    };
  };
};

export type DeleteApiOnlineDeletedOnlineConfirmationMutationVariables =
  Types.Exact<{
    confirmationOnlineHisId?: Types.InputMaybe<
      Types.Scalars["BigInt"]["input"]
    >;
  }>;

export type DeleteApiOnlineDeletedOnlineConfirmationMutation = {
  __typename?: "mutation_root";
  deleteApiOnlineDeletedOnlineConfirmation?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineDeletedOnlineConfirmationHisResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineDeletedOnlineConfirmationHisResponse";
      isSuccess?: boolean;
      status?: number;
    };
  };
};

export type PostApiReceptionUpdatePrescriptionMutationVariables = Types.Exact<{
  prescriptionIssueType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  printEpsReference?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  checkStatus?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type PostApiReceptionUpdatePrescriptionMutation = {
  __typename?: "mutation_root";
  postApiReceptionUpdatePrescription?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionUpdatePrescriptionResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionUpdatePrescriptionResponse";
      isSuccess?: boolean;
      status?: number;
    };
  };
};

export type GetApiReceptionCheckLinkCardQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiReceptionCheckLinkCardQuery = {
  __typename?: "query_root";
  getApiReceptionCheckLinkCard?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionCheckLinkCardResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionCheckLinkCardResponse";
      isLinkCard?: boolean;
    };
  };
};

export type GetApiReceptionValidateReservationQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceptionValidateReservationQuery = {
  __typename?: "query_root";
  getApiReceptionValidateReservation?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionValidateReservationResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionValidateReservationResponse";
      success?: boolean;
    };
  };
};

export type PostApiReceptionBookingFromCalendarOrPortalMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsReceptionBookingFromCalendarOrPortalRequestInput>;
  }>;

export type PostApiReceptionBookingFromCalendarOrPortalMutation = {
  __typename?: "mutation_root";
  postApiReceptionBookingFromCalendarOrPortal?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionBookingFromCalendarOrPortalResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionBookingFromCalendarOrPortalResponse";
      raiinNo?: string;
    };
  };
};

export const GetApiRaiinStatusMstGetListDocument = gql`
  query getApiRaiinStatusMstGetList($sinDate: Int) {
    getApiRaiinStatusMstGetList(sinDate: $sinDate) {
      data {
        data {
          statusKbn
          statusName
          countStatus
        }
      }
    }
  }
`;

/**
 * __useGetApiRaiinStatusMstGetListQuery__
 *
 * To run a query within a React component, call `useGetApiRaiinStatusMstGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiRaiinStatusMstGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiRaiinStatusMstGetListQuery({
 *   variables: {
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiRaiinStatusMstGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiRaiinStatusMstGetListQuery,
    GetApiRaiinStatusMstGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiRaiinStatusMstGetListQuery,
    GetApiRaiinStatusMstGetListQueryVariables
  >(GetApiRaiinStatusMstGetListDocument, options);
}
export function useGetApiRaiinStatusMstGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiRaiinStatusMstGetListQuery,
    GetApiRaiinStatusMstGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiRaiinStatusMstGetListQuery,
    GetApiRaiinStatusMstGetListQueryVariables
  >(GetApiRaiinStatusMstGetListDocument, options);
}
export function useGetApiRaiinStatusMstGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiRaiinStatusMstGetListQuery,
    GetApiRaiinStatusMstGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiRaiinStatusMstGetListQuery,
    GetApiRaiinStatusMstGetListQueryVariables
  >(GetApiRaiinStatusMstGetListDocument, options);
}
export type GetApiRaiinStatusMstGetListQueryHookResult = ReturnType<
  typeof useGetApiRaiinStatusMstGetListQuery
>;
export type GetApiRaiinStatusMstGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiRaiinStatusMstGetListLazyQuery
>;
export type GetApiRaiinStatusMstGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiRaiinStatusMstGetListSuspenseQuery
>;
export type GetApiRaiinStatusMstGetListQueryResult = Apollo.QueryResult<
  GetApiRaiinStatusMstGetListQuery,
  GetApiRaiinStatusMstGetListQueryVariables
>;
export const GetApiRaiinFilterGetListMstDocument = gql`
  query getApiRaiinFilterGetListMst {
    getApiRaiinFilterGetListMst {
      data {
        filterMsts {
          doctorIds
          filterId
          filterName
          kaIds
          selectKbn
          shortcut
          sortNo
          statusIds
          treatmentIds
          labelIds
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiRaiinFilterGetListMstQuery__
 *
 * To run a query within a React component, call `useGetApiRaiinFilterGetListMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiRaiinFilterGetListMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiRaiinFilterGetListMstQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiRaiinFilterGetListMstQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiRaiinFilterGetListMstQuery,
    GetApiRaiinFilterGetListMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiRaiinFilterGetListMstQuery,
    GetApiRaiinFilterGetListMstQueryVariables
  >(GetApiRaiinFilterGetListMstDocument, options);
}
export function useGetApiRaiinFilterGetListMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiRaiinFilterGetListMstQuery,
    GetApiRaiinFilterGetListMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiRaiinFilterGetListMstQuery,
    GetApiRaiinFilterGetListMstQueryVariables
  >(GetApiRaiinFilterGetListMstDocument, options);
}
export function useGetApiRaiinFilterGetListMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiRaiinFilterGetListMstQuery,
    GetApiRaiinFilterGetListMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiRaiinFilterGetListMstQuery,
    GetApiRaiinFilterGetListMstQueryVariables
  >(GetApiRaiinFilterGetListMstDocument, options);
}
export type GetApiRaiinFilterGetListMstQueryHookResult = ReturnType<
  typeof useGetApiRaiinFilterGetListMstQuery
>;
export type GetApiRaiinFilterGetListMstLazyQueryHookResult = ReturnType<
  typeof useGetApiRaiinFilterGetListMstLazyQuery
>;
export type GetApiRaiinFilterGetListMstSuspenseQueryHookResult = ReturnType<
  typeof useGetApiRaiinFilterGetListMstSuspenseQuery
>;
export type GetApiRaiinFilterGetListMstQueryResult = Apollo.QueryResult<
  GetApiRaiinFilterGetListMstQuery,
  GetApiRaiinFilterGetListMstQueryVariables
>;
export const PutApiVisitingUpdateStaticCellDocument = gql`
  mutation putApiVisitingUpdateStaticCell(
    $cellName: String
    $cellValue: String
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
    $grpIds: [Int]
  ) {
    putApiVisitingUpdateStaticCell(
      emrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput: {
        sinDate: $sinDate
        raiinNo: $raiinNo
        ptId: $ptId
        cellValue: $cellValue
        cellName: $cellName
        grpIds: $grpIds
      }
    ) {
      data {
        success
      }
    }
  }
`;
export type PutApiVisitingUpdateStaticCellMutationFn = Apollo.MutationFunction<
  PutApiVisitingUpdateStaticCellMutation,
  PutApiVisitingUpdateStaticCellMutationVariables
>;

/**
 * __usePutApiVisitingUpdateStaticCellMutation__
 *
 * To run a mutation, you first call `usePutApiVisitingUpdateStaticCellMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePutApiVisitingUpdateStaticCellMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [putApiVisitingUpdateStaticCellMutation, { data, loading, error }] = usePutApiVisitingUpdateStaticCellMutation({
 *   variables: {
 *      cellName: // value for 'cellName'
 *      cellValue: // value for 'cellValue'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *      grpIds: // value for 'grpIds'
 *   },
 * });
 */
export function usePutApiVisitingUpdateStaticCellMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PutApiVisitingUpdateStaticCellMutation,
    PutApiVisitingUpdateStaticCellMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PutApiVisitingUpdateStaticCellMutation,
    PutApiVisitingUpdateStaticCellMutationVariables
  >(PutApiVisitingUpdateStaticCellDocument, options);
}
export type PutApiVisitingUpdateStaticCellMutationHookResult = ReturnType<
  typeof usePutApiVisitingUpdateStaticCellMutation
>;
export type PutApiVisitingUpdateStaticCellMutationResult =
  Apollo.MutationResult<PutApiVisitingUpdateStaticCellMutation>;
export type PutApiVisitingUpdateStaticCellMutationOptions =
  Apollo.BaseMutationOptions<
    PutApiVisitingUpdateStaticCellMutation,
    PutApiVisitingUpdateStaticCellMutationVariables
  >;
export const PostApiRaiinFilterSaveListMstDocument = gql`
  mutation postApiRaiinFilterSaveListMst(
    $input: EmrCloudApiRequestsRaiinFilterSaveRaiinFilterMstListRequestInput
  ) {
    postApiRaiinFilterSaveListMst(
      emrCloudApiRequestsRaiinFilterSaveRaiinFilterMstListRequestInput: $input
    ) {
      data {
        success
        filterIds
      }
      message
      status
    }
  }
`;
export type PostApiRaiinFilterSaveListMstMutationFn = Apollo.MutationFunction<
  PostApiRaiinFilterSaveListMstMutation,
  PostApiRaiinFilterSaveListMstMutationVariables
>;

/**
 * __usePostApiRaiinFilterSaveListMstMutation__
 *
 * To run a mutation, you first call `usePostApiRaiinFilterSaveListMstMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiRaiinFilterSaveListMstMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiRaiinFilterSaveListMstMutation, { data, loading, error }] = usePostApiRaiinFilterSaveListMstMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiRaiinFilterSaveListMstMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiRaiinFilterSaveListMstMutation,
    PostApiRaiinFilterSaveListMstMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiRaiinFilterSaveListMstMutation,
    PostApiRaiinFilterSaveListMstMutationVariables
  >(PostApiRaiinFilterSaveListMstDocument, options);
}
export type PostApiRaiinFilterSaveListMstMutationHookResult = ReturnType<
  typeof usePostApiRaiinFilterSaveListMstMutation
>;
export type PostApiRaiinFilterSaveListMstMutationResult =
  Apollo.MutationResult<PostApiRaiinFilterSaveListMstMutation>;
export type PostApiRaiinFilterSaveListMstMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiRaiinFilterSaveListMstMutation,
    PostApiRaiinFilterSaveListMstMutationVariables
  >;
export const GetVisitingGetListDocument = gql`
  query getVisitingGetList($sinDate: Int) {
    getApiVisitingGetList(sinDate: $sinDate) {
      data {
        receptionInfos {
          uketukeNo
          uketukeTime
          sinDate
          yoyakuTime
          status
          ptNum
          kanaName
          name
          sex
          age
          birthday
          kaId
          tantoId
          isDeleted
          kaName
          ptMemo
          monshinStatus
          portalPtId
          ptId
          raiinNo
          referenceNo
          statusKbnUpdateTime
          statusMstKbn
          tantoName
          karteEditionRaiinNo
          raiinMemo
          treatmentDepartmentId
          yoyakuEndTime
          hasUnread
          ptId
          labels {
            colorCd
            grpId
            hpId
            name
            sortNo
          }
          meetingStatus
          meetingStatusName
          reverseName
          reverseType
          hokenName
          odrId
          onlineConfirmationDate
          typeAlert
          errorConfirmOnlineCode
          errorConfirmOnlineMessage
          portalCustomerId
          onlineConfirmationId
          sortNoStatusMst
          reverseId
          reverseDetailId
        }
      }
    }
  }
`;

/**
 * __useGetVisitingGetListQuery__
 *
 * To run a query within a React component, call `useGetVisitingGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVisitingGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVisitingGetListQuery({
 *   variables: {
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetVisitingGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetVisitingGetListQuery,
    GetVisitingGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetVisitingGetListQuery,
    GetVisitingGetListQueryVariables
  >(GetVisitingGetListDocument, options);
}
export function useGetVisitingGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetVisitingGetListQuery,
    GetVisitingGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetVisitingGetListQuery,
    GetVisitingGetListQueryVariables
  >(GetVisitingGetListDocument, options);
}
export function useGetVisitingGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetVisitingGetListQuery,
    GetVisitingGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetVisitingGetListQuery,
    GetVisitingGetListQueryVariables
  >(GetVisitingGetListDocument, options);
}
export type GetVisitingGetListQueryHookResult = ReturnType<
  typeof useGetVisitingGetListQuery
>;
export type GetVisitingGetListLazyQueryHookResult = ReturnType<
  typeof useGetVisitingGetListLazyQuery
>;
export type GetVisitingGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetVisitingGetListSuspenseQuery
>;
export type GetVisitingGetListQueryResult = Apollo.QueryResult<
  GetVisitingGetListQuery,
  GetVisitingGetListQueryVariables
>;
export const GetTreatmentDepartmentListDocument = gql`
  query getTreatmentDepartmentList($isDeleted: Boolean) {
    getApiTreatmentDepartmentGetTreatmentDepartmentList(isDeleted: $isDeleted) {
      data {
        treatmentStatusList {
          treatmentDepartmentId
          title
          treatmentDepartmentStatus
          isDeleted
        }
      }
    }
  }
`;

/**
 * __useGetTreatmentDepartmentListQuery__
 *
 * To run a query within a React component, call `useGetTreatmentDepartmentListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTreatmentDepartmentListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTreatmentDepartmentListQuery({
 *   variables: {
 *      isDeleted: // value for 'isDeleted'
 *   },
 * });
 */
export function useGetTreatmentDepartmentListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetTreatmentDepartmentListQuery,
    GetTreatmentDepartmentListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetTreatmentDepartmentListQuery,
    GetTreatmentDepartmentListQueryVariables
  >(GetTreatmentDepartmentListDocument, options);
}
export function useGetTreatmentDepartmentListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetTreatmentDepartmentListQuery,
    GetTreatmentDepartmentListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetTreatmentDepartmentListQuery,
    GetTreatmentDepartmentListQueryVariables
  >(GetTreatmentDepartmentListDocument, options);
}
export function useGetTreatmentDepartmentListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetTreatmentDepartmentListQuery,
    GetTreatmentDepartmentListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetTreatmentDepartmentListQuery,
    GetTreatmentDepartmentListQueryVariables
  >(GetTreatmentDepartmentListDocument, options);
}
export type GetTreatmentDepartmentListQueryHookResult = ReturnType<
  typeof useGetTreatmentDepartmentListQuery
>;
export type GetTreatmentDepartmentListLazyQueryHookResult = ReturnType<
  typeof useGetTreatmentDepartmentListLazyQuery
>;
export type GetTreatmentDepartmentListSuspenseQueryHookResult = ReturnType<
  typeof useGetTreatmentDepartmentListSuspenseQuery
>;
export type GetTreatmentDepartmentListQueryResult = Apollo.QueryResult<
  GetTreatmentDepartmentListQuery,
  GetTreatmentDepartmentListQueryVariables
>;
export const GetApiLabelMstGetListDocument = gql`
  query getApiLabelMstGetList {
    getApiLabelMstGetList {
      data {
        labels {
          colorCd
          grpId
          grpName
          sortNo
        }
      }
    }
  }
`;

/**
 * __useGetApiLabelMstGetListQuery__
 *
 * To run a query within a React component, call `useGetApiLabelMstGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiLabelMstGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiLabelMstGetListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiLabelMstGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiLabelMstGetListQuery,
    GetApiLabelMstGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiLabelMstGetListQuery,
    GetApiLabelMstGetListQueryVariables
  >(GetApiLabelMstGetListDocument, options);
}
export function useGetApiLabelMstGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiLabelMstGetListQuery,
    GetApiLabelMstGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiLabelMstGetListQuery,
    GetApiLabelMstGetListQueryVariables
  >(GetApiLabelMstGetListDocument, options);
}
export function useGetApiLabelMstGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiLabelMstGetListQuery,
    GetApiLabelMstGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiLabelMstGetListQuery,
    GetApiLabelMstGetListQueryVariables
  >(GetApiLabelMstGetListDocument, options);
}
export type GetApiLabelMstGetListQueryHookResult = ReturnType<
  typeof useGetApiLabelMstGetListQuery
>;
export type GetApiLabelMstGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiLabelMstGetListLazyQuery
>;
export type GetApiLabelMstGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiLabelMstGetListSuspenseQuery
>;
export type GetApiLabelMstGetListQueryResult = Apollo.QueryResult<
  GetApiLabelMstGetListQuery,
  GetApiLabelMstGetListQueryVariables
>;
export const GetApiReceptionGetDocument = gql`
  query getApiReceptionGet($flag: Boolean, $raiinNo: BigInt) {
    getApiReceptionGet(flag: $flag, raiinNo: $raiinNo) {
      data {
        reception {
          canCombine
          canEditPrescription
          comment
          confirmationType
          departmentSName
          hokenPid
          hpId
          infoConsFlg
          isYoyaku
          jikanKbn
          isLinkCard
          kaId
          kaikeiId
          kaikeiTime
          oyaRaiinNo
          patternName
          hasMessage
          paymentMethodCd
          personNumber
          prescriptionDeliInfo
          prescriptionIssueType
          prescriptionName
          ptId
          ptKanaName
          ptName
          raiinBinding
          raiinNo
          santeiKbn
          sex
          sinDate
          sinEndTime
          sinryoKbn
          sinStartTime
          status
          syosaisinKbn
          tantoId
          treatmentDepartmentId
          uketukeId
          uketukeNo
          uketukeSbt
          uketukeTime
          yoyakuEndTime
          yoyakuId
          yoyakuTime
          kaSname
          tantoName
          departmentSName
          printEpsReference
          reserveDetailId
          labels {
            colorCd
            grpId
            hpId
            name
            sortNo
          }
          karteEditionRaiinNo
          hokenPatternModel {
            hokenPid
            kohi1Id
            kohi2Id
            kohi3Id
            kohi4Id
            hokenId
          }
          reserveTypeName
          hokenName
          prescriptionReceiveMethod
          portalCustomerPharmacy {
            pharmacyName
            pharmacyStoreName
            phoneNumber
            faxNumber
            postCode
            address1
            address2
          }
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiReceptionGetQuery__
 *
 * To run a query within a React component, call `useGetApiReceptionGetQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceptionGetQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceptionGetQuery({
 *   variables: {
 *      flag: // value for 'flag'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiReceptionGetQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceptionGetQuery,
    GetApiReceptionGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceptionGetQuery,
    GetApiReceptionGetQueryVariables
  >(GetApiReceptionGetDocument, options);
}
export function useGetApiReceptionGetLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceptionGetQuery,
    GetApiReceptionGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceptionGetQuery,
    GetApiReceptionGetQueryVariables
  >(GetApiReceptionGetDocument, options);
}
export function useGetApiReceptionGetSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceptionGetQuery,
    GetApiReceptionGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceptionGetQuery,
    GetApiReceptionGetQueryVariables
  >(GetApiReceptionGetDocument, options);
}
export type GetApiReceptionGetQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetQuery
>;
export type GetApiReceptionGetLazyQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetLazyQuery
>;
export type GetApiReceptionGetSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetSuspenseQuery
>;
export type GetApiReceptionGetQueryResult = Apollo.QueryResult<
  GetApiReceptionGetQuery,
  GetApiReceptionGetQueryVariables
>;
export const GetApiReceptionGetReceptionCombineDocument = gql`
  query getApiReceptionGetReceptionCombine(
    $hokenPid: Int
    $isCombined: Boolean
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiReceptionGetReceptionCombine(
      sinDate: $sinDate
      raiinNo: $raiinNo
      ptId: $ptId
      isCombined: $isCombined
      hokenPid: $hokenPid
    ) {
      data {
        receptionCombines {
          hpId
          kaId
          kaName
          oyaRaiinNo
          ptId
          ptName
          tantoId
          tantoName
          raiinNo
          treatmentDepartmentId
          treatmentDepartmentName
          uketukeNo
        }
      }
    }
  }
`;

/**
 * __useGetApiReceptionGetReceptionCombineQuery__
 *
 * To run a query within a React component, call `useGetApiReceptionGetReceptionCombineQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceptionGetReceptionCombineQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceptionGetReceptionCombineQuery({
 *   variables: {
 *      hokenPid: // value for 'hokenPid'
 *      isCombined: // value for 'isCombined'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiReceptionGetReceptionCombineQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceptionGetReceptionCombineQuery,
    GetApiReceptionGetReceptionCombineQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceptionGetReceptionCombineQuery,
    GetApiReceptionGetReceptionCombineQueryVariables
  >(GetApiReceptionGetReceptionCombineDocument, options);
}
export function useGetApiReceptionGetReceptionCombineLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceptionGetReceptionCombineQuery,
    GetApiReceptionGetReceptionCombineQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceptionGetReceptionCombineQuery,
    GetApiReceptionGetReceptionCombineQueryVariables
  >(GetApiReceptionGetReceptionCombineDocument, options);
}
export function useGetApiReceptionGetReceptionCombineSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceptionGetReceptionCombineQuery,
    GetApiReceptionGetReceptionCombineQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceptionGetReceptionCombineQuery,
    GetApiReceptionGetReceptionCombineQueryVariables
  >(GetApiReceptionGetReceptionCombineDocument, options);
}
export type GetApiReceptionGetReceptionCombineQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetReceptionCombineQuery
>;
export type GetApiReceptionGetReceptionCombineLazyQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetReceptionCombineLazyQuery
>;
export type GetApiReceptionGetReceptionCombineSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceptionGetReceptionCombineSuspenseQuery>;
export type GetApiReceptionGetReceptionCombineQueryResult = Apollo.QueryResult<
  GetApiReceptionGetReceptionCombineQuery,
  GetApiReceptionGetReceptionCombineQueryVariables
>;
export const PutApiReceptionCombineSplitBillDocument = gql`
  mutation putApiReceptionCombineSplitBill(
    $input: EmrCloudApiRequestsReceptionCombineSplitBillRequestInput
  ) {
    putApiReceptionCombineSplitBill(
      emrCloudApiRequestsReceptionCombineSplitBillRequestInput: $input
    ) {
      data {
        isSuccess
      }
    }
  }
`;
export type PutApiReceptionCombineSplitBillMutationFn = Apollo.MutationFunction<
  PutApiReceptionCombineSplitBillMutation,
  PutApiReceptionCombineSplitBillMutationVariables
>;

/**
 * __usePutApiReceptionCombineSplitBillMutation__
 *
 * To run a mutation, you first call `usePutApiReceptionCombineSplitBillMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePutApiReceptionCombineSplitBillMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [putApiReceptionCombineSplitBillMutation, { data, loading, error }] = usePutApiReceptionCombineSplitBillMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePutApiReceptionCombineSplitBillMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PutApiReceptionCombineSplitBillMutation,
    PutApiReceptionCombineSplitBillMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PutApiReceptionCombineSplitBillMutation,
    PutApiReceptionCombineSplitBillMutationVariables
  >(PutApiReceptionCombineSplitBillDocument, options);
}
export type PutApiReceptionCombineSplitBillMutationHookResult = ReturnType<
  typeof usePutApiReceptionCombineSplitBillMutation
>;
export type PutApiReceptionCombineSplitBillMutationResult =
  Apollo.MutationResult<PutApiReceptionCombineSplitBillMutation>;
export type PutApiReceptionCombineSplitBillMutationOptions =
  Apollo.BaseMutationOptions<
    PutApiReceptionCombineSplitBillMutation,
    PutApiReceptionCombineSplitBillMutationVariables
  >;
export const GetApiPatientInforInsuranceListByPtIdDocument = gql`
  query getApiPatientInforInsuranceListByPtId($ptId: BigInt, $sinDate: Int) {
    getApiPatientInforInsuranceListByPtId(ptId: $ptId, sinDate: $sinDate) {
      data {
        data {
          listInsurance {
            hokenKbn
            hokenMemo
            hokenName
            hokenPid
            hokenSbtCd
            hpId
            isAddNew
            isDeleted
            hokenInf {
              hokenEdaNo
              hokenId
              hokenKbn
              houbetu
              jibaiHokenName
              seqNo
            }
            kohi1 {
              houbetu
              hokenMstModel {
                hokenNameCd
              }
            }
            kohi2 {
              houbetu
              hokenMstModel {
                hokenNameCd
              }
            }
            kohi3 {
              houbetu
              hokenMstModel {
                hokenNameCd
              }
            }
            kohi4 {
              houbetu
              hokenMstModel {
                hokenNameCd
              }
            }
            isEmptyKohi1
            isEmptyKohi2
            isEmptyKohi3
            isEmptyKohi4
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforInsuranceListByPtIdQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforInsuranceListByPtIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforInsuranceListByPtIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforInsuranceListByPtIdQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiPatientInforInsuranceListByPtIdQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforInsuranceListByPtIdQuery,
    GetApiPatientInforInsuranceListByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforInsuranceListByPtIdQuery,
    GetApiPatientInforInsuranceListByPtIdQueryVariables
  >(GetApiPatientInforInsuranceListByPtIdDocument, options);
}
export function useGetApiPatientInforInsuranceListByPtIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforInsuranceListByPtIdQuery,
    GetApiPatientInforInsuranceListByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforInsuranceListByPtIdQuery,
    GetApiPatientInforInsuranceListByPtIdQueryVariables
  >(GetApiPatientInforInsuranceListByPtIdDocument, options);
}
export function useGetApiPatientInforInsuranceListByPtIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforInsuranceListByPtIdQuery,
    GetApiPatientInforInsuranceListByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforInsuranceListByPtIdQuery,
    GetApiPatientInforInsuranceListByPtIdQueryVariables
  >(GetApiPatientInforInsuranceListByPtIdDocument, options);
}
export type GetApiPatientInforInsuranceListByPtIdQueryHookResult = ReturnType<
  typeof useGetApiPatientInforInsuranceListByPtIdQuery
>;
export type GetApiPatientInforInsuranceListByPtIdLazyQueryHookResult =
  ReturnType<typeof useGetApiPatientInforInsuranceListByPtIdLazyQuery>;
export type GetApiPatientInforInsuranceListByPtIdSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforInsuranceListByPtIdSuspenseQuery>;
export type GetApiPatientInforInsuranceListByPtIdQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforInsuranceListByPtIdQuery,
    GetApiPatientInforInsuranceListByPtIdQueryVariables
  >;
export const GetDefaultValueAddReceptionDocument = gql`
  query getDefaultValueAddReception(
    $ptId: BigInt
    $sinDate: Int
    $hokenId: Int
    $requestFrom: Int
    $isContiFiltered: Boolean
    $isInMonthFiltered: Boolean
    $isLastVisit: Boolean
    $birthDay: Int
    $uketukeTime: Int
    $raiinNo: BigInt
  ) {
    getApiDiseasesGetList(
      hokenId: $hokenId
      ptId: $ptId
      requestFrom: $requestFrom
      sinDate: $sinDate
      isContiFiltered: $isContiFiltered
      isInMonthFiltered: $isInMonthFiltered
    ) {
      data {
        diseaseList {
          tenkiKbn
          startDate
          sinDate
          tenkiDate
        }
      }
    }
    getApiReceptionGetLastRaiinInfs(
      isLastVisit: $isLastVisit
      ptId: $ptId
      sinDate: $sinDate
    ) {
      data {
        data {
          sinDate
          tantoId
        }
      }
    }
    getApiReceptionGetDefaultSelectedTime(
      birthDay: $birthDay
      sinDate: $sinDate
      uketukeTime: $uketukeTime
    ) {
      data {
        data {
          jikanKbnDefault
          isPatientChildren
        }
      }
    }
    getApiReceptionGetDefaultPrescription(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        prescriptionIssueType
        prinEpsReference
      }
    }
  }
`;

/**
 * __useGetDefaultValueAddReceptionQuery__
 *
 * To run a query within a React component, call `useGetDefaultValueAddReceptionQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetDefaultValueAddReceptionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetDefaultValueAddReceptionQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      hokenId: // value for 'hokenId'
 *      requestFrom: // value for 'requestFrom'
 *      isContiFiltered: // value for 'isContiFiltered'
 *      isInMonthFiltered: // value for 'isInMonthFiltered'
 *      isLastVisit: // value for 'isLastVisit'
 *      birthDay: // value for 'birthDay'
 *      uketukeTime: // value for 'uketukeTime'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetDefaultValueAddReceptionQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetDefaultValueAddReceptionQuery,
    GetDefaultValueAddReceptionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetDefaultValueAddReceptionQuery,
    GetDefaultValueAddReceptionQueryVariables
  >(GetDefaultValueAddReceptionDocument, options);
}
export function useGetDefaultValueAddReceptionLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetDefaultValueAddReceptionQuery,
    GetDefaultValueAddReceptionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetDefaultValueAddReceptionQuery,
    GetDefaultValueAddReceptionQueryVariables
  >(GetDefaultValueAddReceptionDocument, options);
}
export function useGetDefaultValueAddReceptionSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetDefaultValueAddReceptionQuery,
    GetDefaultValueAddReceptionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetDefaultValueAddReceptionQuery,
    GetDefaultValueAddReceptionQueryVariables
  >(GetDefaultValueAddReceptionDocument, options);
}
export type GetDefaultValueAddReceptionQueryHookResult = ReturnType<
  typeof useGetDefaultValueAddReceptionQuery
>;
export type GetDefaultValueAddReceptionLazyQueryHookResult = ReturnType<
  typeof useGetDefaultValueAddReceptionLazyQuery
>;
export type GetDefaultValueAddReceptionSuspenseQueryHookResult = ReturnType<
  typeof useGetDefaultValueAddReceptionSuspenseQuery
>;
export type GetDefaultValueAddReceptionQueryResult = Apollo.QueryResult<
  GetDefaultValueAddReceptionQuery,
  GetDefaultValueAddReceptionQueryVariables
>;
export const PostApiReceptionInsertDocument = gql`
  mutation postApiReceptionInsert(
    $diseases: [DomainModelsReceptionDiseaseDtoInput]
    $insurances: [DomainModelsReceptionInsuranceDtoInput]
    $kubunInfs: [DomainModelsReceptionRaiinKbnInfDtoInput]
    $reception: DomainModelsReceptionReceptionUpsertItemInput
    $receptionComment: String
  ) {
    postApiReceptionInsert(
      emrCloudApiRequestsReceptionInsertReceptionRequestInput: {
        dto: {
          diseases: $diseases
          insurances: $insurances
          kubunInfs: $kubunInfs
          reception: $reception
          receptionComment: $receptionComment
        }
      }
    ) {
      data {
        raiinNo
      }
      message
      status
    }
  }
`;
export type PostApiReceptionInsertMutationFn = Apollo.MutationFunction<
  PostApiReceptionInsertMutation,
  PostApiReceptionInsertMutationVariables
>;

/**
 * __usePostApiReceptionInsertMutation__
 *
 * To run a mutation, you first call `usePostApiReceptionInsertMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceptionInsertMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceptionInsertMutation, { data, loading, error }] = usePostApiReceptionInsertMutation({
 *   variables: {
 *      diseases: // value for 'diseases'
 *      insurances: // value for 'insurances'
 *      kubunInfs: // value for 'kubunInfs'
 *      reception: // value for 'reception'
 *      receptionComment: // value for 'receptionComment'
 *   },
 * });
 */
export function usePostApiReceptionInsertMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceptionInsertMutation,
    PostApiReceptionInsertMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceptionInsertMutation,
    PostApiReceptionInsertMutationVariables
  >(PostApiReceptionInsertDocument, options);
}
export type PostApiReceptionInsertMutationHookResult = ReturnType<
  typeof usePostApiReceptionInsertMutation
>;
export type PostApiReceptionInsertMutationResult =
  Apollo.MutationResult<PostApiReceptionInsertMutation>;
export type PostApiReceptionInsertMutationOptions = Apollo.BaseMutationOptions<
  PostApiReceptionInsertMutation,
  PostApiReceptionInsertMutationVariables
>;
export const PostApiReceptionUpdateDocument = gql`
  mutation postApiReceptionUpdate(
    $diseases: [DomainModelsReceptionDiseaseDtoInput]
    $insurances: [DomainModelsReceptionInsuranceDtoInput]
    $kubunInfs: [DomainModelsReceptionRaiinKbnInfDtoInput]
    $reception: DomainModelsReceptionReceptionUpsertItemInput
    $receptionComment: String
  ) {
    postApiReceptionUpdate(
      emrCloudApiRequestsReceptionUpdateReceptionRequestInput: {
        dto: {
          diseases: $diseases
          insurances: $insurances
          kubunInfs: $kubunInfs
          reception: $reception
          receptionComment: $receptionComment
        }
      }
    ) {
      message
      status
    }
  }
`;
export type PostApiReceptionUpdateMutationFn = Apollo.MutationFunction<
  PostApiReceptionUpdateMutation,
  PostApiReceptionUpdateMutationVariables
>;

/**
 * __usePostApiReceptionUpdateMutation__
 *
 * To run a mutation, you first call `usePostApiReceptionUpdateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceptionUpdateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceptionUpdateMutation, { data, loading, error }] = usePostApiReceptionUpdateMutation({
 *   variables: {
 *      diseases: // value for 'diseases'
 *      insurances: // value for 'insurances'
 *      kubunInfs: // value for 'kubunInfs'
 *      reception: // value for 'reception'
 *      receptionComment: // value for 'receptionComment'
 *   },
 * });
 */
export function usePostApiReceptionUpdateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceptionUpdateMutation,
    PostApiReceptionUpdateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceptionUpdateMutation,
    PostApiReceptionUpdateMutationVariables
  >(PostApiReceptionUpdateDocument, options);
}
export type PostApiReceptionUpdateMutationHookResult = ReturnType<
  typeof usePostApiReceptionUpdateMutation
>;
export type PostApiReceptionUpdateMutationResult =
  Apollo.MutationResult<PostApiReceptionUpdateMutation>;
export type PostApiReceptionUpdateMutationOptions = Apollo.BaseMutationOptions<
  PostApiReceptionUpdateMutation,
  PostApiReceptionUpdateMutationVariables
>;
export const PostApiPatientInforSaveHokenCheckDocument = gql`
  mutation postApiPatientInforSaveHokenCheck(
    $input: EmrCloudApiRequestsPatientInforSaveHokenCheckRequestInput
  ) {
    postApiPatientInforSaveHokenCheck(
      emrCloudApiRequestsPatientInforSaveHokenCheckRequestInput: $input
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiPatientInforSaveHokenCheckMutationFn =
  Apollo.MutationFunction<
    PostApiPatientInforSaveHokenCheckMutation,
    PostApiPatientInforSaveHokenCheckMutationVariables
  >;

/**
 * __usePostApiPatientInforSaveHokenCheckMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforSaveHokenCheckMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforSaveHokenCheckMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforSaveHokenCheckMutation, { data, loading, error }] = usePostApiPatientInforSaveHokenCheckMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiPatientInforSaveHokenCheckMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforSaveHokenCheckMutation,
    PostApiPatientInforSaveHokenCheckMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforSaveHokenCheckMutation,
    PostApiPatientInforSaveHokenCheckMutationVariables
  >(PostApiPatientInforSaveHokenCheckDocument, options);
}
export type PostApiPatientInforSaveHokenCheckMutationHookResult = ReturnType<
  typeof usePostApiPatientInforSaveHokenCheckMutation
>;
export type PostApiPatientInforSaveHokenCheckMutationResult =
  Apollo.MutationResult<PostApiPatientInforSaveHokenCheckMutation>;
export type PostApiPatientInforSaveHokenCheckMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforSaveHokenCheckMutation,
    PostApiPatientInforSaveHokenCheckMutationVariables
  >;
export const GetApiPdfCreatorReceiptReportBase64Document = gql`
  query getApiPdfCreatorReceiptReportBase64(
    $isCalculateProcess: Boolean = false
    $printType: Int
    $ptId: BigInt
    $raiinNoList: [BigInt]
  ) {
    getApiPdfCreatorReceiptReportBase64(
      isCalculateProcess: $isCalculateProcess
      ptId: $ptId
      raiinNoList: $raiinNoList
      printType: $printType
    ) {
      data {
        content
        fileName
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorReceiptReportBase64Query__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorReceiptReportBase64Query` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorReceiptReportBase64Query` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorReceiptReportBase64Query({
 *   variables: {
 *      isCalculateProcess: // value for 'isCalculateProcess'
 *      printType: // value for 'printType'
 *      ptId: // value for 'ptId'
 *      raiinNoList: // value for 'raiinNoList'
 *   },
 * });
 */
export function useGetApiPdfCreatorReceiptReportBase64Query(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorReceiptReportBase64Query,
    GetApiPdfCreatorReceiptReportBase64QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorReceiptReportBase64Query,
    GetApiPdfCreatorReceiptReportBase64QueryVariables
  >(GetApiPdfCreatorReceiptReportBase64Document, options);
}
export function useGetApiPdfCreatorReceiptReportBase64LazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorReceiptReportBase64Query,
    GetApiPdfCreatorReceiptReportBase64QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorReceiptReportBase64Query,
    GetApiPdfCreatorReceiptReportBase64QueryVariables
  >(GetApiPdfCreatorReceiptReportBase64Document, options);
}
export function useGetApiPdfCreatorReceiptReportBase64SuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorReceiptReportBase64Query,
    GetApiPdfCreatorReceiptReportBase64QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorReceiptReportBase64Query,
    GetApiPdfCreatorReceiptReportBase64QueryVariables
  >(GetApiPdfCreatorReceiptReportBase64Document, options);
}
export type GetApiPdfCreatorReceiptReportBase64QueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorReceiptReportBase64Query
>;
export type GetApiPdfCreatorReceiptReportBase64LazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorReceiptReportBase64LazyQuery
>;
export type GetApiPdfCreatorReceiptReportBase64SuspenseQueryHookResult =
  ReturnType<typeof useGetApiPdfCreatorReceiptReportBase64SuspenseQuery>;
export type GetApiPdfCreatorReceiptReportBase64QueryResult = Apollo.QueryResult<
  GetApiPdfCreatorReceiptReportBase64Query,
  GetApiPdfCreatorReceiptReportBase64QueryVariables
>;
export const GetApiPdfCreatorInDrugBase64Document = gql`
  query getApiPdfCreatorInDrugBase64(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiPdfCreatorInDrugBase64(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        content
        fileName
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorInDrugBase64Query__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorInDrugBase64Query` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorInDrugBase64Query` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorInDrugBase64Query({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiPdfCreatorInDrugBase64Query(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorInDrugBase64Query,
    GetApiPdfCreatorInDrugBase64QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorInDrugBase64Query,
    GetApiPdfCreatorInDrugBase64QueryVariables
  >(GetApiPdfCreatorInDrugBase64Document, options);
}
export function useGetApiPdfCreatorInDrugBase64LazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorInDrugBase64Query,
    GetApiPdfCreatorInDrugBase64QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorInDrugBase64Query,
    GetApiPdfCreatorInDrugBase64QueryVariables
  >(GetApiPdfCreatorInDrugBase64Document, options);
}
export function useGetApiPdfCreatorInDrugBase64SuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorInDrugBase64Query,
    GetApiPdfCreatorInDrugBase64QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorInDrugBase64Query,
    GetApiPdfCreatorInDrugBase64QueryVariables
  >(GetApiPdfCreatorInDrugBase64Document, options);
}
export type GetApiPdfCreatorInDrugBase64QueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorInDrugBase64Query
>;
export type GetApiPdfCreatorInDrugBase64LazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorInDrugBase64LazyQuery
>;
export type GetApiPdfCreatorInDrugBase64SuspenseQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorInDrugBase64SuspenseQuery
>;
export type GetApiPdfCreatorInDrugBase64QueryResult = Apollo.QueryResult<
  GetApiPdfCreatorInDrugBase64Query,
  GetApiPdfCreatorInDrugBase64QueryVariables
>;
export const GetApiPdfCreatorOutDrugBase64Document = gql`
  query getApiPdfCreatorOutDrugBase64(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiPdfCreatorOutDrugBase64(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        content
        fileName
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorOutDrugBase64Query__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorOutDrugBase64Query` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorOutDrugBase64Query` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorOutDrugBase64Query({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiPdfCreatorOutDrugBase64Query(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorOutDrugBase64Query,
    GetApiPdfCreatorOutDrugBase64QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorOutDrugBase64Query,
    GetApiPdfCreatorOutDrugBase64QueryVariables
  >(GetApiPdfCreatorOutDrugBase64Document, options);
}
export function useGetApiPdfCreatorOutDrugBase64LazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorOutDrugBase64Query,
    GetApiPdfCreatorOutDrugBase64QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorOutDrugBase64Query,
    GetApiPdfCreatorOutDrugBase64QueryVariables
  >(GetApiPdfCreatorOutDrugBase64Document, options);
}
export function useGetApiPdfCreatorOutDrugBase64SuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorOutDrugBase64Query,
    GetApiPdfCreatorOutDrugBase64QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorOutDrugBase64Query,
    GetApiPdfCreatorOutDrugBase64QueryVariables
  >(GetApiPdfCreatorOutDrugBase64Document, options);
}
export type GetApiPdfCreatorOutDrugBase64QueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorOutDrugBase64Query
>;
export type GetApiPdfCreatorOutDrugBase64LazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorOutDrugBase64LazyQuery
>;
export type GetApiPdfCreatorOutDrugBase64SuspenseQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorOutDrugBase64SuspenseQuery
>;
export type GetApiPdfCreatorOutDrugBase64QueryResult = Apollo.QueryResult<
  GetApiPdfCreatorOutDrugBase64Query,
  GetApiPdfCreatorOutDrugBase64QueryVariables
>;
export const PostApiPatientInforCheckDrawerLedgerDataExistedDocument = gql`
  mutation postApiPatientInforCheckDrawerLedgerDataExisted(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    postApiPatientInforCheckDrawerLedgerDataExisted(
      emrCloudApiRequestsPatientInforCheckDrawerLedgerDataExistedRequestInput: {
        ptId: $ptId
        raiinNo: $raiinNo
        sinDate: $sinDate
      }
    ) {
      data {
        drawerLedgerDataExisted {
          existedDetailData
          existedReceiptData
          existedOutOrder
          existedInOrder
          existedSijisenCo
          existedDrugInfo
        }
        status
      }
      message
      status
    }
  }
`;
export type PostApiPatientInforCheckDrawerLedgerDataExistedMutationFn =
  Apollo.MutationFunction<
    PostApiPatientInforCheckDrawerLedgerDataExistedMutation,
    PostApiPatientInforCheckDrawerLedgerDataExistedMutationVariables
  >;

/**
 * __usePostApiPatientInforCheckDrawerLedgerDataExistedMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforCheckDrawerLedgerDataExistedMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforCheckDrawerLedgerDataExistedMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforCheckDrawerLedgerDataExistedMutation, { data, loading, error }] = usePostApiPatientInforCheckDrawerLedgerDataExistedMutation({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function usePostApiPatientInforCheckDrawerLedgerDataExistedMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforCheckDrawerLedgerDataExistedMutation,
    PostApiPatientInforCheckDrawerLedgerDataExistedMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforCheckDrawerLedgerDataExistedMutation,
    PostApiPatientInforCheckDrawerLedgerDataExistedMutationVariables
  >(PostApiPatientInforCheckDrawerLedgerDataExistedDocument, options);
}
export type PostApiPatientInforCheckDrawerLedgerDataExistedMutationHookResult =
  ReturnType<typeof usePostApiPatientInforCheckDrawerLedgerDataExistedMutation>;
export type PostApiPatientInforCheckDrawerLedgerDataExistedMutationResult =
  Apollo.MutationResult<PostApiPatientInforCheckDrawerLedgerDataExistedMutation>;
export type PostApiPatientInforCheckDrawerLedgerDataExistedMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforCheckDrawerLedgerDataExistedMutation,
    PostApiPatientInforCheckDrawerLedgerDataExistedMutationVariables
  >;
export const GetApiReceptionGetListFileDocument = gql`
  query getApiReceptionGetListFile(
    $categoryCd: Int
    $fileName: String
    $ptId: BigInt
  ) {
    getApiReceptionGetListFile(
      categoryCd: $categoryCd
      fileName: $fileName
      ptId: $ptId
    ) {
      data {
        data {
          categoryCd
          sinDate
          memo
          hpId
          getDate
          fileLink
          fileId
          dspFileName
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiReceptionGetListFileQuery__
 *
 * To run a query within a React component, call `useGetApiReceptionGetListFileQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceptionGetListFileQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceptionGetListFileQuery({
 *   variables: {
 *      categoryCd: // value for 'categoryCd'
 *      fileName: // value for 'fileName'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiReceptionGetListFileQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceptionGetListFileQuery,
    GetApiReceptionGetListFileQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceptionGetListFileQuery,
    GetApiReceptionGetListFileQueryVariables
  >(GetApiReceptionGetListFileDocument, options);
}
export function useGetApiReceptionGetListFileLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceptionGetListFileQuery,
    GetApiReceptionGetListFileQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceptionGetListFileQuery,
    GetApiReceptionGetListFileQueryVariables
  >(GetApiReceptionGetListFileDocument, options);
}
export function useGetApiReceptionGetListFileSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceptionGetListFileQuery,
    GetApiReceptionGetListFileQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceptionGetListFileQuery,
    GetApiReceptionGetListFileQueryVariables
  >(GetApiReceptionGetListFileDocument, options);
}
export type GetApiReceptionGetListFileQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetListFileQuery
>;
export type GetApiReceptionGetListFileLazyQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetListFileLazyQuery
>;
export type GetApiReceptionGetListFileSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetListFileSuspenseQuery
>;
export type GetApiReceptionGetListFileQueryResult = Apollo.QueryResult<
  GetApiReceptionGetListFileQuery,
  GetApiReceptionGetListFileQueryVariables
>;
export const PutApiReceptionDeletePatientFileDocument = gql`
  mutation putApiReceptionDeletePatientFile(
    $categoryCd: Int
    $fileId: Int
    $ptId: BigInt
  ) {
    putApiReceptionDeletePatientFile(
      emrCloudApiRequestsReceptionDeletePatientFileRequestInput: {
        categoryCd: $categoryCd
        fileId: $fileId
        ptId: $ptId
      }
    ) {
      data {
        isSuccess
      }
      message
      status
    }
  }
`;
export type PutApiReceptionDeletePatientFileMutationFn =
  Apollo.MutationFunction<
    PutApiReceptionDeletePatientFileMutation,
    PutApiReceptionDeletePatientFileMutationVariables
  >;

/**
 * __usePutApiReceptionDeletePatientFileMutation__
 *
 * To run a mutation, you first call `usePutApiReceptionDeletePatientFileMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePutApiReceptionDeletePatientFileMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [putApiReceptionDeletePatientFileMutation, { data, loading, error }] = usePutApiReceptionDeletePatientFileMutation({
 *   variables: {
 *      categoryCd: // value for 'categoryCd'
 *      fileId: // value for 'fileId'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function usePutApiReceptionDeletePatientFileMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PutApiReceptionDeletePatientFileMutation,
    PutApiReceptionDeletePatientFileMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PutApiReceptionDeletePatientFileMutation,
    PutApiReceptionDeletePatientFileMutationVariables
  >(PutApiReceptionDeletePatientFileDocument, options);
}
export type PutApiReceptionDeletePatientFileMutationHookResult = ReturnType<
  typeof usePutApiReceptionDeletePatientFileMutation
>;
export type PutApiReceptionDeletePatientFileMutationResult =
  Apollo.MutationResult<PutApiReceptionDeletePatientFileMutation>;
export type PutApiReceptionDeletePatientFileMutationOptions =
  Apollo.BaseMutationOptions<
    PutApiReceptionDeletePatientFileMutation,
    PutApiReceptionDeletePatientFileMutationVariables
  >;
export const Subscription_SubscriptionDocument = gql`
  subscription subscription_subscription(
    $limit: Int
    $order_by: [subscription_subscription_order_by!]
    $where: subscription_subscription_bool_exp
  ) {
    subscription_subscription(
      limit: $limit
      where: $where
      order_by: $order_by
    ) {
      function_code
      text
      id
    }
  }
`;

/**
 * __useSubscription_SubscriptionSubscription__
 *
 * To run a query within a React component, call `useSubscription_SubscriptionSubscription` and pass it any options that fit your needs.
 * When your component renders, `useSubscription_SubscriptionSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useSubscription_SubscriptionSubscription({
 *   variables: {
 *      limit: // value for 'limit'
 *      order_by: // value for 'order_by'
 *      where: // value for 'where'
 *   },
 * });
 */
export function useSubscription_SubscriptionSubscription(
  baseOptions?: Apollo.SubscriptionHookOptions<
    Subscription_SubscriptionSubscription,
    Subscription_SubscriptionSubscriptionVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSubscription<
    Subscription_SubscriptionSubscription,
    Subscription_SubscriptionSubscriptionVariables
  >(Subscription_SubscriptionDocument, options);
}
export type Subscription_SubscriptionSubscriptionHookResult = ReturnType<
  typeof useSubscription_SubscriptionSubscription
>;
export type Subscription_SubscriptionSubscriptionResult =
  Apollo.SubscriptionResult<Subscription_SubscriptionSubscription>;
export const GetApiInsuranceMstGetPublicExpenseComboboxDocument = gql`
  query getApiInsuranceMstGetPublicExpenseCombobox(
    $ptId: BigInt
    $sinDate: Int
  ) {
    getApiInsuranceMstGetPublicExpenseCombobox(ptId: $ptId, sinDate: $sinDate) {
      data {
        publicExpenseComboboxModel {
          hokenMstAllData {
            displayTextMaster
            endDate
            futanRate
            hokenEdaNo
            hokenName
            hokenNameCd
            hokenNo
            houbetu
            selectedValueMaster
            startDate
            kaiLimitFutan
            dayLimitFutan
            monthLimitFutan
            isTokusyuNoCheck
            isOtherPrefValid
            isLimitList
            isJyukyusyaNoCheck
            isFutansyaNoCheck
            jyuKyuCheckDigit
            isLimitListSum
            futanKbn
            hokenSName
            hokenSbtKbn
            hokenKohiKbn
            calcSpKbn
            prefNo
            ageEnd
            ageStart
            checkDigit
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiInsuranceMstGetPublicExpenseComboboxQuery__
 *
 * To run a query within a React component, call `useGetApiInsuranceMstGetPublicExpenseComboboxQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiInsuranceMstGetPublicExpenseComboboxQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiInsuranceMstGetPublicExpenseComboboxQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiInsuranceMstGetPublicExpenseComboboxQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiInsuranceMstGetPublicExpenseComboboxQuery,
    GetApiInsuranceMstGetPublicExpenseComboboxQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiInsuranceMstGetPublicExpenseComboboxQuery,
    GetApiInsuranceMstGetPublicExpenseComboboxQueryVariables
  >(GetApiInsuranceMstGetPublicExpenseComboboxDocument, options);
}
export function useGetApiInsuranceMstGetPublicExpenseComboboxLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiInsuranceMstGetPublicExpenseComboboxQuery,
    GetApiInsuranceMstGetPublicExpenseComboboxQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiInsuranceMstGetPublicExpenseComboboxQuery,
    GetApiInsuranceMstGetPublicExpenseComboboxQueryVariables
  >(GetApiInsuranceMstGetPublicExpenseComboboxDocument, options);
}
export function useGetApiInsuranceMstGetPublicExpenseComboboxSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiInsuranceMstGetPublicExpenseComboboxQuery,
    GetApiInsuranceMstGetPublicExpenseComboboxQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiInsuranceMstGetPublicExpenseComboboxQuery,
    GetApiInsuranceMstGetPublicExpenseComboboxQueryVariables
  >(GetApiInsuranceMstGetPublicExpenseComboboxDocument, options);
}
export type GetApiInsuranceMstGetPublicExpenseComboboxQueryHookResult =
  ReturnType<typeof useGetApiInsuranceMstGetPublicExpenseComboboxQuery>;
export type GetApiInsuranceMstGetPublicExpenseComboboxLazyQueryHookResult =
  ReturnType<typeof useGetApiInsuranceMstGetPublicExpenseComboboxLazyQuery>;
export type GetApiInsuranceMstGetPublicExpenseComboboxSuspenseQueryHookResult =
  ReturnType<typeof useGetApiInsuranceMstGetPublicExpenseComboboxSuspenseQuery>;
export type GetApiInsuranceMstGetPublicExpenseComboboxQueryResult =
  Apollo.QueryResult<
    GetApiInsuranceMstGetPublicExpenseComboboxQuery,
    GetApiInsuranceMstGetPublicExpenseComboboxQueryVariables
  >;
export const PostApiPatientInforSaveKohiDocument = gql`
  mutation postApiPatientInforSaveKohi(
    $isConfirmOnline: Boolean
    $kohi: EmrCloudApiResponsesInsuranceKohiInfDtoInput
    $limitList: [DomainModelsMaxMoneyLimitListModelInput]
    $onlineConfirmationHistory: DomainModelsOnlineOnlineConfirmHisDtoInput
    $patientInfo: [DomainModelsPatientInforPatientInforConfirmOnlineDtoInput]
    $ptId: BigInt
    $endDateModel: DomainModelsInsuranceEndDateModelInput
    $ptKyuseiModel: DomainModelsPatientInforPtKyuseiModelInput
  ) {
    postApiPatientInforSaveKohi(
      emrCloudApiRequestsInsuranceSaveKohiRequestInput: {
        isConfirmOnline: $isConfirmOnline
        kohi: $kohi
        limitList: $limitList
        onlineConfirmationHistory: $onlineConfirmationHistory
        patientInfo: $patientInfo
        ptId: $ptId
        endDateModel: $endDateModel
        ptKyuseiModel: $ptKyuseiModel
      }
    ) {
      data {
        kohiId
        status
      }
      message
      status
    }
  }
`;
export type PostApiPatientInforSaveKohiMutationFn = Apollo.MutationFunction<
  PostApiPatientInforSaveKohiMutation,
  PostApiPatientInforSaveKohiMutationVariables
>;

/**
 * __usePostApiPatientInforSaveKohiMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforSaveKohiMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforSaveKohiMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforSaveKohiMutation, { data, loading, error }] = usePostApiPatientInforSaveKohiMutation({
 *   variables: {
 *      isConfirmOnline: // value for 'isConfirmOnline'
 *      kohi: // value for 'kohi'
 *      limitList: // value for 'limitList'
 *      onlineConfirmationHistory: // value for 'onlineConfirmationHistory'
 *      patientInfo: // value for 'patientInfo'
 *      ptId: // value for 'ptId'
 *      endDateModel: // value for 'endDateModel'
 *      ptKyuseiModel: // value for 'ptKyuseiModel'
 *   },
 * });
 */
export function usePostApiPatientInforSaveKohiMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforSaveKohiMutation,
    PostApiPatientInforSaveKohiMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforSaveKohiMutation,
    PostApiPatientInforSaveKohiMutationVariables
  >(PostApiPatientInforSaveKohiDocument, options);
}
export type PostApiPatientInforSaveKohiMutationHookResult = ReturnType<
  typeof usePostApiPatientInforSaveKohiMutation
>;
export type PostApiPatientInforSaveKohiMutationResult =
  Apollo.MutationResult<PostApiPatientInforSaveKohiMutation>;
export type PostApiPatientInforSaveKohiMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforSaveKohiMutation,
    PostApiPatientInforSaveKohiMutationVariables
  >;
export const GetApiPatientInforGetMaxMoneyByPtIdDocument = gql`
  query getApiPatientInforGetMaxMoneyByPtId($hokenId: Int, $ptId: BigInt) {
    getApiPatientInforGetMaxMoneyByPtId(hokenId: $hokenId, ptId: $ptId) {
      data {
        data {
          biko
          code
          futanGaku
          hokenPid
          id
          isDeleted
          kohiId
          raiinNo
          seqNo
          sinDate
          sinDateD
          sinDateM
          sinDateY
          sort
          sortKey
          totalGaku
          totalMoney
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetMaxMoneyByPtIdQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetMaxMoneyByPtIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetMaxMoneyByPtIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetMaxMoneyByPtIdQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiPatientInforGetMaxMoneyByPtIdQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetMaxMoneyByPtIdQuery,
    GetApiPatientInforGetMaxMoneyByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetMaxMoneyByPtIdQuery,
    GetApiPatientInforGetMaxMoneyByPtIdQueryVariables
  >(GetApiPatientInforGetMaxMoneyByPtIdDocument, options);
}
export function useGetApiPatientInforGetMaxMoneyByPtIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetMaxMoneyByPtIdQuery,
    GetApiPatientInforGetMaxMoneyByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetMaxMoneyByPtIdQuery,
    GetApiPatientInforGetMaxMoneyByPtIdQueryVariables
  >(GetApiPatientInforGetMaxMoneyByPtIdDocument, options);
}
export function useGetApiPatientInforGetMaxMoneyByPtIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetMaxMoneyByPtIdQuery,
    GetApiPatientInforGetMaxMoneyByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetMaxMoneyByPtIdQuery,
    GetApiPatientInforGetMaxMoneyByPtIdQueryVariables
  >(GetApiPatientInforGetMaxMoneyByPtIdDocument, options);
}
export type GetApiPatientInforGetMaxMoneyByPtIdQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetMaxMoneyByPtIdQuery
>;
export type GetApiPatientInforGetMaxMoneyByPtIdLazyQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetMaxMoneyByPtIdLazyQuery
>;
export type GetApiPatientInforGetMaxMoneyByPtIdSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetMaxMoneyByPtIdSuspenseQuery>;
export type GetApiPatientInforGetMaxMoneyByPtIdQueryResult = Apollo.QueryResult<
  GetApiPatientInforGetMaxMoneyByPtIdQuery,
  GetApiPatientInforGetMaxMoneyByPtIdQueryVariables
>;
export const GetApiReceptionGetMaxMoneyDataDocument = gql`
  query getApiReceptionGetMaxMoneyData(
    $ptId: BigInt
    $hokenKohiId: Int
    $sinDate: Int
  ) {
    getApiReceptionGetMaxMoneyData(
      ptId: $ptId
      hokenKohiId: $hokenKohiId
      sinDate: $sinDate
    ) {
      data {
        data {
          gendoGaku
          rate
        }
      }
    }
  }
`;

/**
 * __useGetApiReceptionGetMaxMoneyDataQuery__
 *
 * To run a query within a React component, call `useGetApiReceptionGetMaxMoneyDataQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceptionGetMaxMoneyDataQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceptionGetMaxMoneyDataQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      hokenKohiId: // value for 'hokenKohiId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiReceptionGetMaxMoneyDataQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceptionGetMaxMoneyDataQuery,
    GetApiReceptionGetMaxMoneyDataQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceptionGetMaxMoneyDataQuery,
    GetApiReceptionGetMaxMoneyDataQueryVariables
  >(GetApiReceptionGetMaxMoneyDataDocument, options);
}
export function useGetApiReceptionGetMaxMoneyDataLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceptionGetMaxMoneyDataQuery,
    GetApiReceptionGetMaxMoneyDataQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceptionGetMaxMoneyDataQuery,
    GetApiReceptionGetMaxMoneyDataQueryVariables
  >(GetApiReceptionGetMaxMoneyDataDocument, options);
}
export function useGetApiReceptionGetMaxMoneyDataSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceptionGetMaxMoneyDataQuery,
    GetApiReceptionGetMaxMoneyDataQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceptionGetMaxMoneyDataQuery,
    GetApiReceptionGetMaxMoneyDataQueryVariables
  >(GetApiReceptionGetMaxMoneyDataDocument, options);
}
export type GetApiReceptionGetMaxMoneyDataQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetMaxMoneyDataQuery
>;
export type GetApiReceptionGetMaxMoneyDataLazyQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetMaxMoneyDataLazyQuery
>;
export type GetApiReceptionGetMaxMoneyDataSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetMaxMoneyDataSuspenseQuery
>;
export type GetApiReceptionGetMaxMoneyDataQueryResult = Apollo.QueryResult<
  GetApiReceptionGetMaxMoneyDataQuery,
  GetApiReceptionGetMaxMoneyDataQueryVariables
>;
export const GetApiVisitingGetListMappingMemberDocument = gql`
  query getApiVisitingGetListMappingMember(
    $birthday: Int
    $kanaName: String
    $modelNum: Int
    $name: String
    $portalCustomerId: Int
    $ptId: BigInt
    $ptNum: BigInt
    $sex: Int
    $status: Int
    $sinDate: Int
  ) {
    getApiVisitingGetListMappingMember(
      birthday: $birthday
      kanaName: $kanaName
      modelNum: $modelNum
      name: $name
      portalCustomerId: $portalCustomerId
      ptId: $ptId
      ptNum: $ptNum
      sex: $sex
      status: $status
      sinDate: $sinDate
    ) {
      data {
        mappingMemberModels {
          birthday
          isBirthDayEqual
          isKanaNameEqual
          isNameEqual
          isSexEqual
          kanaName
          name
          onlineConfirmationHistoryId
          portalCustomerId
          ptId
          ptNum
          raiinNo
          sex
        }
      }
    }
  }
`;

/**
 * __useGetApiVisitingGetListMappingMemberQuery__
 *
 * To run a query within a React component, call `useGetApiVisitingGetListMappingMemberQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiVisitingGetListMappingMemberQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiVisitingGetListMappingMemberQuery({
 *   variables: {
 *      birthday: // value for 'birthday'
 *      kanaName: // value for 'kanaName'
 *      modelNum: // value for 'modelNum'
 *      name: // value for 'name'
 *      portalCustomerId: // value for 'portalCustomerId'
 *      ptId: // value for 'ptId'
 *      ptNum: // value for 'ptNum'
 *      sex: // value for 'sex'
 *      status: // value for 'status'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiVisitingGetListMappingMemberQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiVisitingGetListMappingMemberQuery,
    GetApiVisitingGetListMappingMemberQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiVisitingGetListMappingMemberQuery,
    GetApiVisitingGetListMappingMemberQueryVariables
  >(GetApiVisitingGetListMappingMemberDocument, options);
}
export function useGetApiVisitingGetListMappingMemberLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiVisitingGetListMappingMemberQuery,
    GetApiVisitingGetListMappingMemberQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiVisitingGetListMappingMemberQuery,
    GetApiVisitingGetListMappingMemberQueryVariables
  >(GetApiVisitingGetListMappingMemberDocument, options);
}
export function useGetApiVisitingGetListMappingMemberSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiVisitingGetListMappingMemberQuery,
    GetApiVisitingGetListMappingMemberQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiVisitingGetListMappingMemberQuery,
    GetApiVisitingGetListMappingMemberQueryVariables
  >(GetApiVisitingGetListMappingMemberDocument, options);
}
export type GetApiVisitingGetListMappingMemberQueryHookResult = ReturnType<
  typeof useGetApiVisitingGetListMappingMemberQuery
>;
export type GetApiVisitingGetListMappingMemberLazyQueryHookResult = ReturnType<
  typeof useGetApiVisitingGetListMappingMemberLazyQuery
>;
export type GetApiVisitingGetListMappingMemberSuspenseQueryHookResult =
  ReturnType<typeof useGetApiVisitingGetListMappingMemberSuspenseQuery>;
export type GetApiVisitingGetListMappingMemberQueryResult = Apollo.QueryResult<
  GetApiVisitingGetListMappingMemberQuery,
  GetApiVisitingGetListMappingMemberQueryVariables
>;
export const PostApiVisitingUpdateMappingMemberDocument = gql`
  mutation postApiVisitingUpdateMappingMember(
    $aiChartPtId: BigInt
    $hospitalArrivalStatus: Int
    $onlineConfirmHisId: Int
    $portalPtId: BigInt
    $userId: Int
    $sinDate: Int
  ) {
    postApiVisitingUpdateMappingMember(
      emrCloudApiRequestsMappingMemberUpdateMappingMemberRequestInput: {
        aiChartPtId: $aiChartPtId
        portalPtId: $portalPtId
        userId: $userId
        onlineConfirmHisId: $onlineConfirmHisId
        hospitalArrivalStatus: $hospitalArrivalStatus
        sinDate: $sinDate
      }
    ) {
      data {
        updateMappingModel {
          deletedRaiinNoList
          isContinue
          isSucces
        }
      }
      message
      status
    }
  }
`;
export type PostApiVisitingUpdateMappingMemberMutationFn =
  Apollo.MutationFunction<
    PostApiVisitingUpdateMappingMemberMutation,
    PostApiVisitingUpdateMappingMemberMutationVariables
  >;

/**
 * __usePostApiVisitingUpdateMappingMemberMutation__
 *
 * To run a mutation, you first call `usePostApiVisitingUpdateMappingMemberMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiVisitingUpdateMappingMemberMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiVisitingUpdateMappingMemberMutation, { data, loading, error }] = usePostApiVisitingUpdateMappingMemberMutation({
 *   variables: {
 *      aiChartPtId: // value for 'aiChartPtId'
 *      hospitalArrivalStatus: // value for 'hospitalArrivalStatus'
 *      onlineConfirmHisId: // value for 'onlineConfirmHisId'
 *      portalPtId: // value for 'portalPtId'
 *      userId: // value for 'userId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function usePostApiVisitingUpdateMappingMemberMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiVisitingUpdateMappingMemberMutation,
    PostApiVisitingUpdateMappingMemberMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiVisitingUpdateMappingMemberMutation,
    PostApiVisitingUpdateMappingMemberMutationVariables
  >(PostApiVisitingUpdateMappingMemberDocument, options);
}
export type PostApiVisitingUpdateMappingMemberMutationHookResult = ReturnType<
  typeof usePostApiVisitingUpdateMappingMemberMutation
>;
export type PostApiVisitingUpdateMappingMemberMutationResult =
  Apollo.MutationResult<PostApiVisitingUpdateMappingMemberMutation>;
export type PostApiVisitingUpdateMappingMemberMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiVisitingUpdateMappingMemberMutation,
    PostApiVisitingUpdateMappingMemberMutationVariables
  >;
export const GetApiReceptionGetLastRaiinInfsDocument = gql`
  query getApiReceptionGetLastRaiinInfs(
    $ptId: BigInt
    $sinDate: Int
    $isLastVisit: Boolean
  ) {
    getApiReceptionGetLastRaiinInfs(
      ptId: $ptId
      sinDate: $sinDate
      isLastVisit: $isLastVisit
    ) {
      data {
        data {
          hokenId
          hokenKbn
          hokenKbnName
          hokenPid
          houbetu
          hokensyaNo
          hpId
          isDeleted
          isYoyaku
          jikanKbn
          kaId
          kaSname
          kaikeiId
          kaikeiTime
          oyaRaiinNo
          ptId
          raiinNo
          sName
          santeiKbn
          sinDate
          sinEndTime
          sinStartTime
          status
          syosaisinKbn
          tantoId
          uketukeId
          uketukeNo
          uketukeTime
          yoyakuId
          yoyakuTime
          comment
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiReceptionGetLastRaiinInfsQuery__
 *
 * To run a query within a React component, call `useGetApiReceptionGetLastRaiinInfsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceptionGetLastRaiinInfsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceptionGetLastRaiinInfsQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      isLastVisit: // value for 'isLastVisit'
 *   },
 * });
 */
export function useGetApiReceptionGetLastRaiinInfsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceptionGetLastRaiinInfsQuery,
    GetApiReceptionGetLastRaiinInfsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceptionGetLastRaiinInfsQuery,
    GetApiReceptionGetLastRaiinInfsQueryVariables
  >(GetApiReceptionGetLastRaiinInfsDocument, options);
}
export function useGetApiReceptionGetLastRaiinInfsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceptionGetLastRaiinInfsQuery,
    GetApiReceptionGetLastRaiinInfsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceptionGetLastRaiinInfsQuery,
    GetApiReceptionGetLastRaiinInfsQueryVariables
  >(GetApiReceptionGetLastRaiinInfsDocument, options);
}
export function useGetApiReceptionGetLastRaiinInfsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceptionGetLastRaiinInfsQuery,
    GetApiReceptionGetLastRaiinInfsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceptionGetLastRaiinInfsQuery,
    GetApiReceptionGetLastRaiinInfsQueryVariables
  >(GetApiReceptionGetLastRaiinInfsDocument, options);
}
export type GetApiReceptionGetLastRaiinInfsQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetLastRaiinInfsQuery
>;
export type GetApiReceptionGetLastRaiinInfsLazyQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetLastRaiinInfsLazyQuery
>;
export type GetApiReceptionGetLastRaiinInfsSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetLastRaiinInfsSuspenseQuery
>;
export type GetApiReceptionGetLastRaiinInfsQueryResult = Apollo.QueryResult<
  GetApiReceptionGetLastRaiinInfsQuery,
  GetApiReceptionGetLastRaiinInfsQueryVariables
>;
export const DeleteApiOnlineDeletedOnlineConfirmationDocument = gql`
  mutation deleteApiOnlineDeletedOnlineConfirmation(
    $confirmationOnlineHisId: BigInt
  ) {
    deleteApiOnlineDeletedOnlineConfirmation(
      confirmationOnlineHisId: $confirmationOnlineHisId
    ) {
      data {
        isSuccess
        status
      }
      message
      status
    }
  }
`;
export type DeleteApiOnlineDeletedOnlineConfirmationMutationFn =
  Apollo.MutationFunction<
    DeleteApiOnlineDeletedOnlineConfirmationMutation,
    DeleteApiOnlineDeletedOnlineConfirmationMutationVariables
  >;

/**
 * __useDeleteApiOnlineDeletedOnlineConfirmationMutation__
 *
 * To run a mutation, you first call `useDeleteApiOnlineDeletedOnlineConfirmationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteApiOnlineDeletedOnlineConfirmationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteApiOnlineDeletedOnlineConfirmationMutation, { data, loading, error }] = useDeleteApiOnlineDeletedOnlineConfirmationMutation({
 *   variables: {
 *      confirmationOnlineHisId: // value for 'confirmationOnlineHisId'
 *   },
 * });
 */
export function useDeleteApiOnlineDeletedOnlineConfirmationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteApiOnlineDeletedOnlineConfirmationMutation,
    DeleteApiOnlineDeletedOnlineConfirmationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteApiOnlineDeletedOnlineConfirmationMutation,
    DeleteApiOnlineDeletedOnlineConfirmationMutationVariables
  >(DeleteApiOnlineDeletedOnlineConfirmationDocument, options);
}
export type DeleteApiOnlineDeletedOnlineConfirmationMutationHookResult =
  ReturnType<typeof useDeleteApiOnlineDeletedOnlineConfirmationMutation>;
export type DeleteApiOnlineDeletedOnlineConfirmationMutationResult =
  Apollo.MutationResult<DeleteApiOnlineDeletedOnlineConfirmationMutation>;
export type DeleteApiOnlineDeletedOnlineConfirmationMutationOptions =
  Apollo.BaseMutationOptions<
    DeleteApiOnlineDeletedOnlineConfirmationMutation,
    DeleteApiOnlineDeletedOnlineConfirmationMutationVariables
  >;
export const PostApiReceptionUpdatePrescriptionDocument = gql`
  mutation postApiReceptionUpdatePrescription(
    $prescriptionIssueType: Int
    $printEpsReference: Int
    $raiinNo: BigInt
    $checkStatus: Boolean
  ) {
    postApiReceptionUpdatePrescription(
      emrCloudApiRequestsReceptionUpdatePrescriptionRequestInput: {
        prescriptionIssueType: $prescriptionIssueType
        printEpsReference: $printEpsReference
        raiinNo: $raiinNo
        checkStatus: $checkStatus
      }
    ) {
      data {
        isSuccess
        status
      }
    }
  }
`;
export type PostApiReceptionUpdatePrescriptionMutationFn =
  Apollo.MutationFunction<
    PostApiReceptionUpdatePrescriptionMutation,
    PostApiReceptionUpdatePrescriptionMutationVariables
  >;

/**
 * __usePostApiReceptionUpdatePrescriptionMutation__
 *
 * To run a mutation, you first call `usePostApiReceptionUpdatePrescriptionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceptionUpdatePrescriptionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceptionUpdatePrescriptionMutation, { data, loading, error }] = usePostApiReceptionUpdatePrescriptionMutation({
 *   variables: {
 *      prescriptionIssueType: // value for 'prescriptionIssueType'
 *      printEpsReference: // value for 'printEpsReference'
 *      raiinNo: // value for 'raiinNo'
 *      checkStatus: // value for 'checkStatus'
 *   },
 * });
 */
export function usePostApiReceptionUpdatePrescriptionMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceptionUpdatePrescriptionMutation,
    PostApiReceptionUpdatePrescriptionMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceptionUpdatePrescriptionMutation,
    PostApiReceptionUpdatePrescriptionMutationVariables
  >(PostApiReceptionUpdatePrescriptionDocument, options);
}
export type PostApiReceptionUpdatePrescriptionMutationHookResult = ReturnType<
  typeof usePostApiReceptionUpdatePrescriptionMutation
>;
export type PostApiReceptionUpdatePrescriptionMutationResult =
  Apollo.MutationResult<PostApiReceptionUpdatePrescriptionMutation>;
export type PostApiReceptionUpdatePrescriptionMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceptionUpdatePrescriptionMutation,
    PostApiReceptionUpdatePrescriptionMutationVariables
  >;
export const GetApiReceptionCheckLinkCardDocument = gql`
  query getApiReceptionCheckLinkCard {
    getApiReceptionCheckLinkCard {
      data {
        isLinkCard
      }
    }
  }
`;

/**
 * __useGetApiReceptionCheckLinkCardQuery__
 *
 * To run a query within a React component, call `useGetApiReceptionCheckLinkCardQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceptionCheckLinkCardQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceptionCheckLinkCardQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiReceptionCheckLinkCardQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceptionCheckLinkCardQuery,
    GetApiReceptionCheckLinkCardQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceptionCheckLinkCardQuery,
    GetApiReceptionCheckLinkCardQueryVariables
  >(GetApiReceptionCheckLinkCardDocument, options);
}
export function useGetApiReceptionCheckLinkCardLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceptionCheckLinkCardQuery,
    GetApiReceptionCheckLinkCardQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceptionCheckLinkCardQuery,
    GetApiReceptionCheckLinkCardQueryVariables
  >(GetApiReceptionCheckLinkCardDocument, options);
}
export function useGetApiReceptionCheckLinkCardSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceptionCheckLinkCardQuery,
    GetApiReceptionCheckLinkCardQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceptionCheckLinkCardQuery,
    GetApiReceptionCheckLinkCardQueryVariables
  >(GetApiReceptionCheckLinkCardDocument, options);
}
export type GetApiReceptionCheckLinkCardQueryHookResult = ReturnType<
  typeof useGetApiReceptionCheckLinkCardQuery
>;
export type GetApiReceptionCheckLinkCardLazyQueryHookResult = ReturnType<
  typeof useGetApiReceptionCheckLinkCardLazyQuery
>;
export type GetApiReceptionCheckLinkCardSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceptionCheckLinkCardSuspenseQuery
>;
export type GetApiReceptionCheckLinkCardQueryResult = Apollo.QueryResult<
  GetApiReceptionCheckLinkCardQuery,
  GetApiReceptionCheckLinkCardQueryVariables
>;
export const GetApiReceptionValidateReservationDocument = gql`
  query getApiReceptionValidateReservation(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiReceptionValidateReservation(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        success
      }
    }
  }
`;

/**
 * __useGetApiReceptionValidateReservationQuery__
 *
 * To run a query within a React component, call `useGetApiReceptionValidateReservationQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceptionValidateReservationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceptionValidateReservationQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiReceptionValidateReservationQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceptionValidateReservationQuery,
    GetApiReceptionValidateReservationQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceptionValidateReservationQuery,
    GetApiReceptionValidateReservationQueryVariables
  >(GetApiReceptionValidateReservationDocument, options);
}
export function useGetApiReceptionValidateReservationLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceptionValidateReservationQuery,
    GetApiReceptionValidateReservationQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceptionValidateReservationQuery,
    GetApiReceptionValidateReservationQueryVariables
  >(GetApiReceptionValidateReservationDocument, options);
}
export function useGetApiReceptionValidateReservationSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceptionValidateReservationQuery,
    GetApiReceptionValidateReservationQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceptionValidateReservationQuery,
    GetApiReceptionValidateReservationQueryVariables
  >(GetApiReceptionValidateReservationDocument, options);
}
export type GetApiReceptionValidateReservationQueryHookResult = ReturnType<
  typeof useGetApiReceptionValidateReservationQuery
>;
export type GetApiReceptionValidateReservationLazyQueryHookResult = ReturnType<
  typeof useGetApiReceptionValidateReservationLazyQuery
>;
export type GetApiReceptionValidateReservationSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceptionValidateReservationSuspenseQuery>;
export type GetApiReceptionValidateReservationQueryResult = Apollo.QueryResult<
  GetApiReceptionValidateReservationQuery,
  GetApiReceptionValidateReservationQueryVariables
>;
export const PostApiReceptionBookingFromCalendarOrPortalDocument = gql`
  mutation postApiReceptionBookingFromCalendarOrPortal(
    $input: EmrCloudApiRequestsReceptionBookingFromCalendarOrPortalRequestInput
  ) {
    postApiReceptionBookingFromCalendarOrPortal(
      emrCloudApiRequestsReceptionBookingFromCalendarOrPortalRequestInput: $input
    ) {
      data {
        raiinNo
      }
      message
      status
    }
  }
`;
export type PostApiReceptionBookingFromCalendarOrPortalMutationFn =
  Apollo.MutationFunction<
    PostApiReceptionBookingFromCalendarOrPortalMutation,
    PostApiReceptionBookingFromCalendarOrPortalMutationVariables
  >;

/**
 * __usePostApiReceptionBookingFromCalendarOrPortalMutation__
 *
 * To run a mutation, you first call `usePostApiReceptionBookingFromCalendarOrPortalMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceptionBookingFromCalendarOrPortalMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceptionBookingFromCalendarOrPortalMutation, { data, loading, error }] = usePostApiReceptionBookingFromCalendarOrPortalMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceptionBookingFromCalendarOrPortalMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceptionBookingFromCalendarOrPortalMutation,
    PostApiReceptionBookingFromCalendarOrPortalMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceptionBookingFromCalendarOrPortalMutation,
    PostApiReceptionBookingFromCalendarOrPortalMutationVariables
  >(PostApiReceptionBookingFromCalendarOrPortalDocument, options);
}
export type PostApiReceptionBookingFromCalendarOrPortalMutationHookResult =
  ReturnType<typeof usePostApiReceptionBookingFromCalendarOrPortalMutation>;
export type PostApiReceptionBookingFromCalendarOrPortalMutationResult =
  Apollo.MutationResult<PostApiReceptionBookingFromCalendarOrPortalMutation>;
export type PostApiReceptionBookingFromCalendarOrPortalMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceptionBookingFromCalendarOrPortalMutation,
    PostApiReceptionBookingFromCalendarOrPortalMutationVariables
  >;
