import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiKaGetListMstQueryVariables = Types.Exact<{
  isDeleted?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiKaGetListMstQuery = {
  __typename?: "query_root";
  getApiKaGetListMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKaGetKaMstListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesKaGetKaMstListResponse";
      departments?: Array<{
        __typename?: "DomainModelsKaKaMstModel";
        id?: string;
        kaId?: number;
        sortNo?: number;
        receKaCd?: string;
        kaSname?: string;
        kaName?: string;
        yousikiKaCd?: string;
      }>;
    };
  };
};

export type GetApiKaGetListKaCodeQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiKaGetListKaCodeQuery = {
  __typename?: "query_root";
  getApiKaGetListKaCode?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKaGetKaCodeMstListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKaGetKaCodeMstListResponse";
      kaCodeMstModels?: Array<{
        __typename?: "DomainModelsKaKaCodeMstModel";
        kaSname?: string;
        receKaCd?: string;
        receYousikiKaCd?: string;
        sortNo?: number;
      }>;
    };
  };
};

export type GetApiKaGetKaCodeYousikiMstQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiKaGetKaCodeYousikiMstQuery = {
  __typename?: "query_root";
  getApiKaGetKaCodeYousikiMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKaGetKaCodeYousikiMstResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKaGetKaCodeYousikiMstResponse";
      kacodeYousikiMstModels?: Array<{
        __typename?: "DomainModelsKaKacodeYousikiMstModel";
        displayYousikiKaCd?: string;
        kaName?: string;
        yousikiKaCd?: string;
        sortNo?: number;
      }>;
    };
  };
};

export type PostApiKaSaveListKaMstMutationVariables = Types.Exact<{
  emrCloudApiRequestsKaSaveListKaMstRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsKaSaveListKaMstRequestInput>;
}>;

export type PostApiKaSaveListKaMstMutation = {
  __typename?: "mutation_root";
  postApiKaSaveListKaMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKaSaveListKaMstResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKaSaveListKaMstResponse";
      status?: boolean;
    };
  };
};

export const GetApiKaGetListMstDocument = gql`
  query getApiKaGetListMst($isDeleted: Int) {
    getApiKaGetListMst(isDeleted: $isDeleted) {
      data {
        departments {
          id
          kaId
          sortNo
          receKaCd
          kaSname
          kaName
          yousikiKaCd
        }
      }
    }
  }
`;

/**
 * __useGetApiKaGetListMstQuery__
 *
 * To run a query within a React component, call `useGetApiKaGetListMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKaGetListMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKaGetListMstQuery({
 *   variables: {
 *      isDeleted: // value for 'isDeleted'
 *   },
 * });
 */
export function useGetApiKaGetListMstQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiKaGetListMstQuery,
    GetApiKaGetListMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKaGetListMstQuery,
    GetApiKaGetListMstQueryVariables
  >(GetApiKaGetListMstDocument, options);
}
export function useGetApiKaGetListMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKaGetListMstQuery,
    GetApiKaGetListMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKaGetListMstQuery,
    GetApiKaGetListMstQueryVariables
  >(GetApiKaGetListMstDocument, options);
}
export function useGetApiKaGetListMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKaGetListMstQuery,
    GetApiKaGetListMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKaGetListMstQuery,
    GetApiKaGetListMstQueryVariables
  >(GetApiKaGetListMstDocument, options);
}
export type GetApiKaGetListMstQueryHookResult = ReturnType<
  typeof useGetApiKaGetListMstQuery
>;
export type GetApiKaGetListMstLazyQueryHookResult = ReturnType<
  typeof useGetApiKaGetListMstLazyQuery
>;
export type GetApiKaGetListMstSuspenseQueryHookResult = ReturnType<
  typeof useGetApiKaGetListMstSuspenseQuery
>;
export type GetApiKaGetListMstQueryResult = Apollo.QueryResult<
  GetApiKaGetListMstQuery,
  GetApiKaGetListMstQueryVariables
>;
export const GetApiKaGetListKaCodeDocument = gql`
  query getApiKaGetListKaCode {
    getApiKaGetListKaCode {
      data {
        kaCodeMstModels {
          kaSname
          receKaCd
          receYousikiKaCd
          sortNo
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiKaGetListKaCodeQuery__
 *
 * To run a query within a React component, call `useGetApiKaGetListKaCodeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKaGetListKaCodeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKaGetListKaCodeQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiKaGetListKaCodeQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiKaGetListKaCodeQuery,
    GetApiKaGetListKaCodeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKaGetListKaCodeQuery,
    GetApiKaGetListKaCodeQueryVariables
  >(GetApiKaGetListKaCodeDocument, options);
}
export function useGetApiKaGetListKaCodeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKaGetListKaCodeQuery,
    GetApiKaGetListKaCodeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKaGetListKaCodeQuery,
    GetApiKaGetListKaCodeQueryVariables
  >(GetApiKaGetListKaCodeDocument, options);
}
export function useGetApiKaGetListKaCodeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKaGetListKaCodeQuery,
    GetApiKaGetListKaCodeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKaGetListKaCodeQuery,
    GetApiKaGetListKaCodeQueryVariables
  >(GetApiKaGetListKaCodeDocument, options);
}
export type GetApiKaGetListKaCodeQueryHookResult = ReturnType<
  typeof useGetApiKaGetListKaCodeQuery
>;
export type GetApiKaGetListKaCodeLazyQueryHookResult = ReturnType<
  typeof useGetApiKaGetListKaCodeLazyQuery
>;
export type GetApiKaGetListKaCodeSuspenseQueryHookResult = ReturnType<
  typeof useGetApiKaGetListKaCodeSuspenseQuery
>;
export type GetApiKaGetListKaCodeQueryResult = Apollo.QueryResult<
  GetApiKaGetListKaCodeQuery,
  GetApiKaGetListKaCodeQueryVariables
>;
export const GetApiKaGetKaCodeYousikiMstDocument = gql`
  query getApiKaGetKaCodeYousikiMst {
    getApiKaGetKaCodeYousikiMst {
      data {
        kacodeYousikiMstModels {
          displayYousikiKaCd
          kaName
          yousikiKaCd
          sortNo
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiKaGetKaCodeYousikiMstQuery__
 *
 * To run a query within a React component, call `useGetApiKaGetKaCodeYousikiMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKaGetKaCodeYousikiMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKaGetKaCodeYousikiMstQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiKaGetKaCodeYousikiMstQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiKaGetKaCodeYousikiMstQuery,
    GetApiKaGetKaCodeYousikiMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKaGetKaCodeYousikiMstQuery,
    GetApiKaGetKaCodeYousikiMstQueryVariables
  >(GetApiKaGetKaCodeYousikiMstDocument, options);
}
export function useGetApiKaGetKaCodeYousikiMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKaGetKaCodeYousikiMstQuery,
    GetApiKaGetKaCodeYousikiMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKaGetKaCodeYousikiMstQuery,
    GetApiKaGetKaCodeYousikiMstQueryVariables
  >(GetApiKaGetKaCodeYousikiMstDocument, options);
}
export function useGetApiKaGetKaCodeYousikiMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKaGetKaCodeYousikiMstQuery,
    GetApiKaGetKaCodeYousikiMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKaGetKaCodeYousikiMstQuery,
    GetApiKaGetKaCodeYousikiMstQueryVariables
  >(GetApiKaGetKaCodeYousikiMstDocument, options);
}
export type GetApiKaGetKaCodeYousikiMstQueryHookResult = ReturnType<
  typeof useGetApiKaGetKaCodeYousikiMstQuery
>;
export type GetApiKaGetKaCodeYousikiMstLazyQueryHookResult = ReturnType<
  typeof useGetApiKaGetKaCodeYousikiMstLazyQuery
>;
export type GetApiKaGetKaCodeYousikiMstSuspenseQueryHookResult = ReturnType<
  typeof useGetApiKaGetKaCodeYousikiMstSuspenseQuery
>;
export type GetApiKaGetKaCodeYousikiMstQueryResult = Apollo.QueryResult<
  GetApiKaGetKaCodeYousikiMstQuery,
  GetApiKaGetKaCodeYousikiMstQueryVariables
>;
export const PostApiKaSaveListKaMstDocument = gql`
  mutation postApiKaSaveListKaMst(
    $emrCloudApiRequestsKaSaveListKaMstRequestInput: EmrCloudApiRequestsKaSaveListKaMstRequestInput
  ) {
    postApiKaSaveListKaMst(
      emrCloudApiRequestsKaSaveListKaMstRequestInput: $emrCloudApiRequestsKaSaveListKaMstRequestInput
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiKaSaveListKaMstMutationFn = Apollo.MutationFunction<
  PostApiKaSaveListKaMstMutation,
  PostApiKaSaveListKaMstMutationVariables
>;

/**
 * __usePostApiKaSaveListKaMstMutation__
 *
 * To run a mutation, you first call `usePostApiKaSaveListKaMstMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKaSaveListKaMstMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKaSaveListKaMstMutation, { data, loading, error }] = usePostApiKaSaveListKaMstMutation({
 *   variables: {
 *      emrCloudApiRequestsKaSaveListKaMstRequestInput: // value for 'emrCloudApiRequestsKaSaveListKaMstRequestInput'
 *   },
 * });
 */
export function usePostApiKaSaveListKaMstMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKaSaveListKaMstMutation,
    PostApiKaSaveListKaMstMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKaSaveListKaMstMutation,
    PostApiKaSaveListKaMstMutationVariables
  >(PostApiKaSaveListKaMstDocument, options);
}
export type PostApiKaSaveListKaMstMutationHookResult = ReturnType<
  typeof usePostApiKaSaveListKaMstMutation
>;
export type PostApiKaSaveListKaMstMutationResult =
  Apollo.MutationResult<PostApiKaSaveListKaMstMutation>;
export type PostApiKaSaveListKaMstMutationOptions = Apollo.BaseMutationOptions<
  PostApiKaSaveListKaMstMutation,
  PostApiKaSaveListKaMstMutationVariables
>;
