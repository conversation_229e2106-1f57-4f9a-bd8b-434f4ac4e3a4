import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiDiseasesGetListQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  requestFrom?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isContiFiltered?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isInMonthFiltered?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiDiseasesGetListQuery = {
  __typename?: "query_root";
  getApiDiseasesGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDiseasesGetPtDiseaseListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesDiseasesGetPtDiseaseListResponse";
      diseaseList?: Array<{
        __typename?: "DomainModelsDiseasesPtDiseaseModel";
        isFreeWord?: boolean;
        isTenki?: boolean;
        isContinous?: boolean;
        isInMonth?: boolean;
        hokenId?: number;
        hokenPid?: number;
        icd10?: string;
        icd102013?: string;
        icd1012013?: string;
        icd1022013?: string;
        hpId?: number;
        ptId?: string;
        seqNo?: string;
        byomeiCd?: string;
        sortNo?: number;
        byomei?: string;
        isSuspect?: number;
        startDate?: number;
        tenkiKbn?: number;
        tenkiDate?: number;
        syubyoKbn?: number;
        sikkanKbn?: number;
        nanbyoCd?: number;
        isNodspRece?: number;
        isNodspKarte?: number;
        isDeleted?: number;
        id?: string;
        isImportant?: number;
        sinDate?: number;
        hosokuCmt?: string;
        togetuByomei?: number;
        sikkanCd?: number;
        delDate?: number;
        itemCd?: string;
        isAdopted?: boolean;
        createUser?: string;
        createDate?: string;
        updateDate?: string;
        updateUser?: string;
        fullByomei?: string;
        isDspRece?: boolean;
        receTenkiKbn?: number;
        tenKiBinding?: string;
        isFree?: boolean;
        isMain?: boolean;
        byomeiHankToZen?: string;
        prefixSuffixList?: Array<{
          __typename?: "DomainModelsDiseasesPrefixSuffixModel";
          code?: string;
          name?: string;
        }>;
      }>;
    };
  };
};

export type PostApiDiseasesUpsertMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsDiseasesUpsertPtDiseaseListRequestInput>;
}>;

export type PostApiDiseasesUpsertMutation = {
  __typename?: "mutation_root";
  postApiDiseasesUpsert?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDiseasesUpsertPtDiseaseListResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesDiseasesUpsertPtDiseaseListResponse";
      ids?: Array<string>;
    };
  };
};

export type PostApiMedicalExaminationGetCheckDiseasesMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationGetCheckDiseaseRequestInput>;
  }>;

export type PostApiMedicalExaminationGetCheckDiseasesMutation = {
  __typename?: "mutation_root";
  postApiMedicalExaminationGetCheckDiseases?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationGetCheckDiseaseResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationGetCheckDiseaseResponse";
      checkDiseaseItemOutputDatas?: Array<{
        __typename?: "UseCaseMedicalExaminationGetCheckDiseaseGetCheckDiseaseItemOutputData";
        itemCd?: string;
        itemName?: string;
        checkedDiseaseItems?: Array<{
          __typename?: "UseCaseMedicalExaminationGetCheckDiseaseCheckedDiseaseItem";
          byomei?: string;
          isAdopted?: boolean;
          nanByoCd?: number;
          odrItemNo?: number;
          sikkanCd?: number;
          byomeiMst?: {
            __typename?: "UseCaseMedicalExaminationGetCheckDiseaseByomeiItem";
            byomeiCd?: string;
            byomeiType?: string;
            icd10?: string;
            nanByo?: string;
            sbyomei?: string;
            isAdopted?: boolean;
            sikkan?: string;
          };
          ptDisease?: {
            __typename?: "UseCaseMedicalExaminationGetCheckDiseasePtDiseaseItem";
            byomei?: string;
            byomeiCd?: string;
            hokenPid?: number;
            hosokuCmt?: string;
            hpId?: number;
            isDeleted?: number;
            id?: string;
            nanbyoCd?: number;
            isTenki?: boolean;
            isSuspect?: number;
            ptId?: string;
            seqNo?: string;
            sikkanKbn?: number;
            sinDate?: number;
            sortNo?: number;
            startDate?: number;
            syubyoKbn?: number;
            tenkiDate?: number;
            tenkiKbn?: number;
            prefixSuffixList?: Array<{
              __typename?: "DomainModelsDiseasesPrefixSuffixModel";
              code?: string;
              name?: string;
            }>;
          };
        }>;
      }>;
    };
  };
};

export const GetApiDiseasesGetListDocument = gql`
  query getApiDiseasesGetList(
    $ptId: BigInt
    $sinDate: Int
    $hokenId: Int
    $requestFrom: Int
    $isContiFiltered: Boolean
    $isInMonthFiltered: Boolean
  ) {
    getApiDiseasesGetList(
      ptId: $ptId
      sinDate: $sinDate
      hokenId: $hokenId
      requestFrom: $requestFrom
      isContiFiltered: $isContiFiltered
      isInMonthFiltered: $isInMonthFiltered
    ) {
      data {
        diseaseList {
          isFreeWord
          isTenki
          isContinous
          isInMonth
          hokenId
          hokenPid
          icd10
          icd102013
          icd1012013
          icd1022013
          hpId
          ptId
          seqNo
          byomeiCd
          sortNo
          prefixSuffixList {
            code
            name
          }
          byomei
          isSuspect
          startDate
          tenkiKbn
          tenkiDate
          syubyoKbn
          sikkanKbn
          nanbyoCd
          isNodspRece
          isNodspKarte
          isDeleted
          id
          isImportant
          sinDate
          hosokuCmt
          togetuByomei
          sikkanCd
          delDate
          itemCd
          isAdopted
          createUser
          createDate
          updateDate
          updateUser
          fullByomei
          isDspRece
          receTenkiKbn
          tenKiBinding
          isFree
          isMain
          byomeiHankToZen
        }
      }
    }
  }
`;

/**
 * __useGetApiDiseasesGetListQuery__
 *
 * To run a query within a React component, call `useGetApiDiseasesGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiDiseasesGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiDiseasesGetListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      hokenId: // value for 'hokenId'
 *      requestFrom: // value for 'requestFrom'
 *      isContiFiltered: // value for 'isContiFiltered'
 *      isInMonthFiltered: // value for 'isInMonthFiltered'
 *   },
 * });
 */
export function useGetApiDiseasesGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiDiseasesGetListQuery,
    GetApiDiseasesGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiDiseasesGetListQuery,
    GetApiDiseasesGetListQueryVariables
  >(GetApiDiseasesGetListDocument, options);
}
export function useGetApiDiseasesGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiDiseasesGetListQuery,
    GetApiDiseasesGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiDiseasesGetListQuery,
    GetApiDiseasesGetListQueryVariables
  >(GetApiDiseasesGetListDocument, options);
}
export function useGetApiDiseasesGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiDiseasesGetListQuery,
    GetApiDiseasesGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiDiseasesGetListQuery,
    GetApiDiseasesGetListQueryVariables
  >(GetApiDiseasesGetListDocument, options);
}
export type GetApiDiseasesGetListQueryHookResult = ReturnType<
  typeof useGetApiDiseasesGetListQuery
>;
export type GetApiDiseasesGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiDiseasesGetListLazyQuery
>;
export type GetApiDiseasesGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiDiseasesGetListSuspenseQuery
>;
export type GetApiDiseasesGetListQueryResult = Apollo.QueryResult<
  GetApiDiseasesGetListQuery,
  GetApiDiseasesGetListQueryVariables
>;
export const PostApiDiseasesUpsertDocument = gql`
  mutation postApiDiseasesUpsert(
    $input: EmrCloudApiRequestsDiseasesUpsertPtDiseaseListRequestInput
  ) {
    postApiDiseasesUpsert(
      emrCloudApiRequestsDiseasesUpsertPtDiseaseListRequestInput: $input
    ) {
      data {
        ids
      }
      status
      message
    }
  }
`;
export type PostApiDiseasesUpsertMutationFn = Apollo.MutationFunction<
  PostApiDiseasesUpsertMutation,
  PostApiDiseasesUpsertMutationVariables
>;

/**
 * __usePostApiDiseasesUpsertMutation__
 *
 * To run a mutation, you first call `usePostApiDiseasesUpsertMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiDiseasesUpsertMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiDiseasesUpsertMutation, { data, loading, error }] = usePostApiDiseasesUpsertMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiDiseasesUpsertMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiDiseasesUpsertMutation,
    PostApiDiseasesUpsertMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiDiseasesUpsertMutation,
    PostApiDiseasesUpsertMutationVariables
  >(PostApiDiseasesUpsertDocument, options);
}
export type PostApiDiseasesUpsertMutationHookResult = ReturnType<
  typeof usePostApiDiseasesUpsertMutation
>;
export type PostApiDiseasesUpsertMutationResult =
  Apollo.MutationResult<PostApiDiseasesUpsertMutation>;
export type PostApiDiseasesUpsertMutationOptions = Apollo.BaseMutationOptions<
  PostApiDiseasesUpsertMutation,
  PostApiDiseasesUpsertMutationVariables
>;
export const PostApiMedicalExaminationGetCheckDiseasesDocument = gql`
  mutation postApiMedicalExaminationGetCheckDiseases(
    $input: EmrCloudApiRequestsMedicalExaminationGetCheckDiseaseRequestInput
  ) {
    postApiMedicalExaminationGetCheckDiseases(
      emrCloudApiRequestsMedicalExaminationGetCheckDiseaseRequestInput: $input
    ) {
      data {
        checkDiseaseItemOutputDatas {
          checkedDiseaseItems {
            byomei
            byomeiMst {
              byomeiCd
              byomeiType
              icd10
              nanByo
              sbyomei
              isAdopted
              sikkan
            }
            isAdopted
            nanByoCd
            odrItemNo
            sikkanCd
            ptDisease {
              byomei
              byomeiCd
              hokenPid
              hosokuCmt
              hpId
              isDeleted
              id
              nanbyoCd
              isTenki
              isSuspect
              prefixSuffixList {
                code
                name
              }
              ptId
              seqNo
              sikkanKbn
              sinDate
              sortNo
              startDate
              syubyoKbn
              tenkiDate
              tenkiKbn
            }
          }
          itemCd
          itemName
        }
      }
    }
  }
`;
export type PostApiMedicalExaminationGetCheckDiseasesMutationFn =
  Apollo.MutationFunction<
    PostApiMedicalExaminationGetCheckDiseasesMutation,
    PostApiMedicalExaminationGetCheckDiseasesMutationVariables
  >;

/**
 * __usePostApiMedicalExaminationGetCheckDiseasesMutation__
 *
 * To run a mutation, you first call `usePostApiMedicalExaminationGetCheckDiseasesMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMedicalExaminationGetCheckDiseasesMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMedicalExaminationGetCheckDiseasesMutation, { data, loading, error }] = usePostApiMedicalExaminationGetCheckDiseasesMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiMedicalExaminationGetCheckDiseasesMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMedicalExaminationGetCheckDiseasesMutation,
    PostApiMedicalExaminationGetCheckDiseasesMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMedicalExaminationGetCheckDiseasesMutation,
    PostApiMedicalExaminationGetCheckDiseasesMutationVariables
  >(PostApiMedicalExaminationGetCheckDiseasesDocument, options);
}
export type PostApiMedicalExaminationGetCheckDiseasesMutationHookResult =
  ReturnType<typeof usePostApiMedicalExaminationGetCheckDiseasesMutation>;
export type PostApiMedicalExaminationGetCheckDiseasesMutationResult =
  Apollo.MutationResult<PostApiMedicalExaminationGetCheckDiseasesMutation>;
export type PostApiMedicalExaminationGetCheckDiseasesMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMedicalExaminationGetCheckDiseasesMutation,
    PostApiMedicalExaminationGetCheckDiseasesMutationVariables
  >;
