import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiKarteAllergyGetKarteAllergyListQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
}>;

export type GetApiKarteAllergyGetKarteAllergyListQuery = {
  __typename?: "query_root";
  getApiKarteAllergyGetKarteAllergyList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteAllergyGetKarteAllergyResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesKarteAllergyGetKarteAllergyResponse";
      karteAllergyTab?: {
        __typename?: "DomainModelsKarteAllergyKarteAllergyModel";
        alrgyDrugItems?: Array<{
          __typename?: "DomainModelsSpecialNoteImportantNotePtAlrgyDrugModel";
          cmt?: string;
          drugName?: string;
          endDate?: number;
          isContraindicated?: boolean;
          seqNo?: number;
          startDate?: number;
          itemCd?: string;
        }>;
        alrgyFoodItems?: Array<{
          __typename?: "DomainModelsSpecialNoteImportantNotePtAlrgyFoodModel";
          alrgyKbn?: string;
          cmt?: string;
          endDate?: number;
          foodName?: string;
          seqNo?: number;
          startDate?: number;
        }>;
        alrgyElseItems?: Array<{
          __typename?: "DomainModelsSpecialNoteImportantNotePtAlrgyElseModel";
          alrgyName?: string;
          cmt?: string;
          endDate?: number;
          seqNo?: number;
          startDate?: number;
        }>;
      };
    };
  };
};

export const GetApiKarteAllergyGetKarteAllergyListDocument = gql`
  query getApiKarteAllergyGetKarteAllergyList($ptId: BigInt!) {
    getApiKarteAllergyGetKarteAllergyList(ptId: $ptId) {
      data {
        karteAllergyTab {
          alrgyDrugItems {
            cmt
            drugName
            endDate
            isContraindicated
            seqNo
            startDate
            itemCd
          }
          alrgyFoodItems {
            alrgyKbn
            cmt
            endDate
            foodName
            seqNo
            startDate
          }
          alrgyElseItems {
            alrgyName
            cmt
            endDate
            seqNo
            startDate
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiKarteAllergyGetKarteAllergyListQuery__
 *
 * To run a query within a React component, call `useGetApiKarteAllergyGetKarteAllergyListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKarteAllergyGetKarteAllergyListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKarteAllergyGetKarteAllergyListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiKarteAllergyGetKarteAllergyListQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiKarteAllergyGetKarteAllergyListQuery,
    GetApiKarteAllergyGetKarteAllergyListQueryVariables
  > &
    (
      | {
          variables: GetApiKarteAllergyGetKarteAllergyListQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKarteAllergyGetKarteAllergyListQuery,
    GetApiKarteAllergyGetKarteAllergyListQueryVariables
  >(GetApiKarteAllergyGetKarteAllergyListDocument, options);
}
export function useGetApiKarteAllergyGetKarteAllergyListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKarteAllergyGetKarteAllergyListQuery,
    GetApiKarteAllergyGetKarteAllergyListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKarteAllergyGetKarteAllergyListQuery,
    GetApiKarteAllergyGetKarteAllergyListQueryVariables
  >(GetApiKarteAllergyGetKarteAllergyListDocument, options);
}
export function useGetApiKarteAllergyGetKarteAllergyListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKarteAllergyGetKarteAllergyListQuery,
    GetApiKarteAllergyGetKarteAllergyListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKarteAllergyGetKarteAllergyListQuery,
    GetApiKarteAllergyGetKarteAllergyListQueryVariables
  >(GetApiKarteAllergyGetKarteAllergyListDocument, options);
}
export type GetApiKarteAllergyGetKarteAllergyListQueryHookResult = ReturnType<
  typeof useGetApiKarteAllergyGetKarteAllergyListQuery
>;
export type GetApiKarteAllergyGetKarteAllergyListLazyQueryHookResult =
  ReturnType<typeof useGetApiKarteAllergyGetKarteAllergyListLazyQuery>;
export type GetApiKarteAllergyGetKarteAllergyListSuspenseQueryHookResult =
  ReturnType<typeof useGetApiKarteAllergyGetKarteAllergyListSuspenseQuery>;
export type GetApiKarteAllergyGetKarteAllergyListQueryResult =
  Apollo.QueryResult<
    GetApiKarteAllergyGetKarteAllergyListQuery,
    GetApiKarteAllergyGetKarteAllergyListQueryVariables
  >;
