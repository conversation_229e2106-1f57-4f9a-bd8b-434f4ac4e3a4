import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiKarteFileGeneratePresignedUrlMutationVariables =
  Types.Exact<{
    fileName:
      | Array<Types.InputMaybe<Types.Scalars["String"]["input"]>>
      | Types.InputMaybe<Types.Scalars["String"]["input"]>;
    ptId: Types.Scalars["Int"]["input"];
  }>;

export type PostApiKarteFileGeneratePresignedUrlMutation = {
  __typename?: "mutation_root";
  postApiKarteFileGeneratePreSignedUrl?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteFileGeneratePreSignedUrlResponse";
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteFileGeneratePreSignedUrlResponse";
      preSignedUrls?: Array<{
        __typename?: "DomainModelsKarteFileFilePreSignedUrlModel";
        fileName?: string;
        preSignedUrl?: string;
      }>;
    };
  };
};

export type PostApiKarteFileAddKarteFileMutationVariables = Types.Exact<{
  emrCloudApiRequestsKarteFileAddKarteFileRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsKarteFileAddKarteFileRequestInput>;
}>;

export type PostApiKarteFileAddKarteFileMutation = {
  __typename?: "mutation_root";
  postApiKarteFileAddKarteFile?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteFileAddKarteFileResponse";
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteFileAddKarteFileResponse";
      status?: number;
    };
  };
};

export type GetApiKarteFileGetListKarteFileQueryVariables = Types.Exact<{
  ptId: Types.Scalars["Int"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
}>;

export type GetApiKarteFileGetListKarteFileQuery = {
  __typename?: "query_root";
  getApiKarteFileGetListKarteFile?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteFileGetListKarteFileResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteFileGetListKarteFileResponse";
      data?: Array<{
        __typename?: "DomainModelsKarteFileGetListKarteFileModel";
        categoryCd?: number;
        dspFileName?: string;
        fileId?: number;
        filePath?: string;
        getDate?: number;
        hpId?: number;
        memo?: string;
        sinDate?: number;
      }>;
    };
  };
};

export type PostApiKarteFileUpdateKarteFileSoapMutationVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  updateKarteFileSoaps:
    | Array<
        Types.InputMaybe<Types.DomainModelsKarteFileUpdateKarteFileSoapModelInput>
      >
    | Types.InputMaybe<Types.DomainModelsKarteFileUpdateKarteFileSoapModelInput>;
}>;

export type PostApiKarteFileUpdateKarteFileSoapMutation = {
  __typename?: "mutation_root";
  postApiKarteFileUpdateKarteFileSoap?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteFileUpdateKarteFileSoapResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteFileUpdateKarteFileSoapResponse";
      status?: number;
    };
  };
};

export const PostApiKarteFileGeneratePresignedUrlDocument = gql`
  mutation postApiKarteFileGeneratePresignedUrl(
    $fileName: [String]!
    $ptId: Int!
  ) {
    postApiKarteFileGeneratePreSignedUrl(
      emrCloudApiRequestsKarteFileGeneratePreSignedUrlRequestInput: {
        fileName: $fileName
        ptId: $ptId
      }
    ) {
      status
      data {
        preSignedUrls {
          fileName
          preSignedUrl
        }
      }
    }
  }
`;
export type PostApiKarteFileGeneratePresignedUrlMutationFn =
  Apollo.MutationFunction<
    PostApiKarteFileGeneratePresignedUrlMutation,
    PostApiKarteFileGeneratePresignedUrlMutationVariables
  >;

/**
 * __usePostApiKarteFileGeneratePresignedUrlMutation__
 *
 * To run a mutation, you first call `usePostApiKarteFileGeneratePresignedUrlMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKarteFileGeneratePresignedUrlMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKarteFileGeneratePresignedUrlMutation, { data, loading, error }] = usePostApiKarteFileGeneratePresignedUrlMutation({
 *   variables: {
 *      fileName: // value for 'fileName'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function usePostApiKarteFileGeneratePresignedUrlMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKarteFileGeneratePresignedUrlMutation,
    PostApiKarteFileGeneratePresignedUrlMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKarteFileGeneratePresignedUrlMutation,
    PostApiKarteFileGeneratePresignedUrlMutationVariables
  >(PostApiKarteFileGeneratePresignedUrlDocument, options);
}
export type PostApiKarteFileGeneratePresignedUrlMutationHookResult = ReturnType<
  typeof usePostApiKarteFileGeneratePresignedUrlMutation
>;
export type PostApiKarteFileGeneratePresignedUrlMutationResult =
  Apollo.MutationResult<PostApiKarteFileGeneratePresignedUrlMutation>;
export type PostApiKarteFileGeneratePresignedUrlMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKarteFileGeneratePresignedUrlMutation,
    PostApiKarteFileGeneratePresignedUrlMutationVariables
  >;
export const PostApiKarteFileAddKarteFileDocument = gql`
  mutation postApiKarteFileAddKarteFile(
    $emrCloudApiRequestsKarteFileAddKarteFileRequestInput: EmrCloudApiRequestsKarteFileAddKarteFileRequestInput
  ) {
    postApiKarteFileAddKarteFile(
      emrCloudApiRequestsKarteFileAddKarteFileRequestInput: $emrCloudApiRequestsKarteFileAddKarteFileRequestInput
    ) {
      status
      data {
        status
      }
    }
  }
`;
export type PostApiKarteFileAddKarteFileMutationFn = Apollo.MutationFunction<
  PostApiKarteFileAddKarteFileMutation,
  PostApiKarteFileAddKarteFileMutationVariables
>;

/**
 * __usePostApiKarteFileAddKarteFileMutation__
 *
 * To run a mutation, you first call `usePostApiKarteFileAddKarteFileMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKarteFileAddKarteFileMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKarteFileAddKarteFileMutation, { data, loading, error }] = usePostApiKarteFileAddKarteFileMutation({
 *   variables: {
 *      emrCloudApiRequestsKarteFileAddKarteFileRequestInput: // value for 'emrCloudApiRequestsKarteFileAddKarteFileRequestInput'
 *   },
 * });
 */
export function usePostApiKarteFileAddKarteFileMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKarteFileAddKarteFileMutation,
    PostApiKarteFileAddKarteFileMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKarteFileAddKarteFileMutation,
    PostApiKarteFileAddKarteFileMutationVariables
  >(PostApiKarteFileAddKarteFileDocument, options);
}
export type PostApiKarteFileAddKarteFileMutationHookResult = ReturnType<
  typeof usePostApiKarteFileAddKarteFileMutation
>;
export type PostApiKarteFileAddKarteFileMutationResult =
  Apollo.MutationResult<PostApiKarteFileAddKarteFileMutation>;
export type PostApiKarteFileAddKarteFileMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKarteFileAddKarteFileMutation,
    PostApiKarteFileAddKarteFileMutationVariables
  >;
export const GetApiKarteFileGetListKarteFileDocument = gql`
  query getApiKarteFileGetListKarteFile(
    $ptId: Int!
    $raiinNo: BigInt!
    $sinDate: Int!
  ) {
    getApiKarteFileGetListKarteFile(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      message
      status
      data {
        data {
          categoryCd
          dspFileName
          fileId
          filePath
          getDate
          hpId
          memo
          sinDate
        }
      }
    }
  }
`;

/**
 * __useGetApiKarteFileGetListKarteFileQuery__
 *
 * To run a query within a React component, call `useGetApiKarteFileGetListKarteFileQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKarteFileGetListKarteFileQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKarteFileGetListKarteFileQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiKarteFileGetListKarteFileQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiKarteFileGetListKarteFileQuery,
    GetApiKarteFileGetListKarteFileQueryVariables
  > &
    (
      | {
          variables: GetApiKarteFileGetListKarteFileQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKarteFileGetListKarteFileQuery,
    GetApiKarteFileGetListKarteFileQueryVariables
  >(GetApiKarteFileGetListKarteFileDocument, options);
}
export function useGetApiKarteFileGetListKarteFileLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKarteFileGetListKarteFileQuery,
    GetApiKarteFileGetListKarteFileQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKarteFileGetListKarteFileQuery,
    GetApiKarteFileGetListKarteFileQueryVariables
  >(GetApiKarteFileGetListKarteFileDocument, options);
}
export function useGetApiKarteFileGetListKarteFileSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKarteFileGetListKarteFileQuery,
    GetApiKarteFileGetListKarteFileQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKarteFileGetListKarteFileQuery,
    GetApiKarteFileGetListKarteFileQueryVariables
  >(GetApiKarteFileGetListKarteFileDocument, options);
}
export type GetApiKarteFileGetListKarteFileQueryHookResult = ReturnType<
  typeof useGetApiKarteFileGetListKarteFileQuery
>;
export type GetApiKarteFileGetListKarteFileLazyQueryHookResult = ReturnType<
  typeof useGetApiKarteFileGetListKarteFileLazyQuery
>;
export type GetApiKarteFileGetListKarteFileSuspenseQueryHookResult = ReturnType<
  typeof useGetApiKarteFileGetListKarteFileSuspenseQuery
>;
export type GetApiKarteFileGetListKarteFileQueryResult = Apollo.QueryResult<
  GetApiKarteFileGetListKarteFileQuery,
  GetApiKarteFileGetListKarteFileQueryVariables
>;
export const PostApiKarteFileUpdateKarteFileSoapDocument = gql`
  mutation postApiKarteFileUpdateKarteFileSoap(
    $ptId: BigInt!
    $updateKarteFileSoaps: [DomainModelsKarteFileUpdateKarteFileSoapModelInput]!
  ) {
    postApiKarteFileUpdateKarteFileSoap(
      emrCloudApiRequestsKarteFileUpdateKarteFileSoapRequestInput: {
        ptId: $ptId
        updateKarteFileSoaps: $updateKarteFileSoaps
      }
    ) {
      message
      status
      data {
        status
      }
    }
  }
`;
export type PostApiKarteFileUpdateKarteFileSoapMutationFn =
  Apollo.MutationFunction<
    PostApiKarteFileUpdateKarteFileSoapMutation,
    PostApiKarteFileUpdateKarteFileSoapMutationVariables
  >;

/**
 * __usePostApiKarteFileUpdateKarteFileSoapMutation__
 *
 * To run a mutation, you first call `usePostApiKarteFileUpdateKarteFileSoapMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKarteFileUpdateKarteFileSoapMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKarteFileUpdateKarteFileSoapMutation, { data, loading, error }] = usePostApiKarteFileUpdateKarteFileSoapMutation({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      updateKarteFileSoaps: // value for 'updateKarteFileSoaps'
 *   },
 * });
 */
export function usePostApiKarteFileUpdateKarteFileSoapMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKarteFileUpdateKarteFileSoapMutation,
    PostApiKarteFileUpdateKarteFileSoapMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKarteFileUpdateKarteFileSoapMutation,
    PostApiKarteFileUpdateKarteFileSoapMutationVariables
  >(PostApiKarteFileUpdateKarteFileSoapDocument, options);
}
export type PostApiKarteFileUpdateKarteFileSoapMutationHookResult = ReturnType<
  typeof usePostApiKarteFileUpdateKarteFileSoapMutation
>;
export type PostApiKarteFileUpdateKarteFileSoapMutationResult =
  Apollo.MutationResult<PostApiKarteFileUpdateKarteFileSoapMutation>;
export type PostApiKarteFileUpdateKarteFileSoapMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKarteFileUpdateKarteFileSoapMutation,
    PostApiKarteFileUpdateKarteFileSoapMutationVariables
  >;
