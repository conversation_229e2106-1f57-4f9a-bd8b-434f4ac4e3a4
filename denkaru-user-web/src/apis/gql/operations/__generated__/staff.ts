import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetSessionInfoQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetSessionInfoQuery = {
  __typename?: "query_root";
  getSessionInfo: {
    __typename?: "LoginRes";
    pharmacyFlg: boolean;
    staffInfo: {
      __typename?: "StaffInfo";
      staffId: number;
      staffName: string;
      staffType: number;
      managerKbn: number;
      loginId: string;
      permissions: Array<{
        __typename?: "StaffPermission";
        functionCd: string;
        permission: number;
      }>;
    };
  };
};

export type GetStaffListWithTaskCountQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetStaffListWithTaskCountQuery = {
  __typename?: "query_root";
  getStaffListWithTaskCount: {
    __typename?: "GetStaffListWithTaskCountRes";
    totalTaskCount: number;
    staffs: Array<{
      __typename?: "StaffWithTaskCount";
      staffId: number;
      staffName: string;
      taskCount: number;
    }>;
  };
};

export type GetStaffListQueryVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.GetStaffListReq>;
}>;

export type GetStaffListQuery = {
  __typename?: "query_root";
  getStaffList: {
    __typename?: "GetStaffListRes";
    staffs: Array<{
      __typename?: "StaffInfo";
      staffId: number;
      staffName: string;
      staffKana: string;
      status: number;
      staffType: number;
      managerKbn: number;
      medicalLicenseNo?: string;
      mayakuLicenseNo?: string;
      email?: string;
      loginId: string;
      hospitalID: number;
      permissions: Array<{
        __typename?: "StaffPermission";
        functionCd: string;
        permission: number;
      }>;
    }>;
  };
};

export type GetStaffListSettingQueryVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.GetStaffListReq>;
}>;

export type GetStaffListSettingQuery = {
  __typename?: "query_root";
  getStaffListSetting: {
    __typename?: "GetStaffListRes";
    staffs: Array<{
      __typename?: "StaffInfo";
      staffId: number;
      staffName: string;
      staffKana: string;
      status: number;
      staffType: number;
      managerKbn: number;
      medicalLicenseNo?: string;
      mayakuLicenseNo?: string;
      email?: string;
      loginId: string;
      hospitalID: number;
      permissions: Array<{
        __typename?: "StaffPermission";
        functionCd: string;
        permission: number;
      }>;
    }>;
  };
};

export type CreateStaffMutationVariables = Types.Exact<{
  input: Types.CreateStaffReq;
}>;

export type CreateStaffMutation = {
  __typename?: "mutation_root";
  createStaff: {
    __typename?: "CreateStaffRes";
    staffId: number;
    staffName: string;
    staffKana: string;
    staffType: number;
    managerKbn: number;
    medicalLicenseNo?: string;
    mayakuLicenseNo?: string;
    loginId: string;
    password: string;
    permissions: Array<{
      __typename?: "StaffPermission";
      permission: number;
      functionCd: string;
    }>;
  };
};

export type UpdateStaffMutationVariables = Types.Exact<{
  input: Types.UpdateStaffReq;
}>;

export type UpdateStaffMutation = {
  __typename?: "mutation_root";
  updateStaff: {
    __typename?: "UpdateStaffRes";
    staffId: number;
    staffName: string;
    staffKana: string;
    staffType: number;
    managerKbn: number;
    medicalLicenseNo?: string;
    mayakuLicenseNo?: string;
    loginId: string;
    password: string;
    permissions: Array<{
      __typename?: "StaffPermission";
      permission: number;
      functionCd: string;
    }>;
  };
};

export type GetDoctorsInHospitalQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetDoctorsInHospitalQuery = {
  __typename?: "query_root";
  getDoctorsInHospital: Array<{
    __typename?: "StaffInfo";
    staffId: number;
    staffName: string;
    hospitalID: number;
  }>;
};

export type ChangePasswordMutationVariables = Types.Exact<{
  input: Types.ChangePasswordReq;
}>;

export type ChangePasswordMutation = {
  __typename?: "mutation_root";
  changePassword: { __typename?: "ChangePasswordRes"; success: number };
};

export const GetSessionInfoDocument = gql`
  query getSessionInfo {
    getSessionInfo {
      pharmacyFlg
      staffInfo {
        staffId
        staffName
        staffType
        managerKbn
        loginId
        permissions {
          functionCd
          permission
        }
      }
    }
  }
`;

/**
 * __useGetSessionInfoQuery__
 *
 * To run a query within a React component, call `useGetSessionInfoQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSessionInfoQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSessionInfoQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetSessionInfoQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetSessionInfoQuery,
    GetSessionInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetSessionInfoQuery, GetSessionInfoQueryVariables>(
    GetSessionInfoDocument,
    options,
  );
}
export function useGetSessionInfoLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSessionInfoQuery,
    GetSessionInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetSessionInfoQuery, GetSessionInfoQueryVariables>(
    GetSessionInfoDocument,
    options,
  );
}
export function useGetSessionInfoSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSessionInfoQuery,
    GetSessionInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSessionInfoQuery,
    GetSessionInfoQueryVariables
  >(GetSessionInfoDocument, options);
}
export type GetSessionInfoQueryHookResult = ReturnType<
  typeof useGetSessionInfoQuery
>;
export type GetSessionInfoLazyQueryHookResult = ReturnType<
  typeof useGetSessionInfoLazyQuery
>;
export type GetSessionInfoSuspenseQueryHookResult = ReturnType<
  typeof useGetSessionInfoSuspenseQuery
>;
export type GetSessionInfoQueryResult = Apollo.QueryResult<
  GetSessionInfoQuery,
  GetSessionInfoQueryVariables
>;
export const GetStaffListWithTaskCountDocument = gql`
  query getStaffListWithTaskCount {
    getStaffListWithTaskCount {
      totalTaskCount
      staffs {
        staffId
        staffName
        taskCount
      }
    }
  }
`;

/**
 * __useGetStaffListWithTaskCountQuery__
 *
 * To run a query within a React component, call `useGetStaffListWithTaskCountQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetStaffListWithTaskCountQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetStaffListWithTaskCountQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetStaffListWithTaskCountQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetStaffListWithTaskCountQuery,
    GetStaffListWithTaskCountQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetStaffListWithTaskCountQuery,
    GetStaffListWithTaskCountQueryVariables
  >(GetStaffListWithTaskCountDocument, options);
}
export function useGetStaffListWithTaskCountLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetStaffListWithTaskCountQuery,
    GetStaffListWithTaskCountQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetStaffListWithTaskCountQuery,
    GetStaffListWithTaskCountQueryVariables
  >(GetStaffListWithTaskCountDocument, options);
}
export function useGetStaffListWithTaskCountSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetStaffListWithTaskCountQuery,
    GetStaffListWithTaskCountQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetStaffListWithTaskCountQuery,
    GetStaffListWithTaskCountQueryVariables
  >(GetStaffListWithTaskCountDocument, options);
}
export type GetStaffListWithTaskCountQueryHookResult = ReturnType<
  typeof useGetStaffListWithTaskCountQuery
>;
export type GetStaffListWithTaskCountLazyQueryHookResult = ReturnType<
  typeof useGetStaffListWithTaskCountLazyQuery
>;
export type GetStaffListWithTaskCountSuspenseQueryHookResult = ReturnType<
  typeof useGetStaffListWithTaskCountSuspenseQuery
>;
export type GetStaffListWithTaskCountQueryResult = Apollo.QueryResult<
  GetStaffListWithTaskCountQuery,
  GetStaffListWithTaskCountQueryVariables
>;
export const GetStaffListDocument = gql`
  query getStaffList($input: GetStaffListReq) {
    getStaffList(input: $input) {
      staffs {
        staffId
        staffName
        staffKana
        status
        staffType
        managerKbn
        medicalLicenseNo
        mayakuLicenseNo
        permissions {
          functionCd
          permission
        }
        email
        loginId
        hospitalID
      }
    }
  }
`;

/**
 * __useGetStaffListQuery__
 *
 * To run a query within a React component, call `useGetStaffListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetStaffListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetStaffListQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetStaffListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetStaffListQuery,
    GetStaffListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetStaffListQuery, GetStaffListQueryVariables>(
    GetStaffListDocument,
    options,
  );
}
export function useGetStaffListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetStaffListQuery,
    GetStaffListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetStaffListQuery, GetStaffListQueryVariables>(
    GetStaffListDocument,
    options,
  );
}
export function useGetStaffListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetStaffListQuery,
    GetStaffListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<GetStaffListQuery, GetStaffListQueryVariables>(
    GetStaffListDocument,
    options,
  );
}
export type GetStaffListQueryHookResult = ReturnType<
  typeof useGetStaffListQuery
>;
export type GetStaffListLazyQueryHookResult = ReturnType<
  typeof useGetStaffListLazyQuery
>;
export type GetStaffListSuspenseQueryHookResult = ReturnType<
  typeof useGetStaffListSuspenseQuery
>;
export type GetStaffListQueryResult = Apollo.QueryResult<
  GetStaffListQuery,
  GetStaffListQueryVariables
>;
export const GetStaffListSettingDocument = gql`
  query getStaffListSetting($input: GetStaffListReq) {
    getStaffListSetting(input: $input) {
      staffs {
        staffId
        staffName
        staffKana
        status
        staffType
        managerKbn
        medicalLicenseNo
        mayakuLicenseNo
        permissions {
          functionCd
          permission
        }
        email
        loginId
        hospitalID
      }
    }
  }
`;

/**
 * __useGetStaffListSettingQuery__
 *
 * To run a query within a React component, call `useGetStaffListSettingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetStaffListSettingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetStaffListSettingQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetStaffListSettingQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetStaffListSettingQuery,
    GetStaffListSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetStaffListSettingQuery,
    GetStaffListSettingQueryVariables
  >(GetStaffListSettingDocument, options);
}
export function useGetStaffListSettingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetStaffListSettingQuery,
    GetStaffListSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetStaffListSettingQuery,
    GetStaffListSettingQueryVariables
  >(GetStaffListSettingDocument, options);
}
export function useGetStaffListSettingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetStaffListSettingQuery,
    GetStaffListSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetStaffListSettingQuery,
    GetStaffListSettingQueryVariables
  >(GetStaffListSettingDocument, options);
}
export type GetStaffListSettingQueryHookResult = ReturnType<
  typeof useGetStaffListSettingQuery
>;
export type GetStaffListSettingLazyQueryHookResult = ReturnType<
  typeof useGetStaffListSettingLazyQuery
>;
export type GetStaffListSettingSuspenseQueryHookResult = ReturnType<
  typeof useGetStaffListSettingSuspenseQuery
>;
export type GetStaffListSettingQueryResult = Apollo.QueryResult<
  GetStaffListSettingQuery,
  GetStaffListSettingQueryVariables
>;
export const CreateStaffDocument = gql`
  mutation createStaff($input: CreateStaffReq!) {
    createStaff(input: $input) {
      staffId
      staffName
      staffKana
      staffType
      managerKbn
      medicalLicenseNo
      mayakuLicenseNo
      loginId
      password
      permissions {
        permission
        functionCd
      }
    }
  }
`;
export type CreateStaffMutationFn = Apollo.MutationFunction<
  CreateStaffMutation,
  CreateStaffMutationVariables
>;

/**
 * __useCreateStaffMutation__
 *
 * To run a mutation, you first call `useCreateStaffMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateStaffMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createStaffMutation, { data, loading, error }] = useCreateStaffMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateStaffMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateStaffMutation,
    CreateStaffMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<CreateStaffMutation, CreateStaffMutationVariables>(
    CreateStaffDocument,
    options,
  );
}
export type CreateStaffMutationHookResult = ReturnType<
  typeof useCreateStaffMutation
>;
export type CreateStaffMutationResult =
  Apollo.MutationResult<CreateStaffMutation>;
export type CreateStaffMutationOptions = Apollo.BaseMutationOptions<
  CreateStaffMutation,
  CreateStaffMutationVariables
>;
export const UpdateStaffDocument = gql`
  mutation updateStaff($input: UpdateStaffReq!) {
    updateStaff(input: $input) {
      staffId
      staffName
      staffKana
      staffType
      managerKbn
      medicalLicenseNo
      mayakuLicenseNo
      loginId
      password
      permissions {
        permission
        functionCd
      }
    }
  }
`;
export type UpdateStaffMutationFn = Apollo.MutationFunction<
  UpdateStaffMutation,
  UpdateStaffMutationVariables
>;

/**
 * __useUpdateStaffMutation__
 *
 * To run a mutation, you first call `useUpdateStaffMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateStaffMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateStaffMutation, { data, loading, error }] = useUpdateStaffMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateStaffMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateStaffMutation,
    UpdateStaffMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<UpdateStaffMutation, UpdateStaffMutationVariables>(
    UpdateStaffDocument,
    options,
  );
}
export type UpdateStaffMutationHookResult = ReturnType<
  typeof useUpdateStaffMutation
>;
export type UpdateStaffMutationResult =
  Apollo.MutationResult<UpdateStaffMutation>;
export type UpdateStaffMutationOptions = Apollo.BaseMutationOptions<
  UpdateStaffMutation,
  UpdateStaffMutationVariables
>;
export const GetDoctorsInHospitalDocument = gql`
  query getDoctorsInHospital {
    getDoctorsInHospital {
      staffId
      staffName
      hospitalID
    }
  }
`;

/**
 * __useGetDoctorsInHospitalQuery__
 *
 * To run a query within a React component, call `useGetDoctorsInHospitalQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetDoctorsInHospitalQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetDoctorsInHospitalQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetDoctorsInHospitalQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetDoctorsInHospitalQuery,
    GetDoctorsInHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetDoctorsInHospitalQuery,
    GetDoctorsInHospitalQueryVariables
  >(GetDoctorsInHospitalDocument, options);
}
export function useGetDoctorsInHospitalLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetDoctorsInHospitalQuery,
    GetDoctorsInHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetDoctorsInHospitalQuery,
    GetDoctorsInHospitalQueryVariables
  >(GetDoctorsInHospitalDocument, options);
}
export function useGetDoctorsInHospitalSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetDoctorsInHospitalQuery,
    GetDoctorsInHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetDoctorsInHospitalQuery,
    GetDoctorsInHospitalQueryVariables
  >(GetDoctorsInHospitalDocument, options);
}
export type GetDoctorsInHospitalQueryHookResult = ReturnType<
  typeof useGetDoctorsInHospitalQuery
>;
export type GetDoctorsInHospitalLazyQueryHookResult = ReturnType<
  typeof useGetDoctorsInHospitalLazyQuery
>;
export type GetDoctorsInHospitalSuspenseQueryHookResult = ReturnType<
  typeof useGetDoctorsInHospitalSuspenseQuery
>;
export type GetDoctorsInHospitalQueryResult = Apollo.QueryResult<
  GetDoctorsInHospitalQuery,
  GetDoctorsInHospitalQueryVariables
>;
export const ChangePasswordDocument = gql`
  mutation changePassword($input: ChangePasswordReq!) {
    changePassword(input: $input) {
      success
    }
  }
`;
export type ChangePasswordMutationFn = Apollo.MutationFunction<
  ChangePasswordMutation,
  ChangePasswordMutationVariables
>;

/**
 * __useChangePasswordMutation__
 *
 * To run a mutation, you first call `useChangePasswordMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChangePasswordMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [changePasswordMutation, { data, loading, error }] = useChangePasswordMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useChangePasswordMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ChangePasswordMutation,
    ChangePasswordMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    ChangePasswordMutation,
    ChangePasswordMutationVariables
  >(ChangePasswordDocument, options);
}
export type ChangePasswordMutationHookResult = ReturnType<
  typeof useChangePasswordMutation
>;
export type ChangePasswordMutationResult =
  Apollo.MutationResult<ChangePasswordMutation>;
export type ChangePasswordMutationOptions = Apollo.BaseMutationOptions<
  ChangePasswordMutation,
  ChangePasswordMutationVariables
>;
