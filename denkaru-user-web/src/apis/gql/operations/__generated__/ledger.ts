import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetReportFileNameListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetReportFileNameListQuery = {
  __typename?: "query_root";
  getApiMainMenuGetListStaticReport?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMainMenuGetListReportResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMainMenuGetListReportResponse";
      fileNameList?: Array<string>;
    };
  };
};

export type GetPaymentMethodListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetPaymentMethodListQuery = {
  __typename?: "query_root";
  getApiAccountingPaymentMethod?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingGetPaymentMethodResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingGetPaymentMethodResponse";
      paymentMethodMstModels?: Array<{
        __typename?: "DomainModelsMstItemPaymentMethodMstModel";
        sortNo?: number;
        paymentMethodCd?: number;
        payName?: string;
        paySname?: string;
      }>;
    };
  };
};

export type SaveLedgerSettingsMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsMainMenuSaveStatisticMenuRequestInput;
}>;

export type SaveLedgerSettingsMutation = {
  __typename?: "mutation_root";
  postApiMainMenuSaveStatisticMenuList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMainMenuSaveStatisticMenuResponse";
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesMainMenuSaveStatisticMenuResponse";
      success?: boolean;
      menuIdTemp?: number;
    };
  };
};

export type GetLedgerListQueryVariables = Types.Exact<{
  grpId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetLedgerListQuery = {
  __typename?: "query_root";
  getApiMainMenuGetStatisticMenuList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMainMenuGetStatisticMenuResponse";
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesMainMenuGetStatisticMenuResponse";
      staGrpList?: Array<{
        __typename?: "EmrCloudApiResponsesMainMenuDtoStaGrpDto";
        grpId?: number;
        reportId?: number;
        reportName?: string;
        sortNo?: number;
      }>;
      statisticMenuList?: Array<{
        __typename?: "EmrCloudApiResponsesMainMenuDtoStatisticMenuDto";
        menuId?: number;
        menuName?: string;
        sortNo?: number;
        grpId?: number;
        isPrint?: number;
        reportId?: number;
        staConfigList?: Array<{
          __typename?: "EmrCloudApiResponsesMainMenuDtoStaConfDto";
          confId?: number;
          menuId?: number;
          val?: string;
        }>;
      }>;
    };
  };
};

export type GetJsonSettingQueryVariables = Types.Exact<{
  key: Types.Scalars["String"]["input"];
}>;

export type GetJsonSettingQuery = {
  __typename?: "query_root";
  getApiJsonSettingGet?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesJsonSettingGetJsonSettingResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesJsonSettingGetJsonSettingResponse";
      setting?: {
        __typename?: "UseCaseJsonSettingJsonSettingDto";
        key?: string;
        value?: any;
        userId?: number;
      };
    };
  };
};

export type UpdateJsonSettingMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsJsonSettingUpsertJsonSettingRequestInput>;
}>;

export type UpdateJsonSettingMutation = {
  __typename?: "mutation_root";
  postApiJsonSettingUpsert?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesJsonSettingUpsertJsonSettingResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesJsonSettingUpsertJsonSettingResponse";
      success?: boolean;
    };
  };
};

export type GetApiUketukeSbtGetListMstQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiUketukeSbtGetListMstQuery = {
  __typename?: "query_root";
  getApiUketukeSbtGetListMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesUketukeSbtGetUketukeSbtMstListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesUketukeSbtGetUketukeSbtMstListResponse";
      receptionTypes?: Array<{
        __typename?: "DomainModelsUketukeSbtMstUketukeSbtMstModel";
        kbnId?: number;
        kbnName?: string;
        sortNo?: number;
        isDeleted?: number;
      }>;
    };
  };
};

export type GetApiReceiptGetMedicalDetailsQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetMedicalDetailsQuery = {
  __typename?: "query_root";
  getApiReceiptGetMedicalDetails?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetMedicalDetailsResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetMedicalDetailsResponse";
      holidays?: any;
      sinMeiModels?: Array<{
        __typename?: "DomainModelsAccountingSinMeiModel";
        sinId?: number;
        sinIdBinding?: string;
        itemName?: string;
        quantity?: string;
        tenKai?: string;
        inOutKbn?: number;
        isRowColorGray?: boolean;
        sinRpNo?: number;
        sinSeqNo?: number;
        itemCd?: string;
        isDrug?: boolean;
        days?: Array<number>;
      }>;
    };
  };
};

export const GetReportFileNameListDocument = gql`
  query getReportFileNameList {
    getApiMainMenuGetListStaticReport {
      data {
        fileNameList
      }
    }
  }
`;

/**
 * __useGetReportFileNameListQuery__
 *
 * To run a query within a React component, call `useGetReportFileNameListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetReportFileNameListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetReportFileNameListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetReportFileNameListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetReportFileNameListQuery,
    GetReportFileNameListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetReportFileNameListQuery,
    GetReportFileNameListQueryVariables
  >(GetReportFileNameListDocument, options);
}
export function useGetReportFileNameListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetReportFileNameListQuery,
    GetReportFileNameListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetReportFileNameListQuery,
    GetReportFileNameListQueryVariables
  >(GetReportFileNameListDocument, options);
}
export function useGetReportFileNameListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetReportFileNameListQuery,
    GetReportFileNameListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetReportFileNameListQuery,
    GetReportFileNameListQueryVariables
  >(GetReportFileNameListDocument, options);
}
export type GetReportFileNameListQueryHookResult = ReturnType<
  typeof useGetReportFileNameListQuery
>;
export type GetReportFileNameListLazyQueryHookResult = ReturnType<
  typeof useGetReportFileNameListLazyQuery
>;
export type GetReportFileNameListSuspenseQueryHookResult = ReturnType<
  typeof useGetReportFileNameListSuspenseQuery
>;
export type GetReportFileNameListQueryResult = Apollo.QueryResult<
  GetReportFileNameListQuery,
  GetReportFileNameListQueryVariables
>;
export const GetPaymentMethodListDocument = gql`
  query getPaymentMethodList {
    getApiAccountingPaymentMethod {
      data {
        paymentMethodMstModels {
          sortNo
          paymentMethodCd
          payName
          paySname
        }
      }
    }
  }
`;

/**
 * __useGetPaymentMethodListQuery__
 *
 * To run a query within a React component, call `useGetPaymentMethodListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPaymentMethodListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPaymentMethodListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetPaymentMethodListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetPaymentMethodListQuery,
    GetPaymentMethodListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPaymentMethodListQuery,
    GetPaymentMethodListQueryVariables
  >(GetPaymentMethodListDocument, options);
}
export function useGetPaymentMethodListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPaymentMethodListQuery,
    GetPaymentMethodListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPaymentMethodListQuery,
    GetPaymentMethodListQueryVariables
  >(GetPaymentMethodListDocument, options);
}
export function useGetPaymentMethodListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPaymentMethodListQuery,
    GetPaymentMethodListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPaymentMethodListQuery,
    GetPaymentMethodListQueryVariables
  >(GetPaymentMethodListDocument, options);
}
export type GetPaymentMethodListQueryHookResult = ReturnType<
  typeof useGetPaymentMethodListQuery
>;
export type GetPaymentMethodListLazyQueryHookResult = ReturnType<
  typeof useGetPaymentMethodListLazyQuery
>;
export type GetPaymentMethodListSuspenseQueryHookResult = ReturnType<
  typeof useGetPaymentMethodListSuspenseQuery
>;
export type GetPaymentMethodListQueryResult = Apollo.QueryResult<
  GetPaymentMethodListQuery,
  GetPaymentMethodListQueryVariables
>;
export const SaveLedgerSettingsDocument = gql`
  mutation saveLedgerSettings(
    $input: EmrCloudApiRequestsMainMenuSaveStatisticMenuRequestInput!
  ) {
    postApiMainMenuSaveStatisticMenuList(
      emrCloudApiRequestsMainMenuSaveStatisticMenuRequestInput: $input
    ) {
      message
      data {
        success
        menuIdTemp
      }
    }
  }
`;
export type SaveLedgerSettingsMutationFn = Apollo.MutationFunction<
  SaveLedgerSettingsMutation,
  SaveLedgerSettingsMutationVariables
>;

/**
 * __useSaveLedgerSettingsMutation__
 *
 * To run a mutation, you first call `useSaveLedgerSettingsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveLedgerSettingsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveLedgerSettingsMutation, { data, loading, error }] = useSaveLedgerSettingsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSaveLedgerSettingsMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SaveLedgerSettingsMutation,
    SaveLedgerSettingsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SaveLedgerSettingsMutation,
    SaveLedgerSettingsMutationVariables
  >(SaveLedgerSettingsDocument, options);
}
export type SaveLedgerSettingsMutationHookResult = ReturnType<
  typeof useSaveLedgerSettingsMutation
>;
export type SaveLedgerSettingsMutationResult =
  Apollo.MutationResult<SaveLedgerSettingsMutation>;
export type SaveLedgerSettingsMutationOptions = Apollo.BaseMutationOptions<
  SaveLedgerSettingsMutation,
  SaveLedgerSettingsMutationVariables
>;
export const GetLedgerListDocument = gql`
  query getLedgerList($grpId: Int) {
    getApiMainMenuGetStatisticMenuList(grpId: $grpId) {
      message
      data {
        staGrpList {
          grpId
          reportId
          reportName
          sortNo
        }
        statisticMenuList {
          menuId
          menuName
          sortNo
          grpId
          isPrint
          reportId
          staConfigList {
            confId
            menuId
            val
          }
        }
      }
    }
  }
`;

/**
 * __useGetLedgerListQuery__
 *
 * To run a query within a React component, call `useGetLedgerListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLedgerListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLedgerListQuery({
 *   variables: {
 *      grpId: // value for 'grpId'
 *   },
 * });
 */
export function useGetLedgerListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetLedgerListQuery,
    GetLedgerListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetLedgerListQuery, GetLedgerListQueryVariables>(
    GetLedgerListDocument,
    options,
  );
}
export function useGetLedgerListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetLedgerListQuery,
    GetLedgerListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetLedgerListQuery, GetLedgerListQueryVariables>(
    GetLedgerListDocument,
    options,
  );
}
export function useGetLedgerListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetLedgerListQuery,
    GetLedgerListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetLedgerListQuery,
    GetLedgerListQueryVariables
  >(GetLedgerListDocument, options);
}
export type GetLedgerListQueryHookResult = ReturnType<
  typeof useGetLedgerListQuery
>;
export type GetLedgerListLazyQueryHookResult = ReturnType<
  typeof useGetLedgerListLazyQuery
>;
export type GetLedgerListSuspenseQueryHookResult = ReturnType<
  typeof useGetLedgerListSuspenseQuery
>;
export type GetLedgerListQueryResult = Apollo.QueryResult<
  GetLedgerListQuery,
  GetLedgerListQueryVariables
>;
export const GetJsonSettingDocument = gql`
  query getJsonSetting($key: String!) {
    getApiJsonSettingGet(key: $key) {
      message
      status
      data {
        setting {
          key
          value
          userId
        }
      }
    }
  }
`;

/**
 * __useGetJsonSettingQuery__
 *
 * To run a query within a React component, call `useGetJsonSettingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetJsonSettingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetJsonSettingQuery({
 *   variables: {
 *      key: // value for 'key'
 *   },
 * });
 */
export function useGetJsonSettingQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetJsonSettingQuery,
    GetJsonSettingQueryVariables
  > &
    (
      | { variables: GetJsonSettingQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetJsonSettingQuery, GetJsonSettingQueryVariables>(
    GetJsonSettingDocument,
    options,
  );
}
export function useGetJsonSettingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetJsonSettingQuery,
    GetJsonSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetJsonSettingQuery, GetJsonSettingQueryVariables>(
    GetJsonSettingDocument,
    options,
  );
}
export function useGetJsonSettingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetJsonSettingQuery,
    GetJsonSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetJsonSettingQuery,
    GetJsonSettingQueryVariables
  >(GetJsonSettingDocument, options);
}
export type GetJsonSettingQueryHookResult = ReturnType<
  typeof useGetJsonSettingQuery
>;
export type GetJsonSettingLazyQueryHookResult = ReturnType<
  typeof useGetJsonSettingLazyQuery
>;
export type GetJsonSettingSuspenseQueryHookResult = ReturnType<
  typeof useGetJsonSettingSuspenseQuery
>;
export type GetJsonSettingQueryResult = Apollo.QueryResult<
  GetJsonSettingQuery,
  GetJsonSettingQueryVariables
>;
export const UpdateJsonSettingDocument = gql`
  mutation updateJsonSetting(
    $input: EmrCloudApiRequestsJsonSettingUpsertJsonSettingRequestInput
  ) {
    postApiJsonSettingUpsert(
      emrCloudApiRequestsJsonSettingUpsertJsonSettingRequestInput: $input
    ) {
      message
      status
      data {
        success
      }
    }
  }
`;
export type UpdateJsonSettingMutationFn = Apollo.MutationFunction<
  UpdateJsonSettingMutation,
  UpdateJsonSettingMutationVariables
>;

/**
 * __useUpdateJsonSettingMutation__
 *
 * To run a mutation, you first call `useUpdateJsonSettingMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateJsonSettingMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateJsonSettingMutation, { data, loading, error }] = useUpdateJsonSettingMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateJsonSettingMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateJsonSettingMutation,
    UpdateJsonSettingMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateJsonSettingMutation,
    UpdateJsonSettingMutationVariables
  >(UpdateJsonSettingDocument, options);
}
export type UpdateJsonSettingMutationHookResult = ReturnType<
  typeof useUpdateJsonSettingMutation
>;
export type UpdateJsonSettingMutationResult =
  Apollo.MutationResult<UpdateJsonSettingMutation>;
export type UpdateJsonSettingMutationOptions = Apollo.BaseMutationOptions<
  UpdateJsonSettingMutation,
  UpdateJsonSettingMutationVariables
>;
export const GetApiUketukeSbtGetListMstDocument = gql`
  query getApiUketukeSbtGetListMst {
    getApiUketukeSbtGetListMst {
      data {
        receptionTypes {
          kbnId
          kbnName
          sortNo
          isDeleted
        }
      }
    }
  }
`;

/**
 * __useGetApiUketukeSbtGetListMstQuery__
 *
 * To run a query within a React component, call `useGetApiUketukeSbtGetListMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiUketukeSbtGetListMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiUketukeSbtGetListMstQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiUketukeSbtGetListMstQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiUketukeSbtGetListMstQuery,
    GetApiUketukeSbtGetListMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiUketukeSbtGetListMstQuery,
    GetApiUketukeSbtGetListMstQueryVariables
  >(GetApiUketukeSbtGetListMstDocument, options);
}
export function useGetApiUketukeSbtGetListMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiUketukeSbtGetListMstQuery,
    GetApiUketukeSbtGetListMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiUketukeSbtGetListMstQuery,
    GetApiUketukeSbtGetListMstQueryVariables
  >(GetApiUketukeSbtGetListMstDocument, options);
}
export function useGetApiUketukeSbtGetListMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiUketukeSbtGetListMstQuery,
    GetApiUketukeSbtGetListMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiUketukeSbtGetListMstQuery,
    GetApiUketukeSbtGetListMstQueryVariables
  >(GetApiUketukeSbtGetListMstDocument, options);
}
export type GetApiUketukeSbtGetListMstQueryHookResult = ReturnType<
  typeof useGetApiUketukeSbtGetListMstQuery
>;
export type GetApiUketukeSbtGetListMstLazyQueryHookResult = ReturnType<
  typeof useGetApiUketukeSbtGetListMstLazyQuery
>;
export type GetApiUketukeSbtGetListMstSuspenseQueryHookResult = ReturnType<
  typeof useGetApiUketukeSbtGetListMstSuspenseQuery
>;
export type GetApiUketukeSbtGetListMstQueryResult = Apollo.QueryResult<
  GetApiUketukeSbtGetListMstQuery,
  GetApiUketukeSbtGetListMstQueryVariables
>;
export const GetApiReceiptGetMedicalDetailsDocument = gql`
  query getApiReceiptGetMedicalDetails(
    $hokenId: Int
    $ptId: BigInt
    $sinYm: Int
  ) {
    getApiReceiptGetMedicalDetails(
      hokenId: $hokenId
      ptId: $ptId
      sinYm: $sinYm
    ) {
      status
      message
      data {
        holidays
        sinMeiModels {
          sinId
          sinIdBinding
          itemName
          quantity
          tenKai
          inOutKbn
          isRowColorGray
          sinRpNo
          sinSeqNo
          itemCd
          isDrug
          days
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetMedicalDetailsQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetMedicalDetailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetMedicalDetailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetMedicalDetailsQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceiptGetMedicalDetailsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetMedicalDetailsQuery,
    GetApiReceiptGetMedicalDetailsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetMedicalDetailsQuery,
    GetApiReceiptGetMedicalDetailsQueryVariables
  >(GetApiReceiptGetMedicalDetailsDocument, options);
}
export function useGetApiReceiptGetMedicalDetailsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetMedicalDetailsQuery,
    GetApiReceiptGetMedicalDetailsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetMedicalDetailsQuery,
    GetApiReceiptGetMedicalDetailsQueryVariables
  >(GetApiReceiptGetMedicalDetailsDocument, options);
}
export function useGetApiReceiptGetMedicalDetailsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetMedicalDetailsQuery,
    GetApiReceiptGetMedicalDetailsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetMedicalDetailsQuery,
    GetApiReceiptGetMedicalDetailsQueryVariables
  >(GetApiReceiptGetMedicalDetailsDocument, options);
}
export type GetApiReceiptGetMedicalDetailsQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetMedicalDetailsQuery
>;
export type GetApiReceiptGetMedicalDetailsLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetMedicalDetailsLazyQuery
>;
export type GetApiReceiptGetMedicalDetailsSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetMedicalDetailsSuspenseQuery
>;
export type GetApiReceiptGetMedicalDetailsQueryResult = Apollo.QueryResult<
  GetApiReceiptGetMedicalDetailsQuery,
  GetApiReceiptGetMedicalDetailsQueryVariables
>;
