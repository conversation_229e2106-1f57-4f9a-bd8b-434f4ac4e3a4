import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiFlowSheetGetListHolidayQueryVariables = Types.Exact<{
  holidayFrom: Types.Scalars["Int"]["input"];
  holidayTo: Types.Scalars["Int"]["input"];
}>;

export type GetApiFlowSheetGetListHolidayQuery = {
  __typename?: "query_root";
  getApiFlowSheetGetListHoliday?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesFlowSheetGetListHolidayResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesFlowSheetGetListHolidayResponse";
      listHolidayModel?: Array<{
        __typename?: "DomainModelsFlowSheetHolidayDto";
        holidayKbn?: number;
        holidayName?: string;
        kyusinKbn?: number;
        seqNo?: string;
        sinDate?: number;
      }>;
    };
  };
};

export type PostApiHolidaySaveHolidayMstMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsHolidaySaveHolidayMstRequestInput;
}>;

export type PostApiHolidaySaveHolidayMstMutation = {
  __typename?: "mutation_root";
  postApiHolidaySaveHolidayMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesHolidaySaveHolidayMstResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesHolidaySaveHolidayMstResponse";
      status?: number;
    };
  };
};

export const GetApiFlowSheetGetListHolidayDocument = gql`
  query getApiFlowSheetGetListHoliday($holidayFrom: Int!, $holidayTo: Int!) {
    getApiFlowSheetGetListHoliday(
      holidayFrom: $holidayFrom
      holidayTo: $holidayTo
    ) {
      data {
        listHolidayModel {
          holidayKbn
          holidayName
          kyusinKbn
          seqNo
          sinDate
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiFlowSheetGetListHolidayQuery__
 *
 * To run a query within a React component, call `useGetApiFlowSheetGetListHolidayQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiFlowSheetGetListHolidayQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiFlowSheetGetListHolidayQuery({
 *   variables: {
 *      holidayFrom: // value for 'holidayFrom'
 *      holidayTo: // value for 'holidayTo'
 *   },
 * });
 */
export function useGetApiFlowSheetGetListHolidayQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiFlowSheetGetListHolidayQuery,
    GetApiFlowSheetGetListHolidayQueryVariables
  > &
    (
      | {
          variables: GetApiFlowSheetGetListHolidayQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiFlowSheetGetListHolidayQuery,
    GetApiFlowSheetGetListHolidayQueryVariables
  >(GetApiFlowSheetGetListHolidayDocument, options);
}
export function useGetApiFlowSheetGetListHolidayLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiFlowSheetGetListHolidayQuery,
    GetApiFlowSheetGetListHolidayQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiFlowSheetGetListHolidayQuery,
    GetApiFlowSheetGetListHolidayQueryVariables
  >(GetApiFlowSheetGetListHolidayDocument, options);
}
export function useGetApiFlowSheetGetListHolidaySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiFlowSheetGetListHolidayQuery,
    GetApiFlowSheetGetListHolidayQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiFlowSheetGetListHolidayQuery,
    GetApiFlowSheetGetListHolidayQueryVariables
  >(GetApiFlowSheetGetListHolidayDocument, options);
}
export type GetApiFlowSheetGetListHolidayQueryHookResult = ReturnType<
  typeof useGetApiFlowSheetGetListHolidayQuery
>;
export type GetApiFlowSheetGetListHolidayLazyQueryHookResult = ReturnType<
  typeof useGetApiFlowSheetGetListHolidayLazyQuery
>;
export type GetApiFlowSheetGetListHolidaySuspenseQueryHookResult = ReturnType<
  typeof useGetApiFlowSheetGetListHolidaySuspenseQuery
>;
export type GetApiFlowSheetGetListHolidayQueryResult = Apollo.QueryResult<
  GetApiFlowSheetGetListHolidayQuery,
  GetApiFlowSheetGetListHolidayQueryVariables
>;
export const PostApiHolidaySaveHolidayMstDocument = gql`
  mutation postApiHolidaySaveHolidayMst(
    $input: EmrCloudApiRequestsHolidaySaveHolidayMstRequestInput!
  ) {
    postApiHolidaySaveHolidayMst(
      emrCloudApiRequestsHolidaySaveHolidayMstRequestInput: $input
    ) {
      message
      status
      data {
        status
      }
    }
  }
`;
export type PostApiHolidaySaveHolidayMstMutationFn = Apollo.MutationFunction<
  PostApiHolidaySaveHolidayMstMutation,
  PostApiHolidaySaveHolidayMstMutationVariables
>;

/**
 * __usePostApiHolidaySaveHolidayMstMutation__
 *
 * To run a mutation, you first call `usePostApiHolidaySaveHolidayMstMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiHolidaySaveHolidayMstMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiHolidaySaveHolidayMstMutation, { data, loading, error }] = usePostApiHolidaySaveHolidayMstMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiHolidaySaveHolidayMstMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiHolidaySaveHolidayMstMutation,
    PostApiHolidaySaveHolidayMstMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiHolidaySaveHolidayMstMutation,
    PostApiHolidaySaveHolidayMstMutationVariables
  >(PostApiHolidaySaveHolidayMstDocument, options);
}
export type PostApiHolidaySaveHolidayMstMutationHookResult = ReturnType<
  typeof usePostApiHolidaySaveHolidayMstMutation
>;
export type PostApiHolidaySaveHolidayMstMutationResult =
  Apollo.MutationResult<PostApiHolidaySaveHolidayMstMutation>;
export type PostApiHolidaySaveHolidayMstMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiHolidaySaveHolidayMstMutation,
    PostApiHolidaySaveHolidayMstMutationVariables
  >;
