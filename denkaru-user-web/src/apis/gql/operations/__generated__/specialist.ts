import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetSpecialistsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetSpecialistsQuery = {
  __typename?: "query_root";
  getSpecialists: Array<{
    __typename?: "Specialist";
    specialistId: number;
    name: string;
  }>;
};

export const GetSpecialistsDocument = gql`
  query getSpecialists {
    getSpecialists {
      specialistId
      name
    }
  }
`;

/**
 * __useGetSpecialistsQuery__
 *
 * To run a query within a React component, call `useGetSpecialistsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSpecialistsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSpecialistsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetSpecialistsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetSpecialistsQuery,
    GetSpecialistsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetSpecialistsQuery, GetSpecialistsQueryVariables>(
    GetSpecialistsDocument,
    options,
  );
}
export function useGetSpecialistsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSpecialistsQuery,
    GetSpecialistsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetSpecialistsQuery, GetSpecialistsQueryVariables>(
    GetSpecialistsDocument,
    options,
  );
}
export function useGetSpecialistsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSpecialistsQuery,
    GetSpecialistsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSpecialistsQuery,
    GetSpecialistsQueryVariables
  >(GetSpecialistsDocument, options);
}
export type GetSpecialistsQueryHookResult = ReturnType<
  typeof useGetSpecialistsQuery
>;
export type GetSpecialistsLazyQueryHookResult = ReturnType<
  typeof useGetSpecialistsLazyQuery
>;
export type GetSpecialistsSuspenseQueryHookResult = ReturnType<
  typeof useGetSpecialistsSuspenseQuery
>;
export type GetSpecialistsQueryResult = Apollo.QueryResult<
  GetSpecialistsQuery,
  GetSpecialistsQueryVariables
>;
