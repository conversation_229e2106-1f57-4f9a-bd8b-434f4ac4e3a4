import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiKarteVsphysGetListQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  isSearch: Types.Scalars["Boolean"]["input"];
  startDate?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  endDate?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo: Types.Scalars["Int"]["input"];
}>;

export type GetApiKarteVsphysGetListQuery = {
  __typename?: "query_root";
  getApiKarteVSPHYSGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteVsphySGetKarteVsphysListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesKarteVsphySGetKarteVsphysListResponse";
      physicalInfItems?: {
        __typename?: "DomainModelsKarteVsphySKarteVsphyModel";
        averageDay?: any;
        averageMonths?: any;
        lastSinDate?: number;
        pysicalInfoModels?: Array<{
          __typename?: "DomainModelsSpecialNotePatientInfoPhysicalInfoModel";
          iraiCd?: string;
          iraiDate?: number;
          centerCd?: string;
          centerItemCd1?: string;
          centerItemCd2?: string;
          containerCd?: number;
          digit?: number;
          femaleStd?: string;
          femaleStdHigh?: string;
          femaleStdLow?: string;
          formula?: string;
          hpId?: number;
          isDelete?: number;
          isReadOnly?: boolean;
          unit?: string;
          sortNo?: string;
          oyaItemSeqNo?: number;
          oyaItemCd?: string;
          materialCd?: number;
          maleStdLow?: string;
          maleStdHigh?: string;
          maleStd?: string;
          kensaTime?: string;
          kensaName?: string;
          kensaKana?: string;
          kensaItemSeqNo?: number;
          kensaItemCd?: string;
          kensaInfDetailModels?: Array<{
            __typename?: "DomainModelsSpecialNotePatientInfoKensaInfDetailModel";
            updateDate?: string;
            unit?: string;
            sortNo?: string;
            seqNo?: string;
            resultVal?: string;
            resultType?: string;
            raiinNo?: string;
            ptId?: string;
            kensaName?: string;
            kensaItemCd?: string;
            isDeleted?: number;
            iraiDate?: number;
            iraiCd?: string;
            hpId?: number;
            formula?: string;
            cmtCd2?: string;
            cmtCd1?: string;
            abnormalKbn?: string;
          }>;
        }>;
      };
    };
  };
};

export type PostApiKarteVsphysSaveMutationVariables = Types.Exact<{
  isAdd?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isDeleted?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kensaTime?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  kartePhysicals?: Types.InputMaybe<
    | Array<
        Types.InputMaybe<Types.EmrCloudApiRequestsKarteVsphySSaveKartePhysicalRequestInput>
      >
    | Types.InputMaybe<Types.EmrCloudApiRequestsKarteVsphySSaveKartePhysicalRequestInput>
  >;
  iraiCd?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  iraiDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type PostApiKarteVsphysSaveMutation = {
  __typename?: "mutation_root";
  postApiKarteVSPHYSSave?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteVsphySSaveKarteVsphysResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteVsphySSaveKarteVsphysResponse";
      success?: boolean;
    };
  };
};

export type GetApiSpecialNoteGetStdPointQueryVariables = Types.Exact<{
  sex: Types.Scalars["Int"]["input"];
}>;

export type GetApiSpecialNoteGetStdPointQuery = {
  __typename?: "query_root";
  getApiSpecialNoteGetStdPoint?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSpecialNoteGetStdPointResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSpecialNoteGetStdPointResponse";
      gcStdInfModels?: Array<{
        __typename?: "DomainModelsSpecialNotePatientInfoGcStdInfModel";
        hpId?: number;
        per03?: number;
        per10?: number;
        per25?: number;
        per50?: number;
        per75?: number;
        per90?: number;
        per97?: number;
        point?: number;
        sdAvg?: number;
        sdM10?: number;
        sdM20?: number;
        sdM25?: number;
        sdP10?: number;
        sdP20?: number;
        sdP25?: number;
        sex?: number;
        stdKbn?: number;
      }>;
    };
  };
};

export type PostApiPdfCreatorGrowthCurveMutationVariables = Types.Exact<{
  birthDay?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  heightVisible?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  legend?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  per10?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  per25?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  per3?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  per50?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  printDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  printMode?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId: Types.Scalars["BigInt"]["input"];
  ptNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  scope?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sD1?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  sD2?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  sD25?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  sdAvg?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  sex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  type?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  weightVisible?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type PostApiPdfCreatorGrowthCurveMutation = {
  __typename?: "mutation_root";
  postApiPdfCreatorGrowthCurve?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPdfCreatorReportBase64Response";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPdfCreatorReportBase64Response";
      content?: string;
      fileName?: string;
    };
  };
};

export type GetApiSpecialNoteGetQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiSpecialNoteGetQuery = {
  __typename?: "query_root";
  getApiSpecialNoteGet?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSpecialNoteGetSpecialNoteResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSpecialNoteGetSpecialNoteResponse";
      supples?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtSuppleModel";
        cmt?: string;
        endDate?: number;
        fullEndDate?: number;
        fullStartDate?: number;
        hpId?: number;
        indexCd?: string;
        indexWord?: string;
        isDeleted?: number;
        ptId?: string;
        seqNo?: number;
        sortNo?: number;
        startDate?: number;
      }>;
      summaryInf?: {
        __typename?: "DomainModelsSpecialNoteSummaryInfSummaryInfModel";
        createDate?: string;
        displayUpdateDate?: string;
        hpId?: number;
        id?: string;
        ptId?: string;
        rtext?: string;
        seqNo?: string;
        text?: string;
        updateDate?: string;
      };
      socialHistorys?: Array<{
        __typename?: "DomainModelsKarteMedicalHistoryPtSmokingRelatedModel";
        age?: number;
        brinkmanNumber?: string;
        drinkingAmount?: number;
        drinkingDetail?: string;
        drinkingFrequency?: number;
        hpId?: number;
        isDeleted?: number;
        ptId?: string;
        seqNo?: string;
        smokingDailyCount?: number;
        smokingDetail?: string;
        smokingDuration?: number;
        smokingDurationUnit?: number;
        smokingEndAge?: number;
        smokingEndYear?: number;
        smokingStartAge?: number;
        smokingStartYear?: number;
        smokingStatus?: number;
        totalSmokingDuration?: number;
      }>;
      seikaturekiInfItem?: {
        __typename?: "DomainModelsSpecialNotePatientInfoSeikaturekiInfModel";
        hpId?: number;
        id?: string;
        ptId?: string;
        text?: string;
        seqNo?: string;
      };
      pregnants?: Array<{
        __typename?: "DomainModelsKarteMedicalHistoryPtPregnancyRelatedModel";
        breastfeedStatus?: number;
        hpId?: number;
        isDeleted?: number;
        pregnancyStatus?: number;
        ptId?: string;
      }>;
      pregnancyItems?: Array<{
        __typename?: "DomainModelsSpecialNotePatientInfoPtPregnancyModel";
        endDate?: number;
        hpId?: number;
        id?: string;
        isDeleted?: number;
        ovulationDate?: number;
        ovulationDueDate?: number;
        ovulationWeek?: string;
        periodDate?: number;
        periodDueDate?: number;
        periodWeek?: string;
        ptId?: string;
        seqNo?: number;
        startDate?: number;
        sinDate?: number;
        updateDate?: string;
        updateId?: number;
        updateMachine?: string;
      }>;
      physicalInfos?: Array<{
        __typename?: "DomainModelsSpecialNotePatientInfoPhysicalInfoModel";
        centerCd?: string;
        centerItemCd1?: string;
        centerItemCd2?: string;
        containerCd?: number;
        digit?: number;
        femaleStd?: string;
        femaleStdHigh?: string;
        femaleStdLow?: string;
        formula?: string;
        iraiDate?: number;
        iraiCd?: string;
        hpId?: number;
        isDelete?: number;
        isReadOnly?: boolean;
        unit?: string;
        sortNo?: string;
        oyaItemSeqNo?: number;
        oyaItemCd?: string;
        materialCd?: number;
        maleStdLow?: string;
        maleStdHigh?: string;
        maleStd?: string;
        kensaTime?: string;
        kensaName?: string;
        kensaKana?: string;
        kensaItemSeqNo?: number;
        kensaItemCd?: string;
        kensaInfDetailModels?: Array<{
          __typename?: "DomainModelsSpecialNotePatientInfoKensaInfDetailModel";
          abnormalKbn?: string;
          cmtCd1?: string;
          cmtCd2?: string;
          femaleStd?: string;
          formula?: string;
          hpId?: number;
          iraiCd?: string;
          iraiDate?: number;
          isDeleted?: number;
          kensaItemCd?: string;
          kensaKana?: string;
          kensaName?: string;
          maleStd?: string;
          ptId?: string;
          raiinNo?: string;
          resultType?: string;
          resultVal?: string;
          seqNo?: string;
          sortNo?: string;
          updateDate?: string;
          unit?: string;
        }>;
      }>;
      otherDrugs?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtOtherDrugModel";
        startDate?: number;
        sortNo?: number;
        seqNo?: string;
        ptId?: string;
        itemCd?: string;
        isDeleted?: number;
        hpId?: number;
        fullStartDate?: number;
        fullEndDate?: number;
        endDate?: number;
        drugName?: string;
        cmt?: string;
      }>;
      otcDrugs?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtOtcDrugModel";
        cmt?: string;
        endDate?: number;
        fullStartDate?: number;
        fullEndDate?: number;
        hpId?: number;
        isDeleted?: number;
        ptId?: string;
        seqNo?: string;
        serialNum?: number;
        sortNo?: number;
        startDate?: number;
        tradeName?: string;
      }>;
      kioRekis?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtKioRekiModel";
        byomei?: string;
        byomeiCd?: string;
        byotaiCd?: string;
        isDeleted?: number;
        hpId?: number;
        cmt?: string;
        onSetDate?: string;
        seqNo?: number;
        ptId?: string;
        sortNo?: number;
        startDate?: number;
      }>;
      infectionList?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtInfectionModel";
        byomei?: string;
        byomeiCd?: string;
        byotaiCd?: string;
        cmt?: string;
        hpId?: number;
        isDeleted?: number;
        ptId?: string;
        seqNo?: string;
        sortNo?: number;
        startDate?: number;
      }>;
      families?: Array<{
        __typename?: "DomainModelsFamilyPtFamilyRekiModel";
        byomei?: string;
        byomeiCd?: string;
        byotaiCd?: string;
        cmt?: string;
        familyId?: string;
        hpId?: number;
        id?: string;
        isDeleted?: boolean;
        ptId?: string;
        seqNo?: string;
        sortNo?: number;
        zokugaraCd?: string;
        zokugaraElse?: string;
      }>;
      examResults?: Array<{
        __typename?: "DomainModelsExamResultsExamResultsModel";
        dspCenterName?: string;
        inoutKbn?: number;
        iraiDate?: number;
        sikyuKbn?: number;
        tosekiKbn?: number;
        kensaTime?: string;
        kensaInfDetailModels?: Array<{
          __typename?: "DomainModelsSpecialNotePatientInfoKensaInfDetailModel";
          abnormalKbn?: string;
          cmtCd1?: string;
          cmtCd2?: string;
          femaleStd?: string;
          formula?: string;
          hpId?: number;
          iraiCd?: string;
          iraiDate?: number;
          isDeleted?: number;
          kensaItemCd?: string;
          kensaKana?: string;
          kensaName?: string;
          maleStd?: string;
          raiinNo?: string;
          ptId?: string;
          resultType?: string;
          resultVal?: string;
          seqNo?: string;
          sortNo?: string;
          unit?: string;
          updateDate?: string;
        }>;
      }>;
      cmtInfItem?: {
        __typename?: "DomainModelsPtCmtInfPtCmtInfModel";
        hpId?: number;
        id?: string;
        isDeleted?: number;
        ptId?: string;
        text?: string;
        seqNo?: number;
      };
      alrgyFoodItems?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtAlrgyFoodModel";
        alrgyKbn?: string;
        cmt?: string;
        endDate?: number;
        foodName?: string;
        fullEndDate?: number;
        fullStartDate?: number;
        hpId?: number;
        isDeleted?: number;
        ptId?: string;
        seqNo?: number;
        sortNo?: number;
        startDate?: number;
      }>;
      alrgyElseItems?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtAlrgyElseModel";
        alrgyName?: string;
        cmt?: string;
        endDate?: number;
        fullEndDate?: number;
        fullStartDate?: number;
        hpId?: number;
        ptId?: string;
        isDeleted?: number;
        seqNo?: number;
        sortNo?: number;
        startDate?: number;
      }>;
      alrgyDrugItems?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtAlrgyDrugModel";
        cmt?: string;
        drugName?: string;
        endDate?: number;
        fullEndDate?: number;
        hpId?: number;
        fullStartDate?: number;
        isDeleted?: number;
        isContraindicated?: boolean;
        itemCd?: string;
        ptId?: string;
        seqNo?: number;
        startDate?: number;
        sortNo?: number;
      }>;
    };
  };
};

export const GetApiKarteVsphysGetListDocument = gql`
  query getApiKarteVSPHYSGetList(
    $ptId: BigInt!
    $isSearch: Boolean!
    $startDate: BigInt
    $endDate: BigInt
    $raiinNo: Int!
  ) {
    getApiKarteVSPHYSGetList(
      ptId: $ptId
      isSearch: $isSearch
      startDate: $startDate
      endDate: $endDate
      raiinNo: $raiinNo
    ) {
      data {
        physicalInfItems {
          averageDay
          averageMonths
          pysicalInfoModels {
            iraiCd
            iraiDate
            centerCd
            centerItemCd1
            centerItemCd2
            containerCd
            digit
            femaleStd
            femaleStdHigh
            femaleStdLow
            formula
            hpId
            isDelete
            isReadOnly
            kensaInfDetailModels {
              updateDate
              unit
              sortNo
              seqNo
              resultVal
              resultType
              raiinNo
              ptId
              kensaName
              kensaItemCd
              isDeleted
              iraiDate
              iraiCd
              hpId
              formula
              cmtCd2
              cmtCd1
              abnormalKbn
            }
            unit
            sortNo
            oyaItemSeqNo
            oyaItemCd
            materialCd
            maleStdLow
            maleStdHigh
            maleStd
            kensaTime
            kensaName
            kensaKana
            kensaItemSeqNo
            kensaItemCd
          }
          lastSinDate
        }
      }
    }
  }
`;

/**
 * __useGetApiKarteVsphysGetListQuery__
 *
 * To run a query within a React component, call `useGetApiKarteVsphysGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKarteVsphysGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKarteVsphysGetListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      isSearch: // value for 'isSearch'
 *      startDate: // value for 'startDate'
 *      endDate: // value for 'endDate'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiKarteVsphysGetListQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiKarteVsphysGetListQuery,
    GetApiKarteVsphysGetListQueryVariables
  > &
    (
      | { variables: GetApiKarteVsphysGetListQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKarteVsphysGetListQuery,
    GetApiKarteVsphysGetListQueryVariables
  >(GetApiKarteVsphysGetListDocument, options);
}
export function useGetApiKarteVsphysGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKarteVsphysGetListQuery,
    GetApiKarteVsphysGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKarteVsphysGetListQuery,
    GetApiKarteVsphysGetListQueryVariables
  >(GetApiKarteVsphysGetListDocument, options);
}
export function useGetApiKarteVsphysGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKarteVsphysGetListQuery,
    GetApiKarteVsphysGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKarteVsphysGetListQuery,
    GetApiKarteVsphysGetListQueryVariables
  >(GetApiKarteVsphysGetListDocument, options);
}
export type GetApiKarteVsphysGetListQueryHookResult = ReturnType<
  typeof useGetApiKarteVsphysGetListQuery
>;
export type GetApiKarteVsphysGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiKarteVsphysGetListLazyQuery
>;
export type GetApiKarteVsphysGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiKarteVsphysGetListSuspenseQuery
>;
export type GetApiKarteVsphysGetListQueryResult = Apollo.QueryResult<
  GetApiKarteVsphysGetListQuery,
  GetApiKarteVsphysGetListQueryVariables
>;
export const PostApiKarteVsphysSaveDocument = gql`
  mutation postApiKarteVSPHYSSave(
    $isAdd: Boolean
    $isDeleted: Int
    $kensaTime: String
    $ptId: BigInt
    $raiinNo: BigInt
    $kartePhysicals: [EmrCloudApiRequestsKarteVsphySSaveKartePhysicalRequestInput]
    $iraiCd: BigInt
    $iraiDate: Int
  ) {
    postApiKarteVSPHYSSave(
      emrCloudApiRequestsKarteVsphySSaveKartePhysicalsRequestInput: {
        ptId: $ptId
        kensaTime: $kensaTime
        isDeleted: $isDeleted
        isAdd: $isAdd
        raiinNo: $raiinNo
        kartePhysicals: $kartePhysicals
        iraiCd: $iraiCd
        iraiDate: $iraiDate
      }
    ) {
      message
      status
      data {
        success
      }
    }
  }
`;
export type PostApiKarteVsphysSaveMutationFn = Apollo.MutationFunction<
  PostApiKarteVsphysSaveMutation,
  PostApiKarteVsphysSaveMutationVariables
>;

/**
 * __usePostApiKarteVsphysSaveMutation__
 *
 * To run a mutation, you first call `usePostApiKarteVsphysSaveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKarteVsphysSaveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKarteVsphysSaveMutation, { data, loading, error }] = usePostApiKarteVsphysSaveMutation({
 *   variables: {
 *      isAdd: // value for 'isAdd'
 *      isDeleted: // value for 'isDeleted'
 *      kensaTime: // value for 'kensaTime'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      kartePhysicals: // value for 'kartePhysicals'
 *      iraiCd: // value for 'iraiCd'
 *      iraiDate: // value for 'iraiDate'
 *   },
 * });
 */
export function usePostApiKarteVsphysSaveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKarteVsphysSaveMutation,
    PostApiKarteVsphysSaveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKarteVsphysSaveMutation,
    PostApiKarteVsphysSaveMutationVariables
  >(PostApiKarteVsphysSaveDocument, options);
}
export type PostApiKarteVsphysSaveMutationHookResult = ReturnType<
  typeof usePostApiKarteVsphysSaveMutation
>;
export type PostApiKarteVsphysSaveMutationResult =
  Apollo.MutationResult<PostApiKarteVsphysSaveMutation>;
export type PostApiKarteVsphysSaveMutationOptions = Apollo.BaseMutationOptions<
  PostApiKarteVsphysSaveMutation,
  PostApiKarteVsphysSaveMutationVariables
>;
export const GetApiSpecialNoteGetStdPointDocument = gql`
  query getApiSpecialNoteGetStdPoint($sex: Int!) {
    getApiSpecialNoteGetStdPoint(sex: $sex) {
      data {
        gcStdInfModels {
          hpId
          per03
          per10
          per25
          per50
          per75
          per90
          per97
          point
          sdAvg
          sdM10
          sdM20
          sdM25
          sdP10
          sdP20
          sdP25
          sex
          stdKbn
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiSpecialNoteGetStdPointQuery__
 *
 * To run a query within a React component, call `useGetApiSpecialNoteGetStdPointQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiSpecialNoteGetStdPointQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiSpecialNoteGetStdPointQuery({
 *   variables: {
 *      sex: // value for 'sex'
 *   },
 * });
 */
export function useGetApiSpecialNoteGetStdPointQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiSpecialNoteGetStdPointQuery,
    GetApiSpecialNoteGetStdPointQueryVariables
  > &
    (
      | {
          variables: GetApiSpecialNoteGetStdPointQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiSpecialNoteGetStdPointQuery,
    GetApiSpecialNoteGetStdPointQueryVariables
  >(GetApiSpecialNoteGetStdPointDocument, options);
}
export function useGetApiSpecialNoteGetStdPointLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiSpecialNoteGetStdPointQuery,
    GetApiSpecialNoteGetStdPointQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiSpecialNoteGetStdPointQuery,
    GetApiSpecialNoteGetStdPointQueryVariables
  >(GetApiSpecialNoteGetStdPointDocument, options);
}
export function useGetApiSpecialNoteGetStdPointSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiSpecialNoteGetStdPointQuery,
    GetApiSpecialNoteGetStdPointQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiSpecialNoteGetStdPointQuery,
    GetApiSpecialNoteGetStdPointQueryVariables
  >(GetApiSpecialNoteGetStdPointDocument, options);
}
export type GetApiSpecialNoteGetStdPointQueryHookResult = ReturnType<
  typeof useGetApiSpecialNoteGetStdPointQuery
>;
export type GetApiSpecialNoteGetStdPointLazyQueryHookResult = ReturnType<
  typeof useGetApiSpecialNoteGetStdPointLazyQuery
>;
export type GetApiSpecialNoteGetStdPointSuspenseQueryHookResult = ReturnType<
  typeof useGetApiSpecialNoteGetStdPointSuspenseQuery
>;
export type GetApiSpecialNoteGetStdPointQueryResult = Apollo.QueryResult<
  GetApiSpecialNoteGetStdPointQuery,
  GetApiSpecialNoteGetStdPointQueryVariables
>;
export const PostApiPdfCreatorGrowthCurveDocument = gql`
  mutation postApiPdfCreatorGrowthCurve(
    $birthDay: Int
    $heightVisible: Boolean
    $legend: Boolean
    $per10: Boolean
    $per25: Boolean
    $per3: Boolean
    $per50: Boolean
    $printDate: Int
    $ptName: String
    $printMode: Int
    $ptId: BigInt!
    $ptNum: BigInt
    $scope: Int
    $sD1: Boolean
    $sD2: Boolean
    $sD25: Boolean
    $sdAvg: Boolean
    $sex: Int
    $type: Int
    $weightVisible: Boolean
  ) {
    postApiPdfCreatorGrowthCurve(
      emrCloudApiRequestsExportPdFGrowthCurvePrintDataRequestInput: {
        birthDay: $birthDay
        heightVisible: $heightVisible
        legend: $legend
        per3: $per3
        per10: $per10
        per25: $per25
        per50: $per50
        printDate: $printDate
        printMode: $printMode
        ptId: $ptId
        ptName: $ptName
        ptNum: $ptNum
        sD1: $sD1
        sD2: $sD2
        sD25: $sD25
        scope: $scope
        sdAvg: $sdAvg
        sex: $sex
        type: $type
        weightVisible: $weightVisible
      }
    ) {
      data {
        content
        fileName
      }
      message
      status
    }
  }
`;
export type PostApiPdfCreatorGrowthCurveMutationFn = Apollo.MutationFunction<
  PostApiPdfCreatorGrowthCurveMutation,
  PostApiPdfCreatorGrowthCurveMutationVariables
>;

/**
 * __usePostApiPdfCreatorGrowthCurveMutation__
 *
 * To run a mutation, you first call `usePostApiPdfCreatorGrowthCurveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPdfCreatorGrowthCurveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPdfCreatorGrowthCurveMutation, { data, loading, error }] = usePostApiPdfCreatorGrowthCurveMutation({
 *   variables: {
 *      birthDay: // value for 'birthDay'
 *      heightVisible: // value for 'heightVisible'
 *      legend: // value for 'legend'
 *      per10: // value for 'per10'
 *      per25: // value for 'per25'
 *      per3: // value for 'per3'
 *      per50: // value for 'per50'
 *      printDate: // value for 'printDate'
 *      ptName: // value for 'ptName'
 *      printMode: // value for 'printMode'
 *      ptId: // value for 'ptId'
 *      ptNum: // value for 'ptNum'
 *      scope: // value for 'scope'
 *      sD1: // value for 'sD1'
 *      sD2: // value for 'sD2'
 *      sD25: // value for 'sD25'
 *      sdAvg: // value for 'sdAvg'
 *      sex: // value for 'sex'
 *      type: // value for 'type'
 *      weightVisible: // value for 'weightVisible'
 *   },
 * });
 */
export function usePostApiPdfCreatorGrowthCurveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPdfCreatorGrowthCurveMutation,
    PostApiPdfCreatorGrowthCurveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPdfCreatorGrowthCurveMutation,
    PostApiPdfCreatorGrowthCurveMutationVariables
  >(PostApiPdfCreatorGrowthCurveDocument, options);
}
export type PostApiPdfCreatorGrowthCurveMutationHookResult = ReturnType<
  typeof usePostApiPdfCreatorGrowthCurveMutation
>;
export type PostApiPdfCreatorGrowthCurveMutationResult =
  Apollo.MutationResult<PostApiPdfCreatorGrowthCurveMutation>;
export type PostApiPdfCreatorGrowthCurveMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPdfCreatorGrowthCurveMutation,
    PostApiPdfCreatorGrowthCurveMutationVariables
  >;
export const GetApiSpecialNoteGetDocument = gql`
  query getApiSpecialNoteGet($ptId: BigInt, $raiinNo: BigInt) {
    getApiSpecialNoteGet(ptId: $ptId, raiinNo: $raiinNo) {
      message
      status
      data {
        supples {
          cmt
          endDate
          fullEndDate
          fullStartDate
          hpId
          indexCd
          indexWord
          isDeleted
          ptId
          seqNo
          sortNo
          startDate
        }
        summaryInf {
          createDate
          displayUpdateDate
          hpId
          id
          ptId
          rtext
          seqNo
          text
          updateDate
        }
        socialHistorys {
          age
          brinkmanNumber
          drinkingAmount
          drinkingDetail
          drinkingFrequency
          hpId
          isDeleted
          ptId
          seqNo
          smokingDailyCount
          smokingDetail
          smokingDuration
          smokingDurationUnit
          smokingEndAge
          smokingEndYear
          smokingStartAge
          smokingStartYear
          smokingStatus
          totalSmokingDuration
        }
        seikaturekiInfItem {
          hpId
          id
          ptId
          text
          seqNo
        }
        pregnants {
          breastfeedStatus
          hpId
          isDeleted
          pregnancyStatus
          ptId
        }
        pregnancyItems {
          endDate
          hpId
          id
          isDeleted
          ovulationDate
          ovulationDueDate
          ovulationWeek
          periodDate
          periodDueDate
          periodWeek
          ptId
          seqNo
          startDate
          sinDate
          updateDate
          updateId
          updateMachine
        }
        physicalInfos {
          centerCd
          centerItemCd1
          centerItemCd2
          containerCd
          digit
          femaleStd
          femaleStdHigh
          femaleStdLow
          formula
          iraiDate
          iraiCd
          hpId
          isDelete
          isReadOnly
          unit
          sortNo
          oyaItemSeqNo
          oyaItemCd
          materialCd
          maleStdLow
          maleStdHigh
          maleStd
          kensaTime
          kensaName
          kensaKana
          kensaItemSeqNo
          kensaItemCd
          kensaInfDetailModels {
            abnormalKbn
            cmtCd1
            cmtCd2
            femaleStd
            formula
            hpId
            iraiCd
            iraiDate
            isDeleted
            kensaItemCd
            kensaKana
            kensaName
            maleStd
            ptId
            raiinNo
            resultType
            resultVal
            seqNo
            sortNo
            updateDate
            unit
          }
        }
        otherDrugs {
          startDate
          sortNo
          seqNo
          ptId
          itemCd
          isDeleted
          hpId
          fullStartDate
          fullEndDate
          endDate
          drugName
          cmt
        }
        otcDrugs {
          cmt
          endDate
          fullStartDate
          fullEndDate
          hpId
          isDeleted
          ptId
          seqNo
          serialNum
          sortNo
          startDate
          tradeName
        }
        kioRekis {
          byomei
          byomeiCd
          byotaiCd
          isDeleted
          hpId
          cmt
          onSetDate
          seqNo
          ptId
          sortNo
          startDate
        }
        infectionList {
          byomei
          byomeiCd
          byotaiCd
          cmt
          hpId
          isDeleted
          ptId
          seqNo
          sortNo
          startDate
        }
        families {
          byomei
          byomeiCd
          byotaiCd
          cmt
          familyId
          hpId
          id
          isDeleted
          ptId
          seqNo
          sortNo
          zokugaraCd
          zokugaraElse
        }
        examResults {
          dspCenterName
          inoutKbn
          iraiDate
          sikyuKbn
          tosekiKbn
          kensaInfDetailModels {
            abnormalKbn
            cmtCd1
            cmtCd2
            femaleStd
            formula
            hpId
            iraiCd
            iraiDate
            isDeleted
            kensaItemCd
            kensaKana
            kensaName
            maleStd
            raiinNo
            ptId
            resultType
            resultVal
            seqNo
            sortNo
            unit
            updateDate
          }
          kensaTime
        }
        cmtInfItem {
          hpId
          id
          isDeleted
          ptId
          text
          seqNo
        }
        alrgyFoodItems {
          alrgyKbn
          cmt
          endDate
          foodName
          fullEndDate
          fullStartDate
          hpId
          isDeleted
          ptId
          seqNo
          sortNo
          startDate
        }
        alrgyElseItems {
          alrgyName
          cmt
          endDate
          fullEndDate
          fullStartDate
          hpId
          ptId
          isDeleted
          seqNo
          sortNo
          startDate
        }
        alrgyDrugItems {
          cmt
          drugName
          endDate
          fullEndDate
          hpId
          fullStartDate
          isDeleted
          isContraindicated
          itemCd
          ptId
          seqNo
          startDate
          sortNo
        }
      }
    }
  }
`;

/**
 * __useGetApiSpecialNoteGetQuery__
 *
 * To run a query within a React component, call `useGetApiSpecialNoteGetQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiSpecialNoteGetQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiSpecialNoteGetQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiSpecialNoteGetQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiSpecialNoteGetQuery,
    GetApiSpecialNoteGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiSpecialNoteGetQuery,
    GetApiSpecialNoteGetQueryVariables
  >(GetApiSpecialNoteGetDocument, options);
}
export function useGetApiSpecialNoteGetLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiSpecialNoteGetQuery,
    GetApiSpecialNoteGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiSpecialNoteGetQuery,
    GetApiSpecialNoteGetQueryVariables
  >(GetApiSpecialNoteGetDocument, options);
}
export function useGetApiSpecialNoteGetSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiSpecialNoteGetQuery,
    GetApiSpecialNoteGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiSpecialNoteGetQuery,
    GetApiSpecialNoteGetQueryVariables
  >(GetApiSpecialNoteGetDocument, options);
}
export type GetApiSpecialNoteGetQueryHookResult = ReturnType<
  typeof useGetApiSpecialNoteGetQuery
>;
export type GetApiSpecialNoteGetLazyQueryHookResult = ReturnType<
  typeof useGetApiSpecialNoteGetLazyQuery
>;
export type GetApiSpecialNoteGetSuspenseQueryHookResult = ReturnType<
  typeof useGetApiSpecialNoteGetSuspenseQuery
>;
export type GetApiSpecialNoteGetQueryResult = Apollo.QueryResult<
  GetApiSpecialNoteGetQuery,
  GetApiSpecialNoteGetQueryVariables
>;
