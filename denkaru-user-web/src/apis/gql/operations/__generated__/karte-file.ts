import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiKarteFileMoveKarteFileCategoryMutationVariables =
  Types.Exact<{
    categoryCd?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    fileId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  }>;

export type PostApiKarteFileMoveKarteFileCategoryMutation = {
  __typename?: "mutation_root";
  postApiKarteFileMoveKarteFileCategory?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteFileUpdateKarteFileResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteFileUpdateKarteFileResponse";
      status?: number;
    };
  };
};

export type PostApiKarteFileAttachKarteFileMutationVariables = Types.Exact<{
  fileId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  tabKey?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type PostApiKarteFileAttachKarteFileMutation = {
  __typename?: "mutation_root";
  postApiKarteFileAttachKarteFile?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteFileAttachKarteFileResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteFileAttachKarteFileResponse";
      status?: number;
    };
  };
};

export type GetApiKarteFileGetQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  fileId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiKarteFileGetQuery = {
  __typename?: "query_root";
  getApiKarteFileGet?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteFileGetKarteFileRespone";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteFileGetKarteFileRespone";
      link?: string;
      status?: number;
    };
  };
};

export const PostApiKarteFileMoveKarteFileCategoryDocument = gql`
  mutation postApiKarteFileMoveKarteFileCategory(
    $categoryCd: Int
    $fileId: Int
    $ptId: BigInt
  ) {
    postApiKarteFileMoveKarteFileCategory(
      emrCloudApiRequestsKarteFileMoveKarteFileCategoryRequestInput: {
        categoryCd: $categoryCd
        fileId: $fileId
        ptId: $ptId
      }
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiKarteFileMoveKarteFileCategoryMutationFn =
  Apollo.MutationFunction<
    PostApiKarteFileMoveKarteFileCategoryMutation,
    PostApiKarteFileMoveKarteFileCategoryMutationVariables
  >;

/**
 * __usePostApiKarteFileMoveKarteFileCategoryMutation__
 *
 * To run a mutation, you first call `usePostApiKarteFileMoveKarteFileCategoryMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKarteFileMoveKarteFileCategoryMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKarteFileMoveKarteFileCategoryMutation, { data, loading, error }] = usePostApiKarteFileMoveKarteFileCategoryMutation({
 *   variables: {
 *      categoryCd: // value for 'categoryCd'
 *      fileId: // value for 'fileId'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function usePostApiKarteFileMoveKarteFileCategoryMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKarteFileMoveKarteFileCategoryMutation,
    PostApiKarteFileMoveKarteFileCategoryMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKarteFileMoveKarteFileCategoryMutation,
    PostApiKarteFileMoveKarteFileCategoryMutationVariables
  >(PostApiKarteFileMoveKarteFileCategoryDocument, options);
}
export type PostApiKarteFileMoveKarteFileCategoryMutationHookResult =
  ReturnType<typeof usePostApiKarteFileMoveKarteFileCategoryMutation>;
export type PostApiKarteFileMoveKarteFileCategoryMutationResult =
  Apollo.MutationResult<PostApiKarteFileMoveKarteFileCategoryMutation>;
export type PostApiKarteFileMoveKarteFileCategoryMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKarteFileMoveKarteFileCategoryMutation,
    PostApiKarteFileMoveKarteFileCategoryMutationVariables
  >;
export const PostApiKarteFileAttachKarteFileDocument = gql`
  mutation postApiKarteFileAttachKarteFile(
    $fileId: Int
    $ptId: BigInt
    $raiinNo: Int
    $sinDate: Int
    $tabKey: String
  ) {
    postApiKarteFileAttachKarteFile(
      emrCloudApiRequestsKarteFileAttachKarteFileRequestInput: {
        fileId: $fileId
        ptId: $ptId
        raiinNo: $raiinNo
        sinDate: $sinDate
        tabKey: $tabKey
      }
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiKarteFileAttachKarteFileMutationFn = Apollo.MutationFunction<
  PostApiKarteFileAttachKarteFileMutation,
  PostApiKarteFileAttachKarteFileMutationVariables
>;

/**
 * __usePostApiKarteFileAttachKarteFileMutation__
 *
 * To run a mutation, you first call `usePostApiKarteFileAttachKarteFileMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKarteFileAttachKarteFileMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKarteFileAttachKarteFileMutation, { data, loading, error }] = usePostApiKarteFileAttachKarteFileMutation({
 *   variables: {
 *      fileId: // value for 'fileId'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *      tabKey: // value for 'tabKey'
 *   },
 * });
 */
export function usePostApiKarteFileAttachKarteFileMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKarteFileAttachKarteFileMutation,
    PostApiKarteFileAttachKarteFileMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKarteFileAttachKarteFileMutation,
    PostApiKarteFileAttachKarteFileMutationVariables
  >(PostApiKarteFileAttachKarteFileDocument, options);
}
export type PostApiKarteFileAttachKarteFileMutationHookResult = ReturnType<
  typeof usePostApiKarteFileAttachKarteFileMutation
>;
export type PostApiKarteFileAttachKarteFileMutationResult =
  Apollo.MutationResult<PostApiKarteFileAttachKarteFileMutation>;
export type PostApiKarteFileAttachKarteFileMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKarteFileAttachKarteFileMutation,
    PostApiKarteFileAttachKarteFileMutationVariables
  >;
export const GetApiKarteFileGetDocument = gql`
  query getApiKarteFileGet($ptId: BigInt, $fileId: Int) {
    getApiKarteFileGet(fileId: $fileId, ptId: $ptId) {
      data {
        link
        status
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiKarteFileGetQuery__
 *
 * To run a query within a React component, call `useGetApiKarteFileGetQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKarteFileGetQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKarteFileGetQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      fileId: // value for 'fileId'
 *   },
 * });
 */
export function useGetApiKarteFileGetQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiKarteFileGetQuery,
    GetApiKarteFileGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKarteFileGetQuery,
    GetApiKarteFileGetQueryVariables
  >(GetApiKarteFileGetDocument, options);
}
export function useGetApiKarteFileGetLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKarteFileGetQuery,
    GetApiKarteFileGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKarteFileGetQuery,
    GetApiKarteFileGetQueryVariables
  >(GetApiKarteFileGetDocument, options);
}
export function useGetApiKarteFileGetSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKarteFileGetQuery,
    GetApiKarteFileGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKarteFileGetQuery,
    GetApiKarteFileGetQueryVariables
  >(GetApiKarteFileGetDocument, options);
}
export type GetApiKarteFileGetQueryHookResult = ReturnType<
  typeof useGetApiKarteFileGetQuery
>;
export type GetApiKarteFileGetLazyQueryHookResult = ReturnType<
  typeof useGetApiKarteFileGetLazyQuery
>;
export type GetApiKarteFileGetSuspenseQueryHookResult = ReturnType<
  typeof useGetApiKarteFileGetSuspenseQuery
>;
export type GetApiKarteFileGetQueryResult = Apollo.QueryResult<
  GetApiKarteFileGetQuery,
  GetApiKarteFileGetQueryVariables
>;
