import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiMedicalExaminationGetHistoryFollowSinDateQueryVariables =
  Types.Exact<{
    ptId: Types.Scalars["BigInt"]["input"];
    raiinNo: Types.Scalars["BigInt"]["input"];
    sinDate: Types.Scalars["Int"]["input"];
    flag: Types.Scalars["Int"]["input"];
  }>;

export type GetApiMedicalExaminationGetHistoryFollowSinDateQuery = {
  __typename?: "query_root";
  getApiMedicalExaminationGetHistoryFollowSinDate?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationGetHistoryFollowSindateResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationGetHistoryFollowSindateResponse";
      raiinfList?: Array<{
        __typename?: "UseCaseMedicalExaminationGetHistoryHistoryKarteOdrRaiinItem";
        syosaisinKbn?: number;
        syosaisinDisplay?: string;
        jikanKbn?: number;
        jikanDisplay?: string;
        kaId?: number;
        kaName?: string;
        treatmentDepartmentId?: number;
        raiinNo?: string;
        hokenPid?: number;
        hokenTitle?: string;
        karteEdition?: {
          __typename?: "UseCaseMedicalExaminationGetHistoryKarteEditionItem";
          hokenGroups?: Array<{
            __typename?: "UseCaseMedicalExaminationGetHistoryHokenGroupHistoryItem";
            hokenPid?: number;
            hokenTitle?: string;
            groupOdrItems?: Array<{
              __typename?: "UseCaseMedicalExaminationGetHistoryGroupOdrGHistoryItem";
              groupKouiCode?: number;
              groupName?: string;
              inOutKbn?: number;
              inOutName?: string;
              syohoSbt?: number;
              sikyuKbn?: number;
              tosekiKbn?: number;
              santeiKbn?: number;
              odrInfs?: Array<{
                __typename?: "UseCaseMedicalExaminationGetHistoryOdrInfHistoryItem";
                odrKouiKbn?: number;
                inoutKbn?: number;
                rpName?: string;
                sortNo?: number;
                odrDetails?: Array<{
                  __typename?: "UseCaseOrdInfsGetListTreesOdrInfDetailItem";
                  itemName?: string;
                  displayItemName?: string;
                  ten?: number;
                  isGetPriceInYakka?: boolean;
                  syohoKbn?: number;
                  yakka?: number;
                  unitName?: string;
                  sinKouiKbn?: number;
                  drugKbn?: number;
                  itemCd?: string;
                  termVal?: number;
                  suryo?: number;
                  bunkatu?: string;
                  cmtOpt?: string;
                  rowNo?: number;
                }>;
              }>;
            }>;
          }>;
        };
      }>;
    };
  };
};

export type GetApiAccountingPtByoMeiQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
}>;

export type GetApiAccountingPtByoMeiQuery = {
  __typename?: "query_root";
  getApiAccountingPtByoMei?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingGetPtByoMeiResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingGetPtByoMeiResponse";
      ptDiseaseDtos?: Array<{
        __typename?: "UseCaseAccountingGetPtByoMeiPtDiseaseDto";
        byomeiCd?: string;
        fullByomei?: string;
        startDate?: string;
        tenKiBinding?: string;
        tenkiDate?: string;
        syubyoKbn?: number;
        isSuspect?: number;
        sikkanKbn?: number;
        nanbyoCd?: number;
        hokenId?: number;
      }>;
    };
  };
};

export type GetApiAccountingGetMeiHoGaiQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
  raiinNos?: Types.InputMaybe<
    Array<Types.Scalars["BigInt"]["input"]> | Types.Scalars["BigInt"]["input"]
  >;
}>;

export type GetApiAccountingGetMeiHoGaiQuery = {
  __typename?: "query_root";
  getApiAccountingGetMeiHoGai?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingGetMeiHoGaiResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingGetMeiHoGaiResponse";
      sinMeiModels?: Array<{
        __typename?: "DomainModelsAccountingSinMeiModel";
        sinIdBinding?: string;
        asterisk?: string;
        itemName?: string;
        quantity?: string;
        tenKai?: string;
        totalBinding?: string;
        isRowColorGray?: boolean;
        futanSBinding?: string;
        futanK1Binding?: string;
        futanK2Binding?: string;
        futanK3Binding?: string;
        futanK4Binding?: string;
        enTenKbn?: number;
      }>;
    };
  };
};

export type GetApiAccountingWarningMemoQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
}>;

export type GetApiAccountingWarningMemoQuery = {
  __typename?: "query_root";
  getApiAccountingWarningMemo?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingGetWarningMemoResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingGetWarningMemoResponse";
      warningMemoDtos?: Array<{
        __typename?: "DomainModelsAccountingWarningMemoDto";
        color?: number;
        memo?: string;
        raiinNo?: string;
      }>;
    };
  };
};

export type GetApiAccountingGetListQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
}>;

export type GetApiAccountingGetListQuery = {
  __typename?: "query_root";
  getApiAccountingGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingGetAccountingResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingGetAccountingResponse";
      adjustFutan?: number;
      comment?: string;
      debitBalance?: number;
      isSettled?: boolean;
      kanFutan?: number;
      payType?: number;
      sumAdjust?: number;
      sumAdjustView?: number;
      tax?: number;
      thisCredit?: number;
      thisWari?: number;
      totalPoint?: number;
      totalSelfExpense?: number;
      allSyunoSeikyuModel?: Array<{
        __typename?: "DomainModelsAccountDueSyunoSeikyuModel";
        adjustFutan?: number;
        adjustFutanOne?: number;
        hokenId?: number;
        hpId?: number;
        kanFutanOne?: number;
        newAdjustFutan?: number;
        newSeikyuDetail?: string;
        newSeikyuGaku?: number;
        newSeikyuTensu?: number;
        nyukinKbn?: number;
        ptId?: string;
        raiinNo?: string;
        seikyuDetail?: string;
        seikyuGaku?: number;
        seikyuTensu?: number;
        sinDate?: number;
        sumAdjustOne?: number;
        taxOne?: number;
        totalPointOne?: number;
        totalSelfExpenseOne?: number;
        kaikeiInfModels?: Array<{
          __typename?: "DomainModelsAccountingKaikeiInfModel";
          hpId?: number;
          ptId?: string;
          sinDate?: number;
          raiinNo?: string;
          hokenId?: number;
          kohi1Id?: number;
          kohi2Id?: number;
          kohi3Id?: number;
          kohi4Id?: number;
          hokenKbn?: number;
          hokenSbtCd?: number;
          receSbt?: string;
          houbetu?: string;
          kohi1Houbetu?: string;
          kohi2Houbetu?: string;
          kohi3Houbetu?: string;
          kohi4Houbetu?: string;
          honkeKbn?: number;
          hokenRate?: number;
          ptRate?: number;
          dispRate?: number;
          tensu?: number;
          totalIryohi?: number;
          ptFutan?: number;
          jihiFutan?: number;
          jihiTax?: number;
          jihiOuttax?: number;
          jihiFutanTaxfree?: number;
          jihiFutanTaxNr?: number;
          jihiFutanTaxGen?: number;
          jihiFutanOuttaxNr?: number;
          jihiFutanOuttaxGen?: number;
          jihiTaxNr?: number;
          jihiTaxGen?: number;
          jihiOuttaxNr?: number;
          jihiOuttaxGen?: number;
          adjustFutan?: number;
          adjustRound?: number;
          totalPtFutan?: number;
          adjustFutanVal?: number;
          adjustFutanRange?: number;
          adjustRateVal?: number;
          adjustRateRange?: number;
          kohi1Priority?: string;
          kohi2Priority?: string;
          kohi3Priority?: string;
          kohi4Priority?: string;
          sinYm?: number;
        }>;
        raiinInfModel?: {
          __typename?: "DomainModelsAccountingSyunoRaiinInfModel";
          kaikeiTime?: string;
          status?: number;
          uketukeSbt?: number;
        };
        syunoNyukinModels?: Array<{
          __typename?: "DomainModelsAccountDueSyunoNyukinModel";
          adjustFutan?: number;
          hpId?: number;
          nyukinCmt?: string;
          nyukinDate?: number;
          nyukinGaku?: number;
          nyukinjiDetail?: string;
          nyukinjiSeikyu?: number;
          nyukinjiTensu?: number;
          paymentMethodCd?: number;
          ptId?: string;
          raiinNo?: string;
          seqNo?: string;
          sinDate?: number;
          sortNo?: number;
          uketukeSbt?: number;
        }>;
      }>;
      listRaiinInf?: Array<{
        __typename?: "UseCaseAccountingGetAccountingInfRaiinInfItem";
        hokenId?: number;
        raiinNo?: string;
        isFcoWaiting?: boolean;
      }>;
      syunoSeikyuModels?: Array<{
        __typename?: "DomainModelsAccountDueSyunoSeikyuModel";
        adjustFutan?: number;
        adjustFutanOne?: number;
        hokenId?: number;
        hpId?: number;
        kanFutanOne?: number;
        newAdjustFutan?: number;
        newSeikyuDetail?: string;
        newSeikyuGaku?: number;
        newSeikyuTensu?: number;
        nyukinKbn?: number;
        ptId?: string;
        raiinNo?: string;
        seikyuDetail?: string;
        seikyuGaku?: number;
        seikyuTensu?: number;
        sinDate?: number;
        sumAdjustOne?: number;
        taxOne?: number;
        totalPointOne?: number;
        totalSelfExpenseOne?: number;
        kaikeiInfModels?: Array<{
          __typename?: "DomainModelsAccountingKaikeiInfModel";
          hpId?: number;
          ptId?: string;
          sinDate?: number;
          raiinNo?: string;
          hokenId?: number;
          kohi1Id?: number;
          kohi2Id?: number;
          kohi3Id?: number;
          kohi4Id?: number;
          hokenKbn?: number;
          hokenSbtCd?: number;
          receSbt?: string;
          houbetu?: string;
          kohi1Houbetu?: string;
          kohi2Houbetu?: string;
          kohi3Houbetu?: string;
          kohi4Houbetu?: string;
          honkeKbn?: number;
          hokenRate?: number;
          ptRate?: number;
          dispRate?: number;
          tensu?: number;
          totalIryohi?: number;
          ptFutan?: number;
          jihiFutan?: number;
          jihiTax?: number;
          jihiOuttax?: number;
          jihiFutanTaxfree?: number;
          jihiFutanTaxNr?: number;
          jihiFutanTaxGen?: number;
          jihiFutanOuttaxNr?: number;
          jihiFutanOuttaxGen?: number;
          jihiTaxNr?: number;
          jihiTaxGen?: number;
          jihiOuttaxNr?: number;
          jihiOuttaxGen?: number;
          adjustFutan?: number;
          adjustRound?: number;
          totalPtFutan?: number;
          adjustFutanVal?: number;
          adjustFutanRange?: number;
          adjustRateVal?: number;
          adjustRateRange?: number;
          kohi1Priority?: string;
          kohi2Priority?: string;
          kohi3Priority?: string;
          kohi4Priority?: string;
          sinYm?: number;
        }>;
        raiinInfModel?: {
          __typename?: "DomainModelsAccountingSyunoRaiinInfModel";
          kaikeiTime?: string;
          status?: number;
          uketukeSbt?: number;
        };
        syunoNyukinModels?: Array<{
          __typename?: "DomainModelsAccountDueSyunoNyukinModel";
          adjustFutan?: number;
          hpId?: number;
          nyukinCmt?: string;
          nyukinDate?: number;
          nyukinGaku?: number;
          nyukinjiDetail?: string;
          nyukinjiSeikyu?: number;
          nyukinjiTensu?: number;
          paymentMethodCd?: number;
          ptId?: string;
          raiinNo?: string;
          seqNo?: string;
          sinDate?: number;
          sortNo?: number;
          uketukeSbt?: number;
        }>;
      }>;
      kohiInfModels?: Array<{
        __typename?: "DomainModelsInsuranceKohiInfModel";
        hokenId?: number;
        hokenNo?: number;
        futansyaNo?: string;
        jyukyusyaNo?: string;
        rate?: number;
        gendoGaku?: number;
        tokusyuNo?: string;
        hokenMstModel?: {
          __typename?: "DomainModelsInsuranceMstHokenMstModel";
          dayLimitFutan?: number;
          displayTextMaster?: string;
          endDate?: number;
          futanRate?: number;
          hokenEdaNo?: number;
          hokenName?: string;
          hokenNameCd?: string;
          hokenNo?: number;
          houbetu?: string;
          isFutansyaNoCheck?: number;
          isJyukyusyaNoCheck?: number;
          isLimitList?: number;
          isLimitListSum?: number;
          isTokusyuNoCheck?: number;
          jyuKyuCheckDigit?: number;
          kaiLimitFutan?: number;
          monthLimitFutan?: number;
          selectedValueMaster?: string;
          startDate?: number;
        };
      }>;
    };
  };
};

export type GetApiCheckOpenFormReceiptReportQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  printType: Types.Scalars["Int"]["input"];
  raiinNoList?: Types.InputMaybe<
    Array<Types.Scalars["BigInt"]["input"]> | Types.Scalars["BigInt"]["input"]
  >;
  raiinNoPayList?: Types.InputMaybe<
    Array<Types.Scalars["BigInt"]["input"]> | Types.Scalars["BigInt"]["input"]
  >;
  isCalculateProcess: Types.Scalars["Boolean"]["input"];
}>;

export type GetApiCheckOpenFormReceiptReportQuery = {
  __typename?: "query_root";
  getApiCheckOpenFormReceiptReport: boolean;
};

export type GetApiAccountingGetHeaderInfQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
}>;

export type GetApiAccountingGetHeaderInfQuery = {
  __typename?: "query_root";
  getApiAccountingGetHeaderInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingGetAccountingHeaderResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingGetAccountingHeaderResponse";
      headerDtos?: Array<{
        __typename?: "UseCaseAccountingGetAccountingHeaderHeaderDto";
        raiinNo?: string;
        raiinBinding?: string;
        patternName?: string;
      }>;
    };
  };
};

export type GetApiOrdInfCheckOrdInfInDrugQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
}>;

export type GetApiOrdInfCheckOrdInfInDrugQuery = {
  __typename?: "query_root";
  getApiOrdInfCheckOrdInfInDrug?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOrdInfsCheckOrdInfInDrugResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOrdInfsCheckOrdInfInDrugResponse";
      result?: boolean;
    };
  };
};

export type PostApiAccountingRecaculationMutationVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
}>;

export type PostApiAccountingRecaculationMutation = {
  __typename?: "mutation_root";
  postApiAccountingRecaculation?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingRecaculationResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingRecaculationResponse";
      recaculationStatus?: number;
    };
  };
};

export type GetApiAccountingGetListHokenSelectQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
}>;

export type GetApiAccountingGetListHokenSelectQuery = {
  __typename?: "query_root";
  getApiAccountingGetListHokenSelect?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingGetListHokenSelectResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingGetListHokenSelectResponse";
      hokenSelects?: Array<{
        __typename?: "UseCaseAccountingGetListHokenSelectListHokenSelectDto";
        hokenId?: number;
        hokenName?: string;
      }>;
    };
  };
};

export type PostApiReceptionSaveMaxMoneyDataMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsMaxMoneySaveMaxMoneyRequestInput;
}>;

export type PostApiReceptionSaveMaxMoneyDataMutation = {
  __typename?: "mutation_root";
  postApiReceptionSaveMaxMoneyData?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMaxMoneySaveMaxMoneyResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMaxMoneySaveMaxMoneyResponse";
      state?: number;
    };
  };
};

export type CheckNormalCalculateDoneQueryVariables = Types.Exact<{
  input: Types.CheckNormalCalculateDoneInput;
}>;

export type CheckNormalCalculateDoneQuery = {
  __typename?: "query_root";
  checkNormalCalculateDone: boolean;
};

export const GetApiMedicalExaminationGetHistoryFollowSinDateDocument = gql`
  query getApiMedicalExaminationGetHistoryFollowSinDate(
    $ptId: BigInt!
    $raiinNo: BigInt!
    $sinDate: Int!
    $flag: Int!
  ) {
    getApiMedicalExaminationGetHistoryFollowSinDate(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
      flag: $flag
    ) {
      data {
        raiinfList {
          syosaisinKbn
          syosaisinDisplay
          jikanKbn
          jikanDisplay
          kaId
          kaName
          treatmentDepartmentId
          raiinNo
          hokenPid
          hokenTitle
          karteEdition {
            hokenGroups {
              hokenPid
              hokenTitle
              groupOdrItems {
                groupKouiCode
                groupName
                inOutKbn
                inOutName
                syohoSbt
                sikyuKbn
                tosekiKbn
                santeiKbn
                odrInfs {
                  odrKouiKbn
                  inoutKbn
                  rpName
                  sortNo
                  odrDetails {
                    itemName
                    displayItemName
                    ten
                    isGetPriceInYakka
                    syohoKbn
                    yakka
                    unitName
                    sinKouiKbn
                    drugKbn
                    itemCd
                    termVal
                    suryo
                    itemName
                    bunkatu
                    cmtOpt
                    rowNo
                  }
                }
              }
            }
          }
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiMedicalExaminationGetHistoryFollowSinDateQuery__
 *
 * To run a query within a React component, call `useGetApiMedicalExaminationGetHistoryFollowSinDateQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMedicalExaminationGetHistoryFollowSinDateQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMedicalExaminationGetHistoryFollowSinDateQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *      flag: // value for 'flag'
 *   },
 * });
 */
export function useGetApiMedicalExaminationGetHistoryFollowSinDateQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiMedicalExaminationGetHistoryFollowSinDateQuery,
    GetApiMedicalExaminationGetHistoryFollowSinDateQueryVariables
  > &
    (
      | {
          variables: GetApiMedicalExaminationGetHistoryFollowSinDateQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMedicalExaminationGetHistoryFollowSinDateQuery,
    GetApiMedicalExaminationGetHistoryFollowSinDateQueryVariables
  >(GetApiMedicalExaminationGetHistoryFollowSinDateDocument, options);
}
export function useGetApiMedicalExaminationGetHistoryFollowSinDateLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMedicalExaminationGetHistoryFollowSinDateQuery,
    GetApiMedicalExaminationGetHistoryFollowSinDateQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMedicalExaminationGetHistoryFollowSinDateQuery,
    GetApiMedicalExaminationGetHistoryFollowSinDateQueryVariables
  >(GetApiMedicalExaminationGetHistoryFollowSinDateDocument, options);
}
export function useGetApiMedicalExaminationGetHistoryFollowSinDateSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMedicalExaminationGetHistoryFollowSinDateQuery,
    GetApiMedicalExaminationGetHistoryFollowSinDateQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMedicalExaminationGetHistoryFollowSinDateQuery,
    GetApiMedicalExaminationGetHistoryFollowSinDateQueryVariables
  >(GetApiMedicalExaminationGetHistoryFollowSinDateDocument, options);
}
export type GetApiMedicalExaminationGetHistoryFollowSinDateQueryHookResult =
  ReturnType<typeof useGetApiMedicalExaminationGetHistoryFollowSinDateQuery>;
export type GetApiMedicalExaminationGetHistoryFollowSinDateLazyQueryHookResult =
  ReturnType<
    typeof useGetApiMedicalExaminationGetHistoryFollowSinDateLazyQuery
  >;
export type GetApiMedicalExaminationGetHistoryFollowSinDateSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiMedicalExaminationGetHistoryFollowSinDateSuspenseQuery
  >;
export type GetApiMedicalExaminationGetHistoryFollowSinDateQueryResult =
  Apollo.QueryResult<
    GetApiMedicalExaminationGetHistoryFollowSinDateQuery,
    GetApiMedicalExaminationGetHistoryFollowSinDateQueryVariables
  >;
export const GetApiAccountingPtByoMeiDocument = gql`
  query getApiAccountingPtByoMei($ptId: BigInt!, $sinDate: Int!) {
    getApiAccountingPtByoMei(ptId: $ptId, sinDate: $sinDate) {
      data {
        ptDiseaseDtos {
          byomeiCd
          fullByomei
          startDate
          tenKiBinding
          tenkiDate
          syubyoKbn
          isSuspect
          sikkanKbn
          nanbyoCd
          hokenId
        }
      }
    }
  }
`;

/**
 * __useGetApiAccountingPtByoMeiQuery__
 *
 * To run a query within a React component, call `useGetApiAccountingPtByoMeiQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiAccountingPtByoMeiQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiAccountingPtByoMeiQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiAccountingPtByoMeiQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiAccountingPtByoMeiQuery,
    GetApiAccountingPtByoMeiQueryVariables
  > &
    (
      | { variables: GetApiAccountingPtByoMeiQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiAccountingPtByoMeiQuery,
    GetApiAccountingPtByoMeiQueryVariables
  >(GetApiAccountingPtByoMeiDocument, options);
}
export function useGetApiAccountingPtByoMeiLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiAccountingPtByoMeiQuery,
    GetApiAccountingPtByoMeiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiAccountingPtByoMeiQuery,
    GetApiAccountingPtByoMeiQueryVariables
  >(GetApiAccountingPtByoMeiDocument, options);
}
export function useGetApiAccountingPtByoMeiSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiAccountingPtByoMeiQuery,
    GetApiAccountingPtByoMeiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiAccountingPtByoMeiQuery,
    GetApiAccountingPtByoMeiQueryVariables
  >(GetApiAccountingPtByoMeiDocument, options);
}
export type GetApiAccountingPtByoMeiQueryHookResult = ReturnType<
  typeof useGetApiAccountingPtByoMeiQuery
>;
export type GetApiAccountingPtByoMeiLazyQueryHookResult = ReturnType<
  typeof useGetApiAccountingPtByoMeiLazyQuery
>;
export type GetApiAccountingPtByoMeiSuspenseQueryHookResult = ReturnType<
  typeof useGetApiAccountingPtByoMeiSuspenseQuery
>;
export type GetApiAccountingPtByoMeiQueryResult = Apollo.QueryResult<
  GetApiAccountingPtByoMeiQuery,
  GetApiAccountingPtByoMeiQueryVariables
>;
export const GetApiAccountingGetMeiHoGaiDocument = gql`
  query getApiAccountingGetMeiHoGai(
    $ptId: BigInt!
    $sinDate: Int!
    $raiinNos: [BigInt!]
  ) {
    getApiAccountingGetMeiHoGai(
      ptId: $ptId
      sinDate: $sinDate
      raiinNos: $raiinNos
    ) {
      data {
        sinMeiModels {
          sinIdBinding
          asterisk
          itemName
          quantity
          tenKai
          totalBinding
          isRowColorGray
          futanSBinding
          futanK1Binding
          futanK2Binding
          futanK3Binding
          futanK4Binding
          enTenKbn
        }
      }
    }
  }
`;

/**
 * __useGetApiAccountingGetMeiHoGaiQuery__
 *
 * To run a query within a React component, call `useGetApiAccountingGetMeiHoGaiQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiAccountingGetMeiHoGaiQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiAccountingGetMeiHoGaiQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNos: // value for 'raiinNos'
 *   },
 * });
 */
export function useGetApiAccountingGetMeiHoGaiQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiAccountingGetMeiHoGaiQuery,
    GetApiAccountingGetMeiHoGaiQueryVariables
  > &
    (
      | { variables: GetApiAccountingGetMeiHoGaiQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiAccountingGetMeiHoGaiQuery,
    GetApiAccountingGetMeiHoGaiQueryVariables
  >(GetApiAccountingGetMeiHoGaiDocument, options);
}
export function useGetApiAccountingGetMeiHoGaiLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiAccountingGetMeiHoGaiQuery,
    GetApiAccountingGetMeiHoGaiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiAccountingGetMeiHoGaiQuery,
    GetApiAccountingGetMeiHoGaiQueryVariables
  >(GetApiAccountingGetMeiHoGaiDocument, options);
}
export function useGetApiAccountingGetMeiHoGaiSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiAccountingGetMeiHoGaiQuery,
    GetApiAccountingGetMeiHoGaiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiAccountingGetMeiHoGaiQuery,
    GetApiAccountingGetMeiHoGaiQueryVariables
  >(GetApiAccountingGetMeiHoGaiDocument, options);
}
export type GetApiAccountingGetMeiHoGaiQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetMeiHoGaiQuery
>;
export type GetApiAccountingGetMeiHoGaiLazyQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetMeiHoGaiLazyQuery
>;
export type GetApiAccountingGetMeiHoGaiSuspenseQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetMeiHoGaiSuspenseQuery
>;
export type GetApiAccountingGetMeiHoGaiQueryResult = Apollo.QueryResult<
  GetApiAccountingGetMeiHoGaiQuery,
  GetApiAccountingGetMeiHoGaiQueryVariables
>;
export const GetApiAccountingWarningMemoDocument = gql`
  query getApiAccountingWarningMemo(
    $ptId: BigInt!
    $sinDate: Int!
    $raiinNo: BigInt!
  ) {
    getApiAccountingWarningMemo(
      ptId: $ptId
      sinDate: $sinDate
      raiinNo: $raiinNo
    ) {
      data {
        warningMemoDtos {
          color
          memo
          raiinNo
        }
      }
    }
  }
`;

/**
 * __useGetApiAccountingWarningMemoQuery__
 *
 * To run a query within a React component, call `useGetApiAccountingWarningMemoQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiAccountingWarningMemoQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiAccountingWarningMemoQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiAccountingWarningMemoQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiAccountingWarningMemoQuery,
    GetApiAccountingWarningMemoQueryVariables
  > &
    (
      | { variables: GetApiAccountingWarningMemoQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiAccountingWarningMemoQuery,
    GetApiAccountingWarningMemoQueryVariables
  >(GetApiAccountingWarningMemoDocument, options);
}
export function useGetApiAccountingWarningMemoLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiAccountingWarningMemoQuery,
    GetApiAccountingWarningMemoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiAccountingWarningMemoQuery,
    GetApiAccountingWarningMemoQueryVariables
  >(GetApiAccountingWarningMemoDocument, options);
}
export function useGetApiAccountingWarningMemoSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiAccountingWarningMemoQuery,
    GetApiAccountingWarningMemoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiAccountingWarningMemoQuery,
    GetApiAccountingWarningMemoQueryVariables
  >(GetApiAccountingWarningMemoDocument, options);
}
export type GetApiAccountingWarningMemoQueryHookResult = ReturnType<
  typeof useGetApiAccountingWarningMemoQuery
>;
export type GetApiAccountingWarningMemoLazyQueryHookResult = ReturnType<
  typeof useGetApiAccountingWarningMemoLazyQuery
>;
export type GetApiAccountingWarningMemoSuspenseQueryHookResult = ReturnType<
  typeof useGetApiAccountingWarningMemoSuspenseQuery
>;
export type GetApiAccountingWarningMemoQueryResult = Apollo.QueryResult<
  GetApiAccountingWarningMemoQuery,
  GetApiAccountingWarningMemoQueryVariables
>;
export const GetApiAccountingGetListDocument = gql`
  query getApiAccountingGetList(
    $ptId: BigInt!
    $sinDate: Int!
    $raiinNo: BigInt!
  ) {
    getApiAccountingGetList(ptId: $ptId, sinDate: $sinDate, raiinNo: $raiinNo) {
      message
      status
      data {
        adjustFutan
        allSyunoSeikyuModel {
          adjustFutan
          adjustFutanOne
          hokenId
          hpId
          kaikeiInfModels {
            hpId
            ptId
            sinDate
            raiinNo
            hokenId
            kohi1Id
            kohi2Id
            kohi3Id
            kohi4Id
            hokenKbn
            hokenSbtCd
            receSbt
            houbetu
            kohi1Houbetu
            kohi2Houbetu
            kohi3Houbetu
            kohi4Houbetu
            honkeKbn
            hokenRate
            ptRate
            dispRate
            tensu
            totalIryohi
            ptFutan
            jihiFutan
            jihiTax
            jihiOuttax
            jihiFutanTaxfree
            jihiFutanTaxNr
            jihiFutanTaxGen
            jihiFutanOuttaxNr
            jihiFutanOuttaxGen
            jihiTaxNr
            jihiTaxGen
            jihiOuttaxNr
            jihiOuttaxGen
            adjustFutan
            adjustRound
            totalPtFutan
            adjustFutanVal
            adjustFutanRange
            adjustRateVal
            adjustRateRange
            kohi1Priority
            kohi2Priority
            kohi3Priority
            kohi4Priority
            sinYm
          }
          kanFutanOne
          newAdjustFutan
          newSeikyuDetail
          newSeikyuGaku
          newSeikyuTensu
          nyukinKbn
          ptId
          raiinInfModel {
            kaikeiTime
            status
            uketukeSbt
          }
          raiinNo
          seikyuDetail
          seikyuGaku
          seikyuTensu
          sinDate
          sumAdjustOne
          syunoNyukinModels {
            adjustFutan
            hpId
            nyukinCmt
            nyukinDate
            nyukinGaku
            nyukinjiDetail
            nyukinjiSeikyu
            nyukinjiTensu
            paymentMethodCd
            ptId
            raiinNo
            seqNo
            sinDate
            sortNo
            uketukeSbt
          }
          taxOne
          totalPointOne
          totalSelfExpenseOne
        }
        comment
        debitBalance
        isSettled
        kanFutan
        listRaiinInf {
          hokenId
          raiinNo
          isFcoWaiting
        }
        payType
        sumAdjust
        sumAdjustView
        syunoSeikyuModels {
          adjustFutan
          adjustFutanOne
          hokenId
          hpId
          kaikeiInfModels {
            hpId
            ptId
            sinDate
            raiinNo
            hokenId
            kohi1Id
            kohi2Id
            kohi3Id
            kohi4Id
            hokenKbn
            hokenSbtCd
            receSbt
            houbetu
            kohi1Houbetu
            kohi2Houbetu
            kohi3Houbetu
            kohi4Houbetu
            honkeKbn
            hokenRate
            ptRate
            dispRate
            tensu
            totalIryohi
            ptFutan
            jihiFutan
            jihiTax
            jihiOuttax
            jihiFutanTaxfree
            jihiFutanTaxNr
            jihiFutanTaxGen
            jihiFutanOuttaxNr
            jihiFutanOuttaxGen
            jihiTaxNr
            jihiTaxGen
            jihiOuttaxNr
            jihiOuttaxGen
            adjustFutan
            adjustRound
            totalPtFutan
            adjustFutanVal
            adjustFutanRange
            adjustRateVal
            adjustRateRange
            kohi1Priority
            kohi2Priority
            kohi3Priority
            kohi4Priority
            sinYm
          }
          kanFutanOne
          newAdjustFutan
          newSeikyuDetail
          newSeikyuGaku
          newSeikyuTensu
          nyukinKbn
          ptId
          raiinInfModel {
            kaikeiTime
            status
            uketukeSbt
          }
          raiinNo
          seikyuDetail
          seikyuGaku
          seikyuTensu
          sinDate
          sumAdjustOne
          syunoNyukinModels {
            adjustFutan
            hpId
            nyukinCmt
            nyukinDate
            nyukinGaku
            nyukinjiDetail
            nyukinjiSeikyu
            nyukinjiTensu
            paymentMethodCd
            ptId
            raiinNo
            seqNo
            sinDate
            sortNo
            uketukeSbt
          }
          taxOne
          totalPointOne
          totalSelfExpenseOne
        }
        tax
        thisCredit
        thisWari
        totalPoint
        totalSelfExpense
        kohiInfModels {
          hokenId
          hokenNo
          futansyaNo
          jyukyusyaNo
          rate
          gendoGaku
          tokusyuNo
          hokenMstModel {
            dayLimitFutan
            displayTextMaster
            endDate
            futanRate
            hokenEdaNo
            hokenName
            hokenNameCd
            hokenNo
            houbetu
            isFutansyaNoCheck
            isJyukyusyaNoCheck
            isLimitList
            isLimitListSum
            isTokusyuNoCheck
            jyuKyuCheckDigit
            kaiLimitFutan
            monthLimitFutan
            selectedValueMaster
            startDate
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiAccountingGetListQuery__
 *
 * To run a query within a React component, call `useGetApiAccountingGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiAccountingGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiAccountingGetListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiAccountingGetListQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiAccountingGetListQuery,
    GetApiAccountingGetListQueryVariables
  > &
    (
      | { variables: GetApiAccountingGetListQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiAccountingGetListQuery,
    GetApiAccountingGetListQueryVariables
  >(GetApiAccountingGetListDocument, options);
}
export function useGetApiAccountingGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiAccountingGetListQuery,
    GetApiAccountingGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiAccountingGetListQuery,
    GetApiAccountingGetListQueryVariables
  >(GetApiAccountingGetListDocument, options);
}
export function useGetApiAccountingGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiAccountingGetListQuery,
    GetApiAccountingGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiAccountingGetListQuery,
    GetApiAccountingGetListQueryVariables
  >(GetApiAccountingGetListDocument, options);
}
export type GetApiAccountingGetListQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetListQuery
>;
export type GetApiAccountingGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetListLazyQuery
>;
export type GetApiAccountingGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetListSuspenseQuery
>;
export type GetApiAccountingGetListQueryResult = Apollo.QueryResult<
  GetApiAccountingGetListQuery,
  GetApiAccountingGetListQueryVariables
>;
export const GetApiCheckOpenFormReceiptReportDocument = gql`
  query getApiCheckOpenFormReceiptReport(
    $ptId: BigInt!
    $printType: Int!
    $raiinNoList: [BigInt!]
    $raiinNoPayList: [BigInt!]
    $isCalculateProcess: Boolean!
  ) {
    getApiCheckOpenFormReceiptReport(
      ptId: $ptId
      printType: $printType
      raiinNoList: $raiinNoList
      raiinNoPayList: $raiinNoPayList
      isCalculateProcess: $isCalculateProcess
    )
  }
`;

/**
 * __useGetApiCheckOpenFormReceiptReportQuery__
 *
 * To run a query within a React component, call `useGetApiCheckOpenFormReceiptReportQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiCheckOpenFormReceiptReportQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiCheckOpenFormReceiptReportQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      printType: // value for 'printType'
 *      raiinNoList: // value for 'raiinNoList'
 *      raiinNoPayList: // value for 'raiinNoPayList'
 *      isCalculateProcess: // value for 'isCalculateProcess'
 *   },
 * });
 */
export function useGetApiCheckOpenFormReceiptReportQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiCheckOpenFormReceiptReportQuery,
    GetApiCheckOpenFormReceiptReportQueryVariables
  > &
    (
      | {
          variables: GetApiCheckOpenFormReceiptReportQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiCheckOpenFormReceiptReportQuery,
    GetApiCheckOpenFormReceiptReportQueryVariables
  >(GetApiCheckOpenFormReceiptReportDocument, options);
}
export function useGetApiCheckOpenFormReceiptReportLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiCheckOpenFormReceiptReportQuery,
    GetApiCheckOpenFormReceiptReportQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiCheckOpenFormReceiptReportQuery,
    GetApiCheckOpenFormReceiptReportQueryVariables
  >(GetApiCheckOpenFormReceiptReportDocument, options);
}
export function useGetApiCheckOpenFormReceiptReportSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiCheckOpenFormReceiptReportQuery,
    GetApiCheckOpenFormReceiptReportQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiCheckOpenFormReceiptReportQuery,
    GetApiCheckOpenFormReceiptReportQueryVariables
  >(GetApiCheckOpenFormReceiptReportDocument, options);
}
export type GetApiCheckOpenFormReceiptReportQueryHookResult = ReturnType<
  typeof useGetApiCheckOpenFormReceiptReportQuery
>;
export type GetApiCheckOpenFormReceiptReportLazyQueryHookResult = ReturnType<
  typeof useGetApiCheckOpenFormReceiptReportLazyQuery
>;
export type GetApiCheckOpenFormReceiptReportSuspenseQueryHookResult =
  ReturnType<typeof useGetApiCheckOpenFormReceiptReportSuspenseQuery>;
export type GetApiCheckOpenFormReceiptReportQueryResult = Apollo.QueryResult<
  GetApiCheckOpenFormReceiptReportQuery,
  GetApiCheckOpenFormReceiptReportQueryVariables
>;
export const GetApiAccountingGetHeaderInfDocument = gql`
  query getApiAccountingGetHeaderInf(
    $ptId: BigInt!
    $sinDate: Int!
    $raiinNo: BigInt!
  ) {
    getApiAccountingGetHeaderInf(
      ptId: $ptId
      sinDate: $sinDate
      raiinNo: $raiinNo
    ) {
      data {
        headerDtos {
          raiinNo
          raiinBinding
          patternName
        }
      }
    }
  }
`;

/**
 * __useGetApiAccountingGetHeaderInfQuery__
 *
 * To run a query within a React component, call `useGetApiAccountingGetHeaderInfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiAccountingGetHeaderInfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiAccountingGetHeaderInfQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiAccountingGetHeaderInfQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiAccountingGetHeaderInfQuery,
    GetApiAccountingGetHeaderInfQueryVariables
  > &
    (
      | {
          variables: GetApiAccountingGetHeaderInfQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiAccountingGetHeaderInfQuery,
    GetApiAccountingGetHeaderInfQueryVariables
  >(GetApiAccountingGetHeaderInfDocument, options);
}
export function useGetApiAccountingGetHeaderInfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiAccountingGetHeaderInfQuery,
    GetApiAccountingGetHeaderInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiAccountingGetHeaderInfQuery,
    GetApiAccountingGetHeaderInfQueryVariables
  >(GetApiAccountingGetHeaderInfDocument, options);
}
export function useGetApiAccountingGetHeaderInfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiAccountingGetHeaderInfQuery,
    GetApiAccountingGetHeaderInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiAccountingGetHeaderInfQuery,
    GetApiAccountingGetHeaderInfQueryVariables
  >(GetApiAccountingGetHeaderInfDocument, options);
}
export type GetApiAccountingGetHeaderInfQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetHeaderInfQuery
>;
export type GetApiAccountingGetHeaderInfLazyQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetHeaderInfLazyQuery
>;
export type GetApiAccountingGetHeaderInfSuspenseQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetHeaderInfSuspenseQuery
>;
export type GetApiAccountingGetHeaderInfQueryResult = Apollo.QueryResult<
  GetApiAccountingGetHeaderInfQuery,
  GetApiAccountingGetHeaderInfQueryVariables
>;
export const GetApiOrdInfCheckOrdInfInDrugDocument = gql`
  query getApiOrdInfCheckOrdInfInDrug($ptId: BigInt!, $raiinNo: BigInt!) {
    getApiOrdInfCheckOrdInfInDrug(ptId: $ptId, raiinNo: $raiinNo) {
      data {
        result
      }
    }
  }
`;

/**
 * __useGetApiOrdInfCheckOrdInfInDrugQuery__
 *
 * To run a query within a React component, call `useGetApiOrdInfCheckOrdInfInDrugQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiOrdInfCheckOrdInfInDrugQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiOrdInfCheckOrdInfInDrugQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiOrdInfCheckOrdInfInDrugQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiOrdInfCheckOrdInfInDrugQuery,
    GetApiOrdInfCheckOrdInfInDrugQueryVariables
  > &
    (
      | {
          variables: GetApiOrdInfCheckOrdInfInDrugQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiOrdInfCheckOrdInfInDrugQuery,
    GetApiOrdInfCheckOrdInfInDrugQueryVariables
  >(GetApiOrdInfCheckOrdInfInDrugDocument, options);
}
export function useGetApiOrdInfCheckOrdInfInDrugLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiOrdInfCheckOrdInfInDrugQuery,
    GetApiOrdInfCheckOrdInfInDrugQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiOrdInfCheckOrdInfInDrugQuery,
    GetApiOrdInfCheckOrdInfInDrugQueryVariables
  >(GetApiOrdInfCheckOrdInfInDrugDocument, options);
}
export function useGetApiOrdInfCheckOrdInfInDrugSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiOrdInfCheckOrdInfInDrugQuery,
    GetApiOrdInfCheckOrdInfInDrugQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiOrdInfCheckOrdInfInDrugQuery,
    GetApiOrdInfCheckOrdInfInDrugQueryVariables
  >(GetApiOrdInfCheckOrdInfInDrugDocument, options);
}
export type GetApiOrdInfCheckOrdInfInDrugQueryHookResult = ReturnType<
  typeof useGetApiOrdInfCheckOrdInfInDrugQuery
>;
export type GetApiOrdInfCheckOrdInfInDrugLazyQueryHookResult = ReturnType<
  typeof useGetApiOrdInfCheckOrdInfInDrugLazyQuery
>;
export type GetApiOrdInfCheckOrdInfInDrugSuspenseQueryHookResult = ReturnType<
  typeof useGetApiOrdInfCheckOrdInfInDrugSuspenseQuery
>;
export type GetApiOrdInfCheckOrdInfInDrugQueryResult = Apollo.QueryResult<
  GetApiOrdInfCheckOrdInfInDrugQuery,
  GetApiOrdInfCheckOrdInfInDrugQueryVariables
>;
export const PostApiAccountingRecaculationDocument = gql`
  mutation postApiAccountingRecaculation(
    $ptId: BigInt!
    $sinDate: Int!
    $raiinNo: BigInt!
  ) {
    postApiAccountingRecaculation(
      emrCloudApiRequestsAccountingRecaculationRequestInput: {
        ptId: $ptId
        sinDate: $sinDate
        raiinNo: $raiinNo
      }
    ) {
      data {
        recaculationStatus
      }
    }
  }
`;
export type PostApiAccountingRecaculationMutationFn = Apollo.MutationFunction<
  PostApiAccountingRecaculationMutation,
  PostApiAccountingRecaculationMutationVariables
>;

/**
 * __usePostApiAccountingRecaculationMutation__
 *
 * To run a mutation, you first call `usePostApiAccountingRecaculationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiAccountingRecaculationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiAccountingRecaculationMutation, { data, loading, error }] = usePostApiAccountingRecaculationMutation({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function usePostApiAccountingRecaculationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiAccountingRecaculationMutation,
    PostApiAccountingRecaculationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiAccountingRecaculationMutation,
    PostApiAccountingRecaculationMutationVariables
  >(PostApiAccountingRecaculationDocument, options);
}
export type PostApiAccountingRecaculationMutationHookResult = ReturnType<
  typeof usePostApiAccountingRecaculationMutation
>;
export type PostApiAccountingRecaculationMutationResult =
  Apollo.MutationResult<PostApiAccountingRecaculationMutation>;
export type PostApiAccountingRecaculationMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiAccountingRecaculationMutation,
    PostApiAccountingRecaculationMutationVariables
  >;
export const GetApiAccountingGetListHokenSelectDocument = gql`
  query getApiAccountingGetListHokenSelect(
    $ptId: BigInt!
    $sinDate: Int!
    $raiinNo: BigInt!
  ) {
    getApiAccountingGetListHokenSelect(
      ptId: $ptId
      sinDate: $sinDate
      raiinNo: $raiinNo
    ) {
      data {
        hokenSelects {
          hokenId
          hokenName
        }
      }
    }
  }
`;

/**
 * __useGetApiAccountingGetListHokenSelectQuery__
 *
 * To run a query within a React component, call `useGetApiAccountingGetListHokenSelectQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiAccountingGetListHokenSelectQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiAccountingGetListHokenSelectQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiAccountingGetListHokenSelectQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiAccountingGetListHokenSelectQuery,
    GetApiAccountingGetListHokenSelectQueryVariables
  > &
    (
      | {
          variables: GetApiAccountingGetListHokenSelectQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiAccountingGetListHokenSelectQuery,
    GetApiAccountingGetListHokenSelectQueryVariables
  >(GetApiAccountingGetListHokenSelectDocument, options);
}
export function useGetApiAccountingGetListHokenSelectLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiAccountingGetListHokenSelectQuery,
    GetApiAccountingGetListHokenSelectQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiAccountingGetListHokenSelectQuery,
    GetApiAccountingGetListHokenSelectQueryVariables
  >(GetApiAccountingGetListHokenSelectDocument, options);
}
export function useGetApiAccountingGetListHokenSelectSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiAccountingGetListHokenSelectQuery,
    GetApiAccountingGetListHokenSelectQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiAccountingGetListHokenSelectQuery,
    GetApiAccountingGetListHokenSelectQueryVariables
  >(GetApiAccountingGetListHokenSelectDocument, options);
}
export type GetApiAccountingGetListHokenSelectQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetListHokenSelectQuery
>;
export type GetApiAccountingGetListHokenSelectLazyQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetListHokenSelectLazyQuery
>;
export type GetApiAccountingGetListHokenSelectSuspenseQueryHookResult =
  ReturnType<typeof useGetApiAccountingGetListHokenSelectSuspenseQuery>;
export type GetApiAccountingGetListHokenSelectQueryResult = Apollo.QueryResult<
  GetApiAccountingGetListHokenSelectQuery,
  GetApiAccountingGetListHokenSelectQueryVariables
>;
export const PostApiReceptionSaveMaxMoneyDataDocument = gql`
  mutation postApiReceptionSaveMaxMoneyData(
    $input: EmrCloudApiRequestsMaxMoneySaveMaxMoneyRequestInput!
  ) {
    postApiReceptionSaveMaxMoneyData(
      emrCloudApiRequestsMaxMoneySaveMaxMoneyRequestInput: $input
    ) {
      data {
        state
      }
    }
  }
`;
export type PostApiReceptionSaveMaxMoneyDataMutationFn =
  Apollo.MutationFunction<
    PostApiReceptionSaveMaxMoneyDataMutation,
    PostApiReceptionSaveMaxMoneyDataMutationVariables
  >;

/**
 * __usePostApiReceptionSaveMaxMoneyDataMutation__
 *
 * To run a mutation, you first call `usePostApiReceptionSaveMaxMoneyDataMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceptionSaveMaxMoneyDataMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceptionSaveMaxMoneyDataMutation, { data, loading, error }] = usePostApiReceptionSaveMaxMoneyDataMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceptionSaveMaxMoneyDataMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceptionSaveMaxMoneyDataMutation,
    PostApiReceptionSaveMaxMoneyDataMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceptionSaveMaxMoneyDataMutation,
    PostApiReceptionSaveMaxMoneyDataMutationVariables
  >(PostApiReceptionSaveMaxMoneyDataDocument, options);
}
export type PostApiReceptionSaveMaxMoneyDataMutationHookResult = ReturnType<
  typeof usePostApiReceptionSaveMaxMoneyDataMutation
>;
export type PostApiReceptionSaveMaxMoneyDataMutationResult =
  Apollo.MutationResult<PostApiReceptionSaveMaxMoneyDataMutation>;
export type PostApiReceptionSaveMaxMoneyDataMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceptionSaveMaxMoneyDataMutation,
    PostApiReceptionSaveMaxMoneyDataMutationVariables
  >;
export const CheckNormalCalculateDoneDocument = gql`
  query checkNormalCalculateDone($input: CheckNormalCalculateDoneInput!) {
    checkNormalCalculateDone(input: $input)
  }
`;

/**
 * __useCheckNormalCalculateDoneQuery__
 *
 * To run a query within a React component, call `useCheckNormalCalculateDoneQuery` and pass it any options that fit your needs.
 * When your component renders, `useCheckNormalCalculateDoneQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCheckNormalCalculateDoneQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCheckNormalCalculateDoneQuery(
  baseOptions: Apollo.QueryHookOptions<
    CheckNormalCalculateDoneQuery,
    CheckNormalCalculateDoneQueryVariables
  > &
    (
      | { variables: CheckNormalCalculateDoneQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    CheckNormalCalculateDoneQuery,
    CheckNormalCalculateDoneQueryVariables
  >(CheckNormalCalculateDoneDocument, options);
}
export function useCheckNormalCalculateDoneLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    CheckNormalCalculateDoneQuery,
    CheckNormalCalculateDoneQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    CheckNormalCalculateDoneQuery,
    CheckNormalCalculateDoneQueryVariables
  >(CheckNormalCalculateDoneDocument, options);
}
export function useCheckNormalCalculateDoneSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    CheckNormalCalculateDoneQuery,
    CheckNormalCalculateDoneQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    CheckNormalCalculateDoneQuery,
    CheckNormalCalculateDoneQueryVariables
  >(CheckNormalCalculateDoneDocument, options);
}
export type CheckNormalCalculateDoneQueryHookResult = ReturnType<
  typeof useCheckNormalCalculateDoneQuery
>;
export type CheckNormalCalculateDoneLazyQueryHookResult = ReturnType<
  typeof useCheckNormalCalculateDoneLazyQuery
>;
export type CheckNormalCalculateDoneSuspenseQueryHookResult = ReturnType<
  typeof useCheckNormalCalculateDoneSuspenseQuery
>;
export type CheckNormalCalculateDoneQueryResult = Apollo.QueryResult<
  CheckNormalCalculateDoneQuery,
  CheckNormalCalculateDoneQueryVariables
>;
