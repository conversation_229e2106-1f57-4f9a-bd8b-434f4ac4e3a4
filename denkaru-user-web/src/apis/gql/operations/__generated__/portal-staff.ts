import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetPortalHospitalStaffsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetPortalHospitalStaffsQuery = {
  __typename?: "query_root";
  getPortalHospitalStaffs: Array<{
    __typename?: "PortalHospitalStaff";
    hospitalStaffId: number;
    name: string;
    description?: string;
    order: number;
    experienceDetail?: string;
    specialistDetail?: string;
    isDirector: boolean;
    files?: Array<{
      __typename?: "PortalHospitalStaffFile";
      fileId?: number;
      originalFileName: string;
      s3Key: string;
      createdAt?: string;
    }>;
  }>;
};

export type GetPortalHospitalStaffQueryVariables = Types.Exact<{
  hospitalStaffId: Types.Scalars["Int"]["input"];
}>;

export type GetPortalHospitalStaffQuery = {
  __typename?: "query_root";
  getPortalHospitalStaff: {
    __typename?: "GetPortalHospitalStaffRes";
    portalHospitalStaff?: {
      __typename?: "PortalHospitalStaff";
      hospitalStaffId: number;
      name: string;
      description?: string;
      order: number;
      experienceDetail?: string;
      specialistDetail?: string;
      isDirector: boolean;
      files?: Array<{
        __typename?: "PortalHospitalStaffFile";
        fileId?: number;
        originalFileName: string;
        s3Key: string;
        createdAt?: string;
      }>;
    };
  };
};

export type CreatePortalStaffMutationVariables = Types.Exact<{
  input: Types.CreatePortalHospitalStaffInput;
}>;

export type CreatePortalStaffMutation = {
  __typename?: "mutation_root";
  createPortalHospitalStaff: {
    __typename?: "CreatePortalStaffRes";
    portalStaffId: number;
  };
};

export type EditPortalStaffMutationVariables = Types.Exact<{
  input: Types.EditPortalHospitalStaffInput;
}>;

export type EditPortalStaffMutation = {
  __typename?: "mutation_root";
  editPortalHospitalStaff: boolean;
};

export type DeletePortalStaffMutationVariables = Types.Exact<{
  input: Types.DeletePortalHospitalStaffInput;
}>;

export type DeletePortalStaffMutation = {
  __typename?: "mutation_root";
  deletePortalHospitalStaff: boolean;
};

export type SortPortalStaffMutationVariables = Types.Exact<{
  input: Types.SortPortalHospitalStaffsInput;
}>;

export type SortPortalStaffMutation = {
  __typename?: "mutation_root";
  sortPortalHospitalStaffs: boolean;
};

export const GetPortalHospitalStaffsDocument = gql`
  query getPortalHospitalStaffs {
    getPortalHospitalStaffs {
      hospitalStaffId
      name
      description
      order
      experienceDetail
      files {
        fileId
        originalFileName
        s3Key
        createdAt
      }
      specialistDetail
      isDirector
    }
  }
`;

/**
 * __useGetPortalHospitalStaffsQuery__
 *
 * To run a query within a React component, call `useGetPortalHospitalStaffsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPortalHospitalStaffsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPortalHospitalStaffsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetPortalHospitalStaffsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetPortalHospitalStaffsQuery,
    GetPortalHospitalStaffsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPortalHospitalStaffsQuery,
    GetPortalHospitalStaffsQueryVariables
  >(GetPortalHospitalStaffsDocument, options);
}
export function useGetPortalHospitalStaffsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPortalHospitalStaffsQuery,
    GetPortalHospitalStaffsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPortalHospitalStaffsQuery,
    GetPortalHospitalStaffsQueryVariables
  >(GetPortalHospitalStaffsDocument, options);
}
export function useGetPortalHospitalStaffsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPortalHospitalStaffsQuery,
    GetPortalHospitalStaffsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPortalHospitalStaffsQuery,
    GetPortalHospitalStaffsQueryVariables
  >(GetPortalHospitalStaffsDocument, options);
}
export type GetPortalHospitalStaffsQueryHookResult = ReturnType<
  typeof useGetPortalHospitalStaffsQuery
>;
export type GetPortalHospitalStaffsLazyQueryHookResult = ReturnType<
  typeof useGetPortalHospitalStaffsLazyQuery
>;
export type GetPortalHospitalStaffsSuspenseQueryHookResult = ReturnType<
  typeof useGetPortalHospitalStaffsSuspenseQuery
>;
export type GetPortalHospitalStaffsQueryResult = Apollo.QueryResult<
  GetPortalHospitalStaffsQuery,
  GetPortalHospitalStaffsQueryVariables
>;
export const GetPortalHospitalStaffDocument = gql`
  query getPortalHospitalStaff($hospitalStaffId: Int!) {
    getPortalHospitalStaff(hospitalStaffId: $hospitalStaffId) {
      portalHospitalStaff {
        hospitalStaffId
        name
        description
        order
        experienceDetail
        files {
          fileId
          originalFileName
          s3Key
          createdAt
        }
        specialistDetail
        isDirector
      }
    }
  }
`;

/**
 * __useGetPortalHospitalStaffQuery__
 *
 * To run a query within a React component, call `useGetPortalHospitalStaffQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPortalHospitalStaffQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPortalHospitalStaffQuery({
 *   variables: {
 *      hospitalStaffId: // value for 'hospitalStaffId'
 *   },
 * });
 */
export function useGetPortalHospitalStaffQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPortalHospitalStaffQuery,
    GetPortalHospitalStaffQueryVariables
  > &
    (
      | { variables: GetPortalHospitalStaffQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPortalHospitalStaffQuery,
    GetPortalHospitalStaffQueryVariables
  >(GetPortalHospitalStaffDocument, options);
}
export function useGetPortalHospitalStaffLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPortalHospitalStaffQuery,
    GetPortalHospitalStaffQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPortalHospitalStaffQuery,
    GetPortalHospitalStaffQueryVariables
  >(GetPortalHospitalStaffDocument, options);
}
export function useGetPortalHospitalStaffSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPortalHospitalStaffQuery,
    GetPortalHospitalStaffQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPortalHospitalStaffQuery,
    GetPortalHospitalStaffQueryVariables
  >(GetPortalHospitalStaffDocument, options);
}
export type GetPortalHospitalStaffQueryHookResult = ReturnType<
  typeof useGetPortalHospitalStaffQuery
>;
export type GetPortalHospitalStaffLazyQueryHookResult = ReturnType<
  typeof useGetPortalHospitalStaffLazyQuery
>;
export type GetPortalHospitalStaffSuspenseQueryHookResult = ReturnType<
  typeof useGetPortalHospitalStaffSuspenseQuery
>;
export type GetPortalHospitalStaffQueryResult = Apollo.QueryResult<
  GetPortalHospitalStaffQuery,
  GetPortalHospitalStaffQueryVariables
>;
export const CreatePortalStaffDocument = gql`
  mutation createPortalStaff($input: CreatePortalHospitalStaffInput!) {
    createPortalHospitalStaff(input: $input) {
      portalStaffId
    }
  }
`;
export type CreatePortalStaffMutationFn = Apollo.MutationFunction<
  CreatePortalStaffMutation,
  CreatePortalStaffMutationVariables
>;

/**
 * __useCreatePortalStaffMutation__
 *
 * To run a mutation, you first call `useCreatePortalStaffMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreatePortalStaffMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createPortalStaffMutation, { data, loading, error }] = useCreatePortalStaffMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreatePortalStaffMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreatePortalStaffMutation,
    CreatePortalStaffMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreatePortalStaffMutation,
    CreatePortalStaffMutationVariables
  >(CreatePortalStaffDocument, options);
}
export type CreatePortalStaffMutationHookResult = ReturnType<
  typeof useCreatePortalStaffMutation
>;
export type CreatePortalStaffMutationResult =
  Apollo.MutationResult<CreatePortalStaffMutation>;
export type CreatePortalStaffMutationOptions = Apollo.BaseMutationOptions<
  CreatePortalStaffMutation,
  CreatePortalStaffMutationVariables
>;
export const EditPortalStaffDocument = gql`
  mutation editPortalStaff($input: EditPortalHospitalStaffInput!) {
    editPortalHospitalStaff(input: $input)
  }
`;
export type EditPortalStaffMutationFn = Apollo.MutationFunction<
  EditPortalStaffMutation,
  EditPortalStaffMutationVariables
>;

/**
 * __useEditPortalStaffMutation__
 *
 * To run a mutation, you first call `useEditPortalStaffMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditPortalStaffMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editPortalStaffMutation, { data, loading, error }] = useEditPortalStaffMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useEditPortalStaffMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditPortalStaffMutation,
    EditPortalStaffMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditPortalStaffMutation,
    EditPortalStaffMutationVariables
  >(EditPortalStaffDocument, options);
}
export type EditPortalStaffMutationHookResult = ReturnType<
  typeof useEditPortalStaffMutation
>;
export type EditPortalStaffMutationResult =
  Apollo.MutationResult<EditPortalStaffMutation>;
export type EditPortalStaffMutationOptions = Apollo.BaseMutationOptions<
  EditPortalStaffMutation,
  EditPortalStaffMutationVariables
>;
export const DeletePortalStaffDocument = gql`
  mutation deletePortalStaff($input: DeletePortalHospitalStaffInput!) {
    deletePortalHospitalStaff(input: $input)
  }
`;
export type DeletePortalStaffMutationFn = Apollo.MutationFunction<
  DeletePortalStaffMutation,
  DeletePortalStaffMutationVariables
>;

/**
 * __useDeletePortalStaffMutation__
 *
 * To run a mutation, you first call `useDeletePortalStaffMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeletePortalStaffMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deletePortalStaffMutation, { data, loading, error }] = useDeletePortalStaffMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useDeletePortalStaffMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeletePortalStaffMutation,
    DeletePortalStaffMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeletePortalStaffMutation,
    DeletePortalStaffMutationVariables
  >(DeletePortalStaffDocument, options);
}
export type DeletePortalStaffMutationHookResult = ReturnType<
  typeof useDeletePortalStaffMutation
>;
export type DeletePortalStaffMutationResult =
  Apollo.MutationResult<DeletePortalStaffMutation>;
export type DeletePortalStaffMutationOptions = Apollo.BaseMutationOptions<
  DeletePortalStaffMutation,
  DeletePortalStaffMutationVariables
>;
export const SortPortalStaffDocument = gql`
  mutation sortPortalStaff($input: SortPortalHospitalStaffsInput!) {
    sortPortalHospitalStaffs(input: $input)
  }
`;
export type SortPortalStaffMutationFn = Apollo.MutationFunction<
  SortPortalStaffMutation,
  SortPortalStaffMutationVariables
>;

/**
 * __useSortPortalStaffMutation__
 *
 * To run a mutation, you first call `useSortPortalStaffMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSortPortalStaffMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sortPortalStaffMutation, { data, loading, error }] = useSortPortalStaffMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSortPortalStaffMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SortPortalStaffMutation,
    SortPortalStaffMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SortPortalStaffMutation,
    SortPortalStaffMutationVariables
  >(SortPortalStaffDocument, options);
}
export type SortPortalStaffMutationHookResult = ReturnType<
  typeof useSortPortalStaffMutation
>;
export type SortPortalStaffMutationResult =
  Apollo.MutationResult<SortPortalStaffMutation>;
export type SortPortalStaffMutationOptions = Apollo.BaseMutationOptions<
  SortPortalStaffMutation,
  SortPortalStaffMutationVariables
>;
