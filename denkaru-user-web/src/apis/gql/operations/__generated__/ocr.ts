import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type ProcessOcrMutationVariables = Types.Exact<{
  engine: Types.EnginType;
  fileS3Key: Types.Scalars["String"]["input"];
  prompt?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type ProcessOcrMutation = {
  __typename?: "mutation_root";
  processOCR: { __typename?: "ocrResponse"; result: string; error?: string };
};

export type GetOcrFileUploadUrlQueryVariables = Types.Exact<{
  fileName: Types.Scalars["String"]["input"];
}>;

export type GetOcrFileUploadUrlQuery = {
  __typename?: "query_root";
  getOcrFileUploadUrl: {
    __typename?: "ocrFileUploadResponse";
    s3Key: string;
    url: string;
  };
};

export type NotifyUploadFileSubscriptionVariables = Types.Exact<{
  id: Types.Scalars["String"]["input"];
}>;

export type NotifyUploadFileSubscription = {
  __typename?: "subscription_root";
  notifyUploadFile: { __typename?: "spUploadFile"; data: string };
};

export type UploadFileMutationVariables = Types.Exact<{
  id: Types.Scalars["String"]["input"];
  data: Types.Scalars["String"]["input"];
}>;

export type UploadFileMutation = {
  __typename?: "mutation_root";
  uploadFile: boolean;
};

export const ProcessOcrDocument = gql`
  mutation processOCR(
    $engine: enginType!
    $fileS3Key: String!
    $prompt: String
  ) {
    processOCR(
      input: { engine: $engine, fileS3Key: $fileS3Key, prompt: $prompt }
    ) {
      result
      error
    }
  }
`;
export type ProcessOcrMutationFn = Apollo.MutationFunction<
  ProcessOcrMutation,
  ProcessOcrMutationVariables
>;

/**
 * __useProcessOcrMutation__
 *
 * To run a mutation, you first call `useProcessOcrMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useProcessOcrMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [processOcrMutation, { data, loading, error }] = useProcessOcrMutation({
 *   variables: {
 *      engine: // value for 'engine'
 *      fileS3Key: // value for 'fileS3Key'
 *      prompt: // value for 'prompt'
 *   },
 * });
 */
export function useProcessOcrMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ProcessOcrMutation,
    ProcessOcrMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<ProcessOcrMutation, ProcessOcrMutationVariables>(
    ProcessOcrDocument,
    options,
  );
}
export type ProcessOcrMutationHookResult = ReturnType<
  typeof useProcessOcrMutation
>;
export type ProcessOcrMutationResult =
  Apollo.MutationResult<ProcessOcrMutation>;
export type ProcessOcrMutationOptions = Apollo.BaseMutationOptions<
  ProcessOcrMutation,
  ProcessOcrMutationVariables
>;
export const GetOcrFileUploadUrlDocument = gql`
  query getOcrFileUploadUrl($fileName: String!) {
    getOcrFileUploadUrl(input: { fileName: $fileName }) {
      s3Key
      url
    }
  }
`;

/**
 * __useGetOcrFileUploadUrlQuery__
 *
 * To run a query within a React component, call `useGetOcrFileUploadUrlQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetOcrFileUploadUrlQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetOcrFileUploadUrlQuery({
 *   variables: {
 *      fileName: // value for 'fileName'
 *   },
 * });
 */
export function useGetOcrFileUploadUrlQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetOcrFileUploadUrlQuery,
    GetOcrFileUploadUrlQueryVariables
  > &
    (
      | { variables: GetOcrFileUploadUrlQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetOcrFileUploadUrlQuery,
    GetOcrFileUploadUrlQueryVariables
  >(GetOcrFileUploadUrlDocument, options);
}
export function useGetOcrFileUploadUrlLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetOcrFileUploadUrlQuery,
    GetOcrFileUploadUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetOcrFileUploadUrlQuery,
    GetOcrFileUploadUrlQueryVariables
  >(GetOcrFileUploadUrlDocument, options);
}
export function useGetOcrFileUploadUrlSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetOcrFileUploadUrlQuery,
    GetOcrFileUploadUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetOcrFileUploadUrlQuery,
    GetOcrFileUploadUrlQueryVariables
  >(GetOcrFileUploadUrlDocument, options);
}
export type GetOcrFileUploadUrlQueryHookResult = ReturnType<
  typeof useGetOcrFileUploadUrlQuery
>;
export type GetOcrFileUploadUrlLazyQueryHookResult = ReturnType<
  typeof useGetOcrFileUploadUrlLazyQuery
>;
export type GetOcrFileUploadUrlSuspenseQueryHookResult = ReturnType<
  typeof useGetOcrFileUploadUrlSuspenseQuery
>;
export type GetOcrFileUploadUrlQueryResult = Apollo.QueryResult<
  GetOcrFileUploadUrlQuery,
  GetOcrFileUploadUrlQueryVariables
>;
export const NotifyUploadFileDocument = gql`
  subscription notifyUploadFile($id: String!) {
    notifyUploadFile(id: $id) {
      data
    }
  }
`;

/**
 * __useNotifyUploadFileSubscription__
 *
 * To run a query within a React component, call `useNotifyUploadFileSubscription` and pass it any options that fit your needs.
 * When your component renders, `useNotifyUploadFileSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useNotifyUploadFileSubscription({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useNotifyUploadFileSubscription(
  baseOptions: Apollo.SubscriptionHookOptions<
    NotifyUploadFileSubscription,
    NotifyUploadFileSubscriptionVariables
  > &
    (
      | { variables: NotifyUploadFileSubscriptionVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSubscription<
    NotifyUploadFileSubscription,
    NotifyUploadFileSubscriptionVariables
  >(NotifyUploadFileDocument, options);
}
export type NotifyUploadFileSubscriptionHookResult = ReturnType<
  typeof useNotifyUploadFileSubscription
>;
export type NotifyUploadFileSubscriptionResult =
  Apollo.SubscriptionResult<NotifyUploadFileSubscription>;
export const UploadFileDocument = gql`
  mutation uploadFile($id: String!, $data: String!) {
    uploadFile(id: $id, data: $data)
  }
`;
export type UploadFileMutationFn = Apollo.MutationFunction<
  UploadFileMutation,
  UploadFileMutationVariables
>;

/**
 * __useUploadFileMutation__
 *
 * To run a mutation, you first call `useUploadFileMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUploadFileMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [uploadFileMutation, { data, loading, error }] = useUploadFileMutation({
 *   variables: {
 *      id: // value for 'id'
 *      data: // value for 'data'
 *   },
 * });
 */
export function useUploadFileMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UploadFileMutation,
    UploadFileMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<UploadFileMutation, UploadFileMutationVariables>(
    UploadFileDocument,
    options,
  );
}
export type UploadFileMutationHookResult = ReturnType<
  typeof useUploadFileMutation
>;
export type UploadFileMutationResult =
  Apollo.MutationResult<UploadFileMutation>;
export type UploadFileMutationOptions = Apollo.BaseMutationOptions<
  UploadFileMutation,
  UploadFileMutationVariables
>;
