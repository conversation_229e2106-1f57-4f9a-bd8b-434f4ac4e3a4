import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type SearchTenMasterItemMutationVariables = Types.Exact<{
  drugKbns?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars["Int"]["input"]>>
    | Types.InputMaybe<Types.Scalars["Int"]["input"]>
  >;
  includeMisai?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  includeRosai?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isAllowSearchDeletedItem?: Types.InputMaybe<
    Types.Scalars["Boolean"]["input"]
  >;
  isDeleted?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isExpired?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isExpiredSearchIfNoData?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isIncludeUsage?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isMasterSearch?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isSearch831SuffixOnly?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isSearchGazoDensibaitaiHozon?: Types.InputMaybe<
    Types.Scalars["Boolean"]["input"]
  >;
  isSearchKenSaItem?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isSearchSanteiItem?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isSearchSuggestion?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  itemCodeStartWith?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  keyword: Types.Scalars["String"]["input"];
  itemFilter?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars["Int"]["input"]>>
    | Types.InputMaybe<Types.Scalars["Int"]["input"]>
  >;
  kouiKbn?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kouiKbns?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars["Int"]["input"]>>
    | Types.InputMaybe<Types.Scalars["Int"]["input"]>
  >;
  onlyUsage?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  oriKouiKbn?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  pageCount?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  pageIndex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate: Types.Scalars["Int"]["input"];
  yjCode?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type SearchTenMasterItemMutation = {
  __typename?: "mutation_root";
  postApiMstItemSearchTenMstItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
      tenMsts?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemTenItemDto";
        name?: string;
        itemCd?: string;
      }>;
    };
  };
};

export const SearchTenMasterItemDocument = gql`
  mutation searchTenMasterItem(
    $drugKbns: [Int] = [1, 4, 6]
    $includeMisai: Boolean = true
    $includeRosai: Boolean = true
    $isAllowSearchDeletedItem: Boolean = false
    $isDeleted: Boolean = false
    $isExpired: Boolean = false
    $isExpiredSearchIfNoData: Boolean = false
    $isIncludeUsage: Boolean = false
    $isMasterSearch: Boolean = false
    $isSearch831SuffixOnly: Boolean = false
    $isSearchGazoDensibaitaiHozon: Boolean = true
    $isSearchKenSaItem: Boolean = false
    $isSearchSanteiItem: Boolean = false
    $isSearchSuggestion: Boolean = true
    $itemCodeStartWith: String = ""
    $keyword: String!
    $itemFilter: [Int] = []
    $kouiKbn: Int = -1
    $kouiKbns: [Int] = 10
    $onlyUsage: Boolean = false
    $oriKouiKbn: Int = 0
    $pageCount: Int = 30
    $pageIndex: Int
    $sinDate: Int!
    $yjCode: String = ""
  ) {
    postApiMstItemSearchTenMstItem(
      emrCloudApiRequestsMstItemSearchTenMstItemRequestInput: {
        keyword: $keyword
        drugKbns: $drugKbns
        pageIndex: $pageIndex
        pageCount: $pageCount
        kouiKbn: $kouiKbn
        includeMisai: $includeMisai
        includeRosai: $includeRosai
        isAllowSearchDeletedItem: $isAllowSearchDeletedItem
        isDeleted: $isDeleted
        isExpired: $isExpired
        isExpiredSearchIfNoData: $isExpiredSearchIfNoData
        isIncludeUsage: $isIncludeUsage
        isMasterSearch: $isMasterSearch
        isSearch831SuffixOnly: $isSearch831SuffixOnly
        isSearchGazoDensibaitaiHozon: $isSearchGazoDensibaitaiHozon
        isSearchKenSaItem: $isSearchKenSaItem
        isSearchSanteiItem: $isSearchSanteiItem
        itemCodeStartWith: $itemCodeStartWith
        yjCode: $yjCode
        isSearchSuggestion: $isSearchSuggestion
        onlyUsage: $onlyUsage
        oriKouiKbn: $oriKouiKbn
        itemFilter: $itemFilter
        sinDate: $sinDate
        kouiKbns: $kouiKbns
      }
    ) {
      data {
        tenMsts {
          name
          itemCd
        }
      }
    }
  }
`;
export type SearchTenMasterItemMutationFn = Apollo.MutationFunction<
  SearchTenMasterItemMutation,
  SearchTenMasterItemMutationVariables
>;

/**
 * __useSearchTenMasterItemMutation__
 *
 * To run a mutation, you first call `useSearchTenMasterItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSearchTenMasterItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [searchTenMasterItemMutation, { data, loading, error }] = useSearchTenMasterItemMutation({
 *   variables: {
 *      drugKbns: // value for 'drugKbns'
 *      includeMisai: // value for 'includeMisai'
 *      includeRosai: // value for 'includeRosai'
 *      isAllowSearchDeletedItem: // value for 'isAllowSearchDeletedItem'
 *      isDeleted: // value for 'isDeleted'
 *      isExpired: // value for 'isExpired'
 *      isExpiredSearchIfNoData: // value for 'isExpiredSearchIfNoData'
 *      isIncludeUsage: // value for 'isIncludeUsage'
 *      isMasterSearch: // value for 'isMasterSearch'
 *      isSearch831SuffixOnly: // value for 'isSearch831SuffixOnly'
 *      isSearchGazoDensibaitaiHozon: // value for 'isSearchGazoDensibaitaiHozon'
 *      isSearchKenSaItem: // value for 'isSearchKenSaItem'
 *      isSearchSanteiItem: // value for 'isSearchSanteiItem'
 *      isSearchSuggestion: // value for 'isSearchSuggestion'
 *      itemCodeStartWith: // value for 'itemCodeStartWith'
 *      keyword: // value for 'keyword'
 *      itemFilter: // value for 'itemFilter'
 *      kouiKbn: // value for 'kouiKbn'
 *      kouiKbns: // value for 'kouiKbns'
 *      onlyUsage: // value for 'onlyUsage'
 *      oriKouiKbn: // value for 'oriKouiKbn'
 *      pageCount: // value for 'pageCount'
 *      pageIndex: // value for 'pageIndex'
 *      sinDate: // value for 'sinDate'
 *      yjCode: // value for 'yjCode'
 *   },
 * });
 */
export function useSearchTenMasterItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SearchTenMasterItemMutation,
    SearchTenMasterItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SearchTenMasterItemMutation,
    SearchTenMasterItemMutationVariables
  >(SearchTenMasterItemDocument, options);
}
export type SearchTenMasterItemMutationHookResult = ReturnType<
  typeof useSearchTenMasterItemMutation
>;
export type SearchTenMasterItemMutationResult =
  Apollo.MutationResult<SearchTenMasterItemMutation>;
export type SearchTenMasterItemMutationOptions = Apollo.BaseMutationOptions<
  SearchTenMasterItemMutation,
  SearchTenMasterItemMutationVariables
>;
