import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetLockListQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
}>;

export type GetLockListQuery = {
  __typename?: "query_root";
  getApiLockGetListLockMedicalTab?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesLockGetListLockMedicalTabResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesLockGetListLockMedicalTabResponse";
      lockInfs?: Array<{
        __typename?: "DomainModelsLockLockMedicalModel";
        userName?: string;
        userId?: number;
        lockDate?: string;
        functionCode?: string;
        loginKey?: string;
        tabKey?: string;
      }>;
    };
  };
};

export type AddLockMutationVariables = Types.Exact<{
  functionCod: Types.Scalars["String"]["input"];
  loginKey?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  ptId: Types.Scalars["BigInt"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
  tabKey?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type AddLockMutation = {
  __typename?: "mutation_root";
  postApiLockAddLock?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesLockLockResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesLockLockResponse";
      userName?: string;
      userId?: number;
      lockLevel?: number;
      screenName?: string;
      addLockInputData?: {
        __typename?: "UseCaseLockAddAddLockInputData";
        functionCode?: string;
        hpId?: number;
        loginKey?: string;
        ptId?: string;
        raiinNo?: string;
        sinDate?: number;
        tabKey?: string;
        userId?: number;
      };
    };
  };
};

export type RemoveLockMutationVariables = Types.Exact<{
  functionCod: Types.Scalars["String"]["input"];
  ptId: Types.Scalars["BigInt"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
  tabKey?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  loginKey?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type RemoveLockMutation = {
  __typename?: "mutation_root";
  postApiLockRemoveLock?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesLockUpdateVisitingLockResponse";
    message?: string;
    status?: number;
  };
};

export type RequestUnlockMutationVariables = Types.Exact<{
  functionCd: Types.Scalars["String"]["input"];
  ptId: Types.Scalars["BigInt"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
  userId: Types.Scalars["Int"]["input"];
  loginKey?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  tabKey: Types.Scalars["String"]["input"];
}>;

export type RequestUnlockMutation = {
  __typename?: "mutation_root";
  postApiLockRequestLockMedicalTab?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesLockRequestLockMedicalTabResponse";
    message?: string;
    status?: number;
  };
};

export type AcceptRequestUnlockMutationVariables = Types.Exact<{
  functionCd: Types.Scalars["String"]["input"];
  isAccept: Types.Scalars["Boolean"]["input"];
  loginKey: Types.Scalars["String"]["input"];
  ptId: Types.Scalars["BigInt"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
  tabKey: Types.Scalars["String"]["input"];
  userRequestId: Types.Scalars["Int"]["input"];
}>;

export type AcceptRequestUnlockMutation = {
  __typename?: "mutation_root";
  postApiLockAcceptLockMedicalTab?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesLockAcceptLockMedicalTabResponse";
    message?: string;
    status?: number;
  };
};

export type RemoveLockByTabMutationVariables = Types.Exact<{
  sinDate: Types.Scalars["Int"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
  tabKey: Types.Scalars["String"]["input"];
  ptId: Types.Scalars["BigInt"]["input"];
  loginKey: Types.Scalars["String"]["input"];
}>;

export type RemoveLockByTabMutation = {
  __typename?: "mutation_root";
  postApiLockRemoveLockMedicalByTab?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesLockUpdateVisitingLockResponse";
    message?: string;
    status?: number;
  };
};

export type RemoveAllLockByTabMutationVariables = Types.Exact<{
  loginKey: Types.Scalars["String"]["input"];
}>;

export type RemoveAllLockByTabMutation = {
  __typename?: "mutation_root";
  postApiLockRemoveAllLockMedicalTab?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesLockUpdateVisitingLockResponse";
    message?: string;
    status?: number;
  };
};

export const GetLockListDocument = gql`
  query getLockList($ptId: BigInt!, $raiinNo: BigInt!, $sinDate: Int!) {
    getApiLockGetListLockMedicalTab(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        lockInfs {
          userName
          userId
          lockDate
          functionCode
          loginKey
          tabKey
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetLockListQuery__
 *
 * To run a query within a React component, call `useGetLockListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLockListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLockListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetLockListQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetLockListQuery,
    GetLockListQueryVariables
  > &
    (
      | { variables: GetLockListQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetLockListQuery, GetLockListQueryVariables>(
    GetLockListDocument,
    options,
  );
}
export function useGetLockListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetLockListQuery,
    GetLockListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetLockListQuery, GetLockListQueryVariables>(
    GetLockListDocument,
    options,
  );
}
export function useGetLockListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetLockListQuery,
    GetLockListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<GetLockListQuery, GetLockListQueryVariables>(
    GetLockListDocument,
    options,
  );
}
export type GetLockListQueryHookResult = ReturnType<typeof useGetLockListQuery>;
export type GetLockListLazyQueryHookResult = ReturnType<
  typeof useGetLockListLazyQuery
>;
export type GetLockListSuspenseQueryHookResult = ReturnType<
  typeof useGetLockListSuspenseQuery
>;
export type GetLockListQueryResult = Apollo.QueryResult<
  GetLockListQuery,
  GetLockListQueryVariables
>;
export const AddLockDocument = gql`
  mutation addLock(
    $functionCod: String!
    $loginKey: String
    $ptId: BigInt!
    $raiinNo: BigInt!
    $sinDate: Int!
    $tabKey: String
  ) {
    postApiLockAddLock(
      emrCloudApiRequestsLockLockRequestInput: {
        functionCod: $functionCod
        loginKey: $loginKey
        ptId: $ptId
        raiinNo: $raiinNo
        sinDate: $sinDate
        tabKey: $tabKey
      }
    ) {
      data {
        userName
        userId
        lockLevel
        screenName
        addLockInputData {
          functionCode
          hpId
          loginKey
          ptId
          raiinNo
          sinDate
          tabKey
          userId
        }
      }
      message
      status
    }
  }
`;
export type AddLockMutationFn = Apollo.MutationFunction<
  AddLockMutation,
  AddLockMutationVariables
>;

/**
 * __useAddLockMutation__
 *
 * To run a mutation, you first call `useAddLockMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddLockMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addLockMutation, { data, loading, error }] = useAddLockMutation({
 *   variables: {
 *      functionCod: // value for 'functionCod'
 *      loginKey: // value for 'loginKey'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *      tabKey: // value for 'tabKey'
 *   },
 * });
 */
export function useAddLockMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AddLockMutation,
    AddLockMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<AddLockMutation, AddLockMutationVariables>(
    AddLockDocument,
    options,
  );
}
export type AddLockMutationHookResult = ReturnType<typeof useAddLockMutation>;
export type AddLockMutationResult = Apollo.MutationResult<AddLockMutation>;
export type AddLockMutationOptions = Apollo.BaseMutationOptions<
  AddLockMutation,
  AddLockMutationVariables
>;
export const RemoveLockDocument = gql`
  mutation removeLock(
    $functionCod: String!
    $ptId: BigInt!
    $raiinNo: BigInt!
    $sinDate: Int!
    $tabKey: String
    $loginKey: String
  ) {
    postApiLockRemoveLock(
      emrCloudApiRequestsLockLockRequestInput: {
        functionCod: $functionCod
        ptId: $ptId
        raiinNo: $raiinNo
        sinDate: $sinDate
        tabKey: $tabKey
        loginKey: $loginKey
      }
    ) {
      message
      status
    }
  }
`;
export type RemoveLockMutationFn = Apollo.MutationFunction<
  RemoveLockMutation,
  RemoveLockMutationVariables
>;

/**
 * __useRemoveLockMutation__
 *
 * To run a mutation, you first call `useRemoveLockMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRemoveLockMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [removeLockMutation, { data, loading, error }] = useRemoveLockMutation({
 *   variables: {
 *      functionCod: // value for 'functionCod'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *      tabKey: // value for 'tabKey'
 *      loginKey: // value for 'loginKey'
 *   },
 * });
 */
export function useRemoveLockMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RemoveLockMutation,
    RemoveLockMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<RemoveLockMutation, RemoveLockMutationVariables>(
    RemoveLockDocument,
    options,
  );
}
export type RemoveLockMutationHookResult = ReturnType<
  typeof useRemoveLockMutation
>;
export type RemoveLockMutationResult =
  Apollo.MutationResult<RemoveLockMutation>;
export type RemoveLockMutationOptions = Apollo.BaseMutationOptions<
  RemoveLockMutation,
  RemoveLockMutationVariables
>;
export const RequestUnlockDocument = gql`
  mutation requestUnlock(
    $functionCd: String!
    $ptId: BigInt!
    $raiinNo: BigInt!
    $sinDate: Int!
    $userId: Int!
    $loginKey: String
    $tabKey: String!
  ) {
    postApiLockRequestLockMedicalTab(
      emrCloudApiRequestsLockRequestLockMedicalTabRequestInput: {
        ptId: $ptId
        raiinNo: $raiinNo
        sinDate: $sinDate
        userId: $userId
        functionCd: $functionCd
        loginKey: $loginKey
        tabKey: $tabKey
      }
    ) {
      message
      status
    }
  }
`;
export type RequestUnlockMutationFn = Apollo.MutationFunction<
  RequestUnlockMutation,
  RequestUnlockMutationVariables
>;

/**
 * __useRequestUnlockMutation__
 *
 * To run a mutation, you first call `useRequestUnlockMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRequestUnlockMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [requestUnlockMutation, { data, loading, error }] = useRequestUnlockMutation({
 *   variables: {
 *      functionCd: // value for 'functionCd'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *      userId: // value for 'userId'
 *      loginKey: // value for 'loginKey'
 *      tabKey: // value for 'tabKey'
 *   },
 * });
 */
export function useRequestUnlockMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RequestUnlockMutation,
    RequestUnlockMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    RequestUnlockMutation,
    RequestUnlockMutationVariables
  >(RequestUnlockDocument, options);
}
export type RequestUnlockMutationHookResult = ReturnType<
  typeof useRequestUnlockMutation
>;
export type RequestUnlockMutationResult =
  Apollo.MutationResult<RequestUnlockMutation>;
export type RequestUnlockMutationOptions = Apollo.BaseMutationOptions<
  RequestUnlockMutation,
  RequestUnlockMutationVariables
>;
export const AcceptRequestUnlockDocument = gql`
  mutation acceptRequestUnlock(
    $functionCd: String!
    $isAccept: Boolean!
    $loginKey: String!
    $ptId: BigInt!
    $raiinNo: BigInt!
    $sinDate: Int!
    $tabKey: String!
    $userRequestId: Int!
  ) {
    postApiLockAcceptLockMedicalTab(
      emrCloudApiRequestsLockAcceptLockMedicalTabRequestInput: {
        functionCd: $functionCd
        isAccept: $isAccept
        loginKey: $loginKey
        ptId: $ptId
        raiinNo: $raiinNo
        sinDate: $sinDate
        tabKey: $tabKey
        userRequestId: $userRequestId
      }
    ) {
      message
      status
    }
  }
`;
export type AcceptRequestUnlockMutationFn = Apollo.MutationFunction<
  AcceptRequestUnlockMutation,
  AcceptRequestUnlockMutationVariables
>;

/**
 * __useAcceptRequestUnlockMutation__
 *
 * To run a mutation, you first call `useAcceptRequestUnlockMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAcceptRequestUnlockMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [acceptRequestUnlockMutation, { data, loading, error }] = useAcceptRequestUnlockMutation({
 *   variables: {
 *      functionCd: // value for 'functionCd'
 *      isAccept: // value for 'isAccept'
 *      loginKey: // value for 'loginKey'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *      tabKey: // value for 'tabKey'
 *      userRequestId: // value for 'userRequestId'
 *   },
 * });
 */
export function useAcceptRequestUnlockMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AcceptRequestUnlockMutation,
    AcceptRequestUnlockMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AcceptRequestUnlockMutation,
    AcceptRequestUnlockMutationVariables
  >(AcceptRequestUnlockDocument, options);
}
export type AcceptRequestUnlockMutationHookResult = ReturnType<
  typeof useAcceptRequestUnlockMutation
>;
export type AcceptRequestUnlockMutationResult =
  Apollo.MutationResult<AcceptRequestUnlockMutation>;
export type AcceptRequestUnlockMutationOptions = Apollo.BaseMutationOptions<
  AcceptRequestUnlockMutation,
  AcceptRequestUnlockMutationVariables
>;
export const RemoveLockByTabDocument = gql`
  mutation removeLockByTab(
    $sinDate: Int!
    $raiinNo: BigInt!
    $tabKey: String!
    $ptId: BigInt!
    $loginKey: String!
  ) {
    postApiLockRemoveLockMedicalByTab(
      emrCloudApiRequestsLockRemoveLockMedicalByTabRequestInput: {
        sinDate: $sinDate
        raiinNo: $raiinNo
        tabKey: $tabKey
        ptId: $ptId
        loginKey: $loginKey
      }
    ) {
      message
      status
    }
  }
`;
export type RemoveLockByTabMutationFn = Apollo.MutationFunction<
  RemoveLockByTabMutation,
  RemoveLockByTabMutationVariables
>;

/**
 * __useRemoveLockByTabMutation__
 *
 * To run a mutation, you first call `useRemoveLockByTabMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRemoveLockByTabMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [removeLockByTabMutation, { data, loading, error }] = useRemoveLockByTabMutation({
 *   variables: {
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *      tabKey: // value for 'tabKey'
 *      ptId: // value for 'ptId'
 *      loginKey: // value for 'loginKey'
 *   },
 * });
 */
export function useRemoveLockByTabMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RemoveLockByTabMutation,
    RemoveLockByTabMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    RemoveLockByTabMutation,
    RemoveLockByTabMutationVariables
  >(RemoveLockByTabDocument, options);
}
export type RemoveLockByTabMutationHookResult = ReturnType<
  typeof useRemoveLockByTabMutation
>;
export type RemoveLockByTabMutationResult =
  Apollo.MutationResult<RemoveLockByTabMutation>;
export type RemoveLockByTabMutationOptions = Apollo.BaseMutationOptions<
  RemoveLockByTabMutation,
  RemoveLockByTabMutationVariables
>;
export const RemoveAllLockByTabDocument = gql`
  mutation removeAllLockByTab($loginKey: String!) {
    postApiLockRemoveAllLockMedicalTab(
      emrCloudApiRequestsLockRemoveAllLockMedicalTabRequestInput: {
        loginKey: $loginKey
      }
    ) {
      message
      status
    }
  }
`;
export type RemoveAllLockByTabMutationFn = Apollo.MutationFunction<
  RemoveAllLockByTabMutation,
  RemoveAllLockByTabMutationVariables
>;

/**
 * __useRemoveAllLockByTabMutation__
 *
 * To run a mutation, you first call `useRemoveAllLockByTabMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRemoveAllLockByTabMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [removeAllLockByTabMutation, { data, loading, error }] = useRemoveAllLockByTabMutation({
 *   variables: {
 *      loginKey: // value for 'loginKey'
 *   },
 * });
 */
export function useRemoveAllLockByTabMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RemoveAllLockByTabMutation,
    RemoveAllLockByTabMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    RemoveAllLockByTabMutation,
    RemoveAllLockByTabMutationVariables
  >(RemoveAllLockByTabDocument, options);
}
export type RemoveAllLockByTabMutationHookResult = ReturnType<
  typeof useRemoveAllLockByTabMutation
>;
export type RemoveAllLockByTabMutationResult =
  Apollo.MutationResult<RemoveAllLockByTabMutation>;
export type RemoveAllLockByTabMutationOptions = Apollo.BaseMutationOptions<
  RemoveAllLockByTabMutation,
  RemoveAllLockByTabMutationVariables
>;
