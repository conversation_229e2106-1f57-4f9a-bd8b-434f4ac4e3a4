import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetSelectiveCommentQueryVariables = Types.Exact<{
  itemCds: Types.Scalars["String"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
}>;

export type GetSelectiveCommentQuery = {
  __typename?: "query_root";
  getApiMstItemGetSelectiveComment?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetSelectiveCommentResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetSelectiveCommentResponse";
      comments?: Array<{
        __typename?: "UseCaseMstItemGetSelectiveCommentGetSelectiveCommentItemOfList";
        listGroupComment?: Array<{
          __typename?: "UseCaseMstItemGetSelectiveCommentGroupSelectiveCommentItem";
          listComment?: Array<{
            __typename?: "DomainModelsMstItemRecedenCmtSelectModel";
            tenMst?: {
              __typename?: "DomainModelsMstItemTenItemModel";
              cmtCol1?: number;
              cmtCol2?: number;
              cmtCol3?: number;
              cmtCol4?: number;
              cmtColKeta1?: number;
              cmtColKeta2?: number;
              cmtColKeta3?: number;
              cmtColKeta4?: number;
              drugKbn?: number;
              itemCd?: string;
              name?: string;
              receName?: string;
              sinKouiKbn?: number;
              kouiName?: string;
              odrUnitName?: string;
              masterSbt?: string;
              kohatuKbn?: number;
              yohoKbn?: number;
              kubunToDisplay?: string;
            };
          }>;
        }>;
      }>;
    };
  };
};

export type OrdInfValidateInputItemMutationVariables = Types.Exact<{
  emrCloudApiRequestsOrdInfsValidationInputItemRequestInput: Types.EmrCloudApiRequestsOrdInfsValidationInputItemRequestInput;
}>;

export type OrdInfValidateInputItemMutation = {
  __typename?: "mutation_root";
  postApiOrdInfValidateInputItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOrdInfsValidationInputItemOrdInfListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOrdInfsValidationInputItemOrdInfListResponse";
      validationOdrInfs?: Array<{
        __typename?: "EmrCloudApiResponsesMedicalExaminationValidationTodayOrdItemResponse";
        orderInfDetailPosition?: string;
        orderInfPosition?: string;
        status?: number;
        validationField?: string;
        validationMessage?: string;
      }>;
    };
  };
};

export const GetSelectiveCommentDocument = gql`
  query getSelectiveComment($itemCds: String!, $sinDate: Int!) {
    getApiMstItemGetSelectiveComment(itemCds: $itemCds, sinDate: $sinDate) {
      data {
        comments {
          listGroupComment {
            listComment {
              tenMst {
                cmtCol1
                cmtCol2
                cmtCol3
                cmtCol4
                cmtColKeta1
                cmtColKeta2
                cmtColKeta3
                cmtColKeta4
                drugKbn
                itemCd
                name
                receName
                sinKouiKbn
                kouiName
                odrUnitName
                masterSbt
                kohatuKbn
                yohoKbn
                kubunToDisplay
              }
            }
          }
        }
      }
    }
  }
`;

/**
 * __useGetSelectiveCommentQuery__
 *
 * To run a query within a React component, call `useGetSelectiveCommentQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSelectiveCommentQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSelectiveCommentQuery({
 *   variables: {
 *      itemCds: // value for 'itemCds'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetSelectiveCommentQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetSelectiveCommentQuery,
    GetSelectiveCommentQueryVariables
  > &
    (
      | { variables: GetSelectiveCommentQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSelectiveCommentQuery,
    GetSelectiveCommentQueryVariables
  >(GetSelectiveCommentDocument, options);
}
export function useGetSelectiveCommentLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSelectiveCommentQuery,
    GetSelectiveCommentQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSelectiveCommentQuery,
    GetSelectiveCommentQueryVariables
  >(GetSelectiveCommentDocument, options);
}
export function useGetSelectiveCommentSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSelectiveCommentQuery,
    GetSelectiveCommentQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSelectiveCommentQuery,
    GetSelectiveCommentQueryVariables
  >(GetSelectiveCommentDocument, options);
}
export type GetSelectiveCommentQueryHookResult = ReturnType<
  typeof useGetSelectiveCommentQuery
>;
export type GetSelectiveCommentLazyQueryHookResult = ReturnType<
  typeof useGetSelectiveCommentLazyQuery
>;
export type GetSelectiveCommentSuspenseQueryHookResult = ReturnType<
  typeof useGetSelectiveCommentSuspenseQuery
>;
export type GetSelectiveCommentQueryResult = Apollo.QueryResult<
  GetSelectiveCommentQuery,
  GetSelectiveCommentQueryVariables
>;
export const OrdInfValidateInputItemDocument = gql`
  mutation ordInfValidateInputItem(
    $emrCloudApiRequestsOrdInfsValidationInputItemRequestInput: EmrCloudApiRequestsOrdInfsValidationInputItemRequestInput!
  ) {
    postApiOrdInfValidateInputItem(
      emrCloudApiRequestsOrdInfsValidationInputItemRequestInput: $emrCloudApiRequestsOrdInfsValidationInputItemRequestInput
    ) {
      data {
        validationOdrInfs {
          orderInfDetailPosition
          orderInfPosition
          status
          validationField
          validationMessage
        }
      }
      message
      status
    }
  }
`;
export type OrdInfValidateInputItemMutationFn = Apollo.MutationFunction<
  OrdInfValidateInputItemMutation,
  OrdInfValidateInputItemMutationVariables
>;

/**
 * __useOrdInfValidateInputItemMutation__
 *
 * To run a mutation, you first call `useOrdInfValidateInputItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useOrdInfValidateInputItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [ordInfValidateInputItemMutation, { data, loading, error }] = useOrdInfValidateInputItemMutation({
 *   variables: {
 *      emrCloudApiRequestsOrdInfsValidationInputItemRequestInput: // value for 'emrCloudApiRequestsOrdInfsValidationInputItemRequestInput'
 *   },
 * });
 */
export function useOrdInfValidateInputItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    OrdInfValidateInputItemMutation,
    OrdInfValidateInputItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    OrdInfValidateInputItemMutation,
    OrdInfValidateInputItemMutationVariables
  >(OrdInfValidateInputItemDocument, options);
}
export type OrdInfValidateInputItemMutationHookResult = ReturnType<
  typeof useOrdInfValidateInputItemMutation
>;
export type OrdInfValidateInputItemMutationResult =
  Apollo.MutationResult<OrdInfValidateInputItemMutation>;
export type OrdInfValidateInputItemMutationOptions = Apollo.BaseMutationOptions<
  OrdInfValidateInputItemMutation,
  OrdInfValidateInputItemMutationVariables
>;
