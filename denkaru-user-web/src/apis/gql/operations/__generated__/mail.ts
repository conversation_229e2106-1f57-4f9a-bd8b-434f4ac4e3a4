import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetMailDeliverySettingsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetMailDeliverySettingsQuery = {
  __typename?: "query_root";
  getMailDeliverySettings: {
    __typename?: "MailDeliverySettingsRes";
    staffId: number;
    email: string;
    allowLogin: boolean;
    allowReservation: boolean;
    allowNewMessage: boolean;
    allowTask: boolean;
  };
};

export type SetMailDeliverySettingsMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.MailDeliverySettingsReq>;
}>;

export type SetMailDeliverySettingsMutation = {
  __typename?: "mutation_root";
  setMailDeliverySettings: {
    __typename?: "SetMailDeliverySettingsRes";
    success: number;
    isVerificationSent: number;
  };
};

export const GetMailDeliverySettingsDocument = gql`
  query getMailDeliverySettings {
    getMailDeliverySettings {
      staffId
      email
      allowLogin
      allowReservation
      allowNewMessage
      allowTask
    }
  }
`;

/**
 * __useGetMailDeliverySettingsQuery__
 *
 * To run a query within a React component, call `useGetMailDeliverySettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMailDeliverySettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMailDeliverySettingsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetMailDeliverySettingsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetMailDeliverySettingsQuery,
    GetMailDeliverySettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetMailDeliverySettingsQuery,
    GetMailDeliverySettingsQueryVariables
  >(GetMailDeliverySettingsDocument, options);
}
export function useGetMailDeliverySettingsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetMailDeliverySettingsQuery,
    GetMailDeliverySettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetMailDeliverySettingsQuery,
    GetMailDeliverySettingsQueryVariables
  >(GetMailDeliverySettingsDocument, options);
}
export function useGetMailDeliverySettingsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetMailDeliverySettingsQuery,
    GetMailDeliverySettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetMailDeliverySettingsQuery,
    GetMailDeliverySettingsQueryVariables
  >(GetMailDeliverySettingsDocument, options);
}
export type GetMailDeliverySettingsQueryHookResult = ReturnType<
  typeof useGetMailDeliverySettingsQuery
>;
export type GetMailDeliverySettingsLazyQueryHookResult = ReturnType<
  typeof useGetMailDeliverySettingsLazyQuery
>;
export type GetMailDeliverySettingsSuspenseQueryHookResult = ReturnType<
  typeof useGetMailDeliverySettingsSuspenseQuery
>;
export type GetMailDeliverySettingsQueryResult = Apollo.QueryResult<
  GetMailDeliverySettingsQuery,
  GetMailDeliverySettingsQueryVariables
>;
export const SetMailDeliverySettingsDocument = gql`
  mutation setMailDeliverySettings($input: MailDeliverySettingsReq) {
    setMailDeliverySettings(input: $input) {
      success
      isVerificationSent
    }
  }
`;
export type SetMailDeliverySettingsMutationFn = Apollo.MutationFunction<
  SetMailDeliverySettingsMutation,
  SetMailDeliverySettingsMutationVariables
>;

/**
 * __useSetMailDeliverySettingsMutation__
 *
 * To run a mutation, you first call `useSetMailDeliverySettingsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSetMailDeliverySettingsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [setMailDeliverySettingsMutation, { data, loading, error }] = useSetMailDeliverySettingsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSetMailDeliverySettingsMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SetMailDeliverySettingsMutation,
    SetMailDeliverySettingsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SetMailDeliverySettingsMutation,
    SetMailDeliverySettingsMutationVariables
  >(SetMailDeliverySettingsDocument, options);
}
export type SetMailDeliverySettingsMutationHookResult = ReturnType<
  typeof useSetMailDeliverySettingsMutation
>;
export type SetMailDeliverySettingsMutationResult =
  Apollo.MutationResult<SetMailDeliverySettingsMutation>;
export type SetMailDeliverySettingsMutationOptions = Apollo.BaseMutationOptions<
  SetMailDeliverySettingsMutation,
  SetMailDeliverySettingsMutationVariables
>;
