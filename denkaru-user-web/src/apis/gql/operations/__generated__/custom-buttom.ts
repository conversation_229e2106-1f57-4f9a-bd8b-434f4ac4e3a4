import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiCustomButtonConfListAllCustomButtonConfQueryVariables =
  Types.Exact<{
    ptId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  }>;

export type GetApiCustomButtonConfListAllCustomButtonConfQuery = {
  __typename?: "query_root";
  getApiCustomButtonConfListAllCustomButtonConf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesCustomButtonConfListAllCustomButtonConfResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesCustomButtonConfListAllCustomButtonConfResponse";
      customButtonConfModels?: Array<{
        __typename?: "DomainModelsCustomButtonConfCustomButtonConfModel";
        urlGenerate?: string;
        filename?: string;
        hpId?: number;
        id?: string;
        isUrl?: number;
        name?: string;
        path?: string;
        pattern?: string;
        sort?: number;
        urlImage?: string;
        workdir?: string;
      }>;
    };
  };
};

export type UpdateCustomButtonSortMutationVariables = Types.Exact<{
  updateSortCustomButtonConfInputItem:
    | Array<Types.EmrCloudApiRequestsCustomButtonConfUpdateSortCustomButtonConfRequestCustomButtonConfRequestInput>
    | Types.EmrCloudApiRequestsCustomButtonConfUpdateSortCustomButtonConfRequestCustomButtonConfRequestInput;
}>;

export type UpdateCustomButtonSortMutation = {
  __typename?: "mutation_root";
  postApiCustomButtonConfUpdateSortForListCustomButtonConf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesCustomButtonConfUpdateSortCustomButtonConfResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesCustomButtonConfUpdateSortCustomButtonConfResponse";
      status?: number;
    };
  };
};

export type GetApiCustomButtonParamMstListCustomButtonParamMstsQueryVariables =
  Types.Exact<{ [key: string]: never }>;

export type GetApiCustomButtonParamMstListCustomButtonParamMstsQuery = {
  __typename?: "query_root";
  getApiCustomButtonParamMstListCustomButtonParamMsts?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesCustomButtonParamMstGetCustomButtonParamMstResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesCustomButtonParamMstGetCustomButtonParamMstResponse";
      customButtonParamMstModels?: Array<{
        __typename?: "DomainModelsCustomButtonParamMstCustomButtonParamMstModel";
        id?: string;
        name?: string;
        param?: string;
        sort?: number;
      }>;
    };
  };
};

export type GetApiCustomButtonConfGetDetailCustomButtonConfQueryVariables =
  Types.Exact<{
    id: Types.Scalars["Int"]["input"];
    raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    ptId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiCustomButtonConfGetDetailCustomButtonConfQuery = {
  __typename?: "query_root";
  getApiCustomButtonConfGetDetailCustomButtonConf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesCustomButtonConfGetDetailCustomButtonConfResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesCustomButtonConfGetDetailCustomButtonConfResponse";
      customButtonConfModel?: {
        __typename?: "DomainModelsCustomButtonConfCustomButtonConfModel";
        filename?: string;
        id?: string;
        isUrl?: number;
        name?: string;
        path?: string;
        pattern?: string;
        sort?: number;
        workdir?: string;
        urlImage?: string;
        urlGenerate?: string;
        hpId?: number;
      };
    };
  };
};

export type DeleteApiCustomButtonConfDeleteCustomButtonConfMutationVariables =
  Types.Exact<{
    id: Types.Scalars["Int"]["input"];
  }>;

export type DeleteApiCustomButtonConfDeleteCustomButtonConfMutation = {
  __typename?: "mutation_root";
  deleteApiCustomButtonConfDeleteCustomButtonConf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesCustomButtonConfDeleteCustomButtonConfResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesCustomButtonConfDeleteCustomButtonConfResponse";
      status?: number;
    };
  };
};

export type PostApiCustomButtonConfSaveCustomButtonConfMutationVariables =
  Types.Exact<{
    input: Types.EmrCloudApiRequestsCustomButtonConfSaveCustomButtonConfRequestInput;
  }>;

export type PostApiCustomButtonConfSaveCustomButtonConfMutation = {
  __typename?: "mutation_root";
  postApiCustomButtonConfSaveCustomButtonConf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesCustomButtonConfSaveCustomButtonConfResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesCustomButtonConfSaveCustomButtonConfResponse";
      id?: string;
      status?: number;
    };
  };
};

export const GetApiCustomButtonConfListAllCustomButtonConfDocument = gql`
  query getApiCustomButtonConfListAllCustomButtonConf(
    $ptId: Int
    $raiinNo: BigInt
  ) {
    getApiCustomButtonConfListAllCustomButtonConf(
      ptId: $ptId
      raiinNo: $raiinNo
    ) {
      data {
        customButtonConfModels {
          urlGenerate
          filename
          hpId
          id
          isUrl
          name
          path
          pattern
          sort
          urlImage
          workdir
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiCustomButtonConfListAllCustomButtonConfQuery__
 *
 * To run a query within a React component, call `useGetApiCustomButtonConfListAllCustomButtonConfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiCustomButtonConfListAllCustomButtonConfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiCustomButtonConfListAllCustomButtonConfQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiCustomButtonConfListAllCustomButtonConfQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiCustomButtonConfListAllCustomButtonConfQuery,
    GetApiCustomButtonConfListAllCustomButtonConfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiCustomButtonConfListAllCustomButtonConfQuery,
    GetApiCustomButtonConfListAllCustomButtonConfQueryVariables
  >(GetApiCustomButtonConfListAllCustomButtonConfDocument, options);
}
export function useGetApiCustomButtonConfListAllCustomButtonConfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiCustomButtonConfListAllCustomButtonConfQuery,
    GetApiCustomButtonConfListAllCustomButtonConfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiCustomButtonConfListAllCustomButtonConfQuery,
    GetApiCustomButtonConfListAllCustomButtonConfQueryVariables
  >(GetApiCustomButtonConfListAllCustomButtonConfDocument, options);
}
export function useGetApiCustomButtonConfListAllCustomButtonConfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiCustomButtonConfListAllCustomButtonConfQuery,
    GetApiCustomButtonConfListAllCustomButtonConfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiCustomButtonConfListAllCustomButtonConfQuery,
    GetApiCustomButtonConfListAllCustomButtonConfQueryVariables
  >(GetApiCustomButtonConfListAllCustomButtonConfDocument, options);
}
export type GetApiCustomButtonConfListAllCustomButtonConfQueryHookResult =
  ReturnType<typeof useGetApiCustomButtonConfListAllCustomButtonConfQuery>;
export type GetApiCustomButtonConfListAllCustomButtonConfLazyQueryHookResult =
  ReturnType<typeof useGetApiCustomButtonConfListAllCustomButtonConfLazyQuery>;
export type GetApiCustomButtonConfListAllCustomButtonConfSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiCustomButtonConfListAllCustomButtonConfSuspenseQuery
  >;
export type GetApiCustomButtonConfListAllCustomButtonConfQueryResult =
  Apollo.QueryResult<
    GetApiCustomButtonConfListAllCustomButtonConfQuery,
    GetApiCustomButtonConfListAllCustomButtonConfQueryVariables
  >;
export const UpdateCustomButtonSortDocument = gql`
  mutation updateCustomButtonSort(
    $updateSortCustomButtonConfInputItem: [EmrCloudApiRequestsCustomButtonConfUpdateSortCustomButtonConfRequestCustomButtonConfRequestInput!]!
  ) {
    postApiCustomButtonConfUpdateSortForListCustomButtonConf(
      emrCloudApiRequestsCustomButtonConfUpdateSortCustomButtonConfRequestInput: {
        updateSortCustomButtonConfInputItem: $updateSortCustomButtonConfInputItem
      }
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type UpdateCustomButtonSortMutationFn = Apollo.MutationFunction<
  UpdateCustomButtonSortMutation,
  UpdateCustomButtonSortMutationVariables
>;

/**
 * __useUpdateCustomButtonSortMutation__
 *
 * To run a mutation, you first call `useUpdateCustomButtonSortMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateCustomButtonSortMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateCustomButtonSortMutation, { data, loading, error }] = useUpdateCustomButtonSortMutation({
 *   variables: {
 *      updateSortCustomButtonConfInputItem: // value for 'updateSortCustomButtonConfInputItem'
 *   },
 * });
 */
export function useUpdateCustomButtonSortMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateCustomButtonSortMutation,
    UpdateCustomButtonSortMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateCustomButtonSortMutation,
    UpdateCustomButtonSortMutationVariables
  >(UpdateCustomButtonSortDocument, options);
}
export type UpdateCustomButtonSortMutationHookResult = ReturnType<
  typeof useUpdateCustomButtonSortMutation
>;
export type UpdateCustomButtonSortMutationResult =
  Apollo.MutationResult<UpdateCustomButtonSortMutation>;
export type UpdateCustomButtonSortMutationOptions = Apollo.BaseMutationOptions<
  UpdateCustomButtonSortMutation,
  UpdateCustomButtonSortMutationVariables
>;
export const GetApiCustomButtonParamMstListCustomButtonParamMstsDocument = gql`
  query getApiCustomButtonParamMstListCustomButtonParamMsts {
    getApiCustomButtonParamMstListCustomButtonParamMsts {
      data {
        customButtonParamMstModels {
          id
          name
          param
          sort
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiCustomButtonParamMstListCustomButtonParamMstsQuery__
 *
 * To run a query within a React component, call `useGetApiCustomButtonParamMstListCustomButtonParamMstsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiCustomButtonParamMstListCustomButtonParamMstsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiCustomButtonParamMstListCustomButtonParamMstsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiCustomButtonParamMstListCustomButtonParamMstsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiCustomButtonParamMstListCustomButtonParamMstsQuery,
    GetApiCustomButtonParamMstListCustomButtonParamMstsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiCustomButtonParamMstListCustomButtonParamMstsQuery,
    GetApiCustomButtonParamMstListCustomButtonParamMstsQueryVariables
  >(GetApiCustomButtonParamMstListCustomButtonParamMstsDocument, options);
}
export function useGetApiCustomButtonParamMstListCustomButtonParamMstsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiCustomButtonParamMstListCustomButtonParamMstsQuery,
    GetApiCustomButtonParamMstListCustomButtonParamMstsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiCustomButtonParamMstListCustomButtonParamMstsQuery,
    GetApiCustomButtonParamMstListCustomButtonParamMstsQueryVariables
  >(GetApiCustomButtonParamMstListCustomButtonParamMstsDocument, options);
}
export function useGetApiCustomButtonParamMstListCustomButtonParamMstsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiCustomButtonParamMstListCustomButtonParamMstsQuery,
    GetApiCustomButtonParamMstListCustomButtonParamMstsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiCustomButtonParamMstListCustomButtonParamMstsQuery,
    GetApiCustomButtonParamMstListCustomButtonParamMstsQueryVariables
  >(GetApiCustomButtonParamMstListCustomButtonParamMstsDocument, options);
}
export type GetApiCustomButtonParamMstListCustomButtonParamMstsQueryHookResult =
  ReturnType<
    typeof useGetApiCustomButtonParamMstListCustomButtonParamMstsQuery
  >;
export type GetApiCustomButtonParamMstListCustomButtonParamMstsLazyQueryHookResult =
  ReturnType<
    typeof useGetApiCustomButtonParamMstListCustomButtonParamMstsLazyQuery
  >;
export type GetApiCustomButtonParamMstListCustomButtonParamMstsSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiCustomButtonParamMstListCustomButtonParamMstsSuspenseQuery
  >;
export type GetApiCustomButtonParamMstListCustomButtonParamMstsQueryResult =
  Apollo.QueryResult<
    GetApiCustomButtonParamMstListCustomButtonParamMstsQuery,
    GetApiCustomButtonParamMstListCustomButtonParamMstsQueryVariables
  >;
export const GetApiCustomButtonConfGetDetailCustomButtonConfDocument = gql`
  query getApiCustomButtonConfGetDetailCustomButtonConf(
    $id: Int!
    $raiinNo: BigInt
    $ptId: Int
  ) {
    getApiCustomButtonConfGetDetailCustomButtonConf(
      id: $id
      raiinNo: $raiinNo
      ptId: $ptId
    ) {
      data {
        customButtonConfModel {
          filename
          id
          isUrl
          name
          path
          pattern
          sort
          workdir
          urlImage
          urlGenerate
          hpId
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiCustomButtonConfGetDetailCustomButtonConfQuery__
 *
 * To run a query within a React component, call `useGetApiCustomButtonConfGetDetailCustomButtonConfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiCustomButtonConfGetDetailCustomButtonConfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiCustomButtonConfGetDetailCustomButtonConfQuery({
 *   variables: {
 *      id: // value for 'id'
 *      raiinNo: // value for 'raiinNo'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiCustomButtonConfGetDetailCustomButtonConfQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiCustomButtonConfGetDetailCustomButtonConfQuery,
    GetApiCustomButtonConfGetDetailCustomButtonConfQueryVariables
  > &
    (
      | {
          variables: GetApiCustomButtonConfGetDetailCustomButtonConfQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiCustomButtonConfGetDetailCustomButtonConfQuery,
    GetApiCustomButtonConfGetDetailCustomButtonConfQueryVariables
  >(GetApiCustomButtonConfGetDetailCustomButtonConfDocument, options);
}
export function useGetApiCustomButtonConfGetDetailCustomButtonConfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiCustomButtonConfGetDetailCustomButtonConfQuery,
    GetApiCustomButtonConfGetDetailCustomButtonConfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiCustomButtonConfGetDetailCustomButtonConfQuery,
    GetApiCustomButtonConfGetDetailCustomButtonConfQueryVariables
  >(GetApiCustomButtonConfGetDetailCustomButtonConfDocument, options);
}
export function useGetApiCustomButtonConfGetDetailCustomButtonConfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiCustomButtonConfGetDetailCustomButtonConfQuery,
    GetApiCustomButtonConfGetDetailCustomButtonConfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiCustomButtonConfGetDetailCustomButtonConfQuery,
    GetApiCustomButtonConfGetDetailCustomButtonConfQueryVariables
  >(GetApiCustomButtonConfGetDetailCustomButtonConfDocument, options);
}
export type GetApiCustomButtonConfGetDetailCustomButtonConfQueryHookResult =
  ReturnType<typeof useGetApiCustomButtonConfGetDetailCustomButtonConfQuery>;
export type GetApiCustomButtonConfGetDetailCustomButtonConfLazyQueryHookResult =
  ReturnType<
    typeof useGetApiCustomButtonConfGetDetailCustomButtonConfLazyQuery
  >;
export type GetApiCustomButtonConfGetDetailCustomButtonConfSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiCustomButtonConfGetDetailCustomButtonConfSuspenseQuery
  >;
export type GetApiCustomButtonConfGetDetailCustomButtonConfQueryResult =
  Apollo.QueryResult<
    GetApiCustomButtonConfGetDetailCustomButtonConfQuery,
    GetApiCustomButtonConfGetDetailCustomButtonConfQueryVariables
  >;
export const DeleteApiCustomButtonConfDeleteCustomButtonConfDocument = gql`
  mutation deleteApiCustomButtonConfDeleteCustomButtonConf($id: Int!) {
    deleteApiCustomButtonConfDeleteCustomButtonConf(id: $id) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type DeleteApiCustomButtonConfDeleteCustomButtonConfMutationFn =
  Apollo.MutationFunction<
    DeleteApiCustomButtonConfDeleteCustomButtonConfMutation,
    DeleteApiCustomButtonConfDeleteCustomButtonConfMutationVariables
  >;

/**
 * __useDeleteApiCustomButtonConfDeleteCustomButtonConfMutation__
 *
 * To run a mutation, you first call `useDeleteApiCustomButtonConfDeleteCustomButtonConfMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteApiCustomButtonConfDeleteCustomButtonConfMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteApiCustomButtonConfDeleteCustomButtonConfMutation, { data, loading, error }] = useDeleteApiCustomButtonConfDeleteCustomButtonConfMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeleteApiCustomButtonConfDeleteCustomButtonConfMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteApiCustomButtonConfDeleteCustomButtonConfMutation,
    DeleteApiCustomButtonConfDeleteCustomButtonConfMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteApiCustomButtonConfDeleteCustomButtonConfMutation,
    DeleteApiCustomButtonConfDeleteCustomButtonConfMutationVariables
  >(DeleteApiCustomButtonConfDeleteCustomButtonConfDocument, options);
}
export type DeleteApiCustomButtonConfDeleteCustomButtonConfMutationHookResult =
  ReturnType<typeof useDeleteApiCustomButtonConfDeleteCustomButtonConfMutation>;
export type DeleteApiCustomButtonConfDeleteCustomButtonConfMutationResult =
  Apollo.MutationResult<DeleteApiCustomButtonConfDeleteCustomButtonConfMutation>;
export type DeleteApiCustomButtonConfDeleteCustomButtonConfMutationOptions =
  Apollo.BaseMutationOptions<
    DeleteApiCustomButtonConfDeleteCustomButtonConfMutation,
    DeleteApiCustomButtonConfDeleteCustomButtonConfMutationVariables
  >;
export const PostApiCustomButtonConfSaveCustomButtonConfDocument = gql`
  mutation postApiCustomButtonConfSaveCustomButtonConf(
    $input: EmrCloudApiRequestsCustomButtonConfSaveCustomButtonConfRequestInput!
  ) {
    postApiCustomButtonConfSaveCustomButtonConf(
      emrCloudApiRequestsCustomButtonConfSaveCustomButtonConfRequestInput: $input
    ) {
      data {
        id
        status
      }
      message
      status
    }
  }
`;
export type PostApiCustomButtonConfSaveCustomButtonConfMutationFn =
  Apollo.MutationFunction<
    PostApiCustomButtonConfSaveCustomButtonConfMutation,
    PostApiCustomButtonConfSaveCustomButtonConfMutationVariables
  >;

/**
 * __usePostApiCustomButtonConfSaveCustomButtonConfMutation__
 *
 * To run a mutation, you first call `usePostApiCustomButtonConfSaveCustomButtonConfMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiCustomButtonConfSaveCustomButtonConfMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiCustomButtonConfSaveCustomButtonConfMutation, { data, loading, error }] = usePostApiCustomButtonConfSaveCustomButtonConfMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiCustomButtonConfSaveCustomButtonConfMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiCustomButtonConfSaveCustomButtonConfMutation,
    PostApiCustomButtonConfSaveCustomButtonConfMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiCustomButtonConfSaveCustomButtonConfMutation,
    PostApiCustomButtonConfSaveCustomButtonConfMutationVariables
  >(PostApiCustomButtonConfSaveCustomButtonConfDocument, options);
}
export type PostApiCustomButtonConfSaveCustomButtonConfMutationHookResult =
  ReturnType<typeof usePostApiCustomButtonConfSaveCustomButtonConfMutation>;
export type PostApiCustomButtonConfSaveCustomButtonConfMutationResult =
  Apollo.MutationResult<PostApiCustomButtonConfSaveCustomButtonConfMutation>;
export type PostApiCustomButtonConfSaveCustomButtonConfMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiCustomButtonConfSaveCustomButtonConfMutation,
    PostApiCustomButtonConfSaveCustomButtonConfMutationVariables
  >;
