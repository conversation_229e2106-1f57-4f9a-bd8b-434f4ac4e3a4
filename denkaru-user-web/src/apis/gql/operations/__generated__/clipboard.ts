import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiNextOrderUpsertMutationVariables = Types.Exact<{
  emrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput>;
}>;

export type PostApiNextOrderUpsertMutation = {
  __typename?: "mutation_root";
  postApiNextOrderUpsert?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesNextOrderUpsertNextOrderListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesNextOrderUpsertNextOrderListResponse";
      validationOdrs?: Array<{
        __typename?: "EmrCloudApiResponsesNextOrderOrderInfItemResponse";
        validationOdrs?: Array<{
          __typename?: "EmrCloudApiResponsesMedicalExaminationValidationTodayOrdItemResponse";
          orderInfDetailPosition?: string;
          orderInfPosition?: string;
          validationField?: string;
          status?: number;
          validationMessage?: string;
        }>;
      }>;
      validationNextOrders?: Array<{
        __typename?: "EmrCloudApiResponsesNextOrderNextOrderItemResponse";
        validationNextOrders?: {
          __typename?: "EmrCloudApiResponsesNextOrderNextOrderValidationItemResponse";
          validationMessage?: string;
          status?: number;
        };
      }>;
    };
  };
};

export type GetApiNextOrderGetQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  rsvkrtNo: Types.Scalars["BigInt"]["input"];
  rsvkrtKbn: Types.Scalars["Int"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
}>;

export type GetApiNextOrderGetQuery = {
  __typename?: "query_root";
  getApiNextOrderGet?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesNextOrderGetNextOrderResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesNextOrderGetNextOrderResponse";
      karteInfModel?: {
        __typename?: "DomainModelsNextOrderRsvkrtKarteInfModel";
        richText?: string;
        text?: string;
      };
      groupHokenItems?: Array<{
        __typename?: "UseCaseNextOrderGetGroupHokenItem";
        groupOdrItems?: Array<{
          __typename?: "UseCaseNextOrderGetGroupOdrItem";
          odrInfs?: Array<{
            __typename?: "UseCaseNextOrderGetRsvKrtOrderInfItem";
            tosekiKbn?: number;
            sortNo?: number;
            syohoSbt?: number;
            sikyuKbn?: number;
            santeiKbn?: number;
            rpNo?: string;
            rpName?: string;
            rpEdaNo?: string;
            odrKouiKbn?: number;
            isDeleted?: number;
            inoutKbn?: number;
            createName?: string;
            id?: string;
            hokenPid?: number;
            daysCnt?: number;
            rsvKrtOrderInfDetailItems?: Array<{
              __typename?: "UseCaseNextOrderGetRsvKrtOrderInfDetailItem";
              buiKbn?: number;
              rousaiKbn?: number;
              bunkatu?: string;
              centerName?: string;
              cmtName?: string;
              cmtOpt?: string;
              drugKbn?: number;
              ipnCd?: string;
              ipnName?: string;
              isNodspRece?: number;
              itemCd?: string;
              itemName?: string;
              kohatuKbn?: number;
              kokuji1?: string;
              kokuji2?: string;
              masterSbt?: string;
              rowNo?: number;
              rpEdaNo?: string;
              rpNo?: string;
              sinKouiKbn?: number;
              suryo?: number;
              syohoKbn?: number;
              yohoKbn?: number;
              unitSbt?: number;
              unitName?: string;
              termVal?: number;
              syohoLimitKbn?: number;
              cmtCol1?: number;
              cmtCol2?: number;
              cmtCol3?: number;
              cmtCol4?: number;
              cmtColKeta1?: number;
              cmtColKeta2?: number;
              cmtColKeta3?: number;
              cmtColKeta4?: number;
              isSelectiveComment?: boolean;
            }>;
          }>;
        }>;
      }>;
    };
  };
};

export type GetApiNextOrderGetListQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  isDeleted?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiNextOrderGetListQuery = {
  __typename?: "query_root";
  getApiNextOrderGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesNextOrderGetNextOrderListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesNextOrderGetNextOrderListResponse";
      nextOrders?: Array<{
        __typename?: "UseCaseNextOrderNextOrderLabelItem";
        isCheckAgainNextTime?: boolean;
        rsvName?: string;
        rsvkrtKbn?: number;
        rsvkrtNo?: string;
        rsvDate?: number;
        sortNo?: number;
      }>;
    };
  };
};

export type GetApiNextOrderGetNextOrderNextCheckQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
}>;

export type GetApiNextOrderGetNextOrderNextCheckQuery = {
  __typename?: "query_root";
  getApiNextOrderGetNextOrderNextCheck?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesNextOrderGetNextOrderNextCheckResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesNextOrderGetNextOrderNextCheckResponse";
      nextOrder?: {
        __typename?: "UseCaseNextOrderNextOrderLabelItem";
        isCheckAgainNextTime?: boolean;
        rsvName?: string;
        rsvkrtKbn?: number;
        rsvkrtNo?: string;
        rsvDate?: number;
      };
    };
  };
};

export type PostApiNextOrderUpsertSortNoDataDropMutationVariables =
  Types.Exact<{
    rsvkrtNoFrom: Types.Scalars["BigInt"]["input"];
    rsvkrtNoTo: Types.Scalars["BigInt"]["input"];
    ptId: Types.Scalars["BigInt"]["input"];
  }>;

export type PostApiNextOrderUpsertSortNoDataDropMutation = {
  __typename?: "mutation_root";
  postApiNextOrderUpsertSortNoDataDrop?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationUpsertSortNoDataDropResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationUpsertSortNoDataDropResponse";
      nextOrders?: Array<{
        __typename?: "DomainModelsNextOrderNextOrderModel";
        hpId?: number;
        isCheckAgainNextTime?: boolean;
      }>;
    };
  };
};

export const PostApiNextOrderUpsertDocument = gql`
  mutation postApiNextOrderUpsert(
    $emrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput: EmrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput
  ) {
    postApiNextOrderUpsert(
      emrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput: $emrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput
    ) {
      message
      status
      data {
        validationOdrs {
          validationOdrs {
            orderInfDetailPosition
            orderInfPosition
            validationField
            status
            validationMessage
          }
        }
        validationNextOrders {
          validationNextOrders {
            validationMessage
            status
          }
        }
      }
    }
  }
`;
export type PostApiNextOrderUpsertMutationFn = Apollo.MutationFunction<
  PostApiNextOrderUpsertMutation,
  PostApiNextOrderUpsertMutationVariables
>;

/**
 * __usePostApiNextOrderUpsertMutation__
 *
 * To run a mutation, you first call `usePostApiNextOrderUpsertMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiNextOrderUpsertMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiNextOrderUpsertMutation, { data, loading, error }] = usePostApiNextOrderUpsertMutation({
 *   variables: {
 *      emrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput: // value for 'emrCloudApiRequestsNextOrderUpsertNextOrderListRequestInput'
 *   },
 * });
 */
export function usePostApiNextOrderUpsertMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiNextOrderUpsertMutation,
    PostApiNextOrderUpsertMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiNextOrderUpsertMutation,
    PostApiNextOrderUpsertMutationVariables
  >(PostApiNextOrderUpsertDocument, options);
}
export type PostApiNextOrderUpsertMutationHookResult = ReturnType<
  typeof usePostApiNextOrderUpsertMutation
>;
export type PostApiNextOrderUpsertMutationResult =
  Apollo.MutationResult<PostApiNextOrderUpsertMutation>;
export type PostApiNextOrderUpsertMutationOptions = Apollo.BaseMutationOptions<
  PostApiNextOrderUpsertMutation,
  PostApiNextOrderUpsertMutationVariables
>;
export const GetApiNextOrderGetDocument = gql`
  query getApiNextOrderGet(
    $ptId: BigInt!
    $rsvkrtNo: BigInt!
    $rsvkrtKbn: Int!
    $sinDate: Int!
  ) {
    getApiNextOrderGet(
      ptId: $ptId
      rsvkrtNo: $rsvkrtNo
      rsvkrtKbn: $rsvkrtKbn
      sinDate: $sinDate
    ) {
      data {
        karteInfModel {
          richText
          text
        }
        groupHokenItems {
          groupOdrItems {
            odrInfs {
              tosekiKbn
              sortNo
              syohoSbt
              sikyuKbn
              santeiKbn
              rpNo
              rpName
              rpEdaNo
              odrKouiKbn
              isDeleted
              inoutKbn
              createName
              id
              hokenPid
              daysCnt
              rsvKrtOrderInfDetailItems {
                buiKbn
                rousaiKbn
                bunkatu
                centerName
                cmtName
                cmtOpt
                drugKbn
                ipnCd
                ipnName
                isNodspRece
                itemCd
                itemName
                kohatuKbn
                kokuji1
                kokuji2
                masterSbt
                rowNo
                rpEdaNo
                rpNo
                sinKouiKbn
                suryo
                syohoKbn
                yohoKbn
                unitSbt
                unitName
                termVal
                syohoLimitKbn
                cmtCol1
                cmtCol2
                cmtCol3
                cmtCol4
                cmtColKeta1
                cmtColKeta2
                cmtColKeta3
                cmtColKeta4
                isSelectiveComment
              }
            }
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiNextOrderGetQuery__
 *
 * To run a query within a React component, call `useGetApiNextOrderGetQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiNextOrderGetQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiNextOrderGetQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      rsvkrtNo: // value for 'rsvkrtNo'
 *      rsvkrtKbn: // value for 'rsvkrtKbn'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiNextOrderGetQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiNextOrderGetQuery,
    GetApiNextOrderGetQueryVariables
  > &
    (
      | { variables: GetApiNextOrderGetQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiNextOrderGetQuery,
    GetApiNextOrderGetQueryVariables
  >(GetApiNextOrderGetDocument, options);
}
export function useGetApiNextOrderGetLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiNextOrderGetQuery,
    GetApiNextOrderGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiNextOrderGetQuery,
    GetApiNextOrderGetQueryVariables
  >(GetApiNextOrderGetDocument, options);
}
export function useGetApiNextOrderGetSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiNextOrderGetQuery,
    GetApiNextOrderGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiNextOrderGetQuery,
    GetApiNextOrderGetQueryVariables
  >(GetApiNextOrderGetDocument, options);
}
export type GetApiNextOrderGetQueryHookResult = ReturnType<
  typeof useGetApiNextOrderGetQuery
>;
export type GetApiNextOrderGetLazyQueryHookResult = ReturnType<
  typeof useGetApiNextOrderGetLazyQuery
>;
export type GetApiNextOrderGetSuspenseQueryHookResult = ReturnType<
  typeof useGetApiNextOrderGetSuspenseQuery
>;
export type GetApiNextOrderGetQueryResult = Apollo.QueryResult<
  GetApiNextOrderGetQuery,
  GetApiNextOrderGetQueryVariables
>;
export const GetApiNextOrderGetListDocument = gql`
  query getApiNextOrderGetList($ptId: BigInt!, $isDeleted: Boolean) {
    getApiNextOrderGetList(ptId: $ptId, isDeleted: $isDeleted) {
      data {
        nextOrders {
          isCheckAgainNextTime
          rsvName
          rsvkrtKbn
          rsvkrtNo
          rsvDate
          sortNo
        }
      }
    }
  }
`;

/**
 * __useGetApiNextOrderGetListQuery__
 *
 * To run a query within a React component, call `useGetApiNextOrderGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiNextOrderGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiNextOrderGetListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      isDeleted: // value for 'isDeleted'
 *   },
 * });
 */
export function useGetApiNextOrderGetListQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiNextOrderGetListQuery,
    GetApiNextOrderGetListQueryVariables
  > &
    (
      | { variables: GetApiNextOrderGetListQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiNextOrderGetListQuery,
    GetApiNextOrderGetListQueryVariables
  >(GetApiNextOrderGetListDocument, options);
}
export function useGetApiNextOrderGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiNextOrderGetListQuery,
    GetApiNextOrderGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiNextOrderGetListQuery,
    GetApiNextOrderGetListQueryVariables
  >(GetApiNextOrderGetListDocument, options);
}
export function useGetApiNextOrderGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiNextOrderGetListQuery,
    GetApiNextOrderGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiNextOrderGetListQuery,
    GetApiNextOrderGetListQueryVariables
  >(GetApiNextOrderGetListDocument, options);
}
export type GetApiNextOrderGetListQueryHookResult = ReturnType<
  typeof useGetApiNextOrderGetListQuery
>;
export type GetApiNextOrderGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiNextOrderGetListLazyQuery
>;
export type GetApiNextOrderGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiNextOrderGetListSuspenseQuery
>;
export type GetApiNextOrderGetListQueryResult = Apollo.QueryResult<
  GetApiNextOrderGetListQuery,
  GetApiNextOrderGetListQueryVariables
>;
export const GetApiNextOrderGetNextOrderNextCheckDocument = gql`
  query getApiNextOrderGetNextOrderNextCheck($ptId: BigInt!) {
    getApiNextOrderGetNextOrderNextCheck(ptId: $ptId) {
      data {
        nextOrder {
          isCheckAgainNextTime
          rsvName
          rsvkrtKbn
          rsvkrtNo
          rsvDate
        }
      }
    }
  }
`;

/**
 * __useGetApiNextOrderGetNextOrderNextCheckQuery__
 *
 * To run a query within a React component, call `useGetApiNextOrderGetNextOrderNextCheckQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiNextOrderGetNextOrderNextCheckQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiNextOrderGetNextOrderNextCheckQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiNextOrderGetNextOrderNextCheckQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiNextOrderGetNextOrderNextCheckQuery,
    GetApiNextOrderGetNextOrderNextCheckQueryVariables
  > &
    (
      | {
          variables: GetApiNextOrderGetNextOrderNextCheckQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiNextOrderGetNextOrderNextCheckQuery,
    GetApiNextOrderGetNextOrderNextCheckQueryVariables
  >(GetApiNextOrderGetNextOrderNextCheckDocument, options);
}
export function useGetApiNextOrderGetNextOrderNextCheckLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiNextOrderGetNextOrderNextCheckQuery,
    GetApiNextOrderGetNextOrderNextCheckQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiNextOrderGetNextOrderNextCheckQuery,
    GetApiNextOrderGetNextOrderNextCheckQueryVariables
  >(GetApiNextOrderGetNextOrderNextCheckDocument, options);
}
export function useGetApiNextOrderGetNextOrderNextCheckSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiNextOrderGetNextOrderNextCheckQuery,
    GetApiNextOrderGetNextOrderNextCheckQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiNextOrderGetNextOrderNextCheckQuery,
    GetApiNextOrderGetNextOrderNextCheckQueryVariables
  >(GetApiNextOrderGetNextOrderNextCheckDocument, options);
}
export type GetApiNextOrderGetNextOrderNextCheckQueryHookResult = ReturnType<
  typeof useGetApiNextOrderGetNextOrderNextCheckQuery
>;
export type GetApiNextOrderGetNextOrderNextCheckLazyQueryHookResult =
  ReturnType<typeof useGetApiNextOrderGetNextOrderNextCheckLazyQuery>;
export type GetApiNextOrderGetNextOrderNextCheckSuspenseQueryHookResult =
  ReturnType<typeof useGetApiNextOrderGetNextOrderNextCheckSuspenseQuery>;
export type GetApiNextOrderGetNextOrderNextCheckQueryResult =
  Apollo.QueryResult<
    GetApiNextOrderGetNextOrderNextCheckQuery,
    GetApiNextOrderGetNextOrderNextCheckQueryVariables
  >;
export const PostApiNextOrderUpsertSortNoDataDropDocument = gql`
  mutation postApiNextOrderUpsertSortNoDataDrop(
    $rsvkrtNoFrom: BigInt!
    $rsvkrtNoTo: BigInt!
    $ptId: BigInt!
  ) {
    postApiNextOrderUpsertSortNoDataDrop(
      emrCloudApiRequestsMedicalExaminationUpsertSortNoDataDropRequestInput: {
        rsvkrtNoTo: $rsvkrtNoTo
        ptId: $ptId
        rsvkrtNoFrom: $rsvkrtNoFrom
      }
    ) {
      message
      status
      data {
        nextOrders {
          hpId
          isCheckAgainNextTime
        }
      }
    }
  }
`;
export type PostApiNextOrderUpsertSortNoDataDropMutationFn =
  Apollo.MutationFunction<
    PostApiNextOrderUpsertSortNoDataDropMutation,
    PostApiNextOrderUpsertSortNoDataDropMutationVariables
  >;

/**
 * __usePostApiNextOrderUpsertSortNoDataDropMutation__
 *
 * To run a mutation, you first call `usePostApiNextOrderUpsertSortNoDataDropMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiNextOrderUpsertSortNoDataDropMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiNextOrderUpsertSortNoDataDropMutation, { data, loading, error }] = usePostApiNextOrderUpsertSortNoDataDropMutation({
 *   variables: {
 *      rsvkrtNoFrom: // value for 'rsvkrtNoFrom'
 *      rsvkrtNoTo: // value for 'rsvkrtNoTo'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function usePostApiNextOrderUpsertSortNoDataDropMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiNextOrderUpsertSortNoDataDropMutation,
    PostApiNextOrderUpsertSortNoDataDropMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiNextOrderUpsertSortNoDataDropMutation,
    PostApiNextOrderUpsertSortNoDataDropMutationVariables
  >(PostApiNextOrderUpsertSortNoDataDropDocument, options);
}
export type PostApiNextOrderUpsertSortNoDataDropMutationHookResult = ReturnType<
  typeof usePostApiNextOrderUpsertSortNoDataDropMutation
>;
export type PostApiNextOrderUpsertSortNoDataDropMutationResult =
  Apollo.MutationResult<PostApiNextOrderUpsertSortNoDataDropMutation>;
export type PostApiNextOrderUpsertSortNoDataDropMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiNextOrderUpsertSortNoDataDropMutation,
    PostApiNextOrderUpsertSortNoDataDropMutationVariables
  >;
