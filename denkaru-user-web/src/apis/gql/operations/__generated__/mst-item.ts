import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiMstItemSearchTenMstItemMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsMstItemSearchTenMstItemRequestInput;
}>;

export type PostApiMstItemSearchTenMstItemMutation = {
  __typename?: "mutation_root";
  postApiMstItemSearchTenMstItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
      tenMsts?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemTenItemDto";
        hpId?: number;
        itemCd?: string;
        rousaiKbn?: number;
        kanaName1?: string;
        kanaName2?: string;
        kanaName3?: string;
        kanaName4?: string;
        kanaName5?: string;
        kanaName6?: string;
        kanaName7?: string;
        name?: string;
        receName?: string;
        kohatuKbn?: number;
        madokuKbn?: number;
        kouseisinKbn?: number;
        odrUnitName?: string;
        endDate?: number;
        drugKbn?: number;
        masterSbt?: string;
        buiKbn?: number;
        isAdopted?: number;
        ten?: number;
        tenId?: number;
        kensaMstCenterItemCd1?: string;
        kensaMstCenterItemCd2?: string;
        cmtCol1?: number;
        ipnNameCd?: string;
        sinKouiKbn?: number;
        yjCd?: string;
        cnvUnitName?: string;
        startDate?: number;
        yohoKbn?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        ipnCD?: string;
        rousaiKbnDisplay?: string;
        kouseisinKbnDisplay?: string;
        kubunToDisplay?: string;
        kohatuKbnDisplay?: string;
        kensaCenterItemCDDisplay?: string;
        tenDisplay?: string;
        kouiName?: string;
        odrTermVal?: number;
        cnvTermVal?: number;
        defaultValue?: number;
        modeStatus?: number;
        ipnName?: string;
        handanGrpKbn?: number;
        isKensaMstEmpty?: boolean;
        yakka?: number;
        isGetPriceInYakka?: boolean;
        kasan1?: number;
        kasan2?: number;
        kokuji1?: string;
        kokuji2?: string;
      }>;
    };
  };
};

export type GetApiMstItemDiseaseSearchQueryVariables = Types.Exact<{
  isPrefix?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isByomei?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isSuffix?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isMisaiyou?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  keyword?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  pageIndex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  pageSize?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isHasFreeByomei?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiMstItemDiseaseSearchQuery = {
  __typename?: "query_root";
  getApiMstItemDiseaseSearch?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemDiseaseSearchDiseaseSearchResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemDiseaseSearchDiseaseSearchResponse";
      data?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemDiseaseSearchDiseaseSearchModel";
        byomeiCd?: string;
        byomeiType?: string;
        sbyomei?: string;
        kanaName1?: string;
        sikkanCd?: number;
        sikkan?: string;
        nanByo?: string;
        icd10?: string;
        icd102013?: string;
        isAdopted?: boolean;
      }>;
    };
  };
};

export type GetApiMstItemSearchPostCodeQueryVariables = Types.Exact<{
  pageIndex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  pageSize?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  postCode1?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  address?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  postCode2?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiMstItemSearchPostCodeQuery = {
  __typename?: "query_root";
  getApiMstItemSearchPostCode?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSearchPostCodeRespone";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSearchPostCodeRespone";
      totalCount?: number;
      postCodeMstModels?: Array<{
        __typename?: "DomainModelsMstItemPostCodeMstModel";
        address?: string;
        banti?: string;
        postCd?: string;
        prefName?: string;
        cityName?: string;
        id?: string;
      }>;
    };
  };
};

export type PostApiMstItemConvertStringChkJisKjMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsMstItemConvertStringChkJisKjRequestInput>;
}>;

export type PostApiMstItemConvertStringChkJisKjMutation = {
  __typename?: "mutation_root";
  postApiMstItemConvertStringChkJISKj?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemConvertStringChkJisKjResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemConvertStringChkJISKjResponse";
      result?: string;
      itemError?: string;
    };
  };
};

export type PostApiMstItemGetListMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsMstItemGetTenMstListRequestInput>;
}>;

export type PostApiMstItemGetListMutation = {
  __typename?: "mutation_root";
  postApiMstItemGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetTenMstListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetTenMstListResponse";
      tenMstList?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemTenItemDto";
        itemCd?: string;
        name?: string;
      }>;
    };
  };
};

export const PostApiMstItemSearchTenMstItemDocument = gql`
  mutation postApiMstItemSearchTenMstItem(
    $input: EmrCloudApiRequestsMstItemSearchTenMstItemRequestInput!
  ) {
    postApiMstItemSearchTenMstItem(
      emrCloudApiRequestsMstItemSearchTenMstItemRequestInput: $input
    ) {
      data {
        tenMsts {
          hpId
          itemCd
          rousaiKbn
          kanaName1
          kanaName2
          kanaName3
          kanaName4
          kanaName5
          kanaName6
          kanaName7
          name
          receName
          kohatuKbn
          madokuKbn
          kouseisinKbn
          odrUnitName
          endDate
          drugKbn
          masterSbt
          buiKbn
          isAdopted
          ten
          tenId
          kensaMstCenterItemCd1
          kensaMstCenterItemCd2
          cmtCol1
          ipnNameCd
          sinKouiKbn
          yjCd
          cnvUnitName
          startDate
          yohoKbn
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
          cmtCol2
          cmtCol3
          cmtCol4
          ipnCD
          rousaiKbnDisplay
          kouseisinKbnDisplay
          kubunToDisplay
          kohatuKbnDisplay
          kensaCenterItemCDDisplay
          tenDisplay
          kouiName
          odrTermVal
          cnvTermVal
          defaultValue
          modeStatus
          ipnName
          handanGrpKbn
          isKensaMstEmpty
          yakka
          isGetPriceInYakka
          kasan1
          kasan2
          kokuji1
          kokuji2
        }
      }
    }
  }
`;
export type PostApiMstItemSearchTenMstItemMutationFn = Apollo.MutationFunction<
  PostApiMstItemSearchTenMstItemMutation,
  PostApiMstItemSearchTenMstItemMutationVariables
>;

/**
 * __usePostApiMstItemSearchTenMstItemMutation__
 *
 * To run a mutation, you first call `usePostApiMstItemSearchTenMstItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMstItemSearchTenMstItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMstItemSearchTenMstItemMutation, { data, loading, error }] = usePostApiMstItemSearchTenMstItemMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiMstItemSearchTenMstItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMstItemSearchTenMstItemMutation,
    PostApiMstItemSearchTenMstItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMstItemSearchTenMstItemMutation,
    PostApiMstItemSearchTenMstItemMutationVariables
  >(PostApiMstItemSearchTenMstItemDocument, options);
}
export type PostApiMstItemSearchTenMstItemMutationHookResult = ReturnType<
  typeof usePostApiMstItemSearchTenMstItemMutation
>;
export type PostApiMstItemSearchTenMstItemMutationResult =
  Apollo.MutationResult<PostApiMstItemSearchTenMstItemMutation>;
export type PostApiMstItemSearchTenMstItemMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMstItemSearchTenMstItemMutation,
    PostApiMstItemSearchTenMstItemMutationVariables
  >;
export const GetApiMstItemDiseaseSearchDocument = gql`
  query getApiMstItemDiseaseSearch(
    $isPrefix: Boolean
    $isByomei: Boolean
    $isSuffix: Boolean
    $isMisaiyou: Boolean
    $keyword: String
    $sinDate: Int
    $pageIndex: Int
    $pageSize: Int
    $isHasFreeByomei: Boolean
  ) {
    getApiMstItemDiseaseSearch(
      isByomei: $isByomei
      isPrefix: $isPrefix
      isSuffix: $isSuffix
      isMisaiyou: $isMisaiyou
      keyword: $keyword
      sindate: $sinDate
      pageIndex: $pageIndex
      pageSize: $pageSize
      isHasFreeByomei: $isHasFreeByomei
    ) {
      data {
        data {
          byomeiCd
          byomeiType
          sbyomei
          kanaName1
          sikkanCd
          sikkan
          nanByo
          icd10
          icd102013
          isAdopted
        }
      }
    }
  }
`;

/**
 * __useGetApiMstItemDiseaseSearchQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemDiseaseSearchQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemDiseaseSearchQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemDiseaseSearchQuery({
 *   variables: {
 *      isPrefix: // value for 'isPrefix'
 *      isByomei: // value for 'isByomei'
 *      isSuffix: // value for 'isSuffix'
 *      isMisaiyou: // value for 'isMisaiyou'
 *      keyword: // value for 'keyword'
 *      sinDate: // value for 'sinDate'
 *      pageIndex: // value for 'pageIndex'
 *      pageSize: // value for 'pageSize'
 *      isHasFreeByomei: // value for 'isHasFreeByomei'
 *   },
 * });
 */
export function useGetApiMstItemDiseaseSearchQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemDiseaseSearchQuery,
    GetApiMstItemDiseaseSearchQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemDiseaseSearchQuery,
    GetApiMstItemDiseaseSearchQueryVariables
  >(GetApiMstItemDiseaseSearchDocument, options);
}
export function useGetApiMstItemDiseaseSearchLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemDiseaseSearchQuery,
    GetApiMstItemDiseaseSearchQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemDiseaseSearchQuery,
    GetApiMstItemDiseaseSearchQueryVariables
  >(GetApiMstItemDiseaseSearchDocument, options);
}
export function useGetApiMstItemDiseaseSearchSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemDiseaseSearchQuery,
    GetApiMstItemDiseaseSearchQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemDiseaseSearchQuery,
    GetApiMstItemDiseaseSearchQueryVariables
  >(GetApiMstItemDiseaseSearchDocument, options);
}
export type GetApiMstItemDiseaseSearchQueryHookResult = ReturnType<
  typeof useGetApiMstItemDiseaseSearchQuery
>;
export type GetApiMstItemDiseaseSearchLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemDiseaseSearchLazyQuery
>;
export type GetApiMstItemDiseaseSearchSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemDiseaseSearchSuspenseQuery
>;
export type GetApiMstItemDiseaseSearchQueryResult = Apollo.QueryResult<
  GetApiMstItemDiseaseSearchQuery,
  GetApiMstItemDiseaseSearchQueryVariables
>;
export const GetApiMstItemSearchPostCodeDocument = gql`
  query getApiMstItemSearchPostCode(
    $pageIndex: Int
    $pageSize: Int
    $postCode1: String
    $address: String
    $postCode2: String
  ) {
    getApiMstItemSearchPostCode(
      pageIndex: $pageIndex
      pageSize: $pageSize
      postCode1: $postCode1
      address: $address
      postCode2: $postCode2
    ) {
      data {
        totalCount
        postCodeMstModels {
          address
          banti
          postCd
          prefName
          cityName
          id
        }
      }
    }
  }
`;

/**
 * __useGetApiMstItemSearchPostCodeQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemSearchPostCodeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemSearchPostCodeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemSearchPostCodeQuery({
 *   variables: {
 *      pageIndex: // value for 'pageIndex'
 *      pageSize: // value for 'pageSize'
 *      postCode1: // value for 'postCode1'
 *      address: // value for 'address'
 *      postCode2: // value for 'postCode2'
 *   },
 * });
 */
export function useGetApiMstItemSearchPostCodeQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemSearchPostCodeQuery,
    GetApiMstItemSearchPostCodeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemSearchPostCodeQuery,
    GetApiMstItemSearchPostCodeQueryVariables
  >(GetApiMstItemSearchPostCodeDocument, options);
}
export function useGetApiMstItemSearchPostCodeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemSearchPostCodeQuery,
    GetApiMstItemSearchPostCodeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemSearchPostCodeQuery,
    GetApiMstItemSearchPostCodeQueryVariables
  >(GetApiMstItemSearchPostCodeDocument, options);
}
export function useGetApiMstItemSearchPostCodeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemSearchPostCodeQuery,
    GetApiMstItemSearchPostCodeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemSearchPostCodeQuery,
    GetApiMstItemSearchPostCodeQueryVariables
  >(GetApiMstItemSearchPostCodeDocument, options);
}
export type GetApiMstItemSearchPostCodeQueryHookResult = ReturnType<
  typeof useGetApiMstItemSearchPostCodeQuery
>;
export type GetApiMstItemSearchPostCodeLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemSearchPostCodeLazyQuery
>;
export type GetApiMstItemSearchPostCodeSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemSearchPostCodeSuspenseQuery
>;
export type GetApiMstItemSearchPostCodeQueryResult = Apollo.QueryResult<
  GetApiMstItemSearchPostCodeQuery,
  GetApiMstItemSearchPostCodeQueryVariables
>;
export const PostApiMstItemConvertStringChkJisKjDocument = gql`
  mutation postApiMstItemConvertStringChkJISKj(
    $input: EmrCloudApiRequestsMstItemConvertStringChkJisKjRequestInput
  ) {
    postApiMstItemConvertStringChkJISKj(
      emrCloudApiRequestsMstItemConvertStringChkJisKjRequestInput: $input
    ) {
      data {
        result
        itemError
      }
    }
  }
`;
export type PostApiMstItemConvertStringChkJisKjMutationFn =
  Apollo.MutationFunction<
    PostApiMstItemConvertStringChkJisKjMutation,
    PostApiMstItemConvertStringChkJisKjMutationVariables
  >;

/**
 * __usePostApiMstItemConvertStringChkJisKjMutation__
 *
 * To run a mutation, you first call `usePostApiMstItemConvertStringChkJisKjMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMstItemConvertStringChkJisKjMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMstItemConvertStringChkJisKjMutation, { data, loading, error }] = usePostApiMstItemConvertStringChkJisKjMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiMstItemConvertStringChkJisKjMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMstItemConvertStringChkJisKjMutation,
    PostApiMstItemConvertStringChkJisKjMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMstItemConvertStringChkJisKjMutation,
    PostApiMstItemConvertStringChkJisKjMutationVariables
  >(PostApiMstItemConvertStringChkJisKjDocument, options);
}
export type PostApiMstItemConvertStringChkJisKjMutationHookResult = ReturnType<
  typeof usePostApiMstItemConvertStringChkJisKjMutation
>;
export type PostApiMstItemConvertStringChkJisKjMutationResult =
  Apollo.MutationResult<PostApiMstItemConvertStringChkJisKjMutation>;
export type PostApiMstItemConvertStringChkJisKjMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMstItemConvertStringChkJisKjMutation,
    PostApiMstItemConvertStringChkJisKjMutationVariables
  >;
export const PostApiMstItemGetListDocument = gql`
  mutation postApiMstItemGetList(
    $input: EmrCloudApiRequestsMstItemGetTenMstListRequestInput
  ) {
    postApiMstItemGetList(
      emrCloudApiRequestsMstItemGetTenMstListRequestInput: $input
    ) {
      data {
        tenMstList {
          itemCd
          name
        }
      }
      message
      status
    }
  }
`;
export type PostApiMstItemGetListMutationFn = Apollo.MutationFunction<
  PostApiMstItemGetListMutation,
  PostApiMstItemGetListMutationVariables
>;

/**
 * __usePostApiMstItemGetListMutation__
 *
 * To run a mutation, you first call `usePostApiMstItemGetListMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMstItemGetListMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMstItemGetListMutation, { data, loading, error }] = usePostApiMstItemGetListMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiMstItemGetListMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMstItemGetListMutation,
    PostApiMstItemGetListMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMstItemGetListMutation,
    PostApiMstItemGetListMutationVariables
  >(PostApiMstItemGetListDocument, options);
}
export type PostApiMstItemGetListMutationHookResult = ReturnType<
  typeof usePostApiMstItemGetListMutation
>;
export type PostApiMstItemGetListMutationResult =
  Apollo.MutationResult<PostApiMstItemGetListMutation>;
export type PostApiMstItemGetListMutationOptions = Apollo.BaseMutationOptions<
  PostApiMstItemGetListMutation,
  PostApiMstItemGetListMutationVariables
>;
