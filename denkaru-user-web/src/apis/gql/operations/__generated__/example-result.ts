import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiKensaHistoryGetListKensaInfDetailQueryVariables =
  Types.Exact<{
    endDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    getGetPrevious?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    iraiCd?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    iraiCdStart?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    itemQuantity?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    listSeqNoItems?: Types.InputMaybe<
      | Array<Types.InputMaybe<Types.Scalars["BigInt"]["input"]>>
      | Types.InputMaybe<Types.Scalars["BigInt"]["input"]>
    >;
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    setId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    showAbnormalKbn?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    startDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiKensaHistoryGetListKensaInfDetailQuery = {
  __typename?: "query_root";
  getApiKensaHistoryGetListKensaInfDetail?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaHistoryGetListKensaInfDetailResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKensaHistoryGetListKensaInfDetailResponse";
      data?: {
        __typename?: "DomainModelsKensaIraiListKensaInfDetailModel";
        totalCol?: number;
        kensaInfDetailCol?: Array<{
          __typename?: "DomainModelsKensaIraiListKensaInfDetailModelKensaInfDetailColModel";
          bilirubin?: string;
          index?: number;
          iraiCd?: string;
          iraiDate?: string;
          nyubi?: string;
          seqGroupNo?: string;
          sikyuKbn?: number;
          tosekiKbn?: number;
          yoketu?: string;
        }>;
        kensaInfDetailData?: Array<{
          __typename?: "DomainModelsKensaIraiListKensaInfDetailModelKensaInfDetailDataModel";
          femaleStd?: string;
          iraiDate?: string;
          kensaItemCd?: string;
          kensaKana?: string;
          kensaName?: string;
          maleStd?: string;
          rowSeqId?: string;
          seqNo?: string;
          seqParentNo?: string;
          sortNo?: string;
          unit?: string;
          dynamicArray?: Array<{
            __typename?: "DomainModelsKensaIraiListKensaInfDetailItemModel";
            abnormalKbn?: string;
            bilirubin?: string;
            cmt1?: string;
            cmt2?: string;
            cmtCd1?: string;
            cmtCd2?: string;
            femaleStd?: string;
            femaleStdHigh?: string;
            femaleStdLow?: string;
            inoutKbn?: number;
            iraiCd?: string;
            iraiDate?: string;
            isDeleted?: number;
            kensaItemCd?: string;
            kensaKana?: string;
            kensaName?: string;
            maleStd?: string;
            maleStdHigh?: string;
            maleStdLow?: string;
            nyubi?: string;
            ptId?: string;
            raiinNo?: string;
            resultType?: string;
            resultVal?: string;
            rowSeqId?: string;
            seqGroupNo?: string;
            seqNo?: string;
            seqParentNo?: string;
            sikyuKbn?: number;
            sortNo?: string;
            status?: number;
            tosekiKbn?: number;
            unit?: string;
            yoketu?: string;
          }>;
        }>;
      };
    };
  };
};

export type GetApiUserConfGetListForModelQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiUserConfGetListForModelQuery = {
  __typename?: "query_root";
  getApiUserConfGetListForModel?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesUserConfGetUserConfModelListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesUserConfGetUserConfModelListResponse";
      userConfs?: Array<{
        __typename?: "DomainModelsUserConfUserConfModel";
        grpCd?: number;
        grpItemCd?: number;
        grpItemEdaNo?: number;
        param?: string;
        userId?: number;
        val?: number;
      }>;
    };
  };
};

export type GetApiKensaMstGetInHospitalKensaMstQueryVariables = Types.Exact<{
  isExceptVital: Types.Scalars["Boolean"]["input"];
}>;

export type GetApiKensaMstGetInHospitalKensaMstQuery = {
  __typename?: "query_root";
  getApiKensaMstGetInHospitalKensaMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaMstKensaMstListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKensaMstKensaMstListResponse";
      listData?: Array<{
        __typename?: "DomainModelsKensaMstKensaMstModel";
        femaleStd?: string;
        maleStd?: string;
        kensaName?: string;
        unit?: string;
        kensaItemCd?: string;
      }>;
    };
  };
};

export type PostApiPdfCreatorKensaHistoryReportMutationVariables = Types.Exact<{
  emrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput: Types.EmrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput;
}>;

export type PostApiPdfCreatorKensaHistoryReportMutation = {
  __typename?: "mutation_root";
  postApiPdfCreatorKensaHistoryReport?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPdfCreatorReportBase64Response";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPdfCreatorReportBase64Response";
      content?: string;
      fileName?: string;
    };
  };
};

export type PostApiExamResultSaveMutationVariables = Types.Exact<{
  isAdd?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isDeleted?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kensaTime?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  examResults?: Types.InputMaybe<
    | Array<
        Types.InputMaybe<Types.EmrCloudApiRequestsExamResultSaveExamResultRequestInput>
      >
    | Types.InputMaybe<Types.EmrCloudApiRequestsExamResultSaveExamResultRequestInput>
  >;
  iraiCd?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  iraiDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type PostApiExamResultSaveMutation = {
  __typename?: "mutation_root";
  postApiExamResultSave?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteVsphySSaveKarteVsphysResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteVsphySSaveKarteVsphysResponse";
      success?: boolean;
    };
  };
};

export const GetApiKensaHistoryGetListKensaInfDetailDocument = gql`
  query getApiKensaHistoryGetListKensaInfDetail(
    $endDate: Int
    $getGetPrevious: Boolean
    $iraiCd: Int
    $iraiCdStart: Int
    $itemQuantity: Int
    $listSeqNoItems: [BigInt]
    $ptId: BigInt
    $setId: Int
    $showAbnormalKbn: Boolean
    $startDate: Int
  ) {
    getApiKensaHistoryGetListKensaInfDetail(
      endDate: $endDate
      getGetPrevious: $getGetPrevious
      iraiCd: $iraiCd
      iraiCdStart: $iraiCdStart
      itemQuantity: $itemQuantity
      listSeqNoItems: $listSeqNoItems
      ptId: $ptId
      setId: $setId
      showAbnormalKbn: $showAbnormalKbn
      startDate: $startDate
    ) {
      data {
        data {
          kensaInfDetailCol {
            bilirubin
            index
            iraiCd
            iraiDate
            nyubi
            seqGroupNo
            sikyuKbn
            tosekiKbn
            yoketu
          }
          kensaInfDetailData {
            dynamicArray {
              abnormalKbn
              bilirubin
              cmt1
              cmt2
              cmtCd1
              cmtCd2
              femaleStd
              femaleStdHigh
              femaleStdLow
              inoutKbn
              iraiCd
              iraiDate
              isDeleted
              kensaItemCd
              kensaKana
              kensaName
              maleStd
              maleStdHigh
              maleStdLow
              nyubi
              ptId
              raiinNo
              resultType
              resultVal
              rowSeqId
              seqGroupNo
              seqNo
              seqParentNo
              sikyuKbn
              sortNo
              status
              tosekiKbn
              unit
              yoketu
            }
            femaleStd
            iraiDate
            kensaItemCd
            kensaKana
            kensaName
            maleStd
            rowSeqId
            seqNo
            seqParentNo
            sortNo
            unit
          }
          totalCol
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiKensaHistoryGetListKensaInfDetailQuery__
 *
 * To run a query within a React component, call `useGetApiKensaHistoryGetListKensaInfDetailQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKensaHistoryGetListKensaInfDetailQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKensaHistoryGetListKensaInfDetailQuery({
 *   variables: {
 *      endDate: // value for 'endDate'
 *      getGetPrevious: // value for 'getGetPrevious'
 *      iraiCd: // value for 'iraiCd'
 *      iraiCdStart: // value for 'iraiCdStart'
 *      itemQuantity: // value for 'itemQuantity'
 *      listSeqNoItems: // value for 'listSeqNoItems'
 *      ptId: // value for 'ptId'
 *      setId: // value for 'setId'
 *      showAbnormalKbn: // value for 'showAbnormalKbn'
 *      startDate: // value for 'startDate'
 *   },
 * });
 */
export function useGetApiKensaHistoryGetListKensaInfDetailQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiKensaHistoryGetListKensaInfDetailQuery,
    GetApiKensaHistoryGetListKensaInfDetailQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKensaHistoryGetListKensaInfDetailQuery,
    GetApiKensaHistoryGetListKensaInfDetailQueryVariables
  >(GetApiKensaHistoryGetListKensaInfDetailDocument, options);
}
export function useGetApiKensaHistoryGetListKensaInfDetailLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKensaHistoryGetListKensaInfDetailQuery,
    GetApiKensaHistoryGetListKensaInfDetailQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKensaHistoryGetListKensaInfDetailQuery,
    GetApiKensaHistoryGetListKensaInfDetailQueryVariables
  >(GetApiKensaHistoryGetListKensaInfDetailDocument, options);
}
export function useGetApiKensaHistoryGetListKensaInfDetailSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKensaHistoryGetListKensaInfDetailQuery,
    GetApiKensaHistoryGetListKensaInfDetailQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKensaHistoryGetListKensaInfDetailQuery,
    GetApiKensaHistoryGetListKensaInfDetailQueryVariables
  >(GetApiKensaHistoryGetListKensaInfDetailDocument, options);
}
export type GetApiKensaHistoryGetListKensaInfDetailQueryHookResult = ReturnType<
  typeof useGetApiKensaHistoryGetListKensaInfDetailQuery
>;
export type GetApiKensaHistoryGetListKensaInfDetailLazyQueryHookResult =
  ReturnType<typeof useGetApiKensaHistoryGetListKensaInfDetailLazyQuery>;
export type GetApiKensaHistoryGetListKensaInfDetailSuspenseQueryHookResult =
  ReturnType<typeof useGetApiKensaHistoryGetListKensaInfDetailSuspenseQuery>;
export type GetApiKensaHistoryGetListKensaInfDetailQueryResult =
  Apollo.QueryResult<
    GetApiKensaHistoryGetListKensaInfDetailQuery,
    GetApiKensaHistoryGetListKensaInfDetailQueryVariables
  >;
export const GetApiUserConfGetListForModelDocument = gql`
  query getApiUserConfGetListForModel {
    getApiUserConfGetListForModel {
      data {
        userConfs {
          grpCd
          grpItemCd
          grpItemEdaNo
          param
          userId
          val
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiUserConfGetListForModelQuery__
 *
 * To run a query within a React component, call `useGetApiUserConfGetListForModelQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiUserConfGetListForModelQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiUserConfGetListForModelQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiUserConfGetListForModelQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiUserConfGetListForModelQuery,
    GetApiUserConfGetListForModelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiUserConfGetListForModelQuery,
    GetApiUserConfGetListForModelQueryVariables
  >(GetApiUserConfGetListForModelDocument, options);
}
export function useGetApiUserConfGetListForModelLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiUserConfGetListForModelQuery,
    GetApiUserConfGetListForModelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiUserConfGetListForModelQuery,
    GetApiUserConfGetListForModelQueryVariables
  >(GetApiUserConfGetListForModelDocument, options);
}
export function useGetApiUserConfGetListForModelSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiUserConfGetListForModelQuery,
    GetApiUserConfGetListForModelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiUserConfGetListForModelQuery,
    GetApiUserConfGetListForModelQueryVariables
  >(GetApiUserConfGetListForModelDocument, options);
}
export type GetApiUserConfGetListForModelQueryHookResult = ReturnType<
  typeof useGetApiUserConfGetListForModelQuery
>;
export type GetApiUserConfGetListForModelLazyQueryHookResult = ReturnType<
  typeof useGetApiUserConfGetListForModelLazyQuery
>;
export type GetApiUserConfGetListForModelSuspenseQueryHookResult = ReturnType<
  typeof useGetApiUserConfGetListForModelSuspenseQuery
>;
export type GetApiUserConfGetListForModelQueryResult = Apollo.QueryResult<
  GetApiUserConfGetListForModelQuery,
  GetApiUserConfGetListForModelQueryVariables
>;
export const GetApiKensaMstGetInHospitalKensaMstDocument = gql`
  query getApiKensaMstGetInHospitalKensaMst($isExceptVital: Boolean!) {
    getApiKensaMstGetInHospitalKensaMst(isExceptVital: $isExceptVital) {
      data {
        listData {
          femaleStd
          maleStd
          kensaName
          unit
          kensaItemCd
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiKensaMstGetInHospitalKensaMstQuery__
 *
 * To run a query within a React component, call `useGetApiKensaMstGetInHospitalKensaMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKensaMstGetInHospitalKensaMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKensaMstGetInHospitalKensaMstQuery({
 *   variables: {
 *      isExceptVital: // value for 'isExceptVital'
 *   },
 * });
 */
export function useGetApiKensaMstGetInHospitalKensaMstQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiKensaMstGetInHospitalKensaMstQuery,
    GetApiKensaMstGetInHospitalKensaMstQueryVariables
  > &
    (
      | {
          variables: GetApiKensaMstGetInHospitalKensaMstQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKensaMstGetInHospitalKensaMstQuery,
    GetApiKensaMstGetInHospitalKensaMstQueryVariables
  >(GetApiKensaMstGetInHospitalKensaMstDocument, options);
}
export function useGetApiKensaMstGetInHospitalKensaMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKensaMstGetInHospitalKensaMstQuery,
    GetApiKensaMstGetInHospitalKensaMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKensaMstGetInHospitalKensaMstQuery,
    GetApiKensaMstGetInHospitalKensaMstQueryVariables
  >(GetApiKensaMstGetInHospitalKensaMstDocument, options);
}
export function useGetApiKensaMstGetInHospitalKensaMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKensaMstGetInHospitalKensaMstQuery,
    GetApiKensaMstGetInHospitalKensaMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKensaMstGetInHospitalKensaMstQuery,
    GetApiKensaMstGetInHospitalKensaMstQueryVariables
  >(GetApiKensaMstGetInHospitalKensaMstDocument, options);
}
export type GetApiKensaMstGetInHospitalKensaMstQueryHookResult = ReturnType<
  typeof useGetApiKensaMstGetInHospitalKensaMstQuery
>;
export type GetApiKensaMstGetInHospitalKensaMstLazyQueryHookResult = ReturnType<
  typeof useGetApiKensaMstGetInHospitalKensaMstLazyQuery
>;
export type GetApiKensaMstGetInHospitalKensaMstSuspenseQueryHookResult =
  ReturnType<typeof useGetApiKensaMstGetInHospitalKensaMstSuspenseQuery>;
export type GetApiKensaMstGetInHospitalKensaMstQueryResult = Apollo.QueryResult<
  GetApiKensaMstGetInHospitalKensaMstQuery,
  GetApiKensaMstGetInHospitalKensaMstQueryVariables
>;
export const PostApiPdfCreatorKensaHistoryReportDocument = gql`
  mutation postApiPdfCreatorKensaHistoryReport(
    $emrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput: EmrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput!
  ) {
    postApiPdfCreatorKensaHistoryReport(
      emrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput: $emrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput
    ) {
      data {
        content
        fileName
      }
      message
      status
    }
  }
`;
export type PostApiPdfCreatorKensaHistoryReportMutationFn =
  Apollo.MutationFunction<
    PostApiPdfCreatorKensaHistoryReportMutation,
    PostApiPdfCreatorKensaHistoryReportMutationVariables
  >;

/**
 * __usePostApiPdfCreatorKensaHistoryReportMutation__
 *
 * To run a mutation, you first call `usePostApiPdfCreatorKensaHistoryReportMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPdfCreatorKensaHistoryReportMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPdfCreatorKensaHistoryReportMutation, { data, loading, error }] = usePostApiPdfCreatorKensaHistoryReportMutation({
 *   variables: {
 *      emrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput: // value for 'emrCloudApiRequestsKensaHistoryKensaHistoryReportRequestInput'
 *   },
 * });
 */
export function usePostApiPdfCreatorKensaHistoryReportMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPdfCreatorKensaHistoryReportMutation,
    PostApiPdfCreatorKensaHistoryReportMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPdfCreatorKensaHistoryReportMutation,
    PostApiPdfCreatorKensaHistoryReportMutationVariables
  >(PostApiPdfCreatorKensaHistoryReportDocument, options);
}
export type PostApiPdfCreatorKensaHistoryReportMutationHookResult = ReturnType<
  typeof usePostApiPdfCreatorKensaHistoryReportMutation
>;
export type PostApiPdfCreatorKensaHistoryReportMutationResult =
  Apollo.MutationResult<PostApiPdfCreatorKensaHistoryReportMutation>;
export type PostApiPdfCreatorKensaHistoryReportMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPdfCreatorKensaHistoryReportMutation,
    PostApiPdfCreatorKensaHistoryReportMutationVariables
  >;
export const PostApiExamResultSaveDocument = gql`
  mutation postApiExamResultSave(
    $isAdd: Boolean
    $isDeleted: Int
    $kensaTime: String
    $ptId: BigInt
    $raiinNo: BigInt
    $examResults: [EmrCloudApiRequestsExamResultSaveExamResultRequestInput]
    $iraiCd: BigInt
    $iraiDate: Int
  ) {
    postApiExamResultSave(
      emrCloudApiRequestsExamResultSaveExamResultsRequestInput: {
        ptId: $ptId
        kensaTime: $kensaTime
        isDeleted: $isDeleted
        isAdd: $isAdd
        raiinNo: $raiinNo
        examResults: $examResults
        iraiCd: $iraiCd
        iraiDate: $iraiDate
      }
    ) {
      message
      status
      data {
        success
      }
    }
  }
`;
export type PostApiExamResultSaveMutationFn = Apollo.MutationFunction<
  PostApiExamResultSaveMutation,
  PostApiExamResultSaveMutationVariables
>;

/**
 * __usePostApiExamResultSaveMutation__
 *
 * To run a mutation, you first call `usePostApiExamResultSaveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiExamResultSaveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiExamResultSaveMutation, { data, loading, error }] = usePostApiExamResultSaveMutation({
 *   variables: {
 *      isAdd: // value for 'isAdd'
 *      isDeleted: // value for 'isDeleted'
 *      kensaTime: // value for 'kensaTime'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      examResults: // value for 'examResults'
 *      iraiCd: // value for 'iraiCd'
 *      iraiDate: // value for 'iraiDate'
 *   },
 * });
 */
export function usePostApiExamResultSaveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiExamResultSaveMutation,
    PostApiExamResultSaveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiExamResultSaveMutation,
    PostApiExamResultSaveMutationVariables
  >(PostApiExamResultSaveDocument, options);
}
export type PostApiExamResultSaveMutationHookResult = ReturnType<
  typeof usePostApiExamResultSaveMutation
>;
export type PostApiExamResultSaveMutationResult =
  Apollo.MutationResult<PostApiExamResultSaveMutation>;
export type PostApiExamResultSaveMutationOptions = Apollo.BaseMutationOptions<
  PostApiExamResultSaveMutation,
  PostApiExamResultSaveMutationVariables
>;
