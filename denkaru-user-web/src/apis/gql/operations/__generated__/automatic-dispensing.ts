import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiEpsGetAutomaticDispensingQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiEpsGetAutomaticDispensingQuery = {
  __typename?: "query_root";
  getApiEpsGetEpsDispensings?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsGetDispensingInfListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsGetDispensingInfListResponse";
      dispensingInfListModel?: Array<{
        __typename?: "DomainModelsEpsDispensingEpsDispensingModel";
        bango?: string;
        cancelReason?: string;
        createDate?: string;
        createId?: number;
        createMachine?: string;
        dispensingDate?: number;
        dispensingDocument?: string;
        dispensingResultId?: string;
        dispensingTimes?: number;
        edaNo?: string;
        epsUpdateDateTime?: string;
        hokensyaNo?: string;
        hpId?: number;
        id?: string;
        isDeleted?: number;
        kigo?: string;
        kohiFutansyaNo?: string;
        kohiJyukyusyaNo?: string;
        messageFlg?: number;
        prescriptionId?: string;
        ptId?: string;
        receptionPharmacyName?: string;
        resultType?: number;
        updateDate?: string;
        updateId?: number;
        updateMachine?: string;
      }>;
      epsPrescriptionModel?: Array<{
        __typename?: "DomainModelsEpsPrescriptionEpsPrescriptionModel";
        accessCode?: string;
        bango?: string;
        createDate?: string;
        createId?: number;
        deleteReasonDisplay?: string;
        deletedDate?: string;
        deletedReason?: number;
        dispensingDate?: number;
        dispensingDateDisplay?: string;
        edaNo?: string;
        epsUpdateDateTime?: string;
        hokenDisplay?: string;
        hokensyaNo?: string;
        hpId?: number;
        id?: number;
        issueType?: number;
        issueTypeDisplay?: string;
        kaId?: number;
        kaSName?: string;
        kigo?: string;
        kohiFutansyaNo?: string;
        kohiJyukyusyaNo?: string;
        messageFlag?: string;
        pharmacyName?: string;
        prescriptionDocument?: string;
        prescriptionId?: string;
        ptId?: string;
        ptName?: string;
        ptNum?: string;
        ptNumDisplay?: string;
        raiinNo?: string;
        refileCount?: number;
        refill?: string;
        resultType?: number;
        resultTypeDisplay?: string;
        seqNo?: string;
        sinDate?: number;
        sinDateDisplay?: string;
        status?: number;
        statusDisplay?: string;
        tantoId?: number;
        tantoName?: string;
        updateDate?: string;
        updateId?: number;
      }>;
    };
  };
};

export const GetApiEpsGetAutomaticDispensingDocument = gql`
  query getApiEpsGetAutomaticDispensing($ptId: BigInt) {
    getApiEpsGetEpsDispensings(ptId: $ptId) {
      data {
        dispensingInfListModel {
          bango
          cancelReason
          createDate
          createId
          createMachine
          dispensingDate
          dispensingDocument
          dispensingResultId
          dispensingTimes
          edaNo
          epsUpdateDateTime
          hokensyaNo
          hpId
          id
          isDeleted
          kigo
          kohiFutansyaNo
          kohiJyukyusyaNo
          messageFlg
          prescriptionId
          ptId
          receptionPharmacyName
          resultType
          updateDate
          updateId
          updateMachine
        }
        epsPrescriptionModel {
          accessCode
          bango
          createDate
          createId
          deleteReasonDisplay
          deletedDate
          deletedReason
          dispensingDate
          dispensingDateDisplay
          edaNo
          epsUpdateDateTime
          hokenDisplay
          hokensyaNo
          hpId
          id
          issueType
          issueTypeDisplay
          kaId
          kaSName
          kigo
          kohiFutansyaNo
          kohiJyukyusyaNo
          messageFlag
          pharmacyName
          prescriptionDocument
          prescriptionId
          ptId
          ptName
          ptNum
          ptNumDisplay
          raiinNo
          refileCount
          refill
          resultType
          resultTypeDisplay
          seqNo
          sinDate
          sinDateDisplay
          status
          statusDisplay
          tantoId
          tantoName
          updateDate
          updateId
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiEpsGetAutomaticDispensingQuery__
 *
 * To run a query within a React component, call `useGetApiEpsGetAutomaticDispensingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiEpsGetAutomaticDispensingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiEpsGetAutomaticDispensingQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiEpsGetAutomaticDispensingQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiEpsGetAutomaticDispensingQuery,
    GetApiEpsGetAutomaticDispensingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiEpsGetAutomaticDispensingQuery,
    GetApiEpsGetAutomaticDispensingQueryVariables
  >(GetApiEpsGetAutomaticDispensingDocument, options);
}
export function useGetApiEpsGetAutomaticDispensingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiEpsGetAutomaticDispensingQuery,
    GetApiEpsGetAutomaticDispensingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiEpsGetAutomaticDispensingQuery,
    GetApiEpsGetAutomaticDispensingQueryVariables
  >(GetApiEpsGetAutomaticDispensingDocument, options);
}
export function useGetApiEpsGetAutomaticDispensingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiEpsGetAutomaticDispensingQuery,
    GetApiEpsGetAutomaticDispensingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiEpsGetAutomaticDispensingQuery,
    GetApiEpsGetAutomaticDispensingQueryVariables
  >(GetApiEpsGetAutomaticDispensingDocument, options);
}
export type GetApiEpsGetAutomaticDispensingQueryHookResult = ReturnType<
  typeof useGetApiEpsGetAutomaticDispensingQuery
>;
export type GetApiEpsGetAutomaticDispensingLazyQueryHookResult = ReturnType<
  typeof useGetApiEpsGetAutomaticDispensingLazyQuery
>;
export type GetApiEpsGetAutomaticDispensingSuspenseQueryHookResult = ReturnType<
  typeof useGetApiEpsGetAutomaticDispensingSuspenseQuery
>;
export type GetApiEpsGetAutomaticDispensingQueryResult = Apollo.QueryResult<
  GetApiEpsGetAutomaticDispensingQuery,
  GetApiEpsGetAutomaticDispensingQueryVariables
>;
