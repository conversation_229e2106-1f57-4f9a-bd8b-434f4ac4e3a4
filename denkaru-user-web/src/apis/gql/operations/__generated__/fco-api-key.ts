import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type CreateFcoApiKeyMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsFcoApiKeyCreateFcoApiKeyRequestInput;
}>;

export type CreateFcoApiKeyMutation = {
  __typename?: "mutation_root";
  postApiFcoApiKeyCreateFcoApiKey?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesFcoApiKeyFcoApiKeyResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesFcoApiKeyFcoApiKeyResponse";
      data?: {
        __typename?: "DomainModelsFcoApiKeyFcoApiKeyModel";
        fcoApiKeyId?: string;
        hpId?: number;
        apiKey?: string;
        label?: string;
        createdAt?: string;
        updatedAt?: string;
        deletedAt?: string;
        isDeleted?: boolean;
      };
    };
  };
};

export type GetFcoApiKeysQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetFcoApiKeysQuery = {
  __typename?: "query_root";
  getApiFcoApiKeyGetFcoApiKeys?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesFcoApiKeyFcoApiKeyListResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesFcoApiKeyFcoApiKeyListResponse";
      listData?: Array<{
        __typename?: "DomainModelsFcoApiKeyFcoApiKeyModel";
        fcoApiKeyId?: string;
        hpId?: number;
        apiKey?: string;
        label?: string;
        createdAt?: string;
        updatedAt?: string;
        deletedAt?: string;
        isDeleted?: boolean;
      }>;
    };
  };
};

export type UpdateFcoApiKeyMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsFcoApiKeyUpdateFcoApiKeyRequestInput;
}>;

export type UpdateFcoApiKeyMutation = {
  __typename?: "mutation_root";
  postApiFcoApiKeyUpdateFcoApiKey?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesFcoApiKeyFcoApiKeyResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesFcoApiKeyFcoApiKeyResponse";
      data?: {
        __typename?: "DomainModelsFcoApiKeyFcoApiKeyModel";
        fcoApiKeyId?: string;
        hpId?: number;
        apiKey?: string;
        label?: string;
        createdAt?: string;
        updatedAt?: string;
        deletedAt?: string;
        isDeleted?: boolean;
      };
    };
  };
};

export type DeleteFcoApiKeyMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsFcoApiKeyDeleteFcoApiKeyRequestInput;
}>;

export type DeleteFcoApiKeyMutation = {
  __typename?: "mutation_root";
  postApiFcoApiKeyDeleteFcoApiKey?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesFcoApiKeyFcoApiKeyDeleteResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesFcoApiKeyFcoApiKeyDeleteResponse";
      isDeleted?: boolean;
    };
  };
};

export const CreateFcoApiKeyDocument = gql`
  mutation CreateFcoApiKey(
    $input: EmrCloudApiRequestsFcoApiKeyCreateFcoApiKeyRequestInput!
  ) {
    postApiFcoApiKeyCreateFcoApiKey(
      emrCloudApiRequestsFcoApiKeyCreateFcoApiKeyRequestInput: $input
    ) {
      status
      message
      data {
        data {
          fcoApiKeyId
          hpId
          apiKey
          label
          createdAt
          updatedAt
          deletedAt
          isDeleted
        }
      }
    }
  }
`;
export type CreateFcoApiKeyMutationFn = Apollo.MutationFunction<
  CreateFcoApiKeyMutation,
  CreateFcoApiKeyMutationVariables
>;

/**
 * __useCreateFcoApiKeyMutation__
 *
 * To run a mutation, you first call `useCreateFcoApiKeyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateFcoApiKeyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createFcoApiKeyMutation, { data, loading, error }] = useCreateFcoApiKeyMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateFcoApiKeyMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateFcoApiKeyMutation,
    CreateFcoApiKeyMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateFcoApiKeyMutation,
    CreateFcoApiKeyMutationVariables
  >(CreateFcoApiKeyDocument, options);
}
export type CreateFcoApiKeyMutationHookResult = ReturnType<
  typeof useCreateFcoApiKeyMutation
>;
export type CreateFcoApiKeyMutationResult =
  Apollo.MutationResult<CreateFcoApiKeyMutation>;
export type CreateFcoApiKeyMutationOptions = Apollo.BaseMutationOptions<
  CreateFcoApiKeyMutation,
  CreateFcoApiKeyMutationVariables
>;
export const GetFcoApiKeysDocument = gql`
  query GetFcoApiKeys {
    getApiFcoApiKeyGetFcoApiKeys {
      status
      message
      data {
        listData {
          fcoApiKeyId
          hpId
          apiKey
          label
          createdAt
          updatedAt
          deletedAt
          isDeleted
        }
      }
    }
  }
`;

/**
 * __useGetFcoApiKeysQuery__
 *
 * To run a query within a React component, call `useGetFcoApiKeysQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetFcoApiKeysQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetFcoApiKeysQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetFcoApiKeysQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetFcoApiKeysQuery,
    GetFcoApiKeysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetFcoApiKeysQuery, GetFcoApiKeysQueryVariables>(
    GetFcoApiKeysDocument,
    options,
  );
}
export function useGetFcoApiKeysLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetFcoApiKeysQuery,
    GetFcoApiKeysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetFcoApiKeysQuery, GetFcoApiKeysQueryVariables>(
    GetFcoApiKeysDocument,
    options,
  );
}
export function useGetFcoApiKeysSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetFcoApiKeysQuery,
    GetFcoApiKeysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetFcoApiKeysQuery,
    GetFcoApiKeysQueryVariables
  >(GetFcoApiKeysDocument, options);
}
export type GetFcoApiKeysQueryHookResult = ReturnType<
  typeof useGetFcoApiKeysQuery
>;
export type GetFcoApiKeysLazyQueryHookResult = ReturnType<
  typeof useGetFcoApiKeysLazyQuery
>;
export type GetFcoApiKeysSuspenseQueryHookResult = ReturnType<
  typeof useGetFcoApiKeysSuspenseQuery
>;
export type GetFcoApiKeysQueryResult = Apollo.QueryResult<
  GetFcoApiKeysQuery,
  GetFcoApiKeysQueryVariables
>;
export const UpdateFcoApiKeyDocument = gql`
  mutation UpdateFcoApiKey(
    $input: EmrCloudApiRequestsFcoApiKeyUpdateFcoApiKeyRequestInput!
  ) {
    postApiFcoApiKeyUpdateFcoApiKey(
      emrCloudApiRequestsFcoApiKeyUpdateFcoApiKeyRequestInput: $input
    ) {
      status
      message
      data {
        data {
          fcoApiKeyId
          hpId
          apiKey
          label
          createdAt
          updatedAt
          deletedAt
          isDeleted
        }
      }
    }
  }
`;
export type UpdateFcoApiKeyMutationFn = Apollo.MutationFunction<
  UpdateFcoApiKeyMutation,
  UpdateFcoApiKeyMutationVariables
>;

/**
 * __useUpdateFcoApiKeyMutation__
 *
 * To run a mutation, you first call `useUpdateFcoApiKeyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateFcoApiKeyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateFcoApiKeyMutation, { data, loading, error }] = useUpdateFcoApiKeyMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateFcoApiKeyMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateFcoApiKeyMutation,
    UpdateFcoApiKeyMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateFcoApiKeyMutation,
    UpdateFcoApiKeyMutationVariables
  >(UpdateFcoApiKeyDocument, options);
}
export type UpdateFcoApiKeyMutationHookResult = ReturnType<
  typeof useUpdateFcoApiKeyMutation
>;
export type UpdateFcoApiKeyMutationResult =
  Apollo.MutationResult<UpdateFcoApiKeyMutation>;
export type UpdateFcoApiKeyMutationOptions = Apollo.BaseMutationOptions<
  UpdateFcoApiKeyMutation,
  UpdateFcoApiKeyMutationVariables
>;
export const DeleteFcoApiKeyDocument = gql`
  mutation DeleteFcoApiKey(
    $input: EmrCloudApiRequestsFcoApiKeyDeleteFcoApiKeyRequestInput!
  ) {
    postApiFcoApiKeyDeleteFcoApiKey(
      emrCloudApiRequestsFcoApiKeyDeleteFcoApiKeyRequestInput: $input
    ) {
      status
      message
      data {
        isDeleted
      }
    }
  }
`;
export type DeleteFcoApiKeyMutationFn = Apollo.MutationFunction<
  DeleteFcoApiKeyMutation,
  DeleteFcoApiKeyMutationVariables
>;

/**
 * __useDeleteFcoApiKeyMutation__
 *
 * To run a mutation, you first call `useDeleteFcoApiKeyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteFcoApiKeyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteFcoApiKeyMutation, { data, loading, error }] = useDeleteFcoApiKeyMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useDeleteFcoApiKeyMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteFcoApiKeyMutation,
    DeleteFcoApiKeyMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteFcoApiKeyMutation,
    DeleteFcoApiKeyMutationVariables
  >(DeleteFcoApiKeyDocument, options);
}
export type DeleteFcoApiKeyMutationHookResult = ReturnType<
  typeof useDeleteFcoApiKeyMutation
>;
export type DeleteFcoApiKeyMutationResult =
  Apollo.MutationResult<DeleteFcoApiKeyMutation>;
export type DeleteFcoApiKeyMutationOptions = Apollo.BaseMutationOptions<
  DeleteFcoApiKeyMutation,
  DeleteFcoApiKeyMutationVariables
>;
