import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type CreateCalendarMutationVariables = Types.Exact<{
  input: Types.ReservationCalendarCreateInput;
}>;

export type CreateCalendarMutation = {
  __typename?: "mutation_root";
  createReservationCalendar: { __typename?: "Calendar"; calendarID: number };
};

export type EditReservationCalendarMutationVariables = Types.Exact<{
  calendarId: Types.Scalars["Int"]["input"];
  input: Types.ReservationCalendarCreateInput;
}>;

export type EditReservationCalendarMutation = {
  __typename?: "mutation_root";
  editReservationCalendar: { __typename?: "Calendar"; calendarID: number };
};

export type DeleteReservationCalendarMutationVariables = Types.Exact<{
  input: Types.DeleteCalendarInput;
}>;

export type DeleteReservationCalendarMutation = {
  __typename?: "mutation_root";
  deleteReservationCalendar: boolean;
};

export type GetCalendarsQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetCalendarsQuery = {
  __typename?: "query_root";
  getCalendars: Array<{
    __typename?: "Calendar";
    calendarID: number;
    label?: string;
    calendarName?: string;
    reservationMethodType?: number;
    doctor?: { __typename?: "StaffInfo"; staffId: number; staffName: string };
    calendarTreatMents?: Array<{
      __typename?: "CalendarTreatment";
      calendarTreatmentID: number;
      treatmentDepartment?: {
        __typename?: "TreatmentDepartment";
        treatmentDepartmentId: number;
        treatmentCategoryId: number;
        firstConsultationTime: number;
        nextConsultationTime: number;
        treatmentType: number;
        treatmentMethod: number;
        description: string;
        title: string;
      };
    }>;
  }>;
};

export type GetCalendarByIdQueryVariables = Types.Exact<{
  input: Types.GetCalendarInput;
}>;

export type GetCalendarByIdQuery = {
  __typename?: "query_root";
  getCalendarById: {
    __typename?: "Calendar";
    calendarID: number;
    reservableMinutesBeforeExamEndTime?: number;
    reservableSlotSettingType?: number;
    reservableStartDays?: number;
    reservableStartTime?: string;
    calendarNameSettingType?: number;
    reservationMethodType?: number;
    calendarName?: string;
    numberOfDoctors?: number;
    label?: string;
    calendarTimeSlot?: number;
    doctor?: { __typename?: "StaffInfo"; staffId: number; staffName: string };
    calendarTreatMents?: Array<{
      __typename?: "CalendarTreatment";
      treatmentDepartmentID: number;
      treatmentDepartment?: {
        __typename?: "TreatmentDepartment";
        treatmentDepartmentId: number;
        firstConsultationTime: number;
        nextConsultationTime: number;
        treatmentType: number;
        title: string;
      };
    }>;
    calendarBasicSettings?: Array<{
      __typename?: "CalendarBasicSetting";
      calendarBasicSettingID: number;
      daysOfWeek: Array<number>;
      weeksOfMonth: Array<number>;
      startDate: string;
      endDate?: string;
      startTime: string;
      endTime: string;
      closeOnHolidayFlag: boolean;
      reservableSlot?: number;
      startWaitingNumber?: number;
    }>;
  };
};

export type GetCalendarByIdSettingQueryVariables = Types.Exact<{
  input: Types.GetCalendarInput;
}>;

export type GetCalendarByIdSettingQuery = {
  __typename?: "query_root";
  getCalendarByIdSetting: {
    __typename?: "Calendar";
    calendarID: number;
    reservableMinutesBeforeExamEndTime?: number;
    reservableSlotSettingType?: number;
    reservableStartDays?: number;
    reservableStartTime?: string;
    calendarNameSettingType?: number;
    reservationMethodType?: number;
    calendarName?: string;
    numberOfDoctors?: number;
    label?: string;
    calendarTimeSlot?: number;
    doctor?: { __typename?: "StaffInfo"; staffId: number; staffName: string };
    calendarTreatMents?: Array<{
      __typename?: "CalendarTreatment";
      treatmentDepartmentID: number;
      treatmentDepartment?: {
        __typename?: "TreatmentDepartment";
        treatmentDepartmentId: number;
        firstConsultationTime: number;
        nextConsultationTime: number;
        treatmentType: number;
        title: string;
      };
    }>;
    calendarBasicSettings?: Array<{
      __typename?: "CalendarBasicSetting";
      calendarBasicSettingID: number;
      daysOfWeek: Array<number>;
      weeksOfMonth: Array<number>;
      startDate: string;
      endDate?: string;
      startTime: string;
      endTime: string;
      closeOnHolidayFlag: boolean;
      reservableSlot?: number;
      startWaitingNumber?: number;
    }>;
  };
};

export type GetTreatmentDepartmentsCalendarQueryVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.GetTreatmentDepartmentsByConditions>;
}>;

export type GetTreatmentDepartmentsCalendarQuery = {
  __typename?: "query_root";
  getTreatmentDepartments: Array<{
    __typename?: "TreatmentDepartment";
    treatmentDepartmentId: number;
    title: string;
    firstConsultationTime: number;
    nextConsultationTime: number;
    treatmentType: number;
    treatmentMethod: number;
    description: string;
    treatmentCategoryId: number;
    calendarTreatments?: Array<{
      __typename?: "CalendarTreatment";
      calendarTreatmentID: number;
      calendar?: {
        __typename?: "Calendar";
        calendarID: number;
        label?: string;
        reservationMethodType?: number;
        calendarName?: string;
        doctor?: {
          __typename?: "StaffInfo";
          staffId: number;
          staffName: string;
        };
        calendarBasicSettings?: Array<{
          __typename?: "CalendarBasicSetting";
          startTime: string;
          endTime: string;
        }>;
      };
    }>;
  }>;
};

export type GetCalendarWorkingTimeQueryVariables = Types.Exact<{
  input: Types.GetCalendarWorkingTimeInput;
}>;

export type GetCalendarWorkingTimeQuery = {
  __typename?: "query_root";
  getCalendarWorkingTime: Array<{
    __typename?: "CalendarWorkingTime";
    startTime: string;
    endTime: string;
  }>;
};

export type GetPublicCalendarsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetPublicCalendarsQuery = {
  __typename?: "query_root";
  getPublicCalendars: Array<{
    __typename?: "Calendar";
    calendarID: number;
    label?: string;
    calendarName?: string;
    reservationMethodType?: number;
    doctor?: { __typename?: "StaffInfo"; staffId: number; staffName: string };
    calendarTreatMents?: Array<{
      __typename?: "CalendarTreatment";
      calendarTreatmentID: number;
      treatmentDepartment?: {
        __typename?: "TreatmentDepartment";
        treatmentDepartmentId: number;
        treatmentCategoryId: number;
        firstConsultationTime: number;
        nextConsultationTime: number;
        treatmentType: number;
        treatmentMethod: number;
        description: string;
        title: string;
      };
    }>;
  }>;
};

export const CreateCalendarDocument = gql`
  mutation createCalendar($input: ReservationCalendarCreateInput!) {
    createReservationCalendar(input: $input) {
      calendarID
    }
  }
`;
export type CreateCalendarMutationFn = Apollo.MutationFunction<
  CreateCalendarMutation,
  CreateCalendarMutationVariables
>;

/**
 * __useCreateCalendarMutation__
 *
 * To run a mutation, you first call `useCreateCalendarMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateCalendarMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createCalendarMutation, { data, loading, error }] = useCreateCalendarMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateCalendarMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateCalendarMutation,
    CreateCalendarMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateCalendarMutation,
    CreateCalendarMutationVariables
  >(CreateCalendarDocument, options);
}
export type CreateCalendarMutationHookResult = ReturnType<
  typeof useCreateCalendarMutation
>;
export type CreateCalendarMutationResult =
  Apollo.MutationResult<CreateCalendarMutation>;
export type CreateCalendarMutationOptions = Apollo.BaseMutationOptions<
  CreateCalendarMutation,
  CreateCalendarMutationVariables
>;
export const EditReservationCalendarDocument = gql`
  mutation editReservationCalendar(
    $calendarId: Int!
    $input: ReservationCalendarCreateInput!
  ) {
    editReservationCalendar(calendarId: $calendarId, input: $input) {
      calendarID
    }
  }
`;
export type EditReservationCalendarMutationFn = Apollo.MutationFunction<
  EditReservationCalendarMutation,
  EditReservationCalendarMutationVariables
>;

/**
 * __useEditReservationCalendarMutation__
 *
 * To run a mutation, you first call `useEditReservationCalendarMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditReservationCalendarMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editReservationCalendarMutation, { data, loading, error }] = useEditReservationCalendarMutation({
 *   variables: {
 *      calendarId: // value for 'calendarId'
 *      input: // value for 'input'
 *   },
 * });
 */
export function useEditReservationCalendarMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditReservationCalendarMutation,
    EditReservationCalendarMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditReservationCalendarMutation,
    EditReservationCalendarMutationVariables
  >(EditReservationCalendarDocument, options);
}
export type EditReservationCalendarMutationHookResult = ReturnType<
  typeof useEditReservationCalendarMutation
>;
export type EditReservationCalendarMutationResult =
  Apollo.MutationResult<EditReservationCalendarMutation>;
export type EditReservationCalendarMutationOptions = Apollo.BaseMutationOptions<
  EditReservationCalendarMutation,
  EditReservationCalendarMutationVariables
>;
export const DeleteReservationCalendarDocument = gql`
  mutation deleteReservationCalendar($input: DeleteCalendarInput!) {
    deleteReservationCalendar(input: $input)
  }
`;
export type DeleteReservationCalendarMutationFn = Apollo.MutationFunction<
  DeleteReservationCalendarMutation,
  DeleteReservationCalendarMutationVariables
>;

/**
 * __useDeleteReservationCalendarMutation__
 *
 * To run a mutation, you first call `useDeleteReservationCalendarMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteReservationCalendarMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteReservationCalendarMutation, { data, loading, error }] = useDeleteReservationCalendarMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useDeleteReservationCalendarMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteReservationCalendarMutation,
    DeleteReservationCalendarMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteReservationCalendarMutation,
    DeleteReservationCalendarMutationVariables
  >(DeleteReservationCalendarDocument, options);
}
export type DeleteReservationCalendarMutationHookResult = ReturnType<
  typeof useDeleteReservationCalendarMutation
>;
export type DeleteReservationCalendarMutationResult =
  Apollo.MutationResult<DeleteReservationCalendarMutation>;
export type DeleteReservationCalendarMutationOptions =
  Apollo.BaseMutationOptions<
    DeleteReservationCalendarMutation,
    DeleteReservationCalendarMutationVariables
  >;
export const GetCalendarsDocument = gql`
  query getCalendars {
    getCalendars {
      calendarID
      label
      calendarName
      reservationMethodType
      doctor {
        staffId
        staffName
      }
      calendarTreatMents {
        calendarTreatmentID
        treatmentDepartment {
          treatmentDepartmentId
          treatmentCategoryId
          firstConsultationTime
          nextConsultationTime
          treatmentType
          treatmentMethod
          description
          title
        }
      }
    }
  }
`;

/**
 * __useGetCalendarsQuery__
 *
 * To run a query within a React component, call `useGetCalendarsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCalendarsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCalendarsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetCalendarsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetCalendarsQuery,
    GetCalendarsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetCalendarsQuery, GetCalendarsQueryVariables>(
    GetCalendarsDocument,
    options,
  );
}
export function useGetCalendarsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetCalendarsQuery,
    GetCalendarsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetCalendarsQuery, GetCalendarsQueryVariables>(
    GetCalendarsDocument,
    options,
  );
}
export function useGetCalendarsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetCalendarsQuery,
    GetCalendarsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<GetCalendarsQuery, GetCalendarsQueryVariables>(
    GetCalendarsDocument,
    options,
  );
}
export type GetCalendarsQueryHookResult = ReturnType<
  typeof useGetCalendarsQuery
>;
export type GetCalendarsLazyQueryHookResult = ReturnType<
  typeof useGetCalendarsLazyQuery
>;
export type GetCalendarsSuspenseQueryHookResult = ReturnType<
  typeof useGetCalendarsSuspenseQuery
>;
export type GetCalendarsQueryResult = Apollo.QueryResult<
  GetCalendarsQuery,
  GetCalendarsQueryVariables
>;
export const GetCalendarByIdDocument = gql`
  query getCalendarById($input: GetCalendarInput!) {
    getCalendarById(input: $input) {
      calendarID
      reservableMinutesBeforeExamEndTime
      reservableSlotSettingType
      reservableStartDays
      reservableStartTime
      calendarNameSettingType
      reservationMethodType
      calendarName
      numberOfDoctors
      label
      doctor {
        staffId
        staffName
      }
      calendarTreatMents {
        treatmentDepartmentID
        treatmentDepartment {
          treatmentDepartmentId
          firstConsultationTime
          nextConsultationTime
          treatmentType
          title
        }
      }
      calendarTimeSlot
      calendarBasicSettings {
        calendarBasicSettingID
        daysOfWeek
        weeksOfMonth
        startDate
        endDate
        startTime
        endTime
        closeOnHolidayFlag
        reservableSlot
        startWaitingNumber
      }
    }
  }
`;

/**
 * __useGetCalendarByIdQuery__
 *
 * To run a query within a React component, call `useGetCalendarByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCalendarByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCalendarByIdQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetCalendarByIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetCalendarByIdQuery,
    GetCalendarByIdQueryVariables
  > &
    (
      | { variables: GetCalendarByIdQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetCalendarByIdQuery, GetCalendarByIdQueryVariables>(
    GetCalendarByIdDocument,
    options,
  );
}
export function useGetCalendarByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetCalendarByIdQuery,
    GetCalendarByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetCalendarByIdQuery,
    GetCalendarByIdQueryVariables
  >(GetCalendarByIdDocument, options);
}
export function useGetCalendarByIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetCalendarByIdQuery,
    GetCalendarByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetCalendarByIdQuery,
    GetCalendarByIdQueryVariables
  >(GetCalendarByIdDocument, options);
}
export type GetCalendarByIdQueryHookResult = ReturnType<
  typeof useGetCalendarByIdQuery
>;
export type GetCalendarByIdLazyQueryHookResult = ReturnType<
  typeof useGetCalendarByIdLazyQuery
>;
export type GetCalendarByIdSuspenseQueryHookResult = ReturnType<
  typeof useGetCalendarByIdSuspenseQuery
>;
export type GetCalendarByIdQueryResult = Apollo.QueryResult<
  GetCalendarByIdQuery,
  GetCalendarByIdQueryVariables
>;
export const GetCalendarByIdSettingDocument = gql`
  query getCalendarByIdSetting($input: GetCalendarInput!) {
    getCalendarByIdSetting(input: $input) {
      calendarID
      reservableMinutesBeforeExamEndTime
      reservableSlotSettingType
      reservableStartDays
      reservableStartTime
      calendarNameSettingType
      reservationMethodType
      calendarName
      numberOfDoctors
      label
      doctor {
        staffId
        staffName
      }
      calendarTreatMents {
        treatmentDepartmentID
        treatmentDepartment {
          treatmentDepartmentId
          firstConsultationTime
          nextConsultationTime
          treatmentType
          title
        }
      }
      calendarBasicSettings {
        calendarBasicSettingID
        daysOfWeek
        weeksOfMonth
        startDate
        endDate
        startTime
        endTime
        closeOnHolidayFlag
        reservableSlot
        startWaitingNumber
      }
      calendarTimeSlot
    }
  }
`;

/**
 * __useGetCalendarByIdSettingQuery__
 *
 * To run a query within a React component, call `useGetCalendarByIdSettingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCalendarByIdSettingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCalendarByIdSettingQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetCalendarByIdSettingQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetCalendarByIdSettingQuery,
    GetCalendarByIdSettingQueryVariables
  > &
    (
      | { variables: GetCalendarByIdSettingQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetCalendarByIdSettingQuery,
    GetCalendarByIdSettingQueryVariables
  >(GetCalendarByIdSettingDocument, options);
}
export function useGetCalendarByIdSettingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetCalendarByIdSettingQuery,
    GetCalendarByIdSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetCalendarByIdSettingQuery,
    GetCalendarByIdSettingQueryVariables
  >(GetCalendarByIdSettingDocument, options);
}
export function useGetCalendarByIdSettingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetCalendarByIdSettingQuery,
    GetCalendarByIdSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetCalendarByIdSettingQuery,
    GetCalendarByIdSettingQueryVariables
  >(GetCalendarByIdSettingDocument, options);
}
export type GetCalendarByIdSettingQueryHookResult = ReturnType<
  typeof useGetCalendarByIdSettingQuery
>;
export type GetCalendarByIdSettingLazyQueryHookResult = ReturnType<
  typeof useGetCalendarByIdSettingLazyQuery
>;
export type GetCalendarByIdSettingSuspenseQueryHookResult = ReturnType<
  typeof useGetCalendarByIdSettingSuspenseQuery
>;
export type GetCalendarByIdSettingQueryResult = Apollo.QueryResult<
  GetCalendarByIdSettingQuery,
  GetCalendarByIdSettingQueryVariables
>;
export const GetTreatmentDepartmentsCalendarDocument = gql`
  query getTreatmentDepartmentsCalendar(
    $input: GetTreatmentDepartmentsByConditions
  ) {
    getTreatmentDepartments(input: $input) {
      treatmentDepartmentId
      title
      firstConsultationTime
      nextConsultationTime
      treatmentType
      treatmentMethod
      description
      treatmentCategoryId
      title
      calendarTreatments {
        calendarTreatmentID
        calendar {
          calendarID
          label
          reservationMethodType
          calendarName
          doctor {
            staffId
            staffName
          }
          calendarBasicSettings {
            startTime
            endTime
          }
        }
      }
    }
  }
`;

/**
 * __useGetTreatmentDepartmentsCalendarQuery__
 *
 * To run a query within a React component, call `useGetTreatmentDepartmentsCalendarQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTreatmentDepartmentsCalendarQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTreatmentDepartmentsCalendarQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetTreatmentDepartmentsCalendarQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetTreatmentDepartmentsCalendarQuery,
    GetTreatmentDepartmentsCalendarQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetTreatmentDepartmentsCalendarQuery,
    GetTreatmentDepartmentsCalendarQueryVariables
  >(GetTreatmentDepartmentsCalendarDocument, options);
}
export function useGetTreatmentDepartmentsCalendarLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetTreatmentDepartmentsCalendarQuery,
    GetTreatmentDepartmentsCalendarQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetTreatmentDepartmentsCalendarQuery,
    GetTreatmentDepartmentsCalendarQueryVariables
  >(GetTreatmentDepartmentsCalendarDocument, options);
}
export function useGetTreatmentDepartmentsCalendarSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetTreatmentDepartmentsCalendarQuery,
    GetTreatmentDepartmentsCalendarQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetTreatmentDepartmentsCalendarQuery,
    GetTreatmentDepartmentsCalendarQueryVariables
  >(GetTreatmentDepartmentsCalendarDocument, options);
}
export type GetTreatmentDepartmentsCalendarQueryHookResult = ReturnType<
  typeof useGetTreatmentDepartmentsCalendarQuery
>;
export type GetTreatmentDepartmentsCalendarLazyQueryHookResult = ReturnType<
  typeof useGetTreatmentDepartmentsCalendarLazyQuery
>;
export type GetTreatmentDepartmentsCalendarSuspenseQueryHookResult = ReturnType<
  typeof useGetTreatmentDepartmentsCalendarSuspenseQuery
>;
export type GetTreatmentDepartmentsCalendarQueryResult = Apollo.QueryResult<
  GetTreatmentDepartmentsCalendarQuery,
  GetTreatmentDepartmentsCalendarQueryVariables
>;
export const GetCalendarWorkingTimeDocument = gql`
  query getCalendarWorkingTime($input: GetCalendarWorkingTimeInput!) {
    getCalendarWorkingTime(input: $input) {
      startTime
      endTime
    }
  }
`;

/**
 * __useGetCalendarWorkingTimeQuery__
 *
 * To run a query within a React component, call `useGetCalendarWorkingTimeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCalendarWorkingTimeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCalendarWorkingTimeQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetCalendarWorkingTimeQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetCalendarWorkingTimeQuery,
    GetCalendarWorkingTimeQueryVariables
  > &
    (
      | { variables: GetCalendarWorkingTimeQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetCalendarWorkingTimeQuery,
    GetCalendarWorkingTimeQueryVariables
  >(GetCalendarWorkingTimeDocument, options);
}
export function useGetCalendarWorkingTimeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetCalendarWorkingTimeQuery,
    GetCalendarWorkingTimeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetCalendarWorkingTimeQuery,
    GetCalendarWorkingTimeQueryVariables
  >(GetCalendarWorkingTimeDocument, options);
}
export function useGetCalendarWorkingTimeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetCalendarWorkingTimeQuery,
    GetCalendarWorkingTimeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetCalendarWorkingTimeQuery,
    GetCalendarWorkingTimeQueryVariables
  >(GetCalendarWorkingTimeDocument, options);
}
export type GetCalendarWorkingTimeQueryHookResult = ReturnType<
  typeof useGetCalendarWorkingTimeQuery
>;
export type GetCalendarWorkingTimeLazyQueryHookResult = ReturnType<
  typeof useGetCalendarWorkingTimeLazyQuery
>;
export type GetCalendarWorkingTimeSuspenseQueryHookResult = ReturnType<
  typeof useGetCalendarWorkingTimeSuspenseQuery
>;
export type GetCalendarWorkingTimeQueryResult = Apollo.QueryResult<
  GetCalendarWorkingTimeQuery,
  GetCalendarWorkingTimeQueryVariables
>;
export const GetPublicCalendarsDocument = gql`
  query getPublicCalendars {
    getPublicCalendars {
      calendarID
      label
      calendarName
      reservationMethodType
      doctor {
        staffId
        staffName
      }
      calendarTreatMents {
        calendarTreatmentID
        treatmentDepartment {
          treatmentDepartmentId
          treatmentCategoryId
          firstConsultationTime
          nextConsultationTime
          treatmentType
          treatmentMethod
          description
          title
        }
      }
    }
  }
`;

/**
 * __useGetPublicCalendarsQuery__
 *
 * To run a query within a React component, call `useGetPublicCalendarsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPublicCalendarsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPublicCalendarsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetPublicCalendarsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetPublicCalendarsQuery,
    GetPublicCalendarsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPublicCalendarsQuery,
    GetPublicCalendarsQueryVariables
  >(GetPublicCalendarsDocument, options);
}
export function useGetPublicCalendarsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPublicCalendarsQuery,
    GetPublicCalendarsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPublicCalendarsQuery,
    GetPublicCalendarsQueryVariables
  >(GetPublicCalendarsDocument, options);
}
export function useGetPublicCalendarsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPublicCalendarsQuery,
    GetPublicCalendarsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPublicCalendarsQuery,
    GetPublicCalendarsQueryVariables
  >(GetPublicCalendarsDocument, options);
}
export type GetPublicCalendarsQueryHookResult = ReturnType<
  typeof useGetPublicCalendarsQuery
>;
export type GetPublicCalendarsLazyQueryHookResult = ReturnType<
  typeof useGetPublicCalendarsLazyQuery
>;
export type GetPublicCalendarsSuspenseQueryHookResult = ReturnType<
  typeof useGetPublicCalendarsSuspenseQuery
>;
export type GetPublicCalendarsQueryResult = Apollo.QueryResult<
  GetPublicCalendarsQuery,
  GetPublicCalendarsQueryVariables
>;
