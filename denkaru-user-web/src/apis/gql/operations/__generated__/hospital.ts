import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetHospitalQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetHospitalQuery = {
  __typename?: "query_root";
  getHospitalById: {
    __typename?: "Hospital";
    insuredBranchCode: string;
    name: string;
    postCode: string;
    address: string;
    telephone: string;
    email: string;
    isOpenClinic: boolean;
    status: number;
    rousaiInsuredBranchCode: string;
    receName: string;
    kaisetuName: string;
  };
};

export type UpdateHospitalMutationVariables = Types.Exact<{
  input: Types.HospitalInput;
}>;

export type UpdateHospitalMutation = {
  __typename?: "mutation_root";
  updateHospital: boolean;
};

export type ChangeStatusHospitalInfoMutationVariables = Types.Exact<{
  input: Types.ChangeHospitalInfoInput;
}>;

export type ChangeStatusHospitalInfoMutation = {
  __typename?: "mutation_root";
  changeStatusHospitalInfo: boolean;
};

export type GetYakkyoku24InfoQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetYakkyoku24InfoQuery = {
  __typename?: "query_root";
  getYakkyoku24Info: {
    __typename?: "Yakkyoku24Info";
    name: string;
    postCode: string;
    telephone1: string;
    telephone2: string;
    faxNumber: string;
    address1: string;
    address2: string;
  };
};

export type GetPublicHospitalQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetPublicHospitalQuery = {
  __typename?: "query_root";
  getPublicHospital: {
    __typename?: "Hospital";
    insuredBranchCode: string;
    name: string;
    postCode: string;
    address: string;
    telephone: string;
    email: string;
    isOpenClinic: boolean;
    status: number;
  };
};

export type GetImportDataByHpIdQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetImportDataByHpIdQuery = {
  __typename?: "query_root";
  getImportDataByHpId?: {
    __typename?: "ImportHospitalData";
    name?: string;
    address1?: string;
    address2?: string;
    postCode?: string;
    homepage?: string;
    telephone?: string;
    directorName?: string;
    descriptionTitle?: string;
    description?: string;
    holidayDetail?: string;
    isCarpark?: boolean;
    carparkDetail?: string;
    stations?: Array<{
      __typename?: "OSStation";
      stationId?: string;
      stationDetail?: {
        __typename?: "OSStationDetail";
        name?: string;
        description?: string;
      };
    }>;
    businessTimes?: Array<{
      __typename?: "OSBusinessTime";
      startTime?: string;
      endTime?: string;
      monFlag?: number;
      tueFlag?: number;
      wedFlag?: number;
      thuFlag?: number;
      friFlag?: number;
      satFlag?: number;
      sunFlag?: number;
    }>;
    tags?: Array<{ __typename?: "OSTag"; name?: string; tagId: number }>;
    examinations?: Array<{
      __typename?: "OSExamination";
      examinationId: number;
      name?: string;
    }>;
    specialists?: Array<{
      __typename?: "OSSpecialist";
      specialistId: number;
      name?: string;
    }>;
    payments?: Array<{
      __typename?: "ImportPayment";
      paymentTypeName?: string;
    }>;
  };
};

export const GetHospitalDocument = gql`
  query getHospital {
    getHospitalById {
      insuredBranchCode
      name
      postCode
      address
      telephone
      email
      isOpenClinic
      status
      rousaiInsuredBranchCode
      receName
      kaisetuName
    }
  }
`;

/**
 * __useGetHospitalQuery__
 *
 * To run a query within a React component, call `useGetHospitalQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetHospitalQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetHospitalQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetHospitalQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetHospitalQuery,
    GetHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetHospitalQuery, GetHospitalQueryVariables>(
    GetHospitalDocument,
    options,
  );
}
export function useGetHospitalLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetHospitalQuery,
    GetHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetHospitalQuery, GetHospitalQueryVariables>(
    GetHospitalDocument,
    options,
  );
}
export function useGetHospitalSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetHospitalQuery,
    GetHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<GetHospitalQuery, GetHospitalQueryVariables>(
    GetHospitalDocument,
    options,
  );
}
export type GetHospitalQueryHookResult = ReturnType<typeof useGetHospitalQuery>;
export type GetHospitalLazyQueryHookResult = ReturnType<
  typeof useGetHospitalLazyQuery
>;
export type GetHospitalSuspenseQueryHookResult = ReturnType<
  typeof useGetHospitalSuspenseQuery
>;
export type GetHospitalQueryResult = Apollo.QueryResult<
  GetHospitalQuery,
  GetHospitalQueryVariables
>;
export const UpdateHospitalDocument = gql`
  mutation updateHospital($input: HospitalInput!) {
    updateHospital(input: $input)
  }
`;
export type UpdateHospitalMutationFn = Apollo.MutationFunction<
  UpdateHospitalMutation,
  UpdateHospitalMutationVariables
>;

/**
 * __useUpdateHospitalMutation__
 *
 * To run a mutation, you first call `useUpdateHospitalMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateHospitalMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateHospitalMutation, { data, loading, error }] = useUpdateHospitalMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateHospitalMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateHospitalMutation,
    UpdateHospitalMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateHospitalMutation,
    UpdateHospitalMutationVariables
  >(UpdateHospitalDocument, options);
}
export type UpdateHospitalMutationHookResult = ReturnType<
  typeof useUpdateHospitalMutation
>;
export type UpdateHospitalMutationResult =
  Apollo.MutationResult<UpdateHospitalMutation>;
export type UpdateHospitalMutationOptions = Apollo.BaseMutationOptions<
  UpdateHospitalMutation,
  UpdateHospitalMutationVariables
>;
export const ChangeStatusHospitalInfoDocument = gql`
  mutation changeStatusHospitalInfo($input: ChangeHospitalInfoInput!) {
    changeStatusHospitalInfo(input: $input)
  }
`;
export type ChangeStatusHospitalInfoMutationFn = Apollo.MutationFunction<
  ChangeStatusHospitalInfoMutation,
  ChangeStatusHospitalInfoMutationVariables
>;

/**
 * __useChangeStatusHospitalInfoMutation__
 *
 * To run a mutation, you first call `useChangeStatusHospitalInfoMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChangeStatusHospitalInfoMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [changeStatusHospitalInfoMutation, { data, loading, error }] = useChangeStatusHospitalInfoMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useChangeStatusHospitalInfoMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ChangeStatusHospitalInfoMutation,
    ChangeStatusHospitalInfoMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    ChangeStatusHospitalInfoMutation,
    ChangeStatusHospitalInfoMutationVariables
  >(ChangeStatusHospitalInfoDocument, options);
}
export type ChangeStatusHospitalInfoMutationHookResult = ReturnType<
  typeof useChangeStatusHospitalInfoMutation
>;
export type ChangeStatusHospitalInfoMutationResult =
  Apollo.MutationResult<ChangeStatusHospitalInfoMutation>;
export type ChangeStatusHospitalInfoMutationOptions =
  Apollo.BaseMutationOptions<
    ChangeStatusHospitalInfoMutation,
    ChangeStatusHospitalInfoMutationVariables
  >;
export const GetYakkyoku24InfoDocument = gql`
  query getYakkyoku24Info {
    getYakkyoku24Info {
      name
      postCode
      telephone1
      telephone2
      faxNumber
      address1
      address2
    }
  }
`;

/**
 * __useGetYakkyoku24InfoQuery__
 *
 * To run a query within a React component, call `useGetYakkyoku24InfoQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetYakkyoku24InfoQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetYakkyoku24InfoQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetYakkyoku24InfoQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetYakkyoku24InfoQuery,
    GetYakkyoku24InfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetYakkyoku24InfoQuery,
    GetYakkyoku24InfoQueryVariables
  >(GetYakkyoku24InfoDocument, options);
}
export function useGetYakkyoku24InfoLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetYakkyoku24InfoQuery,
    GetYakkyoku24InfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetYakkyoku24InfoQuery,
    GetYakkyoku24InfoQueryVariables
  >(GetYakkyoku24InfoDocument, options);
}
export function useGetYakkyoku24InfoSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetYakkyoku24InfoQuery,
    GetYakkyoku24InfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetYakkyoku24InfoQuery,
    GetYakkyoku24InfoQueryVariables
  >(GetYakkyoku24InfoDocument, options);
}
export type GetYakkyoku24InfoQueryHookResult = ReturnType<
  typeof useGetYakkyoku24InfoQuery
>;
export type GetYakkyoku24InfoLazyQueryHookResult = ReturnType<
  typeof useGetYakkyoku24InfoLazyQuery
>;
export type GetYakkyoku24InfoSuspenseQueryHookResult = ReturnType<
  typeof useGetYakkyoku24InfoSuspenseQuery
>;
export type GetYakkyoku24InfoQueryResult = Apollo.QueryResult<
  GetYakkyoku24InfoQuery,
  GetYakkyoku24InfoQueryVariables
>;
export const GetPublicHospitalDocument = gql`
  query getPublicHospital {
    getPublicHospital {
      insuredBranchCode
      name
      postCode
      address
      telephone
      email
      isOpenClinic
      status
    }
  }
`;

/**
 * __useGetPublicHospitalQuery__
 *
 * To run a query within a React component, call `useGetPublicHospitalQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPublicHospitalQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPublicHospitalQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetPublicHospitalQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetPublicHospitalQuery,
    GetPublicHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPublicHospitalQuery,
    GetPublicHospitalQueryVariables
  >(GetPublicHospitalDocument, options);
}
export function useGetPublicHospitalLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPublicHospitalQuery,
    GetPublicHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPublicHospitalQuery,
    GetPublicHospitalQueryVariables
  >(GetPublicHospitalDocument, options);
}
export function useGetPublicHospitalSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPublicHospitalQuery,
    GetPublicHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPublicHospitalQuery,
    GetPublicHospitalQueryVariables
  >(GetPublicHospitalDocument, options);
}
export type GetPublicHospitalQueryHookResult = ReturnType<
  typeof useGetPublicHospitalQuery
>;
export type GetPublicHospitalLazyQueryHookResult = ReturnType<
  typeof useGetPublicHospitalLazyQuery
>;
export type GetPublicHospitalSuspenseQueryHookResult = ReturnType<
  typeof useGetPublicHospitalSuspenseQuery
>;
export type GetPublicHospitalQueryResult = Apollo.QueryResult<
  GetPublicHospitalQuery,
  GetPublicHospitalQueryVariables
>;
export const GetImportDataByHpIdDocument = gql`
  query getImportDataByHpId {
    getImportDataByHpId {
      name
      address1
      address2
      postCode
      homepage
      telephone
      directorName
      stations {
        stationId
        stationDetail {
          name
          description
        }
      }
      descriptionTitle
      description
      businessTimes {
        startTime
        endTime
        monFlag
        tueFlag
        wedFlag
        thuFlag
        friFlag
        satFlag
        sunFlag
      }
      holidayDetail
      isCarpark
      carparkDetail
      tags {
        name
        tagId
      }
      examinations {
        examinationId
        name
      }
      specialists {
        specialistId
        name
      }
      payments {
        paymentTypeName
      }
    }
  }
`;

/**
 * __useGetImportDataByHpIdQuery__
 *
 * To run a query within a React component, call `useGetImportDataByHpIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetImportDataByHpIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetImportDataByHpIdQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetImportDataByHpIdQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetImportDataByHpIdQuery,
    GetImportDataByHpIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetImportDataByHpIdQuery,
    GetImportDataByHpIdQueryVariables
  >(GetImportDataByHpIdDocument, options);
}
export function useGetImportDataByHpIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetImportDataByHpIdQuery,
    GetImportDataByHpIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetImportDataByHpIdQuery,
    GetImportDataByHpIdQueryVariables
  >(GetImportDataByHpIdDocument, options);
}
export function useGetImportDataByHpIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetImportDataByHpIdQuery,
    GetImportDataByHpIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetImportDataByHpIdQuery,
    GetImportDataByHpIdQueryVariables
  >(GetImportDataByHpIdDocument, options);
}
export type GetImportDataByHpIdQueryHookResult = ReturnType<
  typeof useGetImportDataByHpIdQuery
>;
export type GetImportDataByHpIdLazyQueryHookResult = ReturnType<
  typeof useGetImportDataByHpIdLazyQuery
>;
export type GetImportDataByHpIdSuspenseQueryHookResult = ReturnType<
  typeof useGetImportDataByHpIdSuspenseQuery
>;
export type GetImportDataByHpIdQueryResult = Apollo.QueryResult<
  GetImportDataByHpIdQuery,
  GetImportDataByHpIdQueryVariables
>;
