import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type SearchTenMstItemMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsMstItemSearchTenMstItemRequestInput>;
}>;

export type SearchTenMstItemMutation = {
  __typename?: "mutation_root";
  postApiMstItemSearchTenMstItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
      totalCount?: number;
      tenMsts?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemTenItemDto";
        hpId?: number;
        itemCd?: string;
        rousaiKbn?: number;
        kanaName1?: string;
        kanaName2?: string;
        kanaName3?: string;
        kanaName4?: string;
        kanaName5?: string;
        kanaName6?: string;
        kanaName7?: string;
        name?: string;
        receName?: string;
        kohatuKbn?: number;
        madokuKbn?: number;
        kouseisinKbn?: number;
        odrUnitName?: string;
        endDate?: number;
        drugKbn?: number;
        masterSbt?: string;
        buiKbn?: number;
        isAdopted?: number;
        ten?: number;
        tenId?: number;
        kensaMstCenterItemCd1?: string;
        kensaMstCenterItemCd2?: string;
        cmtCol1?: number;
        ipnNameCd?: string;
        sinKouiKbn?: number;
        yjCd?: string;
        cnvUnitName?: string;
        startDate?: number;
        yohoKbn?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        ipnCD?: string;
        rousaiKbnDisplay?: string;
        kouseisinKbnDisplay?: string;
        kubunToDisplay?: string;
        kohatuKbnDisplay?: string;
        kensaCenterItemCDDisplay?: string;
        tenDisplay?: string;
        kouiName?: string;
        odrTermVal?: number;
        cnvTermVal?: number;
        defaultValue?: number;
        modeStatus?: number;
        ipnName?: string;
        handanGrpKbn?: number;
        isKensaMstEmpty?: boolean;
        yakka?: number;
        isGetPriceInYakka?: boolean;
        kasan1?: number;
        kasan2?: number;
        kokuji1?: string;
        kokuji2?: string;
      }>;
    };
  };
};

export type GetListTenMstOriginQueryVariables = Types.Exact<{
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetListTenMstOriginQuery = {
  __typename?: "query_root";
  getApiMstItemGetListTenMstOrigin?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetListTenMstOriginResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetListTenMstOriginResponse";
      startDateDisplay?: Array<number>;
      tenMsts?: Array<{
        __typename?: "EmrCloudApiRequestsMstItemTenMstOriginModelDto";
        ageCheck?: number;
        agekasanCd1?: string;
        agekasanCd1Note?: string;
        agekasanCd2?: string;
        agekasanCd2Note?: string;
        agekasanCd3?: string;
        agekasanCd3Note?: string;
        agekasanCd4?: string;
        agekasanCd4Note?: string;
        agekasanMax1?: string;
        agekasanMax2?: string;
        agekasanMax3?: string;
        agekasanMax4?: string;
        agekasanMin1?: string;
        agekasanMin2?: string;
        agekasanMin3?: string;
        agekasanMin4?: string;
        autoFungoKbn?: number;
        autoHougouKbn?: number;
        buiKbn?: number;
        byomeiKbn?: number;
        capacity?: number;
        cdBu?: number;
        cdEdano?: number;
        cdKbn?: string;
        cdKbnno?: number;
        cdKouno?: number;
        cdSyo?: number;
        chusyaDrugSbt?: number;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
        cmtKbn?: number;
        cnvTermVal?: number;
        cnvUnitName?: string;
        dayCount?: number;
        defaultVal?: number;
        delDate?: number;
        drugKanrenKbn?: number;
        drugKbn?: number;
        endDate?: number;
        fukubikuKotunanKasan?: number;
        fukubikuNaisiKasan?: number;
        fukuyoDaytime?: number;
        fukuyoMorning?: number;
        fukuyoNight?: number;
        fukuyoRise?: number;
        fukuyoSleep?: number;
        futekiKbn?: number;
        futekiSisetuKbn?: number;
        gairaiKanriKbn?: number;
        gazoKasan?: number;
        handanGrpKbn?: number;
        handanKbn?: number;
        hokatuKbn?: number;
        hokatuKensa?: number;
        igakukanri?: number;
        ipnNameCd?: string;
        isAddNew?: boolean;
        isAdopted?: number;
        isDeleted?: number;
        isLastItem?: boolean;
        isNodspKarte?: number;
        isNodspPaperRece?: number;
        isNodspRece?: number;
        isNodspRyosyu?: number;
        isNodspYakutai?: number;
        isNosearch?: number;
        isNotUsage?: boolean;
        isSelected?: boolean;
        isStartDateKeyUpdated?: boolean;
        isUpdated?: boolean;
        isUsage?: boolean;
        itemCd?: string;
        jihiSbt?: number;
        jituday?: number;
        jitudayCount?: number;
        kanaName1?: string;
        kanaName2?: string;
        kanaName3?: string;
        kanaName4?: string;
        kanaName5?: string;
        kanaName6?: string;
        kanaName7?: string;
        kansatuKbn?: number;
        kazeiKbn?: number;
        keibuKbn?: number;
        keikaDate?: number;
        kensaCmt?: number;
        kensaFukusuSantei?: number;
        kensaItemCd?: string;
        kensaItemSeqNo?: number;
        kensaLabel?: number;
        kizamiErr?: number;
        kizamiId?: number;
        kizamiMax?: number;
        kizamiMin?: number;
        kizamiTen?: number;
        kizamiVal?: number;
        kohatuKbn?: number;
        kohyoJun?: number;
        kokuji1?: string;
        kokuji2?: string;
        kokujiBu?: number;
        kokujiEdaNo?: number;
        kokujiKbn?: string;
        kokujiKbnNo?: number;
        kokujiKouNo?: number;
        kokujiSyo?: number;
        koukiKbn?: number;
        kouseisinKbn?: number;
        lowWeightKbn?: number;
        madokuKbn?: number;
        masterSbt?: string;
        masuiKasan?: number;
        masuiKbn?: number;
        maxAge?: string;
        maxCount?: number;
        maxCountErr?: number;
        maxPrice?: number;
        maxTen?: number;
        minAge?: string;
        moniterKasan?: number;
        name?: string;
        odrTermVal?: number;
        odrUnitName?: string;
        originStartDate?: number;
        receName?: string;
        receUnitCd?: string;
        receUnitName?: string;
        renkeiCd1?: string;
        renkeiCd2?: string;
        rousaiKbn?: number;
        ryosyuName?: string;
        saiketuKbn?: number;
        sansoKbn?: number;
        santeiItemCd?: string;
        santeigaiKbn?: number;
        seibutuKbn?: number;
        sekituiKbn?: number;
        selectCmtId?: number;
        shortstayOpe?: number;
        shotCnt?: number;
        sinKouiKbn?: number;
        sinkeiKbn?: number;
        sisetucd1?: number;
        sisetucd10?: number;
        sisetucd2?: number;
        sisetucd3?: number;
        sisetucd4?: number;
        sisetucd5?: number;
        sisetucd6?: number;
        sisetucd7?: number;
        sisetucd8?: number;
        sisetucd9?: number;
        sisiKbn?: number;
        startDate?: number;
        suryoRoundupKbn?: number;
        syohinKanren?: string;
        syotiNyuyojiKbn?: number;
        syukeiSaki?: string;
        syusaiSbt?: number;
        teigenKbn?: number;
        ten?: number;
        tenId?: number;
        tenKbnNo?: string;
        timeKasanKbn?: number;
        toketuKasan?: number;
        tokuzaiAgeKbn?: number;
        tokuzaiSbt?: number;
        tusokuAge?: number;
        tusokuTargetKbn?: number;
        tyoonpaGyokoKbn?: number;
        tyoonpaNaisiKbn?: number;
        tyuCd?: string;
        tyuSeq?: string;
        updDate?: number;
        yakkaCd?: string;
        yjCd?: string;
        yohoKbn?: number;
        zaiKbn?: number;
        zaikeiPoint?: number;
        zoueiKbn?: number;
      }>;
    };
  };
};

export type GetJihiMstListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetJihiMstListQuery = {
  __typename?: "query_root";
  getApiMstItemGetJihiMstList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetJihiMstsResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetJihiMstsResponse";
      listData?: Array<{
        __typename?: "DomainModelsMstItemJihiSbtMstModel";
        status?: number;
        jihiSbt?: number;
        hpId?: number;
        isDeleted?: number;
        isYobo?: number;
        name?: string;
        sortNo?: number;
      }>;
    };
  };
};

export type GetSetDataTenMstQueryVariables = Types.Exact<{
  agekasanCd1Note?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  agekasanCd2Note?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  agekasanCd3Note?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  agekasanCd4Note?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  ipnNameCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  jiCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  santeiItemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetSetDataTenMstQuery = {
  __typename?: "query_root";
  getApiMstItemGetSetDataTenMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetSetDataTenMstResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetSetDataTenMstResponse";
      setData?: {
        __typename?: "DomainModelsMstItemSetDataTenMstOriginModel";
        basicSettingTab?: {
          __typename?: "DomainModelsMstItemBasicSettingTabModel";
          cmtKbnMstModels?: Array<{
            __typename?: "DomainModelsMstItemCmtKbnMstModel";
            cmtKbn?: number;
            endDate?: number;
            endDateBinding?: string;
            id?: string;
            isDeleted?: boolean;
            itemCd?: string;
            startDate?: number;
            startDateBinding?: string;
          }>;
        };
        combinedContraindicationTab?: {
          __typename?: "DomainModelsMstItemCombinedContraindicationTabModel";
          combinedContraindications?: Array<{
            __typename?: "DomainModelsMstItemCombinedContraindicationModel";
            aCd?: string;
            bCd?: string;
            hpId?: number;
            id?: string;
            isAddNew?: boolean;
            isDeleted?: boolean;
            isUpdated?: boolean;
            name?: string;
            originBCd?: string;
            seqNo?: number;
          }>;
        };
        drugInfomationTab?: {
          __typename?: "DomainModelsMstItemDrugInfomationTabModel";
          drugInfs?: Array<{
            __typename?: "DomainModelsMstItemDrugInfModel";
            drugInfo?: string;
            hpId?: number;
            infKbn?: number;
            isDefaultModel?: boolean;
            isDeleted?: number;
            isModified?: boolean;
            isNewModel?: boolean;
            itemCd?: string;
            oldDrugInfo?: string;
            seqNo?: string;
          }>;
          houImage?: {
            __typename?: "DomainModelsMstItemPiImageModel";
            fileName?: string;
            hpId?: number;
            imagePath?: string;
            imageType?: number;
            isDefaultModel?: boolean;
            isDeleted?: boolean;
            isEnable?: boolean;
            isModified?: boolean;
            isNewModel?: boolean;
            itemCd?: string;
          };
          zaiImage?: {
            __typename?: "DomainModelsMstItemPiImageModel";
            fileName?: string;
            hpId?: number;
            imagePath?: string;
            imageType?: number;
            isDefaultModel?: boolean;
            isDeleted?: boolean;
            isEnable?: boolean;
            isModified?: boolean;
            isNewModel?: boolean;
            itemCd?: string;
          };
        };
        haihanTab?: {
          __typename?: "DomainModelsMstItemHaihanTabModel";
          densiHaihanModel1s?: Array<{
            __typename?: "DomainModelsMstItemDensiHaihanModel";
            endDate?: number;
            endDateBinding?: string;
            haihanKbn?: number;
            hpId?: number;
            id?: number;
            initModelType?: number;
            isAddNew?: boolean;
            isDeleted?: boolean;
            isInvalid?: number;
            isModified?: boolean;
            isReadOnly?: boolean;
            itemCd1?: string;
            itemCd2?: string;
            modelType?: number;
            name?: string;
            originItemCd2?: string;
            prevItemCd2?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            startDateBinding?: string;
            targetKbn?: number;
            termCnt?: number;
            termSbt?: number;
            termSbtName?: string;
            userSetting?: number;
          }>;
          densiHaihanModel2s?: Array<{
            __typename?: "DomainModelsMstItemDensiHaihanModel";
            endDate?: number;
            endDateBinding?: string;
            haihanKbn?: number;
            hpId?: number;
            id?: number;
            initModelType?: number;
            isAddNew?: boolean;
            isDeleted?: boolean;
            isInvalid?: number;
            isModified?: boolean;
            isReadOnly?: boolean;
            itemCd1?: string;
            itemCd2?: string;
            modelType?: number;
            name?: string;
            originItemCd2?: string;
            prevItemCd2?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            startDateBinding?: string;
            targetKbn?: number;
            termCnt?: number;
            termSbt?: number;
            termSbtName?: string;
            userSetting?: number;
          }>;
          densiHaihanModel3s?: Array<{
            __typename?: "DomainModelsMstItemDensiHaihanModel";
            endDate?: number;
            endDateBinding?: string;
            haihanKbn?: number;
            hpId?: number;
            id?: number;
            initModelType?: number;
            isAddNew?: boolean;
            isDeleted?: boolean;
            isInvalid?: number;
            isModified?: boolean;
            isReadOnly?: boolean;
            itemCd1?: string;
            itemCd2?: string;
            modelType?: number;
            name?: string;
            originItemCd2?: string;
            prevItemCd2?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            startDateBinding?: string;
            targetKbn?: number;
            termCnt?: number;
            termSbt?: number;
            termSbtName?: string;
            userSetting?: number;
          }>;
        };
        houkatsuTab?: {
          __typename?: "DomainModelsMstItemHoukatsuTabModel";
          listDensiHoukatuGrpModels?: Array<{
            __typename?: "DomainModelsMstItemDensiHoukatuGrpModel";
            canEditItem?: boolean;
            endDate?: number;
            endDateBinding?: string;
            houkatuGrpNo?: string;
            houkatuItemCd?: string;
            houkatuTerm?: number;
            houkatuTermDisplay?: string;
            hpId?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isUpdate?: boolean;
            itemCd?: string;
            name?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            startDateBinding?: string;
            targetKbn?: number;
            userSetting?: number;
          }>;
          listDensiHoukatuMaster?: Array<{
            __typename?: "DomainModelsMstItemDensiHoukatuModel";
            canEditItem?: boolean;
            endDate?: number;
            endDateBinding?: string;
            houkatuGrpItemCd?: string;
            houkatuGrpNo?: string;
            houkatuTerm?: number;
            houkatuTermDisplay?: string;
            hpId?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isInvalidBinding?: boolean;
            isModified?: boolean;
            itemCd?: string;
            name?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            startDateBinding?: string;
            targetKbn?: number;
            userSetting?: number;
            houkatuGrpSeqNo?: string;
          }>;
          listDensiHoukatuModels?: Array<{
            __typename?: "DomainModelsMstItemDensiHoukatuModel";
            canEditItem?: boolean;
            endDate?: number;
            endDateBinding?: string;
            houkatuGrpItemCd?: string;
            houkatuGrpNo?: string;
            houkatuTerm?: number;
            houkatuTermDisplay?: string;
            hpId?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isInvalidBinding?: boolean;
            isModified?: boolean;
            itemCd?: string;
            name?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            startDateBinding?: string;
            targetKbn?: number;
            userSetting?: number;
            houkatuGrpSeqNo?: string;
          }>;
        };
        ijiSettingTab?: {
          __typename?: "DomainModelsMstItemIjiSettingTabModel";
          agekasanCd1Note?: string;
          agekasanCd2Note?: string;
          agekasanCd3Note?: string;
          agekasanCd4Note?: string;
          searchItemName?: string;
        };
        precriptionSettingTab?: {
          __typename?: "DomainModelsMstItemPrecriptionSettingTabModel";
          dosageMst?: {
            __typename?: "DomainModelsMstItemDosageMstModel";
            dayLimit?: number;
            dayMax?: number;
            dayMin?: number;
            dayUnit?: number;
            hpId?: number;
            id?: number;
            isDeleted?: number;
            itemCd?: string;
            modelModified?: boolean;
            onceLimit?: number;
            onceMax?: number;
            onceMin?: number;
            onceUnit?: number;
            seqNo?: number;
          };
          drugDayLimits?: Array<{
            __typename?: "DomainModelsMstItemDrugDayLimitModel";
            endDate?: number;
            hpId?: number;
            id?: number;
            isDeleted?: number;
            itemCd?: string;
            limitDay?: number;
            modelModified?: boolean;
            seqNo?: number;
            startDate?: number;
          }>;
          ipnMinYakkaMsts?: Array<{
            __typename?: "DomainModelsOrdInfIpnMinYakkaMstModel";
            endDate?: number;
            hpId?: number;
            id?: number;
            ipnNameCd?: string;
            isDeleted?: number;
            modelModified?: boolean;
            seqNo?: number;
            startDate?: number;
            yakka?: number;
          }>;
          ipnNameMst?: {
            __typename?: "DomainModelsMstItemIpnNameMstModel";
            endDate?: number;
            hpId?: number;
            ipnName?: string;
            ipnNameCd?: string;
            ipnNameCdOrigin?: string;
            isDeleted?: number;
            modelModified?: boolean;
            seqNo?: number;
            startDate?: number;
          };
          m10DayLimits?: Array<{
            __typename?: "DomainModelsMstItemM10DayLimitModel";
            cmt?: string;
            edDate?: string;
            endDateBinding?: string;
            limitDay?: number;
            limitDayBinding?: string;
            seqNo?: number;
            stDate?: string;
            startDateBinding?: string;
            yjCd?: string;
          }>;
        };
        santeiKaishuTab?: {
          __typename?: "DomainModelsMstItemSanteiKaishuTabModel";
          densiSanteiKaisus?: Array<{
            __typename?: "DomainModelsTodayOdrDensiSanteiKaisuModel";
            endDate?: number;
            hpId?: number;
            id?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isModified?: boolean;
            itemCd?: string;
            itemGrpCd?: string;
            maxCount?: number;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            targetKbn?: number;
            termCount?: number;
            termSbt?: number;
            unitCd?: number;
            userSetting?: number;
          }>;
        };
        teikyoByomeiTab?: {
          __typename?: "DomainModelsMstItemTeikyoByomeiTabModel";
          teikyoByomeis?: Array<{
            __typename?: "DomainModelsMstItemTeikyoByomeiModel";
            byomei?: string;
            byomeiCd?: string;
            editKbn?: number;
            endYM?: number;
            endYMDisplay?: string;
            hpId?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isInvalidTokusyo?: number;
            isModified?: boolean;
            itemCd?: string;
            kanaName?: string;
            sbyomeiOrigin?: string;
            sikkanCd?: number;
            startYM?: number;
            startYMDisplay?: string;
            systemData?: number;
          }>;
          tekiouByomeiMstExcluded?: {
            __typename?: "DomainModelsMstItemTekiouByomeiMstExcludedModel";
            hpId?: number;
            isDeleted?: number;
            itemCd?: string;
            seqNo?: number;
          };
        };
        usageSettingTab?: {
          __typename?: "DomainModelsMstItemUsageSettingTabModel";
          yohoInfMstPrefix?: string;
        };
      };
    };
  };
};

export const SearchTenMstItemDocument = gql`
  mutation searchTenMstItem(
    $input: EmrCloudApiRequestsMstItemSearchTenMstItemRequestInput
  ) {
    postApiMstItemSearchTenMstItem(
      emrCloudApiRequestsMstItemSearchTenMstItemRequestInput: $input
    ) {
      data {
        totalCount
        tenMsts {
          hpId
          itemCd
          rousaiKbn
          kanaName1
          kanaName2
          kanaName3
          kanaName4
          kanaName5
          kanaName6
          kanaName7
          name
          receName
          kohatuKbn
          madokuKbn
          kouseisinKbn
          odrUnitName
          endDate
          drugKbn
          masterSbt
          buiKbn
          isAdopted
          ten
          tenId
          kensaMstCenterItemCd1
          kensaMstCenterItemCd2
          cmtCol1
          ipnNameCd
          sinKouiKbn
          yjCd
          cnvUnitName
          startDate
          yohoKbn
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
          cmtCol2
          cmtCol3
          cmtCol4
          ipnCD
          rousaiKbnDisplay
          kouseisinKbnDisplay
          kubunToDisplay
          kohatuKbnDisplay
          kensaCenterItemCDDisplay
          tenDisplay
          kouiName
          odrTermVal
          cnvTermVal
          defaultValue
          modeStatus
          ipnName
          handanGrpKbn
          isKensaMstEmpty
          yakka
          isGetPriceInYakka
          kasan1
          kasan2
          kokuji1
          kokuji2
        }
      }
      message
      status
    }
  }
`;
export type SearchTenMstItemMutationFn = Apollo.MutationFunction<
  SearchTenMstItemMutation,
  SearchTenMstItemMutationVariables
>;

/**
 * __useSearchTenMstItemMutation__
 *
 * To run a mutation, you first call `useSearchTenMstItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSearchTenMstItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [searchTenMstItemMutation, { data, loading, error }] = useSearchTenMstItemMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSearchTenMstItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SearchTenMstItemMutation,
    SearchTenMstItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SearchTenMstItemMutation,
    SearchTenMstItemMutationVariables
  >(SearchTenMstItemDocument, options);
}
export type SearchTenMstItemMutationHookResult = ReturnType<
  typeof useSearchTenMstItemMutation
>;
export type SearchTenMstItemMutationResult =
  Apollo.MutationResult<SearchTenMstItemMutation>;
export type SearchTenMstItemMutationOptions = Apollo.BaseMutationOptions<
  SearchTenMstItemMutation,
  SearchTenMstItemMutationVariables
>;
export const GetListTenMstOriginDocument = gql`
  query getListTenMstOrigin($itemCd: String) {
    getApiMstItemGetListTenMstOrigin(itemCd: $itemCd) {
      data {
        startDateDisplay
        tenMsts {
          ageCheck
          agekasanCd1
          agekasanCd1Note
          agekasanCd2
          agekasanCd2Note
          agekasanCd3
          agekasanCd3Note
          agekasanCd4
          agekasanCd4Note
          agekasanMax1
          agekasanMax2
          agekasanMax3
          agekasanMax4
          agekasanMin1
          agekasanMin2
          agekasanMin3
          agekasanMin4
          autoFungoKbn
          autoHougouKbn
          buiKbn
          byomeiKbn
          capacity
          cdBu
          cdEdano
          cdKbn
          cdKbnno
          cdKouno
          cdSyo
          chusyaDrugSbt
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
          cmtKbn
          cnvTermVal
          cnvUnitName
          dayCount
          defaultVal
          delDate
          drugKanrenKbn
          drugKbn
          endDate
          fukubikuKotunanKasan
          fukubikuNaisiKasan
          fukuyoDaytime
          fukuyoMorning
          fukuyoNight
          fukuyoRise
          fukuyoSleep
          futekiKbn
          futekiSisetuKbn
          gairaiKanriKbn
          gazoKasan
          handanGrpKbn
          handanKbn
          hokatuKbn
          hokatuKensa
          igakukanri
          ipnNameCd
          isAddNew
          isAdopted
          isDeleted
          isLastItem
          isNodspKarte
          isNodspPaperRece
          isNodspRece
          isNodspRyosyu
          isNodspYakutai
          isNosearch
          isNotUsage
          isSelected
          isStartDateKeyUpdated
          isUpdated
          isUsage
          itemCd
          jihiSbt
          jituday
          jitudayCount
          kanaName1
          kanaName2
          kanaName3
          kanaName4
          kanaName5
          kanaName6
          kanaName7
          kansatuKbn
          kazeiKbn
          keibuKbn
          keikaDate
          kensaCmt
          kensaFukusuSantei
          kensaItemCd
          kensaItemSeqNo
          kensaLabel
          kizamiErr
          kizamiId
          kizamiMax
          kizamiMin
          kizamiTen
          kizamiVal
          kohatuKbn
          kohyoJun
          kokuji1
          kokuji2
          kokujiBu
          kokujiEdaNo
          kokujiKbn
          kokujiKbnNo
          kokujiKouNo
          kokujiSyo
          koukiKbn
          kouseisinKbn
          lowWeightKbn
          madokuKbn
          masterSbt
          masuiKasan
          masuiKbn
          maxAge
          maxCount
          maxCountErr
          maxPrice
          maxTen
          minAge
          moniterKasan
          name
          odrTermVal
          odrUnitName
          originStartDate
          receName
          receUnitCd
          receUnitName
          renkeiCd1
          renkeiCd2
          rousaiKbn
          ryosyuName
          saiketuKbn
          sansoKbn
          santeiItemCd
          santeigaiKbn
          seibutuKbn
          sekituiKbn
          selectCmtId
          shortstayOpe
          shotCnt
          sinKouiKbn
          sinkeiKbn
          sisetucd1
          sisetucd10
          sisetucd2
          sisetucd3
          sisetucd4
          sisetucd5
          sisetucd6
          sisetucd7
          sisetucd8
          sisetucd9
          sisiKbn
          startDate
          suryoRoundupKbn
          syohinKanren
          syotiNyuyojiKbn
          syukeiSaki
          syusaiSbt
          teigenKbn
          ten
          tenId
          tenKbnNo
          timeKasanKbn
          toketuKasan
          tokuzaiAgeKbn
          tokuzaiSbt
          tusokuAge
          tusokuTargetKbn
          tyoonpaGyokoKbn
          tyoonpaNaisiKbn
          tyuCd
          tyuSeq
          updDate
          yakkaCd
          yjCd
          yohoKbn
          zaiKbn
          zaikeiPoint
          zoueiKbn
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetListTenMstOriginQuery__
 *
 * To run a query within a React component, call `useGetListTenMstOriginQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetListTenMstOriginQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetListTenMstOriginQuery({
 *   variables: {
 *      itemCd: // value for 'itemCd'
 *   },
 * });
 */
export function useGetListTenMstOriginQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetListTenMstOriginQuery,
    GetListTenMstOriginQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetListTenMstOriginQuery,
    GetListTenMstOriginQueryVariables
  >(GetListTenMstOriginDocument, options);
}
export function useGetListTenMstOriginLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetListTenMstOriginQuery,
    GetListTenMstOriginQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetListTenMstOriginQuery,
    GetListTenMstOriginQueryVariables
  >(GetListTenMstOriginDocument, options);
}
export function useGetListTenMstOriginSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetListTenMstOriginQuery,
    GetListTenMstOriginQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetListTenMstOriginQuery,
    GetListTenMstOriginQueryVariables
  >(GetListTenMstOriginDocument, options);
}
export type GetListTenMstOriginQueryHookResult = ReturnType<
  typeof useGetListTenMstOriginQuery
>;
export type GetListTenMstOriginLazyQueryHookResult = ReturnType<
  typeof useGetListTenMstOriginLazyQuery
>;
export type GetListTenMstOriginSuspenseQueryHookResult = ReturnType<
  typeof useGetListTenMstOriginSuspenseQuery
>;
export type GetListTenMstOriginQueryResult = Apollo.QueryResult<
  GetListTenMstOriginQuery,
  GetListTenMstOriginQueryVariables
>;
export const GetJihiMstListDocument = gql`
  query getJihiMstList {
    getApiMstItemGetJihiMstList {
      data {
        listData {
          status
          jihiSbt
          hpId
          isDeleted
          isYobo
          name
          sortNo
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetJihiMstListQuery__
 *
 * To run a query within a React component, call `useGetJihiMstListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetJihiMstListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetJihiMstListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetJihiMstListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetJihiMstListQuery,
    GetJihiMstListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetJihiMstListQuery, GetJihiMstListQueryVariables>(
    GetJihiMstListDocument,
    options,
  );
}
export function useGetJihiMstListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetJihiMstListQuery,
    GetJihiMstListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetJihiMstListQuery, GetJihiMstListQueryVariables>(
    GetJihiMstListDocument,
    options,
  );
}
export function useGetJihiMstListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetJihiMstListQuery,
    GetJihiMstListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetJihiMstListQuery,
    GetJihiMstListQueryVariables
  >(GetJihiMstListDocument, options);
}
export type GetJihiMstListQueryHookResult = ReturnType<
  typeof useGetJihiMstListQuery
>;
export type GetJihiMstListLazyQueryHookResult = ReturnType<
  typeof useGetJihiMstListLazyQuery
>;
export type GetJihiMstListSuspenseQueryHookResult = ReturnType<
  typeof useGetJihiMstListSuspenseQuery
>;
export type GetJihiMstListQueryResult = Apollo.QueryResult<
  GetJihiMstListQuery,
  GetJihiMstListQueryVariables
>;
export const GetSetDataTenMstDocument = gql`
  query getSetDataTenMst(
    $agekasanCd1Note: String
    $agekasanCd2Note: String
    $agekasanCd3Note: String
    $agekasanCd4Note: String
    $ipnNameCd: String
    $itemCd: String
    $jiCd: String
    $santeiItemCd: String
    $sinDate: Int
  ) {
    getApiMstItemGetSetDataTenMst(
      agekasanCd1Note: $agekasanCd1Note
      agekasanCd2Note: $agekasanCd2Note
      agekasanCd3Note: $agekasanCd3Note
      agekasanCd4Note: $agekasanCd4Note
      ipnNameCd: $ipnNameCd
      itemCd: $itemCd
      jiCd: $jiCd
      santeiItemCd: $santeiItemCd
      sinDate: $sinDate
    ) {
      data {
        setData {
          basicSettingTab {
            cmtKbnMstModels {
              cmtKbn
              endDate
              endDateBinding
              id
              isDeleted
              itemCd
              startDate
              startDateBinding
            }
          }
          combinedContraindicationTab {
            combinedContraindications {
              aCd
              bCd
              hpId
              id
              isAddNew
              isDeleted
              isUpdated
              name
              originBCd
              seqNo
            }
          }
          drugInfomationTab {
            drugInfs {
              drugInfo
              hpId
              infKbn
              isDefaultModel
              isDeleted
              isModified
              isNewModel
              itemCd
              oldDrugInfo
              seqNo
            }
            houImage {
              fileName
              hpId
              imagePath
              imageType
              isDefaultModel
              isDeleted
              isEnable
              isModified
              isNewModel
              itemCd
            }
            zaiImage {
              fileName
              hpId
              imagePath
              imageType
              isDefaultModel
              isDeleted
              isEnable
              isModified
              isNewModel
              itemCd
            }
          }
          haihanTab {
            densiHaihanModel1s {
              endDate
              endDateBinding
              haihanKbn
              hpId
              id
              initModelType
              isAddNew
              isDeleted
              isInvalid
              isModified
              isReadOnly
              itemCd1
              itemCd2
              modelType
              name
              originItemCd2
              prevItemCd2
              seqNo
              spJyoken
              startDate
              startDateBinding
              targetKbn
              termCnt
              termSbt
              termSbtName
              userSetting
            }
            densiHaihanModel2s {
              endDate
              endDateBinding
              haihanKbn
              hpId
              id
              initModelType
              isAddNew
              isDeleted
              isInvalid
              isModified
              isReadOnly
              itemCd1
              itemCd2
              modelType
              name
              originItemCd2
              prevItemCd2
              seqNo
              spJyoken
              startDate
              startDateBinding
              targetKbn
              termCnt
              termSbt
              termSbtName
              userSetting
            }
            densiHaihanModel3s {
              endDate
              endDateBinding
              haihanKbn
              hpId
              id
              initModelType
              isAddNew
              isDeleted
              isInvalid
              isModified
              isReadOnly
              itemCd1
              itemCd2
              modelType
              name
              originItemCd2
              prevItemCd2
              seqNo
              spJyoken
              startDate
              startDateBinding
              targetKbn
              termCnt
              termSbt
              termSbtName
              userSetting
            }
          }
          houkatsuTab {
            listDensiHoukatuGrpModels {
              canEditItem
              endDate
              endDateBinding
              houkatuGrpNo
              houkatuItemCd
              houkatuTerm
              houkatuTermDisplay
              hpId
              isDeleted
              isInvalid
              isUpdate
              itemCd
              name
              seqNo
              spJyoken
              startDate
              startDateBinding
              targetKbn
              userSetting
            }
            listDensiHoukatuMaster {
              canEditItem
              endDate
              endDateBinding
              houkatuGrpItemCd
              houkatuGrpNo
              houkatuTerm
              houkatuTermDisplay
              hpId
              isDeleted
              isInvalid
              isInvalidBinding
              isModified
              itemCd
              name
              seqNo
              spJyoken
              startDate
              startDateBinding
              targetKbn
              userSetting
              houkatuGrpSeqNo
            }
            listDensiHoukatuModels {
              canEditItem
              endDate
              endDateBinding
              houkatuGrpItemCd
              houkatuGrpNo
              houkatuTerm
              houkatuTermDisplay
              hpId
              isDeleted
              isInvalid
              isInvalidBinding
              isModified
              itemCd
              name
              seqNo
              spJyoken
              startDate
              startDateBinding
              targetKbn
              userSetting
              houkatuGrpSeqNo
            }
          }
          ijiSettingTab {
            agekasanCd1Note
            agekasanCd2Note
            agekasanCd3Note
            agekasanCd4Note
            searchItemName
          }
          precriptionSettingTab {
            dosageMst {
              dayLimit
              dayMax
              dayMin
              dayUnit
              hpId
              id
              isDeleted
              itemCd
              modelModified
              onceLimit
              onceMax
              onceMin
              onceUnit
              seqNo
            }
            drugDayLimits {
              endDate
              hpId
              id
              isDeleted
              itemCd
              limitDay
              modelModified
              seqNo
              startDate
            }
            ipnMinYakkaMsts {
              endDate
              hpId
              id
              ipnNameCd
              isDeleted
              modelModified
              seqNo
              startDate
              yakka
            }
            ipnNameMst {
              endDate
              hpId
              ipnName
              ipnNameCd
              ipnNameCdOrigin
              isDeleted
              modelModified
              seqNo
              startDate
            }
            m10DayLimits {
              cmt
              edDate
              endDateBinding
              limitDay
              limitDayBinding
              seqNo
              stDate
              startDateBinding
              yjCd
            }
          }
          santeiKaishuTab {
            densiSanteiKaisus {
              endDate
              hpId
              id
              isDeleted
              isInvalid
              isModified
              itemCd
              itemGrpCd
              maxCount
              seqNo
              spJyoken
              startDate
              targetKbn
              termCount
              termSbt
              unitCd
              userSetting
            }
          }
          teikyoByomeiTab {
            teikyoByomeis {
              byomei
              byomeiCd
              editKbn
              endYM
              endYMDisplay
              hpId
              isDeleted
              isInvalid
              isInvalidTokusyo
              isModified
              itemCd
              kanaName
              sbyomeiOrigin
              sikkanCd
              startYM
              startYMDisplay
              systemData
            }
            tekiouByomeiMstExcluded {
              hpId
              isDeleted
              itemCd
              seqNo
            }
          }
          usageSettingTab {
            yohoInfMstPrefix
          }
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetSetDataTenMstQuery__
 *
 * To run a query within a React component, call `useGetSetDataTenMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSetDataTenMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSetDataTenMstQuery({
 *   variables: {
 *      agekasanCd1Note: // value for 'agekasanCd1Note'
 *      agekasanCd2Note: // value for 'agekasanCd2Note'
 *      agekasanCd3Note: // value for 'agekasanCd3Note'
 *      agekasanCd4Note: // value for 'agekasanCd4Note'
 *      ipnNameCd: // value for 'ipnNameCd'
 *      itemCd: // value for 'itemCd'
 *      jiCd: // value for 'jiCd'
 *      santeiItemCd: // value for 'santeiItemCd'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetSetDataTenMstQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetSetDataTenMstQuery,
    GetSetDataTenMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetSetDataTenMstQuery, GetSetDataTenMstQueryVariables>(
    GetSetDataTenMstDocument,
    options,
  );
}
export function useGetSetDataTenMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSetDataTenMstQuery,
    GetSetDataTenMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSetDataTenMstQuery,
    GetSetDataTenMstQueryVariables
  >(GetSetDataTenMstDocument, options);
}
export function useGetSetDataTenMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSetDataTenMstQuery,
    GetSetDataTenMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSetDataTenMstQuery,
    GetSetDataTenMstQueryVariables
  >(GetSetDataTenMstDocument, options);
}
export type GetSetDataTenMstQueryHookResult = ReturnType<
  typeof useGetSetDataTenMstQuery
>;
export type GetSetDataTenMstLazyQueryHookResult = ReturnType<
  typeof useGetSetDataTenMstLazyQuery
>;
export type GetSetDataTenMstSuspenseQueryHookResult = ReturnType<
  typeof useGetSetDataTenMstSuspenseQuery
>;
export type GetSetDataTenMstQueryResult = Apollo.QueryResult<
  GetSetDataTenMstQuery,
  GetSetDataTenMstQueryVariables
>;
