import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiInputItemGetDrugInfQueryVariables = Types.Exact<{
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiInputItemGetDrugInfQuery = {
  __typename?: "query_root";
  getApiInputItemGetDrugInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDrugInforGetDrugInforResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesDrugInforGetDrugInforResponse";
      drugInf?: {
        __typename?: "DomainModelsDrugInforDrugInforModel";
        name?: string;
        genericName?: string;
        unit?: string;
        maker?: string;
        vender?: string;
        kohatuKbnName?: string;
        ten?: number;
        receUnitName?: string;
        mark?: string;
        pathPicHou?: string;
        listPicHou?: Array<string>;
        listPicZai?: Array<string>;
        pathPicZai?: string;
        otherPicHou?: string;
        otherPicZai?: string;
        yjCode?: string;
        customPathPicHou?: string;
        customPathPicZai?: string;
        defaultPathPicHou?: string;
        defaultPathPicZai?: string;
        kohatuKbn?: number;
        madokuKbn?: number;
        kouseisinKbn?: number;
      };
    };
  };
};

export type DrugTreeFieldsFragment = {
  __typename?: "DomainModelsDrugDetailDrugMenuItemModel";
  dbLevel?: number;
  drugMenuName?: string;
  indexOfMenuLevel?: number;
  level?: number;
  menuName?: string;
  rawDrugMenuName?: string;
  seqNo?: number;
  yjCode?: string;
};

export type GetApiInputItemGetDrugMenuTreeQueryVariables = Types.Exact<{
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiInputItemGetDrugMenuTreeQuery = {
  __typename?: "query_root";
  getApiInputItemGetDrugMenuTree?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDrugDetailGetDrugDetailResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesDrugDetailGetDrugDetailResponse";
      listData?: Array<{
        __typename?: "DomainModelsDrugDetailDrugMenuItemModel";
        dbLevel?: number;
        drugMenuName?: string;
        indexOfMenuLevel?: number;
        level?: number;
        menuName?: string;
        rawDrugMenuName?: string;
        seqNo?: number;
        yjCode?: string;
        children?: Array<{
          __typename?: "DomainModelsDrugDetailDrugMenuItemModel";
          dbLevel?: number;
          drugMenuName?: string;
          indexOfMenuLevel?: number;
          level?: number;
          menuName?: string;
          rawDrugMenuName?: string;
          seqNo?: number;
          yjCode?: string;
          children?: Array<{
            __typename?: "DomainModelsDrugDetailDrugMenuItemModel";
            dbLevel?: number;
            drugMenuName?: string;
            indexOfMenuLevel?: number;
            level?: number;
            menuName?: string;
            rawDrugMenuName?: string;
            seqNo?: number;
            yjCode?: string;
          }>;
        }>;
      }>;
    };
  };
};

export type GetApiInputItemShowMdbByomeiQueryVariables = Types.Exact<{
  drugName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  level?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  yJCode?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiInputItemShowMdbByomeiQuery = {
  __typename?: "query_root";
  getApiInputItemShowMdbByomei?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDrugDetailShowDrugDetailHtmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesDrugDetailShowDrugDetailHtmlResponse";
      htmlData?: string;
    };
  };
};

export type GetApiInputItemShowKanjaMukeQueryVariables = Types.Exact<{
  level?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  drugName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  yJCode?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiInputItemShowKanjaMukeQuery = {
  __typename?: "query_root";
  getApiInputItemShowKanjaMuke?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDrugDetailShowDrugDetailHtmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesDrugDetailShowDrugDetailHtmlResponse";
      htmlData?: string;
    };
  };
};

export type GetApiInputItemShowProductInfQueryVariables = Types.Exact<{
  drugName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  level?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  yJCode?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiInputItemShowProductInfQuery = {
  __typename?: "query_root";
  getApiInputItemShowProductInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDrugDetailShowDrugDetailHtmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesDrugDetailShowDrugDetailHtmlResponse";
      htmlData?: string;
    };
  };
};

export type GetApiPdfCreatorGetDataPrintDrugInfoQueryVariables = Types.Exact<{
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  level?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  type?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  yJCode?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiPdfCreatorGetDataPrintDrugInfoQuery = {
  __typename?: "query_root";
  getApiPdfCreatorGetDataPrintDrugInfo?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponseDataPrintDrugInfoResponse";
    data?: string;
    message?: string;
    status?: number;
  };
};

export const DrugTreeFieldsFragmentDoc = gql`
  fragment DrugTreeFields on DomainModelsDrugDetailDrugMenuItemModel {
    dbLevel
    drugMenuName
    indexOfMenuLevel
    level
    menuName
    rawDrugMenuName
    seqNo
    yjCode
  }
`;
export const GetApiInputItemGetDrugInfDocument = gql`
  query getApiInputItemGetDrugInf($itemCd: String, $sinDate: Int) {
    getApiInputItemGetDrugInf(itemCd: $itemCd, sinDate: $sinDate) {
      data {
        drugInf {
          name
          genericName
          unit
          maker
          vender
          kohatuKbnName
          ten
          receUnitName
          mark
          pathPicHou
          listPicHou
          listPicZai
          pathPicZai
          otherPicHou
          otherPicZai
          yjCode
          customPathPicHou
          customPathPicZai
          defaultPathPicHou
          defaultPathPicZai
          kohatuKbn
          madokuKbn
          kouseisinKbn
        }
      }
    }
  }
`;

/**
 * __useGetApiInputItemGetDrugInfQuery__
 *
 * To run a query within a React component, call `useGetApiInputItemGetDrugInfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiInputItemGetDrugInfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiInputItemGetDrugInfQuery({
 *   variables: {
 *      itemCd: // value for 'itemCd'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiInputItemGetDrugInfQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiInputItemGetDrugInfQuery,
    GetApiInputItemGetDrugInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiInputItemGetDrugInfQuery,
    GetApiInputItemGetDrugInfQueryVariables
  >(GetApiInputItemGetDrugInfDocument, options);
}
export function useGetApiInputItemGetDrugInfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiInputItemGetDrugInfQuery,
    GetApiInputItemGetDrugInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiInputItemGetDrugInfQuery,
    GetApiInputItemGetDrugInfQueryVariables
  >(GetApiInputItemGetDrugInfDocument, options);
}
export function useGetApiInputItemGetDrugInfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiInputItemGetDrugInfQuery,
    GetApiInputItemGetDrugInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiInputItemGetDrugInfQuery,
    GetApiInputItemGetDrugInfQueryVariables
  >(GetApiInputItemGetDrugInfDocument, options);
}
export type GetApiInputItemGetDrugInfQueryHookResult = ReturnType<
  typeof useGetApiInputItemGetDrugInfQuery
>;
export type GetApiInputItemGetDrugInfLazyQueryHookResult = ReturnType<
  typeof useGetApiInputItemGetDrugInfLazyQuery
>;
export type GetApiInputItemGetDrugInfSuspenseQueryHookResult = ReturnType<
  typeof useGetApiInputItemGetDrugInfSuspenseQuery
>;
export type GetApiInputItemGetDrugInfQueryResult = Apollo.QueryResult<
  GetApiInputItemGetDrugInfQuery,
  GetApiInputItemGetDrugInfQueryVariables
>;
export const GetApiInputItemGetDrugMenuTreeDocument = gql`
  query getApiInputItemGetDrugMenuTree($sinDate: Int, $itemCd: String) {
    getApiInputItemGetDrugMenuTree(itemCd: $itemCd, sinDate: $sinDate) {
      data {
        listData {
          ...DrugTreeFields
          children {
            ...DrugTreeFields
            children {
              ...DrugTreeFields
            }
          }
        }
      }
    }
  }
  ${DrugTreeFieldsFragmentDoc}
`;

/**
 * __useGetApiInputItemGetDrugMenuTreeQuery__
 *
 * To run a query within a React component, call `useGetApiInputItemGetDrugMenuTreeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiInputItemGetDrugMenuTreeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiInputItemGetDrugMenuTreeQuery({
 *   variables: {
 *      sinDate: // value for 'sinDate'
 *      itemCd: // value for 'itemCd'
 *   },
 * });
 */
export function useGetApiInputItemGetDrugMenuTreeQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiInputItemGetDrugMenuTreeQuery,
    GetApiInputItemGetDrugMenuTreeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiInputItemGetDrugMenuTreeQuery,
    GetApiInputItemGetDrugMenuTreeQueryVariables
  >(GetApiInputItemGetDrugMenuTreeDocument, options);
}
export function useGetApiInputItemGetDrugMenuTreeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiInputItemGetDrugMenuTreeQuery,
    GetApiInputItemGetDrugMenuTreeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiInputItemGetDrugMenuTreeQuery,
    GetApiInputItemGetDrugMenuTreeQueryVariables
  >(GetApiInputItemGetDrugMenuTreeDocument, options);
}
export function useGetApiInputItemGetDrugMenuTreeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiInputItemGetDrugMenuTreeQuery,
    GetApiInputItemGetDrugMenuTreeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiInputItemGetDrugMenuTreeQuery,
    GetApiInputItemGetDrugMenuTreeQueryVariables
  >(GetApiInputItemGetDrugMenuTreeDocument, options);
}
export type GetApiInputItemGetDrugMenuTreeQueryHookResult = ReturnType<
  typeof useGetApiInputItemGetDrugMenuTreeQuery
>;
export type GetApiInputItemGetDrugMenuTreeLazyQueryHookResult = ReturnType<
  typeof useGetApiInputItemGetDrugMenuTreeLazyQuery
>;
export type GetApiInputItemGetDrugMenuTreeSuspenseQueryHookResult = ReturnType<
  typeof useGetApiInputItemGetDrugMenuTreeSuspenseQuery
>;
export type GetApiInputItemGetDrugMenuTreeQueryResult = Apollo.QueryResult<
  GetApiInputItemGetDrugMenuTreeQuery,
  GetApiInputItemGetDrugMenuTreeQueryVariables
>;
export const GetApiInputItemShowMdbByomeiDocument = gql`
  query getApiInputItemShowMdbByomei(
    $drugName: String
    $itemCd: String
    $level: Int
    $yJCode: String
  ) {
    getApiInputItemShowMdbByomei(
      drugName: $drugName
      itemCd: $itemCd
      level: $level
      yJCode: $yJCode
    ) {
      data {
        htmlData
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiInputItemShowMdbByomeiQuery__
 *
 * To run a query within a React component, call `useGetApiInputItemShowMdbByomeiQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiInputItemShowMdbByomeiQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiInputItemShowMdbByomeiQuery({
 *   variables: {
 *      drugName: // value for 'drugName'
 *      itemCd: // value for 'itemCd'
 *      level: // value for 'level'
 *      yJCode: // value for 'yJCode'
 *   },
 * });
 */
export function useGetApiInputItemShowMdbByomeiQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiInputItemShowMdbByomeiQuery,
    GetApiInputItemShowMdbByomeiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiInputItemShowMdbByomeiQuery,
    GetApiInputItemShowMdbByomeiQueryVariables
  >(GetApiInputItemShowMdbByomeiDocument, options);
}
export function useGetApiInputItemShowMdbByomeiLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiInputItemShowMdbByomeiQuery,
    GetApiInputItemShowMdbByomeiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiInputItemShowMdbByomeiQuery,
    GetApiInputItemShowMdbByomeiQueryVariables
  >(GetApiInputItemShowMdbByomeiDocument, options);
}
export function useGetApiInputItemShowMdbByomeiSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiInputItemShowMdbByomeiQuery,
    GetApiInputItemShowMdbByomeiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiInputItemShowMdbByomeiQuery,
    GetApiInputItemShowMdbByomeiQueryVariables
  >(GetApiInputItemShowMdbByomeiDocument, options);
}
export type GetApiInputItemShowMdbByomeiQueryHookResult = ReturnType<
  typeof useGetApiInputItemShowMdbByomeiQuery
>;
export type GetApiInputItemShowMdbByomeiLazyQueryHookResult = ReturnType<
  typeof useGetApiInputItemShowMdbByomeiLazyQuery
>;
export type GetApiInputItemShowMdbByomeiSuspenseQueryHookResult = ReturnType<
  typeof useGetApiInputItemShowMdbByomeiSuspenseQuery
>;
export type GetApiInputItemShowMdbByomeiQueryResult = Apollo.QueryResult<
  GetApiInputItemShowMdbByomeiQuery,
  GetApiInputItemShowMdbByomeiQueryVariables
>;
export const GetApiInputItemShowKanjaMukeDocument = gql`
  query getApiInputItemShowKanjaMuke(
    $level: Int
    $itemCd: String
    $drugName: String
    $yJCode: String
  ) {
    getApiInputItemShowKanjaMuke(
      drugName: $drugName
      itemCd: $itemCd
      level: $level
      yJCode: $yJCode
    ) {
      data {
        htmlData
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiInputItemShowKanjaMukeQuery__
 *
 * To run a query within a React component, call `useGetApiInputItemShowKanjaMukeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiInputItemShowKanjaMukeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiInputItemShowKanjaMukeQuery({
 *   variables: {
 *      level: // value for 'level'
 *      itemCd: // value for 'itemCd'
 *      drugName: // value for 'drugName'
 *      yJCode: // value for 'yJCode'
 *   },
 * });
 */
export function useGetApiInputItemShowKanjaMukeQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiInputItemShowKanjaMukeQuery,
    GetApiInputItemShowKanjaMukeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiInputItemShowKanjaMukeQuery,
    GetApiInputItemShowKanjaMukeQueryVariables
  >(GetApiInputItemShowKanjaMukeDocument, options);
}
export function useGetApiInputItemShowKanjaMukeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiInputItemShowKanjaMukeQuery,
    GetApiInputItemShowKanjaMukeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiInputItemShowKanjaMukeQuery,
    GetApiInputItemShowKanjaMukeQueryVariables
  >(GetApiInputItemShowKanjaMukeDocument, options);
}
export function useGetApiInputItemShowKanjaMukeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiInputItemShowKanjaMukeQuery,
    GetApiInputItemShowKanjaMukeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiInputItemShowKanjaMukeQuery,
    GetApiInputItemShowKanjaMukeQueryVariables
  >(GetApiInputItemShowKanjaMukeDocument, options);
}
export type GetApiInputItemShowKanjaMukeQueryHookResult = ReturnType<
  typeof useGetApiInputItemShowKanjaMukeQuery
>;
export type GetApiInputItemShowKanjaMukeLazyQueryHookResult = ReturnType<
  typeof useGetApiInputItemShowKanjaMukeLazyQuery
>;
export type GetApiInputItemShowKanjaMukeSuspenseQueryHookResult = ReturnType<
  typeof useGetApiInputItemShowKanjaMukeSuspenseQuery
>;
export type GetApiInputItemShowKanjaMukeQueryResult = Apollo.QueryResult<
  GetApiInputItemShowKanjaMukeQuery,
  GetApiInputItemShowKanjaMukeQueryVariables
>;
export const GetApiInputItemShowProductInfDocument = gql`
  query getApiInputItemShowProductInf(
    $drugName: String
    $itemCd: String
    $level: Int
    $sinDate: Int
    $yJCode: String
  ) {
    getApiInputItemShowProductInf(
      drugName: $drugName
      itemCd: $itemCd
      level: $level
      sinDate: $sinDate
      yJCode: $yJCode
    ) {
      data {
        htmlData
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiInputItemShowProductInfQuery__
 *
 * To run a query within a React component, call `useGetApiInputItemShowProductInfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiInputItemShowProductInfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiInputItemShowProductInfQuery({
 *   variables: {
 *      drugName: // value for 'drugName'
 *      itemCd: // value for 'itemCd'
 *      level: // value for 'level'
 *      sinDate: // value for 'sinDate'
 *      yJCode: // value for 'yJCode'
 *   },
 * });
 */
export function useGetApiInputItemShowProductInfQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiInputItemShowProductInfQuery,
    GetApiInputItemShowProductInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiInputItemShowProductInfQuery,
    GetApiInputItemShowProductInfQueryVariables
  >(GetApiInputItemShowProductInfDocument, options);
}
export function useGetApiInputItemShowProductInfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiInputItemShowProductInfQuery,
    GetApiInputItemShowProductInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiInputItemShowProductInfQuery,
    GetApiInputItemShowProductInfQueryVariables
  >(GetApiInputItemShowProductInfDocument, options);
}
export function useGetApiInputItemShowProductInfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiInputItemShowProductInfQuery,
    GetApiInputItemShowProductInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiInputItemShowProductInfQuery,
    GetApiInputItemShowProductInfQueryVariables
  >(GetApiInputItemShowProductInfDocument, options);
}
export type GetApiInputItemShowProductInfQueryHookResult = ReturnType<
  typeof useGetApiInputItemShowProductInfQuery
>;
export type GetApiInputItemShowProductInfLazyQueryHookResult = ReturnType<
  typeof useGetApiInputItemShowProductInfLazyQuery
>;
export type GetApiInputItemShowProductInfSuspenseQueryHookResult = ReturnType<
  typeof useGetApiInputItemShowProductInfSuspenseQuery
>;
export type GetApiInputItemShowProductInfQueryResult = Apollo.QueryResult<
  GetApiInputItemShowProductInfQuery,
  GetApiInputItemShowProductInfQueryVariables
>;
export const GetApiPdfCreatorGetDataPrintDrugInfoDocument = gql`
  query getApiPdfCreatorGetDataPrintDrugInfo(
    $itemCd: String
    $level: Int
    $sinDate: Int
    $type: Int
    $yJCode: String
  ) {
    getApiPdfCreatorGetDataPrintDrugInfo(
      itemCd: $itemCd
      level: $level
      sinDate: $sinDate
      type: $type
      yJCode: $yJCode
    ) {
      data
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorGetDataPrintDrugInfoQuery__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorGetDataPrintDrugInfoQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorGetDataPrintDrugInfoQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorGetDataPrintDrugInfoQuery({
 *   variables: {
 *      itemCd: // value for 'itemCd'
 *      level: // value for 'level'
 *      sinDate: // value for 'sinDate'
 *      type: // value for 'type'
 *      yJCode: // value for 'yJCode'
 *   },
 * });
 */
export function useGetApiPdfCreatorGetDataPrintDrugInfoQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorGetDataPrintDrugInfoQuery,
    GetApiPdfCreatorGetDataPrintDrugInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorGetDataPrintDrugInfoQuery,
    GetApiPdfCreatorGetDataPrintDrugInfoQueryVariables
  >(GetApiPdfCreatorGetDataPrintDrugInfoDocument, options);
}
export function useGetApiPdfCreatorGetDataPrintDrugInfoLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorGetDataPrintDrugInfoQuery,
    GetApiPdfCreatorGetDataPrintDrugInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorGetDataPrintDrugInfoQuery,
    GetApiPdfCreatorGetDataPrintDrugInfoQueryVariables
  >(GetApiPdfCreatorGetDataPrintDrugInfoDocument, options);
}
export function useGetApiPdfCreatorGetDataPrintDrugInfoSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorGetDataPrintDrugInfoQuery,
    GetApiPdfCreatorGetDataPrintDrugInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorGetDataPrintDrugInfoQuery,
    GetApiPdfCreatorGetDataPrintDrugInfoQueryVariables
  >(GetApiPdfCreatorGetDataPrintDrugInfoDocument, options);
}
export type GetApiPdfCreatorGetDataPrintDrugInfoQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorGetDataPrintDrugInfoQuery
>;
export type GetApiPdfCreatorGetDataPrintDrugInfoLazyQueryHookResult =
  ReturnType<typeof useGetApiPdfCreatorGetDataPrintDrugInfoLazyQuery>;
export type GetApiPdfCreatorGetDataPrintDrugInfoSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPdfCreatorGetDataPrintDrugInfoSuspenseQuery>;
export type GetApiPdfCreatorGetDataPrintDrugInfoQueryResult =
  Apollo.QueryResult<
    GetApiPdfCreatorGetDataPrintDrugInfoQuery,
    GetApiPdfCreatorGetDataPrintDrugInfoQueryVariables
  >;
