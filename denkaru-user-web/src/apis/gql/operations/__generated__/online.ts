import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiOnlineSaveOqConfirmationMutationVariables = Types.Exact<{
  payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineSaveOqConfirmationRequestInput>;
}>;

export type PostApiOnlineSaveOqConfirmationMutation = {
  __typename?: "mutation_root";
  postApiOnlineSaveOQConfirmation?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineSaveOqConfirmationResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineSaveOQConfirmationResponse";
      successed?: boolean;
    };
  };
};

export type PostApiOnlineSaveOnlineConfirmHistoryMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineSaveOnlineConfirmationHistoryRequestInput>;
  }>;

export type PostApiOnlineSaveOnlineConfirmHistoryMutation = {
  __typename?: "mutation_root";
  postApiOnlineSaveOnlineConfirmHistory?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineSaveOnlineConfirmationHistoryResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineSaveOnlineConfirmationHistoryResponse";
      id?: string;
    };
  };
};

export type GetApiOnlineGetViewResultQueryVariables = Types.Exact<{
  batchConfirmationType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isConfirmState?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isStatus?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  receptionNo?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiOnlineGetViewResultQuery = {
  __typename?: "query_root";
  getApiOnlineGetViewResult?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineGetViewResultResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineGetViewResultResponse";
      listViewResultDto?: Array<{
        __typename?: "DomainModelsOnlineViewResultViewResultDto";
        batchConfirmationType?: number;
        beneficiaryNumber?: string;
        birthdate?: string;
        confirmationState?: number;
        id?: string;
        insuredBranchNumber?: string;
        insuredCardSymbol?: string;
        insuredIdentificationNumber?: string;
        insurerNumber?: string;
        message?: string;
        name?: string;
        nameKana?: string;
        processExecutionTime?: string;
        ptNum?: string;
        publicExpenseNumber?: string;
        status?: number;
        xmlString?: string;
        ptId?: string;
        sex?: string;
      }>;
    };
  };
};

export type PostApiOnlineOnlineViewResultUpdateConfirmMutationVariables =
  Types.Exact<{
    confirmType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    isRegisteredPatient?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    onlineDetailId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    xmlValue?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  }>;

export type PostApiOnlineOnlineViewResultUpdateConfirmMutation = {
  __typename?: "mutation_root";
  postApiOnlineOnlineViewResultUpdateConfirm?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineOnlineViewResultUpdateConfirmResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineOnlineViewResultUpdateConfirmResponse";
      result?: boolean;
      status?: number;
      onlineConfirmationHisId?: string;
    };
  };
};

export type GetApiOnlineOnlineConfirmationDetailByIdQueryVariables =
  Types.Exact<{
    id?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  }>;

export type GetApiOnlineOnlineConfirmationDetailByIdQuery = {
  __typename?: "query_root";
  getApiOnlineOnlineConfirmationDetailById?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineGetOnlineConfirmationDetailByIdResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineGetOnlineConfirmationDetailByIdResponse";
      xmlString?: string;
      xmlModel?: {
        __typename?: "DomainModelsOnlineQualificationConfirmationQcXmlMsgResponse";
        messageBody?: {
          __typename?: "DomainModelsOnlineQualificationConfirmationMessageBody";
          prescriptionIssueSelect?: string;
          processingResultCode?: string;
          processingResultMessage?: string;
          processingResultStatus?: string;
          qualificationValidity?: string;
          qualificationConfirmSearchInfo?: {
            __typename?: "DomainModelsOnlineQualificationConfirmationQualificationConfirmSearchInfo";
            arbitraryIdentifier?: string;
            birthdate?: string;
            insuredBranchNumber?: string;
            insuredCardSymbol?: string;
            insuredIdentificationNumber?: string;
            insurerNumber?: string;
            limitApplicationCertificateRelatedConsFlg?: string;
          };
          prescriptionInfo?: {
            __typename?: "DomainModelsOnlineQualificationConfirmationPrescriptionInfo";
            selectPrescriptionList?: Array<{
              __typename?: "DomainModelsOnlineQualificationConfirmationSelectPrescriptionList";
              prescriptionId?: string;
              requestPrescriptionFileName?: string;
            }>;
          };
          resultList?: {
            __typename?: "DomainModelsOnlineQualificationConfirmationResultList";
            resultOfQualificationConfirmation?: Array<{
              __typename?: "DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation";
              address?: string;
              arbitraryIdentifier?: string;
              beneficiaryNumber?: string;
              birthdate?: string;
              diagnosisInfoAvailableTime?: string;
              diagnosisInfoConsFlg?: string;
              diagnosisInfoConsTime?: string;
              disqualificationDate?: string;
              insuredBranchNumber?: string;
              insuredCardClassification?: string;
              insuredCardExpirationDate?: string;
              insuredCardSymbol?: string;
              insuredCardValidDate?: string;
              insuredCertificateIssuanceDate?: string;
              insuredIdentificationNumber?: string;
              insuredName?: string;
              insuredPartialContributionRatio?: string;
              insurerName?: string;
              insurerNumber?: string;
              limitApplicationCertificateRelatedConsFlg?: string;
              limitApplicationCertificateRelatedConsTime?: string;
              name?: string;
              nameKana?: string;
              nameOfOther?: string;
              nameOfOtherKana?: string;
              operationInfoAvailableTime?: string;
              operationInfoConsFlg?: string;
              operationInfoConsTime?: string;
              personalFamilyClassification?: string;
              pharmacistsInfoAvailableTime?: string;
              pharmacistsInfoConsFlg?: string;
              pharmacistsInfoConsTime?: string;
              postNumber?: string;
              preschoolClassification?: string;
              publicExpenseNumber?: string;
              qualificationDate?: string;
              reasonOfLoss?: string;
              referenceNumber?: string;
              sex1?: string;
              sex2?: string;
              specificDiseasesCertificateRelatedConsFlg?: string;
              specificDiseasesCertificateRelatedConsTime?: string;
              specificHealthCheckupsInfoAvailableTime?: string;
              specificHealthCheckupsInfoConsFlg?: string;
              specificHealthCheckupsInfoConsTime?: string;
              elderlyRecipientCertificateInfo?: {
                __typename?: "DomainModelsOnlineQualificationConfirmationElderlyRecipientCertificateInfo";
                elderlyRecipientCertificateDate?: string;
                elderlyRecipientContributionRatio?: string;
                elderlyRecipientValidEndDate?: string;
                elderlyRecipientValidStartDate?: string;
              };
              limitApplicationCertificateRelatedInfo?: {
                __typename?: "DomainModelsOnlineQualificationConfirmationLimitApplicationCertificateRelatedInfo";
                limitApplicationCertificateClassification?: string;
                limitApplicationCertificateClassificationFlag?: string;
                limitApplicationCertificateDate?: string;
                limitApplicationCertificateLongTermDate?: string;
                limitApplicationCertificateValidEndDate?: string;
                limitApplicationCertificateValidStartDate?: string;
              };
              publicExpenseResultList?: {
                __typename?: "DomainModelsOnlineQualificationConfirmationPublicExpenseResultList";
                medicalTicketInfo?: Array<{
                  __typename?: "DomainModelsOnlineQualificationConfirmationMedicalTicketInfo";
                  consistencyFlag?: string;
                  designatedMedicalInstitutionCode?: string;
                  designatedMedicalInstitutionFlag?: string;
                  designatedMedicalInstitutionName?: string;
                  districtContactName?: string;
                  handlingContactName?: string;
                  injuryName1?: string;
                  injuryName2?: string;
                  injuryName3?: string;
                  issueNumber?: string;
                  medicalTicketExpirationDate?: string;
                  medicalTicketValidDate?: string;
                  medicalTreatmentMonth?: string;
                  medicalTreatmentType?: string;
                  prescriptionIssuerMedicalInstitutionCode?: string;
                  prescriptionIssuerMedicalInstitutionName?: string;
                  remarks1?: string;
                  remarks2?: string;
                  remarks3?: string;
                  selfPayAmount?: string;
                  singleOrCombinedUse?: string;
                  statusOfElderlyMedicalCare?: string;
                  statusOfInfecton?: string;
                  statusOfPrefecturalExpenses?: string;
                  statusOfSocialInsurance?: string;
                  ticketType?: string;
                }>;
              };
              specificDiseasesCertificateList?: {
                __typename?: "DomainModelsOnlineQualificationConfirmationSpecificDiseasesCertificateList";
                specificDiseasesCertificateInfo?: Array<{
                  __typename?: "DomainModelsOnlineQualificationConfirmationSpecificDiseasesCertificateInfo";
                  specificDiseasesCertificateDate?: string;
                  specificDiseasesDiseaseCategory?: string;
                  specificDiseasesSelfPay?: string;
                  specificDiseasesValidEndDate?: string;
                  specificDiseasesValidStartDate?: string;
                }>;
              };
            }>;
          };
        };
        messageHeader?: {
          __typename?: "DomainModelsOnlineQualificationConfirmationMessageHeader";
          arbitraryFileIdentifier?: string;
          characterCodeIdentifier?: string;
          errorCode?: string;
          errorMessage?: string;
          medicalInstitutionCode?: string;
          processExecutionTime?: string;
          qualificationConfirmationDate?: string;
          referenceClassification?: string;
          segmentOfResult?: string;
        };
      };
    };
  };
};

export type PostApiOnlineUpdateRefNoMutationVariables = Types.Exact<{
  payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineUpdateRefNoRequestInput>;
}>;

export type PostApiOnlineUpdateRefNoMutation = {
  __typename?: "mutation_root";
  postApiOnlineUpdateRefNo?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineUpdateRefNoResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineUpdateRefNoResponse";
      refNo?: string;
    };
  };
};

export const PostApiOnlineSaveOqConfirmationDocument = gql`
  mutation postApiOnlineSaveOQConfirmation(
    $payload: EmrCloudApiRequestsOnlineSaveOqConfirmationRequestInput
  ) {
    postApiOnlineSaveOQConfirmation(
      emrCloudApiRequestsOnlineSaveOqConfirmationRequestInput: $payload
    ) {
      data {
        successed
      }
    }
  }
`;
export type PostApiOnlineSaveOqConfirmationMutationFn = Apollo.MutationFunction<
  PostApiOnlineSaveOqConfirmationMutation,
  PostApiOnlineSaveOqConfirmationMutationVariables
>;

/**
 * __usePostApiOnlineSaveOqConfirmationMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineSaveOqConfirmationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineSaveOqConfirmationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineSaveOqConfirmationMutation, { data, loading, error }] = usePostApiOnlineSaveOqConfirmationMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineSaveOqConfirmationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineSaveOqConfirmationMutation,
    PostApiOnlineSaveOqConfirmationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineSaveOqConfirmationMutation,
    PostApiOnlineSaveOqConfirmationMutationVariables
  >(PostApiOnlineSaveOqConfirmationDocument, options);
}
export type PostApiOnlineSaveOqConfirmationMutationHookResult = ReturnType<
  typeof usePostApiOnlineSaveOqConfirmationMutation
>;
export type PostApiOnlineSaveOqConfirmationMutationResult =
  Apollo.MutationResult<PostApiOnlineSaveOqConfirmationMutation>;
export type PostApiOnlineSaveOqConfirmationMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineSaveOqConfirmationMutation,
    PostApiOnlineSaveOqConfirmationMutationVariables
  >;
export const PostApiOnlineSaveOnlineConfirmHistoryDocument = gql`
  mutation postApiOnlineSaveOnlineConfirmHistory(
    $payload: EmrCloudApiRequestsOnlineSaveOnlineConfirmationHistoryRequestInput
  ) {
    postApiOnlineSaveOnlineConfirmHistory(
      emrCloudApiRequestsOnlineSaveOnlineConfirmationHistoryRequestInput: $payload
    ) {
      data {
        id
      }
    }
  }
`;
export type PostApiOnlineSaveOnlineConfirmHistoryMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineSaveOnlineConfirmHistoryMutation,
    PostApiOnlineSaveOnlineConfirmHistoryMutationVariables
  >;

/**
 * __usePostApiOnlineSaveOnlineConfirmHistoryMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineSaveOnlineConfirmHistoryMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineSaveOnlineConfirmHistoryMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineSaveOnlineConfirmHistoryMutation, { data, loading, error }] = usePostApiOnlineSaveOnlineConfirmHistoryMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineSaveOnlineConfirmHistoryMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineSaveOnlineConfirmHistoryMutation,
    PostApiOnlineSaveOnlineConfirmHistoryMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineSaveOnlineConfirmHistoryMutation,
    PostApiOnlineSaveOnlineConfirmHistoryMutationVariables
  >(PostApiOnlineSaveOnlineConfirmHistoryDocument, options);
}
export type PostApiOnlineSaveOnlineConfirmHistoryMutationHookResult =
  ReturnType<typeof usePostApiOnlineSaveOnlineConfirmHistoryMutation>;
export type PostApiOnlineSaveOnlineConfirmHistoryMutationResult =
  Apollo.MutationResult<PostApiOnlineSaveOnlineConfirmHistoryMutation>;
export type PostApiOnlineSaveOnlineConfirmHistoryMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineSaveOnlineConfirmHistoryMutation,
    PostApiOnlineSaveOnlineConfirmHistoryMutationVariables
  >;
export const GetApiOnlineGetViewResultDocument = gql`
  query getApiOnlineGetViewResult(
    $batchConfirmationType: Int
    $isConfirmState: Boolean
    $isStatus: Boolean
    $receptionNo: String
  ) {
    getApiOnlineGetViewResult(
      batchConfirmationType: $batchConfirmationType
      isConfirmState: $isConfirmState
      isStatus: $isStatus
      receptionNo: $receptionNo
    ) {
      data {
        listViewResultDto {
          batchConfirmationType
          beneficiaryNumber
          birthdate
          confirmationState
          id
          insuredBranchNumber
          insuredCardSymbol
          insuredIdentificationNumber
          insurerNumber
          message
          name
          nameKana
          processExecutionTime
          ptNum
          publicExpenseNumber
          status
          xmlString
          ptId
          sex
        }
      }
    }
  }
`;

/**
 * __useGetApiOnlineGetViewResultQuery__
 *
 * To run a query within a React component, call `useGetApiOnlineGetViewResultQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiOnlineGetViewResultQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiOnlineGetViewResultQuery({
 *   variables: {
 *      batchConfirmationType: // value for 'batchConfirmationType'
 *      isConfirmState: // value for 'isConfirmState'
 *      isStatus: // value for 'isStatus'
 *      receptionNo: // value for 'receptionNo'
 *   },
 * });
 */
export function useGetApiOnlineGetViewResultQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiOnlineGetViewResultQuery,
    GetApiOnlineGetViewResultQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiOnlineGetViewResultQuery,
    GetApiOnlineGetViewResultQueryVariables
  >(GetApiOnlineGetViewResultDocument, options);
}
export function useGetApiOnlineGetViewResultLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiOnlineGetViewResultQuery,
    GetApiOnlineGetViewResultQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiOnlineGetViewResultQuery,
    GetApiOnlineGetViewResultQueryVariables
  >(GetApiOnlineGetViewResultDocument, options);
}
export function useGetApiOnlineGetViewResultSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiOnlineGetViewResultQuery,
    GetApiOnlineGetViewResultQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiOnlineGetViewResultQuery,
    GetApiOnlineGetViewResultQueryVariables
  >(GetApiOnlineGetViewResultDocument, options);
}
export type GetApiOnlineGetViewResultQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetViewResultQuery
>;
export type GetApiOnlineGetViewResultLazyQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetViewResultLazyQuery
>;
export type GetApiOnlineGetViewResultSuspenseQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetViewResultSuspenseQuery
>;
export type GetApiOnlineGetViewResultQueryResult = Apollo.QueryResult<
  GetApiOnlineGetViewResultQuery,
  GetApiOnlineGetViewResultQueryVariables
>;
export const PostApiOnlineOnlineViewResultUpdateConfirmDocument = gql`
  mutation postApiOnlineOnlineViewResultUpdateConfirm(
    $confirmType: Int
    $isRegisteredPatient: Boolean
    $onlineDetailId: BigInt
    $xmlValue: String
    $ptId: BigInt
  ) {
    postApiOnlineOnlineViewResultUpdateConfirm(
      emrCloudApiRequestsOnlineOnlineViewResultUpdateConfirmRequestInput: {
        confirmType: $confirmType
        isRegisteredPatient: $isRegisteredPatient
        onlineDetailId: $onlineDetailId
        xmlValue: $xmlValue
        ptId: $ptId
      }
    ) {
      data {
        result
        status
        onlineConfirmationHisId
      }
    }
  }
`;
export type PostApiOnlineOnlineViewResultUpdateConfirmMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineOnlineViewResultUpdateConfirmMutation,
    PostApiOnlineOnlineViewResultUpdateConfirmMutationVariables
  >;

/**
 * __usePostApiOnlineOnlineViewResultUpdateConfirmMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineOnlineViewResultUpdateConfirmMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineOnlineViewResultUpdateConfirmMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineOnlineViewResultUpdateConfirmMutation, { data, loading, error }] = usePostApiOnlineOnlineViewResultUpdateConfirmMutation({
 *   variables: {
 *      confirmType: // value for 'confirmType'
 *      isRegisteredPatient: // value for 'isRegisteredPatient'
 *      onlineDetailId: // value for 'onlineDetailId'
 *      xmlValue: // value for 'xmlValue'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function usePostApiOnlineOnlineViewResultUpdateConfirmMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineOnlineViewResultUpdateConfirmMutation,
    PostApiOnlineOnlineViewResultUpdateConfirmMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineOnlineViewResultUpdateConfirmMutation,
    PostApiOnlineOnlineViewResultUpdateConfirmMutationVariables
  >(PostApiOnlineOnlineViewResultUpdateConfirmDocument, options);
}
export type PostApiOnlineOnlineViewResultUpdateConfirmMutationHookResult =
  ReturnType<typeof usePostApiOnlineOnlineViewResultUpdateConfirmMutation>;
export type PostApiOnlineOnlineViewResultUpdateConfirmMutationResult =
  Apollo.MutationResult<PostApiOnlineOnlineViewResultUpdateConfirmMutation>;
export type PostApiOnlineOnlineViewResultUpdateConfirmMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineOnlineViewResultUpdateConfirmMutation,
    PostApiOnlineOnlineViewResultUpdateConfirmMutationVariables
  >;
export const GetApiOnlineOnlineConfirmationDetailByIdDocument = gql`
  query getApiOnlineOnlineConfirmationDetailById($id: BigInt = "") {
    getApiOnlineOnlineConfirmationDetailById(id: $id) {
      data {
        xmlModel {
          messageBody {
            prescriptionIssueSelect
            processingResultCode
            processingResultMessage
            processingResultStatus
            qualificationValidity
            qualificationConfirmSearchInfo {
              arbitraryIdentifier
              birthdate
              insuredBranchNumber
              insuredCardSymbol
              insuredIdentificationNumber
              insurerNumber
              limitApplicationCertificateRelatedConsFlg
            }
            prescriptionInfo {
              selectPrescriptionList {
                prescriptionId
                requestPrescriptionFileName
              }
            }
            resultList {
              resultOfQualificationConfirmation {
                address
                arbitraryIdentifier
                beneficiaryNumber
                birthdate
                diagnosisInfoAvailableTime
                diagnosisInfoConsFlg
                diagnosisInfoConsTime
                disqualificationDate
                insuredBranchNumber
                insuredCardClassification
                insuredCardExpirationDate
                insuredCardSymbol
                insuredCardValidDate
                insuredCertificateIssuanceDate
                insuredIdentificationNumber
                insuredName
                insuredPartialContributionRatio
                insurerName
                insurerNumber
                limitApplicationCertificateRelatedConsFlg
                limitApplicationCertificateRelatedConsTime
                name
                nameKana
                nameOfOther
                nameOfOtherKana
                operationInfoAvailableTime
                operationInfoConsFlg
                operationInfoConsTime
                personalFamilyClassification
                pharmacistsInfoAvailableTime
                pharmacistsInfoConsFlg
                pharmacistsInfoConsTime
                postNumber
                preschoolClassification
                publicExpenseNumber
                qualificationDate
                reasonOfLoss
                referenceNumber
                sex1
                sex2
                specificDiseasesCertificateRelatedConsFlg
                specificDiseasesCertificateRelatedConsTime
                specificHealthCheckupsInfoAvailableTime
                specificHealthCheckupsInfoConsFlg
                specificHealthCheckupsInfoConsTime
                elderlyRecipientCertificateInfo {
                  elderlyRecipientCertificateDate
                  elderlyRecipientContributionRatio
                  elderlyRecipientValidEndDate
                  elderlyRecipientValidStartDate
                }
                limitApplicationCertificateRelatedInfo {
                  limitApplicationCertificateClassification
                  limitApplicationCertificateClassificationFlag
                  limitApplicationCertificateDate
                  limitApplicationCertificateLongTermDate
                  limitApplicationCertificateValidEndDate
                  limitApplicationCertificateValidStartDate
                }
                publicExpenseResultList {
                  medicalTicketInfo {
                    consistencyFlag
                    designatedMedicalInstitutionCode
                    designatedMedicalInstitutionFlag
                    designatedMedicalInstitutionName
                    districtContactName
                    handlingContactName
                    injuryName1
                    injuryName2
                    injuryName3
                    issueNumber
                    medicalTicketExpirationDate
                    medicalTicketValidDate
                    medicalTreatmentMonth
                    medicalTreatmentType
                    prescriptionIssuerMedicalInstitutionCode
                    prescriptionIssuerMedicalInstitutionName
                    remarks1
                    remarks2
                    remarks3
                    selfPayAmount
                    singleOrCombinedUse
                    statusOfElderlyMedicalCare
                    statusOfInfecton
                    statusOfPrefecturalExpenses
                    statusOfSocialInsurance
                    ticketType
                  }
                }
                specificDiseasesCertificateList {
                  specificDiseasesCertificateInfo {
                    specificDiseasesCertificateDate
                    specificDiseasesDiseaseCategory
                    specificDiseasesSelfPay
                    specificDiseasesValidEndDate
                    specificDiseasesValidStartDate
                  }
                }
              }
            }
          }
          messageHeader {
            arbitraryFileIdentifier
            characterCodeIdentifier
            errorCode
            errorMessage
            medicalInstitutionCode
            processExecutionTime
            qualificationConfirmationDate
            referenceClassification
            segmentOfResult
          }
        }
        xmlString
      }
    }
  }
`;

/**
 * __useGetApiOnlineOnlineConfirmationDetailByIdQuery__
 *
 * To run a query within a React component, call `useGetApiOnlineOnlineConfirmationDetailByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiOnlineOnlineConfirmationDetailByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiOnlineOnlineConfirmationDetailByIdQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetApiOnlineOnlineConfirmationDetailByIdQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiOnlineOnlineConfirmationDetailByIdQuery,
    GetApiOnlineOnlineConfirmationDetailByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiOnlineOnlineConfirmationDetailByIdQuery,
    GetApiOnlineOnlineConfirmationDetailByIdQueryVariables
  >(GetApiOnlineOnlineConfirmationDetailByIdDocument, options);
}
export function useGetApiOnlineOnlineConfirmationDetailByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiOnlineOnlineConfirmationDetailByIdQuery,
    GetApiOnlineOnlineConfirmationDetailByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiOnlineOnlineConfirmationDetailByIdQuery,
    GetApiOnlineOnlineConfirmationDetailByIdQueryVariables
  >(GetApiOnlineOnlineConfirmationDetailByIdDocument, options);
}
export function useGetApiOnlineOnlineConfirmationDetailByIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiOnlineOnlineConfirmationDetailByIdQuery,
    GetApiOnlineOnlineConfirmationDetailByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiOnlineOnlineConfirmationDetailByIdQuery,
    GetApiOnlineOnlineConfirmationDetailByIdQueryVariables
  >(GetApiOnlineOnlineConfirmationDetailByIdDocument, options);
}
export type GetApiOnlineOnlineConfirmationDetailByIdQueryHookResult =
  ReturnType<typeof useGetApiOnlineOnlineConfirmationDetailByIdQuery>;
export type GetApiOnlineOnlineConfirmationDetailByIdLazyQueryHookResult =
  ReturnType<typeof useGetApiOnlineOnlineConfirmationDetailByIdLazyQuery>;
export type GetApiOnlineOnlineConfirmationDetailByIdSuspenseQueryHookResult =
  ReturnType<typeof useGetApiOnlineOnlineConfirmationDetailByIdSuspenseQuery>;
export type GetApiOnlineOnlineConfirmationDetailByIdQueryResult =
  Apollo.QueryResult<
    GetApiOnlineOnlineConfirmationDetailByIdQuery,
    GetApiOnlineOnlineConfirmationDetailByIdQueryVariables
  >;
export const PostApiOnlineUpdateRefNoDocument = gql`
  mutation postApiOnlineUpdateRefNo(
    $payload: EmrCloudApiRequestsOnlineUpdateRefNoRequestInput
  ) {
    postApiOnlineUpdateRefNo(
      emrCloudApiRequestsOnlineUpdateRefNoRequestInput: $payload
    ) {
      data {
        refNo
      }
    }
  }
`;
export type PostApiOnlineUpdateRefNoMutationFn = Apollo.MutationFunction<
  PostApiOnlineUpdateRefNoMutation,
  PostApiOnlineUpdateRefNoMutationVariables
>;

/**
 * __usePostApiOnlineUpdateRefNoMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineUpdateRefNoMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineUpdateRefNoMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineUpdateRefNoMutation, { data, loading, error }] = usePostApiOnlineUpdateRefNoMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineUpdateRefNoMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineUpdateRefNoMutation,
    PostApiOnlineUpdateRefNoMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineUpdateRefNoMutation,
    PostApiOnlineUpdateRefNoMutationVariables
  >(PostApiOnlineUpdateRefNoDocument, options);
}
export type PostApiOnlineUpdateRefNoMutationHookResult = ReturnType<
  typeof usePostApiOnlineUpdateRefNoMutation
>;
export type PostApiOnlineUpdateRefNoMutationResult =
  Apollo.MutationResult<PostApiOnlineUpdateRefNoMutation>;
export type PostApiOnlineUpdateRefNoMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineUpdateRefNoMutation,
    PostApiOnlineUpdateRefNoMutationVariables
  >;
