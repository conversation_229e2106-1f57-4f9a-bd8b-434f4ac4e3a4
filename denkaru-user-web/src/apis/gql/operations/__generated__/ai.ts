import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GenerateDataFromAiSubscriptionVariables = Types.Exact<{
  contentInput: Types.Scalars["String"]["input"];
  prompt: Types.Scalars["String"]["input"];
  llmModelId: Types.Scalars["Int"]["input"];
  needCheckNouns: Types.Scalars["Boolean"]["input"];
}>;

export type GenerateDataFromAiSubscription = {
  __typename?: "subscription_root";
  generateDataFromAI: {
    __typename?: "AIReport";
    rawData?: string;
    latency: string;
    isFinished: boolean;
  };
};

export type GenerateDataFromAi_MutationVariables = Types.Exact<{
  contentInput: Types.Scalars["String"]["input"];
  prompt: Types.Scalars["String"]["input"];
  llmModelId: Types.Scalars["Int"]["input"];
  needCheckNouns: Types.Scalars["Boolean"]["input"];
}>;

export type GenerateDataFromAi_Mutation = {
  __typename?: "mutation_root";
  generateDataFromAI: {
    __typename?: "AIReport";
    rawData?: string;
    latency: string;
    isFinished: boolean;
  };
};

export type TranscribeMutationVariables = Types.Exact<{
  audio: Types.Scalars["String"]["input"];
  initialPrompt: Types.Scalars["String"]["input"];
  aiType: Types.ApiType;
}>;

export type TranscribeMutation = {
  __typename?: "mutation_root";
  transcribe: { __typename?: "TranscribeResult"; transcription: string };
};

export type GetPromptSettingQueryVariables = Types.Exact<{
  promptName: Types.Scalars["String"]["input"];
}>;

export type GetPromptSettingQuery = {
  __typename?: "query_root";
  getPromptSetting: {
    __typename?: "PromptSetting";
    prompt_name: string;
    llm_model: number;
    prompt: string;
  };
};

export type GetLlmModelQueryVariables = Types.Exact<{ [key: string]: never }>;

export type GetLlmModelQuery = {
  __typename?: "query_root";
  getLLMModel: Array<{
    __typename?: "LLMModelResponse";
    id: number;
    name: string;
  }>;
};

export type GetAiAssistPromptsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetAiAssistPromptsQuery = {
  __typename?: "query_root";
  getAIAssistPrompts: Array<{
    __typename?: "AIAssistPrompt";
    promptId: number;
    name: string;
    title: string;
    prompt: string;
    isLatest: boolean;
    isActive: boolean;
    canEdit: boolean;
    orderValue: number;
    basePromptId?: number;
  }>;
};

export type CreateAiAssistPromptMutationVariables = Types.Exact<{
  input: Types.CreatePromptInput;
}>;

export type CreateAiAssistPromptMutation = {
  __typename?: "mutation_root";
  createAIAssistPrompt: boolean;
};

export type EditAiAssistPromptMutationVariables = Types.Exact<{
  input: Types.EditPromptInput;
}>;

export type EditAiAssistPromptMutation = {
  __typename?: "mutation_root";
  editAIAssistPrompt: boolean;
};

export type DeleteAiAssistPromptMutationVariables = Types.Exact<{
  input: Types.DeletePromptInput;
}>;

export type DeleteAiAssistPromptMutation = {
  __typename?: "mutation_root";
  deleteAIAssistPrompt: boolean;
};

export type SortAiAssistPromptMutationVariables = Types.Exact<{
  input: Types.SortPromptInput;
}>;

export type SortAiAssistPromptMutation = {
  __typename?: "mutation_root";
  sortAIAssistPrompt: boolean;
};

export const GenerateDataFromAiDocument = gql`
  subscription generateDataFromAI(
    $contentInput: String!
    $prompt: String!
    $llmModelId: Int!
    $needCheckNouns: Boolean!
  ) {
    generateDataFromAI(
      contentInput: $contentInput
      prompt: $prompt
      llmModelId: $llmModelId
      needCheckNouns: $needCheckNouns
    ) {
      rawData
      latency
      isFinished
    }
  }
`;

/**
 * __useGenerateDataFromAiSubscription__
 *
 * To run a query within a React component, call `useGenerateDataFromAiSubscription` and pass it any options that fit your needs.
 * When your component renders, `useGenerateDataFromAiSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGenerateDataFromAiSubscription({
 *   variables: {
 *      contentInput: // value for 'contentInput'
 *      prompt: // value for 'prompt'
 *      llmModelId: // value for 'llmModelId'
 *      needCheckNouns: // value for 'needCheckNouns'
 *   },
 * });
 */
export function useGenerateDataFromAiSubscription(
  baseOptions: Apollo.SubscriptionHookOptions<
    GenerateDataFromAiSubscription,
    GenerateDataFromAiSubscriptionVariables
  > &
    (
      | { variables: GenerateDataFromAiSubscriptionVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSubscription<
    GenerateDataFromAiSubscription,
    GenerateDataFromAiSubscriptionVariables
  >(GenerateDataFromAiDocument, options);
}
export type GenerateDataFromAiSubscriptionHookResult = ReturnType<
  typeof useGenerateDataFromAiSubscription
>;
export type GenerateDataFromAiSubscriptionResult =
  Apollo.SubscriptionResult<GenerateDataFromAiSubscription>;
export const GenerateDataFromAi_Document = gql`
  mutation generateDataFromAI_(
    $contentInput: String!
    $prompt: String!
    $llmModelId: Int!
    $needCheckNouns: Boolean!
  ) {
    generateDataFromAI(
      contentInput: $contentInput
      prompt: $prompt
      llmModelId: $llmModelId
      needCheckNouns: $needCheckNouns
    ) {
      rawData
      latency
      isFinished
    }
  }
`;
export type GenerateDataFromAi_MutationFn = Apollo.MutationFunction<
  GenerateDataFromAi_Mutation,
  GenerateDataFromAi_MutationVariables
>;

/**
 * __useGenerateDataFromAi_Mutation__
 *
 * To run a mutation, you first call `useGenerateDataFromAi_Mutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useGenerateDataFromAi_Mutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [generateDataFromAiMutation, { data, loading, error }] = useGenerateDataFromAi_Mutation({
 *   variables: {
 *      contentInput: // value for 'contentInput'
 *      prompt: // value for 'prompt'
 *      llmModelId: // value for 'llmModelId'
 *      needCheckNouns: // value for 'needCheckNouns'
 *   },
 * });
 */
export function useGenerateDataFromAi_Mutation(
  baseOptions?: Apollo.MutationHookOptions<
    GenerateDataFromAi_Mutation,
    GenerateDataFromAi_MutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    GenerateDataFromAi_Mutation,
    GenerateDataFromAi_MutationVariables
  >(GenerateDataFromAi_Document, options);
}
export type GenerateDataFromAi_MutationHookResult = ReturnType<
  typeof useGenerateDataFromAi_Mutation
>;
export type GenerateDataFromAi_MutationResult =
  Apollo.MutationResult<GenerateDataFromAi_Mutation>;
export type GenerateDataFromAi_MutationOptions = Apollo.BaseMutationOptions<
  GenerateDataFromAi_Mutation,
  GenerateDataFromAi_MutationVariables
>;
export const TranscribeDocument = gql`
  mutation transcribe(
    $audio: String!
    $initialPrompt: String!
    $aiType: ApiType!
  ) {
    transcribe(audio: $audio, initial_prompt: $initialPrompt, aiType: $aiType) {
      transcription
    }
  }
`;
export type TranscribeMutationFn = Apollo.MutationFunction<
  TranscribeMutation,
  TranscribeMutationVariables
>;

/**
 * __useTranscribeMutation__
 *
 * To run a mutation, you first call `useTranscribeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useTranscribeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [transcribeMutation, { data, loading, error }] = useTranscribeMutation({
 *   variables: {
 *      audio: // value for 'audio'
 *      initialPrompt: // value for 'initialPrompt'
 *      aiType: // value for 'aiType'
 *   },
 * });
 */
export function useTranscribeMutation(
  baseOptions?: Apollo.MutationHookOptions<
    TranscribeMutation,
    TranscribeMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<TranscribeMutation, TranscribeMutationVariables>(
    TranscribeDocument,
    options,
  );
}
export type TranscribeMutationHookResult = ReturnType<
  typeof useTranscribeMutation
>;
export type TranscribeMutationResult =
  Apollo.MutationResult<TranscribeMutation>;
export type TranscribeMutationOptions = Apollo.BaseMutationOptions<
  TranscribeMutation,
  TranscribeMutationVariables
>;
export const GetPromptSettingDocument = gql`
  query getPromptSetting($promptName: String!) {
    getPromptSetting(promptName: $promptName) {
      prompt_name
      llm_model
      prompt
    }
  }
`;

/**
 * __useGetPromptSettingQuery__
 *
 * To run a query within a React component, call `useGetPromptSettingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPromptSettingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPromptSettingQuery({
 *   variables: {
 *      promptName: // value for 'promptName'
 *   },
 * });
 */
export function useGetPromptSettingQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPromptSettingQuery,
    GetPromptSettingQueryVariables
  > &
    (
      | { variables: GetPromptSettingQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetPromptSettingQuery, GetPromptSettingQueryVariables>(
    GetPromptSettingDocument,
    options,
  );
}
export function useGetPromptSettingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPromptSettingQuery,
    GetPromptSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPromptSettingQuery,
    GetPromptSettingQueryVariables
  >(GetPromptSettingDocument, options);
}
export function useGetPromptSettingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPromptSettingQuery,
    GetPromptSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPromptSettingQuery,
    GetPromptSettingQueryVariables
  >(GetPromptSettingDocument, options);
}
export type GetPromptSettingQueryHookResult = ReturnType<
  typeof useGetPromptSettingQuery
>;
export type GetPromptSettingLazyQueryHookResult = ReturnType<
  typeof useGetPromptSettingLazyQuery
>;
export type GetPromptSettingSuspenseQueryHookResult = ReturnType<
  typeof useGetPromptSettingSuspenseQuery
>;
export type GetPromptSettingQueryResult = Apollo.QueryResult<
  GetPromptSettingQuery,
  GetPromptSettingQueryVariables
>;
export const GetLlmModelDocument = gql`
  query getLLMModel {
    getLLMModel {
      id
      name
    }
  }
`;

/**
 * __useGetLlmModelQuery__
 *
 * To run a query within a React component, call `useGetLlmModelQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLlmModelQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLlmModelQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetLlmModelQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetLlmModelQuery,
    GetLlmModelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetLlmModelQuery, GetLlmModelQueryVariables>(
    GetLlmModelDocument,
    options,
  );
}
export function useGetLlmModelLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetLlmModelQuery,
    GetLlmModelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetLlmModelQuery, GetLlmModelQueryVariables>(
    GetLlmModelDocument,
    options,
  );
}
export function useGetLlmModelSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetLlmModelQuery,
    GetLlmModelQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<GetLlmModelQuery, GetLlmModelQueryVariables>(
    GetLlmModelDocument,
    options,
  );
}
export type GetLlmModelQueryHookResult = ReturnType<typeof useGetLlmModelQuery>;
export type GetLlmModelLazyQueryHookResult = ReturnType<
  typeof useGetLlmModelLazyQuery
>;
export type GetLlmModelSuspenseQueryHookResult = ReturnType<
  typeof useGetLlmModelSuspenseQuery
>;
export type GetLlmModelQueryResult = Apollo.QueryResult<
  GetLlmModelQuery,
  GetLlmModelQueryVariables
>;
export const GetAiAssistPromptsDocument = gql`
  query getAIAssistPrompts {
    getAIAssistPrompts {
      promptId
      name
      title
      prompt
      isLatest
      isActive
      canEdit
      orderValue
      basePromptId
    }
  }
`;

/**
 * __useGetAiAssistPromptsQuery__
 *
 * To run a query within a React component, call `useGetAiAssistPromptsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAiAssistPromptsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAiAssistPromptsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetAiAssistPromptsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetAiAssistPromptsQuery,
    GetAiAssistPromptsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetAiAssistPromptsQuery,
    GetAiAssistPromptsQueryVariables
  >(GetAiAssistPromptsDocument, options);
}
export function useGetAiAssistPromptsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetAiAssistPromptsQuery,
    GetAiAssistPromptsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetAiAssistPromptsQuery,
    GetAiAssistPromptsQueryVariables
  >(GetAiAssistPromptsDocument, options);
}
export function useGetAiAssistPromptsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetAiAssistPromptsQuery,
    GetAiAssistPromptsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetAiAssistPromptsQuery,
    GetAiAssistPromptsQueryVariables
  >(GetAiAssistPromptsDocument, options);
}
export type GetAiAssistPromptsQueryHookResult = ReturnType<
  typeof useGetAiAssistPromptsQuery
>;
export type GetAiAssistPromptsLazyQueryHookResult = ReturnType<
  typeof useGetAiAssistPromptsLazyQuery
>;
export type GetAiAssistPromptsSuspenseQueryHookResult = ReturnType<
  typeof useGetAiAssistPromptsSuspenseQuery
>;
export type GetAiAssistPromptsQueryResult = Apollo.QueryResult<
  GetAiAssistPromptsQuery,
  GetAiAssistPromptsQueryVariables
>;
export const CreateAiAssistPromptDocument = gql`
  mutation createAIAssistPrompt($input: CreatePromptInput!) {
    createAIAssistPrompt(input: $input)
  }
`;
export type CreateAiAssistPromptMutationFn = Apollo.MutationFunction<
  CreateAiAssistPromptMutation,
  CreateAiAssistPromptMutationVariables
>;

/**
 * __useCreateAiAssistPromptMutation__
 *
 * To run a mutation, you first call `useCreateAiAssistPromptMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateAiAssistPromptMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createAiAssistPromptMutation, { data, loading, error }] = useCreateAiAssistPromptMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateAiAssistPromptMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateAiAssistPromptMutation,
    CreateAiAssistPromptMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateAiAssistPromptMutation,
    CreateAiAssistPromptMutationVariables
  >(CreateAiAssistPromptDocument, options);
}
export type CreateAiAssistPromptMutationHookResult = ReturnType<
  typeof useCreateAiAssistPromptMutation
>;
export type CreateAiAssistPromptMutationResult =
  Apollo.MutationResult<CreateAiAssistPromptMutation>;
export type CreateAiAssistPromptMutationOptions = Apollo.BaseMutationOptions<
  CreateAiAssistPromptMutation,
  CreateAiAssistPromptMutationVariables
>;
export const EditAiAssistPromptDocument = gql`
  mutation editAIAssistPrompt($input: EditPromptInput!) {
    editAIAssistPrompt(input: $input)
  }
`;
export type EditAiAssistPromptMutationFn = Apollo.MutationFunction<
  EditAiAssistPromptMutation,
  EditAiAssistPromptMutationVariables
>;

/**
 * __useEditAiAssistPromptMutation__
 *
 * To run a mutation, you first call `useEditAiAssistPromptMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditAiAssistPromptMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editAiAssistPromptMutation, { data, loading, error }] = useEditAiAssistPromptMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useEditAiAssistPromptMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditAiAssistPromptMutation,
    EditAiAssistPromptMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditAiAssistPromptMutation,
    EditAiAssistPromptMutationVariables
  >(EditAiAssistPromptDocument, options);
}
export type EditAiAssistPromptMutationHookResult = ReturnType<
  typeof useEditAiAssistPromptMutation
>;
export type EditAiAssistPromptMutationResult =
  Apollo.MutationResult<EditAiAssistPromptMutation>;
export type EditAiAssistPromptMutationOptions = Apollo.BaseMutationOptions<
  EditAiAssistPromptMutation,
  EditAiAssistPromptMutationVariables
>;
export const DeleteAiAssistPromptDocument = gql`
  mutation deleteAIAssistPrompt($input: DeletePromptInput!) {
    deleteAIAssistPrompt(input: $input)
  }
`;
export type DeleteAiAssistPromptMutationFn = Apollo.MutationFunction<
  DeleteAiAssistPromptMutation,
  DeleteAiAssistPromptMutationVariables
>;

/**
 * __useDeleteAiAssistPromptMutation__
 *
 * To run a mutation, you first call `useDeleteAiAssistPromptMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteAiAssistPromptMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteAiAssistPromptMutation, { data, loading, error }] = useDeleteAiAssistPromptMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useDeleteAiAssistPromptMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteAiAssistPromptMutation,
    DeleteAiAssistPromptMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteAiAssistPromptMutation,
    DeleteAiAssistPromptMutationVariables
  >(DeleteAiAssistPromptDocument, options);
}
export type DeleteAiAssistPromptMutationHookResult = ReturnType<
  typeof useDeleteAiAssistPromptMutation
>;
export type DeleteAiAssistPromptMutationResult =
  Apollo.MutationResult<DeleteAiAssistPromptMutation>;
export type DeleteAiAssistPromptMutationOptions = Apollo.BaseMutationOptions<
  DeleteAiAssistPromptMutation,
  DeleteAiAssistPromptMutationVariables
>;
export const SortAiAssistPromptDocument = gql`
  mutation sortAIAssistPrompt($input: SortPromptInput!) {
    sortAIAssistPrompt(input: $input)
  }
`;
export type SortAiAssistPromptMutationFn = Apollo.MutationFunction<
  SortAiAssistPromptMutation,
  SortAiAssistPromptMutationVariables
>;

/**
 * __useSortAiAssistPromptMutation__
 *
 * To run a mutation, you first call `useSortAiAssistPromptMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSortAiAssistPromptMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sortAiAssistPromptMutation, { data, loading, error }] = useSortAiAssistPromptMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSortAiAssistPromptMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SortAiAssistPromptMutation,
    SortAiAssistPromptMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SortAiAssistPromptMutation,
    SortAiAssistPromptMutationVariables
  >(SortAiAssistPromptDocument, options);
}
export type SortAiAssistPromptMutationHookResult = ReturnType<
  typeof useSortAiAssistPromptMutation
>;
export type SortAiAssistPromptMutationResult =
  Apollo.MutationResult<SortAiAssistPromptMutation>;
export type SortAiAssistPromptMutationOptions = Apollo.BaseMutationOptions<
  SortAiAssistPromptMutation,
  SortAiAssistPromptMutationVariables
>;
