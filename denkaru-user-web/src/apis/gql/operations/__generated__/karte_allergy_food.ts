import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type SaveKarteAllergyFoodMutationVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  alrgyKbn: Types.Scalars["String"]["input"];
  cmt: Types.Scalars["String"]["input"];
  endDate: Types.Scalars["Int"]["input"];
  foodName: Types.Scalars["String"]["input"];
  startDate: Types.Scalars["Int"]["input"];
  isDeleted: Types.Scalars["Int"]["input"];
  seqNo: Types.Scalars["Int"]["input"];
}>;

export type SaveKarteAllergyFoodMutation = {
  __typename?: "mutation_root";
  postApiKarteAllergySaveKarteAllergyFood?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteAllergySaveKarteAllergyFoodResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteAllergySaveKarteAllergyFoodResponse";
      status?: number;
    };
  };
};

export type GetFoodAllergyDataQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetFoodAllergyDataQuery = {
  __typename?: "query_root";
  getApiMstItemGetFoodAlrgy?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetFoodAlrgyMasterDataResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetFoodAlrgyMasterDataResponse";
      foodAlrgyKbnModels?: Array<{
        __typename?: "DomainModelsMstItemFoodAlrgyKbnModel";
        foodKbn?: string;
        foodName?: string;
        isDrugAdditives?: boolean;
      }>;
    };
  };
};

export const SaveKarteAllergyFoodDocument = gql`
  mutation saveKarteAllergyFood(
    $ptId: BigInt!
    $alrgyKbn: String!
    $cmt: String!
    $endDate: Int!
    $foodName: String!
    $startDate: Int!
    $isDeleted: Int!
    $seqNo: Int!
  ) {
    postApiKarteAllergySaveKarteAllergyFood(
      emrCloudApiRequestsKarteAllergySaveKarteAllergyFoodRequestInput: {
        saveKarteAllergyFoodInputItems: {
          alrgyKbn: $alrgyKbn
          cmt: $cmt
          endDate: $endDate
          foodName: $foodName
          startDate: $startDate
          ptId: $ptId
          isDeleted: $isDeleted
          seqNo: $seqNo
        }
        ptId: $ptId
      }
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type SaveKarteAllergyFoodMutationFn = Apollo.MutationFunction<
  SaveKarteAllergyFoodMutation,
  SaveKarteAllergyFoodMutationVariables
>;

/**
 * __useSaveKarteAllergyFoodMutation__
 *
 * To run a mutation, you first call `useSaveKarteAllergyFoodMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveKarteAllergyFoodMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveKarteAllergyFoodMutation, { data, loading, error }] = useSaveKarteAllergyFoodMutation({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      alrgyKbn: // value for 'alrgyKbn'
 *      cmt: // value for 'cmt'
 *      endDate: // value for 'endDate'
 *      foodName: // value for 'foodName'
 *      startDate: // value for 'startDate'
 *      isDeleted: // value for 'isDeleted'
 *      seqNo: // value for 'seqNo'
 *   },
 * });
 */
export function useSaveKarteAllergyFoodMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SaveKarteAllergyFoodMutation,
    SaveKarteAllergyFoodMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SaveKarteAllergyFoodMutation,
    SaveKarteAllergyFoodMutationVariables
  >(SaveKarteAllergyFoodDocument, options);
}
export type SaveKarteAllergyFoodMutationHookResult = ReturnType<
  typeof useSaveKarteAllergyFoodMutation
>;
export type SaveKarteAllergyFoodMutationResult =
  Apollo.MutationResult<SaveKarteAllergyFoodMutation>;
export type SaveKarteAllergyFoodMutationOptions = Apollo.BaseMutationOptions<
  SaveKarteAllergyFoodMutation,
  SaveKarteAllergyFoodMutationVariables
>;
export const GetFoodAllergyDataDocument = gql`
  query getFoodAllergyData {
    getApiMstItemGetFoodAlrgy {
      data {
        foodAlrgyKbnModels {
          foodKbn
          foodName
          isDrugAdditives
        }
      }
    }
  }
`;

/**
 * __useGetFoodAllergyDataQuery__
 *
 * To run a query within a React component, call `useGetFoodAllergyDataQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetFoodAllergyDataQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetFoodAllergyDataQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetFoodAllergyDataQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetFoodAllergyDataQuery,
    GetFoodAllergyDataQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetFoodAllergyDataQuery,
    GetFoodAllergyDataQueryVariables
  >(GetFoodAllergyDataDocument, options);
}
export function useGetFoodAllergyDataLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetFoodAllergyDataQuery,
    GetFoodAllergyDataQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetFoodAllergyDataQuery,
    GetFoodAllergyDataQueryVariables
  >(GetFoodAllergyDataDocument, options);
}
export function useGetFoodAllergyDataSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetFoodAllergyDataQuery,
    GetFoodAllergyDataQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetFoodAllergyDataQuery,
    GetFoodAllergyDataQueryVariables
  >(GetFoodAllergyDataDocument, options);
}
export type GetFoodAllergyDataQueryHookResult = ReturnType<
  typeof useGetFoodAllergyDataQuery
>;
export type GetFoodAllergyDataLazyQueryHookResult = ReturnType<
  typeof useGetFoodAllergyDataLazyQuery
>;
export type GetFoodAllergyDataSuspenseQueryHookResult = ReturnType<
  typeof useGetFoodAllergyDataSuspenseQuery
>;
export type GetFoodAllergyDataQueryResult = Apollo.QueryResult<
  GetFoodAllergyDataQuery,
  GetFoodAllergyDataQueryVariables
>;
