import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetPrefecturesQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetPrefecturesQuery = {
  __typename?: "query_root";
  getPrefectures: Array<{
    __typename?: "Prefecture";
    name: string;
    prefectureId: number;
  }>;
};

export type SearchAddressByPostcodeQueryVariables = Types.Exact<{
  postcode: Types.Scalars["String"]["input"];
}>;

export type SearchAddressByPostcodeQuery = {
  __typename?: "query_root";
  searchAddressByPostcode?: {
    __typename?: "SearchAddressByPostcodeRes";
    postCodeMstModels: Array<{
      __typename?: "PostCodeMstModel";
      postCd: string;
      address: string;
      isDeleted: number;
      prefName: string;
      cityName: string;
      banti: string;
    }>;
  };
};

export const GetPrefecturesDocument = gql`
  query getPrefectures {
    getPrefectures {
      name
      prefectureId
    }
  }
`;

/**
 * __useGetPrefecturesQuery__
 *
 * To run a query within a React component, call `useGetPrefecturesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPrefecturesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPrefecturesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetPrefecturesQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetPrefecturesQuery,
    GetPrefecturesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetPrefecturesQuery, GetPrefecturesQueryVariables>(
    GetPrefecturesDocument,
    options,
  );
}
export function useGetPrefecturesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPrefecturesQuery,
    GetPrefecturesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetPrefecturesQuery, GetPrefecturesQueryVariables>(
    GetPrefecturesDocument,
    options,
  );
}
export function useGetPrefecturesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPrefecturesQuery,
    GetPrefecturesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPrefecturesQuery,
    GetPrefecturesQueryVariables
  >(GetPrefecturesDocument, options);
}
export type GetPrefecturesQueryHookResult = ReturnType<
  typeof useGetPrefecturesQuery
>;
export type GetPrefecturesLazyQueryHookResult = ReturnType<
  typeof useGetPrefecturesLazyQuery
>;
export type GetPrefecturesSuspenseQueryHookResult = ReturnType<
  typeof useGetPrefecturesSuspenseQuery
>;
export type GetPrefecturesQueryResult = Apollo.QueryResult<
  GetPrefecturesQuery,
  GetPrefecturesQueryVariables
>;
export const SearchAddressByPostcodeDocument = gql`
  query searchAddressByPostcode($postcode: String!) {
    searchAddressByPostcode(postcode: $postcode) {
      postCodeMstModels {
        postCd
        address
        isDeleted
        prefName
        cityName
        banti
      }
    }
  }
`;

/**
 * __useSearchAddressByPostcodeQuery__
 *
 * To run a query within a React component, call `useSearchAddressByPostcodeQuery` and pass it any options that fit your needs.
 * When your component renders, `useSearchAddressByPostcodeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useSearchAddressByPostcodeQuery({
 *   variables: {
 *      postcode: // value for 'postcode'
 *   },
 * });
 */
export function useSearchAddressByPostcodeQuery(
  baseOptions: Apollo.QueryHookOptions<
    SearchAddressByPostcodeQuery,
    SearchAddressByPostcodeQueryVariables
  > &
    (
      | { variables: SearchAddressByPostcodeQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    SearchAddressByPostcodeQuery,
    SearchAddressByPostcodeQueryVariables
  >(SearchAddressByPostcodeDocument, options);
}
export function useSearchAddressByPostcodeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    SearchAddressByPostcodeQuery,
    SearchAddressByPostcodeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    SearchAddressByPostcodeQuery,
    SearchAddressByPostcodeQueryVariables
  >(SearchAddressByPostcodeDocument, options);
}
export function useSearchAddressByPostcodeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    SearchAddressByPostcodeQuery,
    SearchAddressByPostcodeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    SearchAddressByPostcodeQuery,
    SearchAddressByPostcodeQueryVariables
  >(SearchAddressByPostcodeDocument, options);
}
export type SearchAddressByPostcodeQueryHookResult = ReturnType<
  typeof useSearchAddressByPostcodeQuery
>;
export type SearchAddressByPostcodeLazyQueryHookResult = ReturnType<
  typeof useSearchAddressByPostcodeLazyQuery
>;
export type SearchAddressByPostcodeSuspenseQueryHookResult = ReturnType<
  typeof useSearchAddressByPostcodeSuspenseQuery
>;
export type SearchAddressByPostcodeQueryResult = Apollo.QueryResult<
  SearchAddressByPostcodeQuery,
  SearchAddressByPostcodeQueryVariables
>;
