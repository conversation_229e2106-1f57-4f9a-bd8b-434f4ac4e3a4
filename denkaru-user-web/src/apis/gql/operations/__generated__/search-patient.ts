import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiPatientInforSearchAdvancedMutationVariables = Types.Exact<{
  pageIndex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  pageSize?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  fromPtNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  toPtNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  name?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  sex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  fromAge?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  toAge?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  fromBirthDay?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  toBirthDay?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  homePost?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  address?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  phoneNum?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  fromVisitDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  toVisitDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  fromLastVisitDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  toLastVisitDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  fromInsuranceNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  toInsuranceNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  fromPublicExpensesNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  toPublicExpensesNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  fromSpecialPublicExpensesNum?: Types.InputMaybe<
    Types.Scalars["String"]["input"]
  >;
  toSpecialPublicExpensesNum?: Types.InputMaybe<
    Types.Scalars["String"]["input"]
  >;
  hokenNum?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi1Num?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi2Num?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi3Num?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi4Num?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi1EdaNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi2EdaNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi3EdaNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi4EdaNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  orderLogicalOperator?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  tenMsts?: Types.InputMaybe<
    | Array<
        Types.InputMaybe<Types.DomainModelsPatientInforTenMstSearchInputInput>
      >
    | Types.InputMaybe<Types.DomainModelsPatientInforTenMstSearchInputInput>
  >;
  departmentId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  treatmentDepartmentId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  doctorId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  byomeiLogicalOperator?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  byomeis?: Types.InputMaybe<
    | Array<
        Types.InputMaybe<Types.DomainModelsPatientInforByomeiSearchInputInput>
      >
    | Types.InputMaybe<Types.DomainModelsPatientInforByomeiSearchInputInput>
  >;
  byomeiStartDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  byomeiEndDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isSuspectedDisease?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  resultKbn?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sortData?: Types.InputMaybe<Types.Scalars["JSON"]["input"]>;
  isOrderOr?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type PostApiPatientInforSearchAdvancedMutation = {
  __typename?: "mutation_root";
  postApiPatientInforSearchAdvanced?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforSearchPatientInfoAdvancedResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforSearchPatientInfoAdvancedResponse";
      patientInfos?: Array<{
        __typename?: "UseCasePatientInforPatientDenrakuInfoWithGroup";
        gender?: number;
        lastAppointmentDate?: string;
        nextAppointmentDate?: string;
        patientID?: string;
        patientName?: string;
        patientNameKana?: string;
        portalCustomerID?: number;
        birthdayRaw?: number;
        patientNumber?: string;
      }>;
    };
  };
};

export type PostApiMstItemSearchTenMstItemForPatientMutationVariables =
  Types.Exact<{
    keyword?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    pageCount?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    pageIndex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type PostApiMstItemSearchTenMstItemForPatientMutation = {
  __typename?: "mutation_root";
  postApiMstItemSearchTenMstItemForPatient?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
      tenMsts?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemTenItemDto";
        name?: string;
        itemCd?: string;
        kanaName1?: string;
      }>;
    };
  };
};

export type GetApiMstItemDiseaseSearchForPatientQueryVariables = Types.Exact<{
  keyword?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  pageIndex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  pageSize?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiMstItemDiseaseSearchForPatientQuery = {
  __typename?: "query_root";
  getApiMstItemDiseaseSearchForPatient?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemDiseaseSearchDiseaseSearchResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemDiseaseSearchDiseaseSearchResponse";
      data?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemDiseaseSearchDiseaseSearchModel";
        byomeiCd?: string;
        byomeiType?: string;
        icd10?: string;
        icd102013?: string;
        isAdopted?: boolean;
        kanaName1?: string;
        nanByo?: string;
        sbyomei?: string;
        sikkan?: string;
        sikkanCd?: number;
      }>;
    };
  };
};

export const PostApiPatientInforSearchAdvancedDocument = gql`
  mutation postApiPatientInforSearchAdvanced(
    $pageIndex: Int
    $pageSize: Int
    $fromPtNum: BigInt
    $toPtNum: BigInt
    $name: String
    $sex: Int
    $fromAge: Int
    $toAge: Int
    $fromBirthDay: Int
    $toBirthDay: Int
    $homePost: String
    $address: String
    $phoneNum: String
    $fromVisitDate: Int
    $toVisitDate: Int
    $fromLastVisitDate: Int
    $toLastVisitDate: Int
    $fromInsuranceNum: BigInt
    $toInsuranceNum: BigInt
    $fromPublicExpensesNum: BigInt
    $toPublicExpensesNum: BigInt
    $fromSpecialPublicExpensesNum: String
    $toSpecialPublicExpensesNum: String
    $hokenNum: Int
    $kohi1Num: Int
    $kohi2Num: Int
    $kohi3Num: Int
    $kohi4Num: Int
    $kohi1EdaNo: Int
    $kohi2EdaNo: Int
    $kohi3EdaNo: Int
    $kohi4EdaNo: Int
    $orderLogicalOperator: Int
    $tenMsts: [DomainModelsPatientInforTenMstSearchInputInput] = []
    $departmentId: Int
    $treatmentDepartmentId: Int
    $doctorId: Int
    $byomeiLogicalOperator: Int
    $byomeis: [DomainModelsPatientInforByomeiSearchInputInput] = []
    $byomeiStartDate: Int
    $byomeiEndDate: Int
    $isSuspectedDisease: Boolean
    $resultKbn: Int
    $sortData: JSON = {}
    $isOrderOr: Boolean
  ) {
    postApiPatientInforSearchAdvanced(
      emrCloudApiRequestsPatientInforSearchPatientInfoAdvancedRequestInput: {
        pageIndex: $pageIndex
        pageSize: $pageSize
        fromPtNum: $fromPtNum
        toPtNum: $toPtNum
        name: $name
        sex: $sex
        toAge: $toAge
        fromAge: $fromAge
        fromBirthDay: $fromBirthDay
        toBirthDay: $toBirthDay
        homePost: $homePost
        address: $address
        phoneNum: $phoneNum
        toVisitDate: $toVisitDate
        fromVisitDate: $fromVisitDate
        fromLastVisitDate: $fromLastVisitDate
        toLastVisitDate: $toLastVisitDate
        fromInsuranceNum: $fromInsuranceNum
        toInsuranceNum: $toInsuranceNum
        fromPublicExpensesNum: $fromPublicExpensesNum
        toPublicExpensesNum: $toPublicExpensesNum
        fromSpecialPublicExpensesNum: $fromSpecialPublicExpensesNum
        toSpecialPublicExpensesNum: $toSpecialPublicExpensesNum
        hokenNum: $hokenNum
        kohi1Num: $kohi1Num
        kohi2Num: $kohi2Num
        kohi3Num: $kohi3Num
        kohi4Num: $kohi4Num
        kohi1EdaNo: $kohi1EdaNo
        kohi2EdaNo: $kohi2EdaNo
        kohi3EdaNo: $kohi3EdaNo
        kohi4EdaNo: $kohi4EdaNo
        orderLogicalOperator: $orderLogicalOperator
        tenMsts: $tenMsts
        departmentId: $departmentId
        treatmentDepartmentId: $treatmentDepartmentId
        doctorId: $doctorId
        byomeiLogicalOperator: $byomeiLogicalOperator
        byomeis: $byomeis
        byomeiStartDate: $byomeiStartDate
        byomeiEndDate: $byomeiEndDate
        isSuspectedDisease: $isSuspectedDisease
        resultKbn: $resultKbn
        sortData: $sortData
        isOrderOr: $isOrderOr
      }
    ) {
      data {
        patientInfos {
          gender
          lastAppointmentDate
          nextAppointmentDate
          patientID
          patientName
          patientNameKana
          portalCustomerID
          birthdayRaw
          patientNumber
        }
      }
    }
  }
`;
export type PostApiPatientInforSearchAdvancedMutationFn =
  Apollo.MutationFunction<
    PostApiPatientInforSearchAdvancedMutation,
    PostApiPatientInforSearchAdvancedMutationVariables
  >;

/**
 * __usePostApiPatientInforSearchAdvancedMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforSearchAdvancedMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforSearchAdvancedMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforSearchAdvancedMutation, { data, loading, error }] = usePostApiPatientInforSearchAdvancedMutation({
 *   variables: {
 *      pageIndex: // value for 'pageIndex'
 *      pageSize: // value for 'pageSize'
 *      fromPtNum: // value for 'fromPtNum'
 *      toPtNum: // value for 'toPtNum'
 *      name: // value for 'name'
 *      sex: // value for 'sex'
 *      fromAge: // value for 'fromAge'
 *      toAge: // value for 'toAge'
 *      fromBirthDay: // value for 'fromBirthDay'
 *      toBirthDay: // value for 'toBirthDay'
 *      homePost: // value for 'homePost'
 *      address: // value for 'address'
 *      phoneNum: // value for 'phoneNum'
 *      fromVisitDate: // value for 'fromVisitDate'
 *      toVisitDate: // value for 'toVisitDate'
 *      fromLastVisitDate: // value for 'fromLastVisitDate'
 *      toLastVisitDate: // value for 'toLastVisitDate'
 *      fromInsuranceNum: // value for 'fromInsuranceNum'
 *      toInsuranceNum: // value for 'toInsuranceNum'
 *      fromPublicExpensesNum: // value for 'fromPublicExpensesNum'
 *      toPublicExpensesNum: // value for 'toPublicExpensesNum'
 *      fromSpecialPublicExpensesNum: // value for 'fromSpecialPublicExpensesNum'
 *      toSpecialPublicExpensesNum: // value for 'toSpecialPublicExpensesNum'
 *      hokenNum: // value for 'hokenNum'
 *      kohi1Num: // value for 'kohi1Num'
 *      kohi2Num: // value for 'kohi2Num'
 *      kohi3Num: // value for 'kohi3Num'
 *      kohi4Num: // value for 'kohi4Num'
 *      kohi1EdaNo: // value for 'kohi1EdaNo'
 *      kohi2EdaNo: // value for 'kohi2EdaNo'
 *      kohi3EdaNo: // value for 'kohi3EdaNo'
 *      kohi4EdaNo: // value for 'kohi4EdaNo'
 *      orderLogicalOperator: // value for 'orderLogicalOperator'
 *      tenMsts: // value for 'tenMsts'
 *      departmentId: // value for 'departmentId'
 *      treatmentDepartmentId: // value for 'treatmentDepartmentId'
 *      doctorId: // value for 'doctorId'
 *      byomeiLogicalOperator: // value for 'byomeiLogicalOperator'
 *      byomeis: // value for 'byomeis'
 *      byomeiStartDate: // value for 'byomeiStartDate'
 *      byomeiEndDate: // value for 'byomeiEndDate'
 *      isSuspectedDisease: // value for 'isSuspectedDisease'
 *      resultKbn: // value for 'resultKbn'
 *      sortData: // value for 'sortData'
 *      isOrderOr: // value for 'isOrderOr'
 *   },
 * });
 */
export function usePostApiPatientInforSearchAdvancedMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforSearchAdvancedMutation,
    PostApiPatientInforSearchAdvancedMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforSearchAdvancedMutation,
    PostApiPatientInforSearchAdvancedMutationVariables
  >(PostApiPatientInforSearchAdvancedDocument, options);
}
export type PostApiPatientInforSearchAdvancedMutationHookResult = ReturnType<
  typeof usePostApiPatientInforSearchAdvancedMutation
>;
export type PostApiPatientInforSearchAdvancedMutationResult =
  Apollo.MutationResult<PostApiPatientInforSearchAdvancedMutation>;
export type PostApiPatientInforSearchAdvancedMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforSearchAdvancedMutation,
    PostApiPatientInforSearchAdvancedMutationVariables
  >;
export const PostApiMstItemSearchTenMstItemForPatientDocument = gql`
  mutation postApiMstItemSearchTenMstItemForPatient(
    $keyword: String
    $pageCount: Int
    $pageIndex: Int
    $sinDate: Int
  ) {
    postApiMstItemSearchTenMstItemForPatient(
      emrCloudApiRequestsMstItemSearchTenMstItemForPatientRequestInput: {
        keyword: $keyword
        pageCount: $pageCount
        pageIndex: $pageIndex
        sinDate: $sinDate
      }
    ) {
      data {
        tenMsts {
          name
          itemCd
          kanaName1
        }
      }
    }
  }
`;
export type PostApiMstItemSearchTenMstItemForPatientMutationFn =
  Apollo.MutationFunction<
    PostApiMstItemSearchTenMstItemForPatientMutation,
    PostApiMstItemSearchTenMstItemForPatientMutationVariables
  >;

/**
 * __usePostApiMstItemSearchTenMstItemForPatientMutation__
 *
 * To run a mutation, you first call `usePostApiMstItemSearchTenMstItemForPatientMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMstItemSearchTenMstItemForPatientMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMstItemSearchTenMstItemForPatientMutation, { data, loading, error }] = usePostApiMstItemSearchTenMstItemForPatientMutation({
 *   variables: {
 *      keyword: // value for 'keyword'
 *      pageCount: // value for 'pageCount'
 *      pageIndex: // value for 'pageIndex'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function usePostApiMstItemSearchTenMstItemForPatientMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMstItemSearchTenMstItemForPatientMutation,
    PostApiMstItemSearchTenMstItemForPatientMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMstItemSearchTenMstItemForPatientMutation,
    PostApiMstItemSearchTenMstItemForPatientMutationVariables
  >(PostApiMstItemSearchTenMstItemForPatientDocument, options);
}
export type PostApiMstItemSearchTenMstItemForPatientMutationHookResult =
  ReturnType<typeof usePostApiMstItemSearchTenMstItemForPatientMutation>;
export type PostApiMstItemSearchTenMstItemForPatientMutationResult =
  Apollo.MutationResult<PostApiMstItemSearchTenMstItemForPatientMutation>;
export type PostApiMstItemSearchTenMstItemForPatientMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMstItemSearchTenMstItemForPatientMutation,
    PostApiMstItemSearchTenMstItemForPatientMutationVariables
  >;
export const GetApiMstItemDiseaseSearchForPatientDocument = gql`
  query getApiMstItemDiseaseSearchForPatient(
    $keyword: String
    $pageIndex: Int
    $pageSize: Int
  ) {
    getApiMstItemDiseaseSearchForPatient(
      keyword: $keyword
      pageIndex: $pageIndex
      pageSize: $pageSize
    ) {
      data {
        data {
          byomeiCd
          byomeiType
          icd10
          icd102013
          isAdopted
          kanaName1
          nanByo
          sbyomei
          sikkan
          sikkanCd
        }
      }
    }
  }
`;

/**
 * __useGetApiMstItemDiseaseSearchForPatientQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemDiseaseSearchForPatientQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemDiseaseSearchForPatientQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemDiseaseSearchForPatientQuery({
 *   variables: {
 *      keyword: // value for 'keyword'
 *      pageIndex: // value for 'pageIndex'
 *      pageSize: // value for 'pageSize'
 *   },
 * });
 */
export function useGetApiMstItemDiseaseSearchForPatientQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemDiseaseSearchForPatientQuery,
    GetApiMstItemDiseaseSearchForPatientQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemDiseaseSearchForPatientQuery,
    GetApiMstItemDiseaseSearchForPatientQueryVariables
  >(GetApiMstItemDiseaseSearchForPatientDocument, options);
}
export function useGetApiMstItemDiseaseSearchForPatientLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemDiseaseSearchForPatientQuery,
    GetApiMstItemDiseaseSearchForPatientQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemDiseaseSearchForPatientQuery,
    GetApiMstItemDiseaseSearchForPatientQueryVariables
  >(GetApiMstItemDiseaseSearchForPatientDocument, options);
}
export function useGetApiMstItemDiseaseSearchForPatientSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemDiseaseSearchForPatientQuery,
    GetApiMstItemDiseaseSearchForPatientQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemDiseaseSearchForPatientQuery,
    GetApiMstItemDiseaseSearchForPatientQueryVariables
  >(GetApiMstItemDiseaseSearchForPatientDocument, options);
}
export type GetApiMstItemDiseaseSearchForPatientQueryHookResult = ReturnType<
  typeof useGetApiMstItemDiseaseSearchForPatientQuery
>;
export type GetApiMstItemDiseaseSearchForPatientLazyQueryHookResult =
  ReturnType<typeof useGetApiMstItemDiseaseSearchForPatientLazyQuery>;
export type GetApiMstItemDiseaseSearchForPatientSuspenseQueryHookResult =
  ReturnType<typeof useGetApiMstItemDiseaseSearchForPatientSuspenseQuery>;
export type GetApiMstItemDiseaseSearchForPatientQueryResult =
  Apollo.QueryResult<
    GetApiMstItemDiseaseSearchForPatientQuery,
    GetApiMstItemDiseaseSearchForPatientQueryVariables
  >;
