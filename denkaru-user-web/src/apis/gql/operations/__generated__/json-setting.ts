import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiJsonSettingGetListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiJsonSettingGetListQuery = {
  __typename?: "query_root";
  getApiJsonSettingGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesJsonSettingGetAllJsonSettingResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesJsonSettingGetAllJsonSettingResponse";
      settings?: Array<{
        __typename?: "UseCaseJsonSettingJsonSettingDto";
        key?: string;
        userId?: number;
        value?: any;
      }>;
    };
  };
};

export type GetApiJsonSettingGetQueryVariables = Types.Exact<{
  key: Types.Scalars["String"]["input"];
}>;

export type GetApiJsonSettingGetQuery = {
  __typename?: "query_root";
  getApiJsonSettingGet?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesJsonSettingGetJsonSettingResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesJsonSettingGetJsonSettingResponse";
      setting?: {
        __typename?: "UseCaseJsonSettingJsonSettingDto";
        key?: string;
        userId?: number;
        value?: any;
      };
    };
  };
};

export type PostApiJsonSettingUpsertMutationVariables = Types.Exact<{
  userId: Types.Scalars["Int"]["input"];
  key: Types.Scalars["String"]["input"];
  value: Types.Scalars["JSON"]["input"];
}>;

export type PostApiJsonSettingUpsertMutation = {
  __typename?: "mutation_root";
  postApiJsonSettingUpsert?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesJsonSettingUpsertJsonSettingResponse";
    status?: number;
  };
};

export const GetApiJsonSettingGetListDocument = gql`
  query getApiJsonSettingGetList {
    getApiJsonSettingGetList {
      data {
        settings {
          key
          userId
          value
        }
      }
    }
  }
`;

/**
 * __useGetApiJsonSettingGetListQuery__
 *
 * To run a query within a React component, call `useGetApiJsonSettingGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiJsonSettingGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiJsonSettingGetListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiJsonSettingGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiJsonSettingGetListQuery,
    GetApiJsonSettingGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiJsonSettingGetListQuery,
    GetApiJsonSettingGetListQueryVariables
  >(GetApiJsonSettingGetListDocument, options);
}
export function useGetApiJsonSettingGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiJsonSettingGetListQuery,
    GetApiJsonSettingGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiJsonSettingGetListQuery,
    GetApiJsonSettingGetListQueryVariables
  >(GetApiJsonSettingGetListDocument, options);
}
export function useGetApiJsonSettingGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiJsonSettingGetListQuery,
    GetApiJsonSettingGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiJsonSettingGetListQuery,
    GetApiJsonSettingGetListQueryVariables
  >(GetApiJsonSettingGetListDocument, options);
}
export type GetApiJsonSettingGetListQueryHookResult = ReturnType<
  typeof useGetApiJsonSettingGetListQuery
>;
export type GetApiJsonSettingGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiJsonSettingGetListLazyQuery
>;
export type GetApiJsonSettingGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiJsonSettingGetListSuspenseQuery
>;
export type GetApiJsonSettingGetListQueryResult = Apollo.QueryResult<
  GetApiJsonSettingGetListQuery,
  GetApiJsonSettingGetListQueryVariables
>;
export const GetApiJsonSettingGetDocument = gql`
  query getApiJsonSettingGet($key: String!) {
    getApiJsonSettingGet(key: $key) {
      data {
        setting {
          key
          userId
          value
        }
      }
    }
  }
`;

/**
 * __useGetApiJsonSettingGetQuery__
 *
 * To run a query within a React component, call `useGetApiJsonSettingGetQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiJsonSettingGetQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiJsonSettingGetQuery({
 *   variables: {
 *      key: // value for 'key'
 *   },
 * });
 */
export function useGetApiJsonSettingGetQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiJsonSettingGetQuery,
    GetApiJsonSettingGetQueryVariables
  > &
    (
      | { variables: GetApiJsonSettingGetQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiJsonSettingGetQuery,
    GetApiJsonSettingGetQueryVariables
  >(GetApiJsonSettingGetDocument, options);
}
export function useGetApiJsonSettingGetLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiJsonSettingGetQuery,
    GetApiJsonSettingGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiJsonSettingGetQuery,
    GetApiJsonSettingGetQueryVariables
  >(GetApiJsonSettingGetDocument, options);
}
export function useGetApiJsonSettingGetSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiJsonSettingGetQuery,
    GetApiJsonSettingGetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiJsonSettingGetQuery,
    GetApiJsonSettingGetQueryVariables
  >(GetApiJsonSettingGetDocument, options);
}
export type GetApiJsonSettingGetQueryHookResult = ReturnType<
  typeof useGetApiJsonSettingGetQuery
>;
export type GetApiJsonSettingGetLazyQueryHookResult = ReturnType<
  typeof useGetApiJsonSettingGetLazyQuery
>;
export type GetApiJsonSettingGetSuspenseQueryHookResult = ReturnType<
  typeof useGetApiJsonSettingGetSuspenseQuery
>;
export type GetApiJsonSettingGetQueryResult = Apollo.QueryResult<
  GetApiJsonSettingGetQuery,
  GetApiJsonSettingGetQueryVariables
>;
export const PostApiJsonSettingUpsertDocument = gql`
  mutation postApiJsonSettingUpsert(
    $userId: Int!
    $key: String!
    $value: JSON!
  ) {
    postApiJsonSettingUpsert(
      emrCloudApiRequestsJsonSettingUpsertJsonSettingRequestInput: {
        setting: { userId: $userId, key: $key, value: $value }
      }
    ) {
      status
    }
  }
`;
export type PostApiJsonSettingUpsertMutationFn = Apollo.MutationFunction<
  PostApiJsonSettingUpsertMutation,
  PostApiJsonSettingUpsertMutationVariables
>;

/**
 * __usePostApiJsonSettingUpsertMutation__
 *
 * To run a mutation, you first call `usePostApiJsonSettingUpsertMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiJsonSettingUpsertMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiJsonSettingUpsertMutation, { data, loading, error }] = usePostApiJsonSettingUpsertMutation({
 *   variables: {
 *      userId: // value for 'userId'
 *      key: // value for 'key'
 *      value: // value for 'value'
 *   },
 * });
 */
export function usePostApiJsonSettingUpsertMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiJsonSettingUpsertMutation,
    PostApiJsonSettingUpsertMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiJsonSettingUpsertMutation,
    PostApiJsonSettingUpsertMutationVariables
  >(PostApiJsonSettingUpsertDocument, options);
}
export type PostApiJsonSettingUpsertMutationHookResult = ReturnType<
  typeof usePostApiJsonSettingUpsertMutation
>;
export type PostApiJsonSettingUpsertMutationResult =
  Apollo.MutationResult<PostApiJsonSettingUpsertMutation>;
export type PostApiJsonSettingUpsertMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiJsonSettingUpsertMutation,
    PostApiJsonSettingUpsertMutationVariables
  >;
