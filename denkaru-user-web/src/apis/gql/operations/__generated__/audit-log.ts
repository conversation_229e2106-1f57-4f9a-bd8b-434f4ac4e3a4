import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetAuditlogQueryVariables = Types.Exact<{
  date: Types.Scalars["DateTime"]["input"];
  limit?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  cursor?: Types.InputMaybe<Types.Scalars["Int64"]["input"]>;
}>;

export type GetAuditlogQuery = {
  __typename?: "query_root";
  getAuditlog?: Array<{
    __typename?: "Auditlog";
    logId: string;
    logDate: string;
    loginId: string;
    hpId: number;
    hpName: string;
    userId: number;
    userName: string;
    eventCd: string;
    eventName: string;
    ptId?: string;
    ptName?: string;
    sinDay?: number;
    raiinNo?: string;
    machine?: string;
    hosoku?: string;
    isOperator: number;
    pageType: string;
  }>;
};

export type AddAuditlogMutationVariables = Types.Exact<{
  input: Types.AddAuditlogInput;
}>;

export type AddAuditlogMutation = {
  __typename?: "mutation_root";
  addAuditlog: boolean;
};

export type PostApiAuditLogSaveMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsAuditLogSaveAuditLogItemInput>;
}>;

export type PostApiAuditLogSaveMutation = {
  __typename?: "mutation_root";
  postApiAuditLogSave?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAuditLogSaveAuditLogResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesAuditLogSaveAuditLogResponse";
      value?: boolean;
    };
  };
};

export const GetAuditlogDocument = gql`
  query getAuditlog($date: DateTime!, $limit: Int, $cursor: Int64) {
    getAuditlog(date: $date, limit: $limit, cursor: $cursor) {
      logId
      logDate
      loginId
      hpId
      hpName
      userId
      userName
      eventCd
      eventName
      ptId
      ptName
      sinDay
      raiinNo
      machine
      hosoku
      logId
      isOperator
      pageType
    }
  }
`;

/**
 * __useGetAuditlogQuery__
 *
 * To run a query within a React component, call `useGetAuditlogQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAuditlogQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAuditlogQuery({
 *   variables: {
 *      date: // value for 'date'
 *      limit: // value for 'limit'
 *      cursor: // value for 'cursor'
 *   },
 * });
 */
export function useGetAuditlogQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetAuditlogQuery,
    GetAuditlogQueryVariables
  > &
    (
      | { variables: GetAuditlogQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetAuditlogQuery, GetAuditlogQueryVariables>(
    GetAuditlogDocument,
    options,
  );
}
export function useGetAuditlogLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetAuditlogQuery,
    GetAuditlogQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetAuditlogQuery, GetAuditlogQueryVariables>(
    GetAuditlogDocument,
    options,
  );
}
export function useGetAuditlogSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetAuditlogQuery,
    GetAuditlogQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<GetAuditlogQuery, GetAuditlogQueryVariables>(
    GetAuditlogDocument,
    options,
  );
}
export type GetAuditlogQueryHookResult = ReturnType<typeof useGetAuditlogQuery>;
export type GetAuditlogLazyQueryHookResult = ReturnType<
  typeof useGetAuditlogLazyQuery
>;
export type GetAuditlogSuspenseQueryHookResult = ReturnType<
  typeof useGetAuditlogSuspenseQuery
>;
export type GetAuditlogQueryResult = Apollo.QueryResult<
  GetAuditlogQuery,
  GetAuditlogQueryVariables
>;
export const AddAuditlogDocument = gql`
  mutation addAuditlog($input: AddAuditlogInput!) {
    addAuditlog(input: $input)
  }
`;
export type AddAuditlogMutationFn = Apollo.MutationFunction<
  AddAuditlogMutation,
  AddAuditlogMutationVariables
>;

/**
 * __useAddAuditlogMutation__
 *
 * To run a mutation, you first call `useAddAuditlogMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddAuditlogMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addAuditlogMutation, { data, loading, error }] = useAddAuditlogMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAddAuditlogMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AddAuditlogMutation,
    AddAuditlogMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<AddAuditlogMutation, AddAuditlogMutationVariables>(
    AddAuditlogDocument,
    options,
  );
}
export type AddAuditlogMutationHookResult = ReturnType<
  typeof useAddAuditlogMutation
>;
export type AddAuditlogMutationResult =
  Apollo.MutationResult<AddAuditlogMutation>;
export type AddAuditlogMutationOptions = Apollo.BaseMutationOptions<
  AddAuditlogMutation,
  AddAuditlogMutationVariables
>;
export const PostApiAuditLogSaveDocument = gql`
  mutation postApiAuditLogSave(
    $input: EmrCloudApiRequestsAuditLogSaveAuditLogItemInput
  ) {
    postApiAuditLogSave(
      emrCloudApiRequestsAuditLogSaveAuditLogRequestInput: {
        auditTrailLogModel: $input
      }
    ) {
      status
      message
      data {
        value
      }
    }
  }
`;
export type PostApiAuditLogSaveMutationFn = Apollo.MutationFunction<
  PostApiAuditLogSaveMutation,
  PostApiAuditLogSaveMutationVariables
>;

/**
 * __usePostApiAuditLogSaveMutation__
 *
 * To run a mutation, you first call `usePostApiAuditLogSaveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiAuditLogSaveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiAuditLogSaveMutation, { data, loading, error }] = usePostApiAuditLogSaveMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiAuditLogSaveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiAuditLogSaveMutation,
    PostApiAuditLogSaveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiAuditLogSaveMutation,
    PostApiAuditLogSaveMutationVariables
  >(PostApiAuditLogSaveDocument, options);
}
export type PostApiAuditLogSaveMutationHookResult = ReturnType<
  typeof usePostApiAuditLogSaveMutation
>;
export type PostApiAuditLogSaveMutationResult =
  Apollo.MutationResult<PostApiAuditLogSaveMutation>;
export type PostApiAuditLogSaveMutationOptions = Apollo.BaseMutationOptions<
  PostApiAuditLogSaveMutation,
  PostApiAuditLogSaveMutationVariables
>;
