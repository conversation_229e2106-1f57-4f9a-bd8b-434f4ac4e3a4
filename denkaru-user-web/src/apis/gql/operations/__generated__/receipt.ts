import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiReceiptGetListMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput;
}>;

export type PostApiReceiptGetListMutation = {
  __typename?: "mutation_root";
  postApiReceiptGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptReceiptListAdvancedSearchResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptReceiptListAdvancedSearchResponse";
      receiptList?: Array<{
        __typename?: "UseCaseReceiptReceiptListAdvancedSearchReceiptListAdvancedSearchItem";
        seikyuKbn?: number;
        sinYm?: number;
        isReceInfDetailExist?: number;
        isPaperRece?: number;
        hokenId?: number;
        hokenKbn?: number;
        output?: number;
        fusenKbn?: number;
        statusKbn?: number;
        isPending?: number;
        ptNum?: string;
        ptId?: string;
        kanaName?: string;
        name?: string;
        sex?: number;
        age?: number;
        lastSinDateByHokenId?: number;
        birthDay?: number;
        receSbt?: string;
        hokensyaNo?: string;
        tensu?: number;
        hokenSbtCd?: number;
        kohi1Nissu?: number;
        isSyoukiInfExist?: number;
        isReceCmtExist?: number;
        isSyobyoKeikaExist?: number;
        receSeikyuCmt?: string;
        lastVisitDate?: number;
        kaName?: string;
        sName?: string;
        isPtKyuseiExist?: number;
        futansyaNoKohi1?: string;
        futansyaNoKohi2?: string;
        futansyaNoKohi3?: string;
        futansyaNoKohi4?: string;
        isPtTest?: boolean;
        hokenNissu?: number;
        receCheckCmt?: string;
        birthDayDisplay?: string;
        lastVisitDateDisplay?: string;
        jibaiHokenName?: string;
        jibaiHokenTanto?: string;
        jibaiHokenTel?: string;
        rousaiCityName?: string;
        rousaiJigyosyoName?: string;
        rousaiKofuNo?: string;
        rousaiPrefName?: string;
      }>;
    };
  };
};

export type GetApiReceiptValidateCreateUkeFileQueryVariables = Types.Exact<{
  seikyuYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  modeType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptValidateCreateUkeFileQuery = {
  __typename?: "query_root";
  getApiReceiptValidateCreateUKEFile?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptValidateCreateUkeFileResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptValidateCreateUKEFileResponse";
      status?: number;
    };
  };
};

export type PostApiReceiptCreateUkeFileMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsReceiptCreateUkeFileRequestInput;
}>;

export type PostApiReceiptCreateUkeFileMutation = {
  __typename?: "mutation_root";
  postApiReceiptCreateUKEFile?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptCreateUkeFileResponse";
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptCreateUKEFileResponse";
      file?: Array<File>;
    };
  };
};

export type GetApiReceiptGetListSokatuMstQueryVariables = Types.Exact<{
  seikyuYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetListSokatuMstQuery = {
  __typename?: "query_root";
  getApiReceiptGetListSokatuMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetListSokatuMstResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetListSokatuMstResponse";
      sokatuMstModels?: Array<{
        __typename?: "DomainModelsReceiptSokatuMstModel";
        prefNo?: number;
        startYm?: number;
        endYm?: number;
        reportId?: number;
        reportEdaNo?: number;
        sortNo?: number;
        reportName?: string;
        printType?: number;
        printNoType?: number;
        dataAll?: number;
        dataDisk?: number;
        dataPaper?: number;
        dataKbn?: number;
        diskKind?: string;
        diskCnt?: number;
        isSort?: number;
      }>;
    };
  };
};

export type GetApiReceiptGetRecePreviewListQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  receiptPreviewType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetRecePreviewListQuery = {
  __typename?: "query_root";
  getApiReceiptGetRecePreviewList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetRecePreviewListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetRecePreviewListResponse";
      recePreviewList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoRecePreviewDto";
        seikyuYm?: number;
        sinYm?: number;
        hokenId?: number;
        hokenKbn?: number;
        seikyuYmDisplay?: string;
        sinYmDisplay?: string;
        hokenPatternName?: string;
      }>;
    };
  };
};

export type GetApiReceiptGetReceCheckOptionListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiReceiptGetReceCheckOptionListQuery = {
  __typename?: "query_root";
  getApiReceiptGetReceCheckOptionList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetReceCheckOptionListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetReceCheckOptionListResponse";
      receCheckOptionList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceCheckOptionDto";
        checkboxKey?: string;
        errCd?: string;
        checkOpt?: number;
        isInvalid?: boolean;
      }>;
    };
  };
};

export type PostApiReceiptSaveReceCheckOptMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsReceiptSaveReceCheckOptRequestInput;
}>;

export type PostApiReceiptSaveReceCheckOptMutation = {
  __typename?: "mutation_root";
  postApiReceiptSaveReceCheckOpt?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptSaveReceCheckOptResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptSaveReceCheckOptResponse";
      success?: boolean;
    };
  };
};

export type GetApiReceiptGetListKaikeiInfQueryVariables = Types.Exact<{
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiReceiptGetListKaikeiInfQuery = {
  __typename?: "query_root";
  getApiReceiptGetListKaikeiInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetListKaikeiInfResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetListKaikeiInfResponse";
      ptHokenInfKaikeiList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoPtHokenInfKaikeiDto";
        hokenId?: number;
        ptId?: string;
        hokenKbn?: number;
        houbetu?: string;
        honkeKbn?: number;
        hokensyaNo?: string;
        hokenStartDate?: number;
        hokenEndDate?: number;
        hokenName?: string;
      }>;
    };
  };
};

export type GetApiReceiptGetInsuranceReceInfListQueryVariables = Types.Exact<{
  seikyuYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetInsuranceReceInfListQuery = {
  __typename?: "query_root";
  getApiReceiptGetInsuranceReceInfList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetInsuranceReceInfListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetInsuranceReceInfListResponse";
      hokenName?: string;
      sinYmDisplay?: string;
      insuranceReceInf?: {
        __typename?: "UseCaseReceiptInsuranceReceInfItem";
        seikyuYm?: number;
        ptId?: string;
        sinYm?: number;
        hokenId?: number;
        hokenPId?: number;
        hokenId2?: number;
        kohi1Id?: number;
        kohi2Id?: number;
        kohi3Id?: number;
        kohi4Id?: number;
        hokenKbn?: number;
        receSbt?: string;
        hokensyaNo?: string;
        hokenReceTensu?: number;
        hokenReceFutan?: number;
        kohi1ReceTensu?: number;
        kohi1ReceFutan?: number;
        kohi1ReceKyufu?: number;
        kohi2ReceTensu?: number;
        kohi2ReceFutan?: number;
        kohi2ReceKyufu?: number;
        kohi3ReceTensu?: number;
        kohi3ReceFutan?: number;
        kohi3ReceKyufu?: number;
        kohi4ReceTensu?: number;
        kohi4ReceFutan?: number;
        kohi4ReceKyufu?: number;
        hokenNissu?: number;
        kohi1Nissu?: number;
        kohi2Nissu?: number;
        kohi3Nissu?: number;
        kohi4Nissu?: number;
        kohi1ReceKisai?: number;
        kohi2ReceKisai?: number;
        kohi3ReceKisai?: number;
        kohi4ReceKisai?: number;
        tokki1?: string;
        tokki2?: string;
        tokki3?: string;
        tokki4?: string;
        tokki5?: string;
        rousaiIFutan?: number;
        rousaiRoFutan?: number;
        jibaiITensu?: number;
        jibaiRoTensu?: number;
        jibaiHaFutan?: number;
        jibaiNiFutan?: number;
        jibaiHoSindan?: number;
        jibaiHeMeisai?: number;
        jibaiAFutan?: number;
        jibaiBFutan?: number;
        jibaiCFutan?: number;
        jibaiDFutan?: number;
        jibaiKenpoFutan?: number;
        futansyaNoKohi1?: string;
        futansyaNoKohi2?: string;
        futansyaNoKohi3?: string;
        futansyaNoKohi4?: string;
        jyukyusyaNoKohi1?: string;
        jyukyusyaNoKohi2?: string;
        jyukyusyaNoKohi3?: string;
        jyukyusyaNoKohi4?: string;
        hokenInfRousaiKofuNo?: string;
        insuranceName?: string;
        kigo?: string;
        bango?: string;
        edaNo?: string;
      };
    };
  };
};

export type GetApiReceiptGetDiseaseReceListQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetDiseaseReceListQuery = {
  __typename?: "query_root";
  getApiReceiptGetDiseaseReceList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetDiseaseReceListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetDiseaseReceListResponse";
      diseaseReceList?: Array<{
        __typename?: "UseCaseReceiptGetDiseaseReceListDiseaseReceOutputItem";
        byomei?: string;
        startDate?: string;
        tenkiKbn?: string;
        tenkiDate?: string;
        isMain?: boolean;
        isSuspect?: number;
        nanbyoCd?: number;
        sikkanKbn?: number;
        hosokuCmt?: string;
      }>;
    };
  };
};

export type GetApiReceiptGetReceiCheckListQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetReceiCheckListQuery = {
  __typename?: "query_root";
  getApiReceiptGetReceiCheckList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetReceiCheckListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetReceiCheckListResponse";
      receiptCheckCmtErrList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceiptCheckCmtErrDto";
        seqNo?: number;
        sortNo?: number;
        isChecked?: boolean;
        textDisplay1?: string;
        textDisplay2?: string;
        errorCd?: string;
        aCd?: string;
        bCd?: string;
        sinDate?: number;
        statusColor?: number;
        receiptCheckIsErrItem?: boolean;
      }>;
    };
  };
};

export type PostApiReceiptSaveReceCheckCmtListMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsReceiptSaveReceCheckCmtListRequestInput>;
}>;

export type PostApiReceiptSaveReceCheckCmtListMutation = {
  __typename?: "mutation_root";
  postApiReceiptSaveReceCheckCmtList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptSaveReceCheckCmtListResponse";
    status?: number;
  };
};

export type GetApiReceiptGetListSinKouiQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiReceiptGetListSinKouiQuery = {
  __typename?: "query_root";
  getApiReceiptGetListSinKoui?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSinKouiGetListSinKouiResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSinKouiGetListSinKouiResponse";
      sinYms?: Array<number>;
    };
  };
};

export type GetApiReceiptGetInsuranceInfQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetInsuranceInfQuery = {
  __typename?: "query_root";
  getApiReceiptGetInsuranceInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetInsuranceInfResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetInsuranceInfResponse";
      insuranceInfDtos?: Array<{
        __typename?: "UseCaseReceiptGetInsuranceInfInsuranceInfDto";
        hokenId?: number;
        insuranceName?: string;
        bango?: string;
        edaNo?: string;
        futansyaNoKohi1?: string;
        futansyaNoKohi2?: string;
        futansyaNoKohi3?: string;
        futansyaNoKohi4?: string;
        hokenKbn?: number;
        hokensyaNo?: string;
        ichibuFutan?: string;
        jyukyusyaNoKohi1?: string;
        jyukyusyaNoKohi2?: string;
        jyukyusyaNoKohi3?: string;
        jyukyusyaNoKohi4?: string;
        kigo?: string;
        kohi1Id?: number;
        kohi1ReceKisai?: boolean;
        kohi2Id?: number;
        kohi2ReceKisai?: boolean;
        kohi3Id?: number;
        kohi3ReceKisai?: boolean;
        kohi4ReceKisai?: boolean;
        nissu?: number;
        ptFutan?: string;
        tensu?: string;
      }>;
    };
  };
};

export type GetApiReceiptGetSinMeiInMonthListQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  seikyuYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetSinMeiInMonthListQuery = {
  __typename?: "query_root";
  getApiReceiptGetSinMeiInMonthList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetMedicalDetailsResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetMedicalDetailsResponse";
      holidays?: any;
      sinMeiModels?: Array<{
        __typename?: "DomainModelsAccountingSinMeiModel";
        sinId?: number;
        sinIdBinding?: string;
        itemName?: string;
        quantity?: string;
        tenKai?: string;
        inOutKbn?: number;
        isRowColorGray?: boolean;
        sinRpNo?: number;
        sinSeqNo?: number;
        itemCd?: string;
        isDrug?: boolean;
        days?: Array<number>;
      }>;
    };
  };
};

export type GetApiReceiptGetReceHenReasonQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  seikyuYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetReceHenReasonQuery = {
  __typename?: "query_root";
  getApiReceiptGetReceHenReason?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetReceHenReasonResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetReceHenReasonResponse";
      receReasonCmt?: string;
    };
  };
};

export type GetApiReceGetSinMeiInMonthListQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  seikyuYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceGetSinMeiInMonthListQuery = {
  __typename?: "query_root";
  getApiReceiptGetSinMeiInMonthList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetMedicalDetailsResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetMedicalDetailsResponse";
      holidays?: any;
      sinMeiModels?: Array<{
        __typename?: "DomainModelsAccountingSinMeiModel";
        sinId?: number;
        sinIdBinding?: string;
        itemName?: string;
        suryo?: number;
        unitName?: string;
        tenKai?: string;
        totalTen?: number;
        totalKingaku?: number;
        kingaku?: number;
        futanS?: number;
        futanK1?: number;
        futanK2?: number;
        futanK3?: number;
        futanK4?: number;
        cdKbn?: string;
        jihiSbt?: number;
        enTenKbn?: number;
        santeiKbn?: number;
        inOutKbn?: number;
        sinRpNo?: number;
        sinSeqNo?: number;
        quantity?: string;
        sinHoTotalTen?: number;
        total?: number;
        totalBinding?: string;
        futanSBinding?: string;
        futanK1Binding?: string;
        futanK2Binding?: string;
        futanK3Binding?: string;
        futanK4Binding?: string;
        asterisk?: string;
        isRowColorGray?: boolean;
        isForegroundRed?: boolean;
        days?: Array<number>;
        itemCd?: string;
        drugKbn?: number;
        isDrug?: boolean;
      }>;
    };
  };
};

export type GetApiReceiptGetReceStatusQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  seikyuYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetReceStatusQuery = {
  __typename?: "query_root";
  getApiReceiptGetReceStatus?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetReceStatusResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetReceStatusResponse";
      ptId?: string;
      seikyuYm?: number;
      hokenId?: number;
      sinYm?: number;
      fusenKbn?: number;
      isPaperRece?: boolean;
      statusKbn?: number;
      isPrechecked?: boolean;
      isOutput?: boolean;
    };
  };
};

export type GetApiReceiptGetReceiptEditQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  seikyuYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetReceiptEditQuery = {
  __typename?: "query_root";
  getApiReceiptGetReceiptEdit?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetReceiptEditResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetReceiptEditResponse";
      seqNo?: number;
      tokkiMstDictionary?: any;
      receiptEditOrigin?: {
        __typename?: "EmrCloudApiResponsesReceiptDtoReceiptEditDto";
        tokki1Id?: string;
        tokki2Id?: string;
        tokki3Id?: string;
        tokki4Id?: string;
        tokki5Id?: string;
        hokenNissu?: number;
        kohi1Nissu?: number;
        kohi2Nissu?: number;
        kohi3Nissu?: number;
        kohi4Nissu?: number;
        kohi1ReceKyufu?: number;
        kohi2ReceKyufu?: number;
        kohi3ReceKyufu?: number;
        kohi4ReceKyufu?: number;
        hokenReceTensu?: number;
        hokenReceFutan?: number;
        kohi1ReceTensu?: number;
        kohi1ReceFutan?: number;
        kohi2ReceTensu?: number;
        kohi2ReceFutan?: number;
        kohi3ReceTensu?: number;
        kohi3ReceFutan?: number;
        kohi4ReceTensu?: number;
        kohi4ReceFutan?: number;
      };
      receiptEditCurrent?: {
        __typename?: "EmrCloudApiResponsesReceiptDtoReceiptEditDto";
        tokki1Id?: string;
        tokki2Id?: string;
        tokki3Id?: string;
        tokki4Id?: string;
        tokki5Id?: string;
        hokenNissu?: number;
        kohi1Nissu?: number;
        kohi2Nissu?: number;
        kohi3Nissu?: number;
        kohi4Nissu?: number;
        kohi1ReceKyufu?: number;
        kohi2ReceKyufu?: number;
        kohi3ReceKyufu?: number;
        kohi4ReceKyufu?: number;
        hokenReceTensu?: number;
        hokenReceFutan?: number;
        kohi1ReceTensu?: number;
        kohi1ReceFutan?: number;
        kohi2ReceTensu?: number;
        kohi2ReceFutan?: number;
        kohi3ReceTensu?: number;
        kohi3ReceFutan?: number;
        kohi4ReceTensu?: number;
        kohi4ReceFutan?: number;
      };
    };
  };
};

export type PostApiReceiptSaveReceStatusMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsReceiptSaveReceStatusRequestInput>;
}>;

export type PostApiReceiptSaveReceStatusMutation = {
  __typename?: "mutation_root";
  postApiReceiptSaveReceStatus?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptSaveReceStatusResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptSaveReceStatusResponse";
      success?: boolean;
    };
  };
};

export type PostApiReceiptSaveReceiptEditMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsReceiptSaveReceiptEditRequestInput>;
}>;

export type PostApiReceiptSaveReceiptEditMutation = {
  __typename?: "mutation_root";
  postApiReceiptSaveReceiptEdit?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptSaveReceiptEditResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptSaveReceiptEditResponse";
      success?: boolean;
    };
  };
};

export type GetApiReceiptCheckExisReceInfEditQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  seikyuYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptCheckExisReceInfEditQuery = {
  __typename?: "query_root";
  getApiReceiptCheckExisReceInfEdit?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptCheckExisReceInfEditResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptCheckExisReceInfEditResponse";
      receInfEdit?: boolean;
    };
  };
};

export type GetApiReceiptGetSyoukiInfListQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetSyoukiInfListQuery = {
  __typename?: "query_root";
  getApiReceiptGetSyoukiInfList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetSyoukiInfListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetSyoukiInfListResponse";
      syoukiInfList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoSyoukiInfDto";
        seqNo?: number;
        sortNo?: number;
        syoukiKbn?: number;
        syouki?: string;
      }>;
      syoukiKbnMstList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoSyoukiKbnMstDto";
        syoukiKbn?: number;
        name?: string;
        startYm?: number;
        syoukiKbnDisplay?: string;
      }>;
    };
  };
};

export type PostApiReceiptSaveSyoukiInfListMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsReceiptSaveSyoukiInfListRequestInput>;
}>;

export type PostApiReceiptSaveSyoukiInfListMutation = {
  __typename?: "mutation_root";
  postApiReceiptSaveSyoukiInfList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptSaveSyoukiInfListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptSaveSyoukiInfListResponse";
      syoukiInfInvalidList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoSyoukiInfDto";
        seqNo?: number;
        sortNo?: number;
        syoukiKbn?: number;
        syouki?: string;
      }>;
    };
  };
};

export type GetApiReceiptSyoukiInfHistoryQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptSyoukiInfHistoryQuery = {
  __typename?: "query_root";
  getApiReceiptSyoukiInfHistory?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptSyoukiInfHistoryResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptSyoukiInfHistoryResponse";
      data?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoSyoukiInfHistoryDto";
        sinYm?: number;
        sinYmDisplay?: string;
        hokenId?: number;
        hokenName?: string;
        syoukiInfList?: Array<{
          __typename?: "EmrCloudApiResponsesReceiptDtoSyoukiInfDto";
          seqNo?: number;
          sortNo?: number;
          syoukiKbn?: number;
          syouki?: string;
        }>;
        syoukiKbnMstList?: Array<{
          __typename?: "EmrCloudApiResponsesReceiptDtoSyoukiKbnMstDto";
          syoukiKbn?: number;
          name?: string;
          startYm?: number;
          syoukiKbnDisplay?: string;
        }>;
      }>;
    };
  };
};

export type GetApiReceiptGetSyobyoKeikaListQueryVariables = Types.Exact<{
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  hokenKbn?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetSyobyoKeikaListQuery = {
  __typename?: "query_root";
  getApiReceiptGetSyobyoKeikaList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetSyobyoKeikaListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetSyobyoKeikaListResponse";
      syobyoKeikaList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoSyobyoKeikaDto";
        sinDay?: number;
        seqNo?: number;
        keika?: string;
      }>;
    };
  };
};

export type PostApiReceiptSaveSyobyoKeikaListMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsReceiptSaveSyobyoKeikaListRequestInput>;
}>;

export type PostApiReceiptSaveSyobyoKeikaListMutation = {
  __typename?: "mutation_root";
  postApiReceiptSaveSyobyoKeikaList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptSaveSyobyoKeikaListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptSaveSyobyoKeikaListResponse";
      syobyoKeikaInvalidList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoSyobyoKeikaDto";
        keika?: string;
        seqNo?: number;
        sinDay?: number;
      }>;
    };
  };
};

export type GetApiReceiptSyobyoKeikaHistoryQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiReceiptSyobyoKeikaHistoryQuery = {
  __typename?: "query_root";
  getApiReceiptSyobyoKeikaHistory?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptSyobyoKeikaHistoryResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptSyobyoKeikaHistoryResponse";
      data?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoSyobyoKeikaHistoryDto";
        sinYm?: number;
        sinYmDisplay?: string;
        hokenId?: number;
        hokenName?: string;
        syobyoKeikaList?: Array<{
          __typename?: "EmrCloudApiResponsesReceiptDtoSyobyoKeikaDto";
          sinDay?: number;
          seqNo?: number;
          keika?: string;
        }>;
      }>;
    };
  };
};

export type GetApiReceiptGetSinDateRaiinInfListQueryVariables = Types.Exact<{
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetSinDateRaiinInfListQuery = {
  __typename?: "query_root";
  getApiReceiptGetSinDateRaiinInfList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetSinDateRaiinInfListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetSinDateRaiinInfListResponse";
      sinDateList?: Array<number>;
    };
  };
};

export type GetApiReceiptGetReceByomeiCheckingQueryVariables = Types.Exact<{
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetReceByomeiCheckingQuery = {
  __typename?: "query_root";
  getApiReceiptGetReceByomeiChecking?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetReceByomeiCheckingResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetReceByomeiCheckingResponse";
      data?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceByomeiCheckingDto";
        displayItemName?: string;
        listCheckingItem?: Array<{
          __typename?: "EmrCloudApiResponsesReceiptDtoByomeiCheckingDto";
          itemCd?: string;
          byomeiCd?: string;
          byomei?: string;
          fullByomei?: string;
          sikkanKbn?: number;
          sikkanCd?: number;
          isAdopted?: boolean;
          nanbyoCd?: number;
        }>;
      }>;
    };
  };
};

export type GetApiReceiptGetReceCmtListQueryVariables = Types.Exact<{
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetReceCmtListQuery = {
  __typename?: "query_root";
  getApiReceiptGetReceCmtList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetReceCmtListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetReceCmtListResponse";
      headerItemCmtList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
        id?: string;
        ptId?: string;
        seqNo?: number;
        sinYm?: number;
        hokenId?: number;
        cmtKbn?: number;
        cmtSbt?: number;
        cmt?: string;
        cmtData?: string;
        itemCd?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
      }>;
      footerItemCmtList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
        id?: string;
        ptId?: string;
        seqNo?: number;
        sinYm?: number;
        hokenId?: number;
        cmtKbn?: number;
        cmtSbt?: number;
        cmt?: string;
        cmtData?: string;
        itemCd?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
      }>;
      headerFreeCmtList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
        id?: string;
        ptId?: string;
        seqNo?: number;
        sinYm?: number;
        hokenId?: number;
        cmtKbn?: number;
        cmtSbt?: number;
        cmt?: string;
        cmtData?: string;
        itemCd?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
      }>;
      footerFreeCmtList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
        id?: string;
        ptId?: string;
        seqNo?: number;
        sinYm?: number;
        hokenId?: number;
        cmtKbn?: number;
        cmtSbt?: number;
        cmt?: string;
        cmtData?: string;
        itemCd?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
      }>;
    };
  };
};

export type GetApiReceiptReceCmtHistoryQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiReceiptReceCmtHistoryQuery = {
  __typename?: "query_root";
  getApiReceiptReceCmtHistory?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptReceCmtHistoryResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptReceCmtHistoryResponse";
      data?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtHistoryDto";
        sinYm?: number;
        sinYmDisplay?: string;
        hokenId?: number;
        hokenName?: string;
        headerItemCmtList?: Array<{
          __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
          id?: string;
          ptId?: string;
          seqNo?: number;
          sinYm?: number;
          hokenId?: number;
          cmtKbn?: number;
          cmtSbt?: number;
          cmt?: string;
          cmtData?: string;
          itemCd?: string;
          cmtCol1?: number;
          cmtCol2?: number;
          cmtCol3?: number;
          cmtCol4?: number;
          cmtColKeta1?: number;
          cmtColKeta2?: number;
          cmtColKeta3?: number;
          cmtColKeta4?: number;
        }>;
        footerItemCmtList?: Array<{
          __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
          id?: string;
          ptId?: string;
          seqNo?: number;
          sinYm?: number;
          hokenId?: number;
          cmtKbn?: number;
          cmtSbt?: number;
          cmt?: string;
          cmtData?: string;
          itemCd?: string;
          cmtCol1?: number;
          cmtCol2?: number;
          cmtCol3?: number;
          cmtCol4?: number;
          cmtColKeta1?: number;
          cmtColKeta2?: number;
          cmtColKeta3?: number;
          cmtColKeta4?: number;
        }>;
        headerFreeCmtList?: Array<{
          __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
          id?: string;
          ptId?: string;
          seqNo?: number;
          sinYm?: number;
          hokenId?: number;
          cmtKbn?: number;
          cmtSbt?: number;
          cmt?: string;
          cmtData?: string;
          itemCd?: string;
          cmtCol1?: number;
          cmtCol2?: number;
          cmtCol3?: number;
          cmtCol4?: number;
          cmtColKeta1?: number;
          cmtColKeta2?: number;
          cmtColKeta3?: number;
          cmtColKeta4?: number;
        }>;
        footerFreeCmtList?: Array<{
          __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
          id?: string;
          ptId?: string;
          seqNo?: number;
          sinYm?: number;
          hokenId?: number;
          cmtKbn?: number;
          cmtSbt?: number;
          cmt?: string;
          cmtData?: string;
          itemCd?: string;
          cmtCol1?: number;
          cmtCol2?: number;
          cmtCol3?: number;
          cmtCol4?: number;
          cmtColKeta1?: number;
          cmtColKeta2?: number;
          cmtColKeta3?: number;
          cmtColKeta4?: number;
        }>;
      }>;
    };
  };
};

export type PostApiReceiptSaveReceCmtListMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsReceiptSaveReceCmtListRequestInput>;
}>;

export type PostApiReceiptSaveReceCmtListMutation = {
  __typename?: "mutation_root";
  postApiReceiptSaveReceCmtList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptSaveReceCmtListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptSaveReceCmtListResponse";
      status?: boolean;
      headerItemCmtInvalidList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
        id?: string;
        ptId?: string;
        seqNo?: number;
        sinYm?: number;
        hokenId?: number;
        cmtKbn?: number;
        cmtSbt?: number;
        cmt?: string;
        cmtData?: string;
        itemCd?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
      }>;
      footerItemCmtInvalidList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
        id?: string;
        ptId?: string;
        seqNo?: number;
        sinYm?: number;
        hokenId?: number;
        cmtKbn?: number;
        cmtSbt?: number;
        cmt?: string;
        cmtData?: string;
        itemCd?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
      }>;
      headerFreeCmtInvalidList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
        id?: string;
        ptId?: string;
        seqNo?: number;
        sinYm?: number;
        hokenId?: number;
        cmtKbn?: number;
        cmtSbt?: number;
        cmt?: string;
        cmtData?: string;
        itemCd?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
      }>;
      footerFreeCmtInvalidList?: Array<{
        __typename?: "EmrCloudApiResponsesReceiptDtoReceCmtDto";
        id?: string;
        ptId?: string;
        seqNo?: number;
        sinYm?: number;
        hokenId?: number;
        cmtKbn?: number;
        cmtSbt?: number;
        cmt?: string;
        cmtData?: string;
        itemCd?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
      }>;
    };
  };
};

export type GetApiReceiptGetListRaiinInfQueryVariables = Types.Exact<{
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  dayInMonth?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  rpNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  seqNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptGetListRaiinInfQuery = {
  __typename?: "query_root";
  getApiReceiptGetListRaiinInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptGetListRaiinInfResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptGetListRaiinInfResponse";
      raiinInfList?: Array<{
        __typename?: "UseCaseReceiptRaiinInfItem";
        ptId?: string;
        sinDate?: number;
        raiinNo?: string;
        raiinNoBinding?: string;
        uketukeTime?: string;
        uketukeTimeBinding?: string;
        sinEndTime?: string;
        sinEndTimeBinding?: string;
        status?: number;
      }>;
    };
  };
};

export type GetApiReceiptCheckExistSyobyoKeikaQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDay?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiReceiptCheckExistSyobyoKeikaQuery = {
  __typename?: "query_root";
  getApiReceiptCheckExistSyobyoKeika?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceiptCheckExistSyobyoKeikaResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceiptCheckExistSyobyoKeikaResponse";
      isExisted?: boolean;
    };
  };
};

export const PostApiReceiptGetListDocument = gql`
  mutation postApiReceiptGetList(
    $input: EmrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput!
  ) {
    postApiReceiptGetList(
      emrCloudApiRequestsReceiptReceiptListAdvancedSearchRequestInput: $input
    ) {
      data {
        receiptList {
          seikyuKbn
          sinYm
          isReceInfDetailExist
          isPaperRece
          hokenId
          hokenKbn
          output
          fusenKbn
          statusKbn
          isPending
          ptNum
          ptId
          kanaName
          name
          sex
          age
          lastSinDateByHokenId
          birthDay
          receSbt
          hokensyaNo
          tensu
          hokenSbtCd
          kohi1Nissu
          isSyoukiInfExist
          isReceCmtExist
          isSyobyoKeikaExist
          receSeikyuCmt
          lastVisitDate
          kaName
          sName
          isPtKyuseiExist
          futansyaNoKohi1
          futansyaNoKohi2
          futansyaNoKohi3
          futansyaNoKohi4
          isPtTest
          hokenNissu
          receCheckCmt
          birthDayDisplay
          lastVisitDateDisplay
          jibaiHokenName
          jibaiHokenTanto
          jibaiHokenTel
          rousaiCityName
          rousaiJigyosyoName
          rousaiKofuNo
          rousaiPrefName
        }
      }
    }
  }
`;
export type PostApiReceiptGetListMutationFn = Apollo.MutationFunction<
  PostApiReceiptGetListMutation,
  PostApiReceiptGetListMutationVariables
>;

/**
 * __usePostApiReceiptGetListMutation__
 *
 * To run a mutation, you first call `usePostApiReceiptGetListMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceiptGetListMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceiptGetListMutation, { data, loading, error }] = usePostApiReceiptGetListMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceiptGetListMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceiptGetListMutation,
    PostApiReceiptGetListMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceiptGetListMutation,
    PostApiReceiptGetListMutationVariables
  >(PostApiReceiptGetListDocument, options);
}
export type PostApiReceiptGetListMutationHookResult = ReturnType<
  typeof usePostApiReceiptGetListMutation
>;
export type PostApiReceiptGetListMutationResult =
  Apollo.MutationResult<PostApiReceiptGetListMutation>;
export type PostApiReceiptGetListMutationOptions = Apollo.BaseMutationOptions<
  PostApiReceiptGetListMutation,
  PostApiReceiptGetListMutationVariables
>;
export const GetApiReceiptValidateCreateUkeFileDocument = gql`
  query getApiReceiptValidateCreateUKEFile($seikyuYm: Int, $modeType: Int) {
    getApiReceiptValidateCreateUKEFile(
      seikyuYm: $seikyuYm
      modeType: $modeType
    ) {
      data {
        status
      }
    }
  }
`;

/**
 * __useGetApiReceiptValidateCreateUkeFileQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptValidateCreateUkeFileQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptValidateCreateUkeFileQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptValidateCreateUkeFileQuery({
 *   variables: {
 *      seikyuYm: // value for 'seikyuYm'
 *      modeType: // value for 'modeType'
 *   },
 * });
 */
export function useGetApiReceiptValidateCreateUkeFileQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptValidateCreateUkeFileQuery,
    GetApiReceiptValidateCreateUkeFileQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptValidateCreateUkeFileQuery,
    GetApiReceiptValidateCreateUkeFileQueryVariables
  >(GetApiReceiptValidateCreateUkeFileDocument, options);
}
export function useGetApiReceiptValidateCreateUkeFileLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptValidateCreateUkeFileQuery,
    GetApiReceiptValidateCreateUkeFileQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptValidateCreateUkeFileQuery,
    GetApiReceiptValidateCreateUkeFileQueryVariables
  >(GetApiReceiptValidateCreateUkeFileDocument, options);
}
export function useGetApiReceiptValidateCreateUkeFileSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptValidateCreateUkeFileQuery,
    GetApiReceiptValidateCreateUkeFileQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptValidateCreateUkeFileQuery,
    GetApiReceiptValidateCreateUkeFileQueryVariables
  >(GetApiReceiptValidateCreateUkeFileDocument, options);
}
export type GetApiReceiptValidateCreateUkeFileQueryHookResult = ReturnType<
  typeof useGetApiReceiptValidateCreateUkeFileQuery
>;
export type GetApiReceiptValidateCreateUkeFileLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptValidateCreateUkeFileLazyQuery
>;
export type GetApiReceiptValidateCreateUkeFileSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceiptValidateCreateUkeFileSuspenseQuery>;
export type GetApiReceiptValidateCreateUkeFileQueryResult = Apollo.QueryResult<
  GetApiReceiptValidateCreateUkeFileQuery,
  GetApiReceiptValidateCreateUkeFileQueryVariables
>;
export const PostApiReceiptCreateUkeFileDocument = gql`
  mutation postApiReceiptCreateUKEFile(
    $input: EmrCloudApiRequestsReceiptCreateUkeFileRequestInput!
  ) {
    postApiReceiptCreateUKEFile(
      emrCloudApiRequestsReceiptCreateUkeFileRequestInput: $input
    ) {
      status
      data {
        file
      }
    }
  }
`;
export type PostApiReceiptCreateUkeFileMutationFn = Apollo.MutationFunction<
  PostApiReceiptCreateUkeFileMutation,
  PostApiReceiptCreateUkeFileMutationVariables
>;

/**
 * __usePostApiReceiptCreateUkeFileMutation__
 *
 * To run a mutation, you first call `usePostApiReceiptCreateUkeFileMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceiptCreateUkeFileMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceiptCreateUkeFileMutation, { data, loading, error }] = usePostApiReceiptCreateUkeFileMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceiptCreateUkeFileMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceiptCreateUkeFileMutation,
    PostApiReceiptCreateUkeFileMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceiptCreateUkeFileMutation,
    PostApiReceiptCreateUkeFileMutationVariables
  >(PostApiReceiptCreateUkeFileDocument, options);
}
export type PostApiReceiptCreateUkeFileMutationHookResult = ReturnType<
  typeof usePostApiReceiptCreateUkeFileMutation
>;
export type PostApiReceiptCreateUkeFileMutationResult =
  Apollo.MutationResult<PostApiReceiptCreateUkeFileMutation>;
export type PostApiReceiptCreateUkeFileMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceiptCreateUkeFileMutation,
    PostApiReceiptCreateUkeFileMutationVariables
  >;
export const GetApiReceiptGetListSokatuMstDocument = gql`
  query getApiReceiptGetListSokatuMst($seikyuYm: Int) {
    getApiReceiptGetListSokatuMst(seikyuYm: $seikyuYm) {
      data {
        sokatuMstModels {
          prefNo
          startYm
          endYm
          reportId
          reportEdaNo
          sortNo
          reportName
          printType
          printNoType
          dataAll
          dataDisk
          dataPaper
          dataKbn
          diskKind
          diskCnt
          isSort
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetListSokatuMstQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetListSokatuMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetListSokatuMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetListSokatuMstQuery({
 *   variables: {
 *      seikyuYm: // value for 'seikyuYm'
 *   },
 * });
 */
export function useGetApiReceiptGetListSokatuMstQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetListSokatuMstQuery,
    GetApiReceiptGetListSokatuMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetListSokatuMstQuery,
    GetApiReceiptGetListSokatuMstQueryVariables
  >(GetApiReceiptGetListSokatuMstDocument, options);
}
export function useGetApiReceiptGetListSokatuMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetListSokatuMstQuery,
    GetApiReceiptGetListSokatuMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetListSokatuMstQuery,
    GetApiReceiptGetListSokatuMstQueryVariables
  >(GetApiReceiptGetListSokatuMstDocument, options);
}
export function useGetApiReceiptGetListSokatuMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetListSokatuMstQuery,
    GetApiReceiptGetListSokatuMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetListSokatuMstQuery,
    GetApiReceiptGetListSokatuMstQueryVariables
  >(GetApiReceiptGetListSokatuMstDocument, options);
}
export type GetApiReceiptGetListSokatuMstQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListSokatuMstQuery
>;
export type GetApiReceiptGetListSokatuMstLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListSokatuMstLazyQuery
>;
export type GetApiReceiptGetListSokatuMstSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListSokatuMstSuspenseQuery
>;
export type GetApiReceiptGetListSokatuMstQueryResult = Apollo.QueryResult<
  GetApiReceiptGetListSokatuMstQuery,
  GetApiReceiptGetListSokatuMstQueryVariables
>;
export const GetApiReceiptGetRecePreviewListDocument = gql`
  query getApiReceiptGetRecePreviewList(
    $ptId: BigInt
    $receiptPreviewType: Int
  ) {
    getApiReceiptGetRecePreviewList(
      ptId: $ptId
      receiptPreviewType: $receiptPreviewType
    ) {
      data {
        recePreviewList {
          seikyuYm
          sinYm
          hokenId
          hokenKbn
          seikyuYmDisplay
          sinYmDisplay
          hokenPatternName
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetRecePreviewListQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetRecePreviewListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetRecePreviewListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetRecePreviewListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      receiptPreviewType: // value for 'receiptPreviewType'
 *   },
 * });
 */
export function useGetApiReceiptGetRecePreviewListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetRecePreviewListQuery,
    GetApiReceiptGetRecePreviewListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetRecePreviewListQuery,
    GetApiReceiptGetRecePreviewListQueryVariables
  >(GetApiReceiptGetRecePreviewListDocument, options);
}
export function useGetApiReceiptGetRecePreviewListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetRecePreviewListQuery,
    GetApiReceiptGetRecePreviewListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetRecePreviewListQuery,
    GetApiReceiptGetRecePreviewListQueryVariables
  >(GetApiReceiptGetRecePreviewListDocument, options);
}
export function useGetApiReceiptGetRecePreviewListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetRecePreviewListQuery,
    GetApiReceiptGetRecePreviewListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetRecePreviewListQuery,
    GetApiReceiptGetRecePreviewListQueryVariables
  >(GetApiReceiptGetRecePreviewListDocument, options);
}
export type GetApiReceiptGetRecePreviewListQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetRecePreviewListQuery
>;
export type GetApiReceiptGetRecePreviewListLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetRecePreviewListLazyQuery
>;
export type GetApiReceiptGetRecePreviewListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetRecePreviewListSuspenseQuery
>;
export type GetApiReceiptGetRecePreviewListQueryResult = Apollo.QueryResult<
  GetApiReceiptGetRecePreviewListQuery,
  GetApiReceiptGetRecePreviewListQueryVariables
>;
export const GetApiReceiptGetReceCheckOptionListDocument = gql`
  query getApiReceiptGetReceCheckOptionList {
    getApiReceiptGetReceCheckOptionList {
      data {
        receCheckOptionList {
          checkboxKey
          errCd
          checkOpt
          isInvalid
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetReceCheckOptionListQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetReceCheckOptionListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetReceCheckOptionListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetReceCheckOptionListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiReceiptGetReceCheckOptionListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetReceCheckOptionListQuery,
    GetApiReceiptGetReceCheckOptionListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetReceCheckOptionListQuery,
    GetApiReceiptGetReceCheckOptionListQueryVariables
  >(GetApiReceiptGetReceCheckOptionListDocument, options);
}
export function useGetApiReceiptGetReceCheckOptionListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetReceCheckOptionListQuery,
    GetApiReceiptGetReceCheckOptionListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetReceCheckOptionListQuery,
    GetApiReceiptGetReceCheckOptionListQueryVariables
  >(GetApiReceiptGetReceCheckOptionListDocument, options);
}
export function useGetApiReceiptGetReceCheckOptionListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetReceCheckOptionListQuery,
    GetApiReceiptGetReceCheckOptionListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetReceCheckOptionListQuery,
    GetApiReceiptGetReceCheckOptionListQueryVariables
  >(GetApiReceiptGetReceCheckOptionListDocument, options);
}
export type GetApiReceiptGetReceCheckOptionListQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceCheckOptionListQuery
>;
export type GetApiReceiptGetReceCheckOptionListLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceCheckOptionListLazyQuery
>;
export type GetApiReceiptGetReceCheckOptionListSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceiptGetReceCheckOptionListSuspenseQuery>;
export type GetApiReceiptGetReceCheckOptionListQueryResult = Apollo.QueryResult<
  GetApiReceiptGetReceCheckOptionListQuery,
  GetApiReceiptGetReceCheckOptionListQueryVariables
>;
export const PostApiReceiptSaveReceCheckOptDocument = gql`
  mutation postApiReceiptSaveReceCheckOpt(
    $input: EmrCloudApiRequestsReceiptSaveReceCheckOptRequestInput!
  ) {
    postApiReceiptSaveReceCheckOpt(
      emrCloudApiRequestsReceiptSaveReceCheckOptRequestInput: $input
    ) {
      data {
        success
      }
    }
  }
`;
export type PostApiReceiptSaveReceCheckOptMutationFn = Apollo.MutationFunction<
  PostApiReceiptSaveReceCheckOptMutation,
  PostApiReceiptSaveReceCheckOptMutationVariables
>;

/**
 * __usePostApiReceiptSaveReceCheckOptMutation__
 *
 * To run a mutation, you first call `usePostApiReceiptSaveReceCheckOptMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceiptSaveReceCheckOptMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceiptSaveReceCheckOptMutation, { data, loading, error }] = usePostApiReceiptSaveReceCheckOptMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceiptSaveReceCheckOptMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceiptSaveReceCheckOptMutation,
    PostApiReceiptSaveReceCheckOptMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceiptSaveReceCheckOptMutation,
    PostApiReceiptSaveReceCheckOptMutationVariables
  >(PostApiReceiptSaveReceCheckOptDocument, options);
}
export type PostApiReceiptSaveReceCheckOptMutationHookResult = ReturnType<
  typeof usePostApiReceiptSaveReceCheckOptMutation
>;
export type PostApiReceiptSaveReceCheckOptMutationResult =
  Apollo.MutationResult<PostApiReceiptSaveReceCheckOptMutation>;
export type PostApiReceiptSaveReceCheckOptMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceiptSaveReceCheckOptMutation,
    PostApiReceiptSaveReceCheckOptMutationVariables
  >;
export const GetApiReceiptGetListKaikeiInfDocument = gql`
  query getApiReceiptGetListKaikeiInf($sinYm: Int, $ptId: BigInt) {
    getApiReceiptGetListKaikeiInf(sinYm: $sinYm, ptId: $ptId) {
      data {
        ptHokenInfKaikeiList {
          hokenId
          ptId
          hokenKbn
          houbetu
          honkeKbn
          hokensyaNo
          hokenStartDate
          hokenEndDate
          hokenName
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetListKaikeiInfQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetListKaikeiInfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetListKaikeiInfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetListKaikeiInfQuery({
 *   variables: {
 *      sinYm: // value for 'sinYm'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiReceiptGetListKaikeiInfQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetListKaikeiInfQuery,
    GetApiReceiptGetListKaikeiInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetListKaikeiInfQuery,
    GetApiReceiptGetListKaikeiInfQueryVariables
  >(GetApiReceiptGetListKaikeiInfDocument, options);
}
export function useGetApiReceiptGetListKaikeiInfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetListKaikeiInfQuery,
    GetApiReceiptGetListKaikeiInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetListKaikeiInfQuery,
    GetApiReceiptGetListKaikeiInfQueryVariables
  >(GetApiReceiptGetListKaikeiInfDocument, options);
}
export function useGetApiReceiptGetListKaikeiInfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetListKaikeiInfQuery,
    GetApiReceiptGetListKaikeiInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetListKaikeiInfQuery,
    GetApiReceiptGetListKaikeiInfQueryVariables
  >(GetApiReceiptGetListKaikeiInfDocument, options);
}
export type GetApiReceiptGetListKaikeiInfQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListKaikeiInfQuery
>;
export type GetApiReceiptGetListKaikeiInfLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListKaikeiInfLazyQuery
>;
export type GetApiReceiptGetListKaikeiInfSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListKaikeiInfSuspenseQuery
>;
export type GetApiReceiptGetListKaikeiInfQueryResult = Apollo.QueryResult<
  GetApiReceiptGetListKaikeiInfQuery,
  GetApiReceiptGetListKaikeiInfQueryVariables
>;
export const GetApiReceiptGetInsuranceReceInfListDocument = gql`
  query getApiReceiptGetInsuranceReceInfList(
    $seikyuYm: Int
    $sinYm: Int
    $ptId: BigInt
    $hokenId: Int
  ) {
    getApiReceiptGetInsuranceReceInfList(
      seikyuYm: $seikyuYm
      sinYm: $sinYm
      ptId: $ptId
      hokenId: $hokenId
    ) {
      data {
        insuranceReceInf {
          seikyuYm
          ptId
          sinYm
          hokenId
          hokenPId
          hokenId2
          kohi1Id
          kohi2Id
          kohi3Id
          kohi4Id
          hokenKbn
          receSbt
          hokensyaNo
          hokenReceTensu
          hokenReceFutan
          kohi1ReceTensu
          kohi1ReceFutan
          kohi1ReceKyufu
          kohi2ReceTensu
          kohi2ReceFutan
          kohi2ReceKyufu
          kohi3ReceTensu
          kohi3ReceFutan
          kohi3ReceKyufu
          kohi4ReceTensu
          kohi4ReceFutan
          kohi4ReceKyufu
          hokenNissu
          kohi1Nissu
          kohi2Nissu
          kohi3Nissu
          kohi4Nissu
          kohi1ReceKisai
          kohi2ReceKisai
          kohi3ReceKisai
          kohi4ReceKisai
          tokki1
          tokki2
          tokki3
          tokki4
          tokki5
          rousaiIFutan
          rousaiRoFutan
          jibaiITensu
          jibaiRoTensu
          jibaiHaFutan
          jibaiNiFutan
          jibaiHoSindan
          jibaiHeMeisai
          jibaiAFutan
          jibaiBFutan
          jibaiCFutan
          jibaiDFutan
          jibaiKenpoFutan
          futansyaNoKohi1
          futansyaNoKohi2
          futansyaNoKohi3
          futansyaNoKohi4
          jyukyusyaNoKohi1
          jyukyusyaNoKohi2
          jyukyusyaNoKohi3
          jyukyusyaNoKohi4
          hokenInfRousaiKofuNo
          insuranceName
          kigo
          bango
          edaNo
        }
        hokenName
        sinYmDisplay
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetInsuranceReceInfListQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetInsuranceReceInfListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetInsuranceReceInfListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetInsuranceReceInfListQuery({
 *   variables: {
 *      seikyuYm: // value for 'seikyuYm'
 *      sinYm: // value for 'sinYm'
 *      ptId: // value for 'ptId'
 *      hokenId: // value for 'hokenId'
 *   },
 * });
 */
export function useGetApiReceiptGetInsuranceReceInfListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetInsuranceReceInfListQuery,
    GetApiReceiptGetInsuranceReceInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetInsuranceReceInfListQuery,
    GetApiReceiptGetInsuranceReceInfListQueryVariables
  >(GetApiReceiptGetInsuranceReceInfListDocument, options);
}
export function useGetApiReceiptGetInsuranceReceInfListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetInsuranceReceInfListQuery,
    GetApiReceiptGetInsuranceReceInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetInsuranceReceInfListQuery,
    GetApiReceiptGetInsuranceReceInfListQueryVariables
  >(GetApiReceiptGetInsuranceReceInfListDocument, options);
}
export function useGetApiReceiptGetInsuranceReceInfListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetInsuranceReceInfListQuery,
    GetApiReceiptGetInsuranceReceInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetInsuranceReceInfListQuery,
    GetApiReceiptGetInsuranceReceInfListQueryVariables
  >(GetApiReceiptGetInsuranceReceInfListDocument, options);
}
export type GetApiReceiptGetInsuranceReceInfListQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetInsuranceReceInfListQuery
>;
export type GetApiReceiptGetInsuranceReceInfListLazyQueryHookResult =
  ReturnType<typeof useGetApiReceiptGetInsuranceReceInfListLazyQuery>;
export type GetApiReceiptGetInsuranceReceInfListSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceiptGetInsuranceReceInfListSuspenseQuery>;
export type GetApiReceiptGetInsuranceReceInfListQueryResult =
  Apollo.QueryResult<
    GetApiReceiptGetInsuranceReceInfListQuery,
    GetApiReceiptGetInsuranceReceInfListQueryVariables
  >;
export const GetApiReceiptGetDiseaseReceListDocument = gql`
  query getApiReceiptGetDiseaseReceList(
    $hokenId: Int
    $ptId: BigInt
    $sinYm: Int
  ) {
    getApiReceiptGetDiseaseReceList(
      hokenId: $hokenId
      ptId: $ptId
      sinYm: $sinYm
    ) {
      data {
        diseaseReceList {
          byomei
          startDate
          tenkiKbn
          tenkiDate
          isMain
          isSuspect
          nanbyoCd
          sikkanKbn
          hosokuCmt
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetDiseaseReceListQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetDiseaseReceListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetDiseaseReceListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetDiseaseReceListQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceiptGetDiseaseReceListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetDiseaseReceListQuery,
    GetApiReceiptGetDiseaseReceListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetDiseaseReceListQuery,
    GetApiReceiptGetDiseaseReceListQueryVariables
  >(GetApiReceiptGetDiseaseReceListDocument, options);
}
export function useGetApiReceiptGetDiseaseReceListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetDiseaseReceListQuery,
    GetApiReceiptGetDiseaseReceListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetDiseaseReceListQuery,
    GetApiReceiptGetDiseaseReceListQueryVariables
  >(GetApiReceiptGetDiseaseReceListDocument, options);
}
export function useGetApiReceiptGetDiseaseReceListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetDiseaseReceListQuery,
    GetApiReceiptGetDiseaseReceListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetDiseaseReceListQuery,
    GetApiReceiptGetDiseaseReceListQueryVariables
  >(GetApiReceiptGetDiseaseReceListDocument, options);
}
export type GetApiReceiptGetDiseaseReceListQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetDiseaseReceListQuery
>;
export type GetApiReceiptGetDiseaseReceListLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetDiseaseReceListLazyQuery
>;
export type GetApiReceiptGetDiseaseReceListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetDiseaseReceListSuspenseQuery
>;
export type GetApiReceiptGetDiseaseReceListQueryResult = Apollo.QueryResult<
  GetApiReceiptGetDiseaseReceListQuery,
  GetApiReceiptGetDiseaseReceListQueryVariables
>;
export const GetApiReceiptGetReceiCheckListDocument = gql`
  query getApiReceiptGetReceiCheckList(
    $hokenId: Int
    $ptId: BigInt
    $sinYm: Int
  ) {
    getApiReceiptGetReceiCheckList(
      hokenId: $hokenId
      ptId: $ptId
      sinYm: $sinYm
    ) {
      data {
        receiptCheckCmtErrList {
          seqNo
          sortNo
          isChecked
          textDisplay1
          textDisplay2
          errorCd
          aCd
          bCd
          sinDate
          statusColor
          receiptCheckIsErrItem
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetReceiCheckListQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetReceiCheckListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetReceiCheckListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetReceiCheckListQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceiptGetReceiCheckListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetReceiCheckListQuery,
    GetApiReceiptGetReceiCheckListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetReceiCheckListQuery,
    GetApiReceiptGetReceiCheckListQueryVariables
  >(GetApiReceiptGetReceiCheckListDocument, options);
}
export function useGetApiReceiptGetReceiCheckListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetReceiCheckListQuery,
    GetApiReceiptGetReceiCheckListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetReceiCheckListQuery,
    GetApiReceiptGetReceiCheckListQueryVariables
  >(GetApiReceiptGetReceiCheckListDocument, options);
}
export function useGetApiReceiptGetReceiCheckListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetReceiCheckListQuery,
    GetApiReceiptGetReceiCheckListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetReceiCheckListQuery,
    GetApiReceiptGetReceiCheckListQueryVariables
  >(GetApiReceiptGetReceiCheckListDocument, options);
}
export type GetApiReceiptGetReceiCheckListQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceiCheckListQuery
>;
export type GetApiReceiptGetReceiCheckListLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceiCheckListLazyQuery
>;
export type GetApiReceiptGetReceiCheckListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceiCheckListSuspenseQuery
>;
export type GetApiReceiptGetReceiCheckListQueryResult = Apollo.QueryResult<
  GetApiReceiptGetReceiCheckListQuery,
  GetApiReceiptGetReceiCheckListQueryVariables
>;
export const PostApiReceiptSaveReceCheckCmtListDocument = gql`
  mutation postApiReceiptSaveReceCheckCmtList(
    $input: EmrCloudApiRequestsReceiptSaveReceCheckCmtListRequestInput
  ) {
    postApiReceiptSaveReceCheckCmtList(
      emrCloudApiRequestsReceiptSaveReceCheckCmtListRequestInput: $input
    ) {
      status
    }
  }
`;
export type PostApiReceiptSaveReceCheckCmtListMutationFn =
  Apollo.MutationFunction<
    PostApiReceiptSaveReceCheckCmtListMutation,
    PostApiReceiptSaveReceCheckCmtListMutationVariables
  >;

/**
 * __usePostApiReceiptSaveReceCheckCmtListMutation__
 *
 * To run a mutation, you first call `usePostApiReceiptSaveReceCheckCmtListMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceiptSaveReceCheckCmtListMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceiptSaveReceCheckCmtListMutation, { data, loading, error }] = usePostApiReceiptSaveReceCheckCmtListMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceiptSaveReceCheckCmtListMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceiptSaveReceCheckCmtListMutation,
    PostApiReceiptSaveReceCheckCmtListMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceiptSaveReceCheckCmtListMutation,
    PostApiReceiptSaveReceCheckCmtListMutationVariables
  >(PostApiReceiptSaveReceCheckCmtListDocument, options);
}
export type PostApiReceiptSaveReceCheckCmtListMutationHookResult = ReturnType<
  typeof usePostApiReceiptSaveReceCheckCmtListMutation
>;
export type PostApiReceiptSaveReceCheckCmtListMutationResult =
  Apollo.MutationResult<PostApiReceiptSaveReceCheckCmtListMutation>;
export type PostApiReceiptSaveReceCheckCmtListMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceiptSaveReceCheckCmtListMutation,
    PostApiReceiptSaveReceCheckCmtListMutationVariables
  >;
export const GetApiReceiptGetListSinKouiDocument = gql`
  query getApiReceiptGetListSinKoui($ptId: BigInt) {
    getApiReceiptGetListSinKoui(ptId: $ptId) {
      data {
        sinYms
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiReceiptGetListSinKouiQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetListSinKouiQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetListSinKouiQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetListSinKouiQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiReceiptGetListSinKouiQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetListSinKouiQuery,
    GetApiReceiptGetListSinKouiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetListSinKouiQuery,
    GetApiReceiptGetListSinKouiQueryVariables
  >(GetApiReceiptGetListSinKouiDocument, options);
}
export function useGetApiReceiptGetListSinKouiLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetListSinKouiQuery,
    GetApiReceiptGetListSinKouiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetListSinKouiQuery,
    GetApiReceiptGetListSinKouiQueryVariables
  >(GetApiReceiptGetListSinKouiDocument, options);
}
export function useGetApiReceiptGetListSinKouiSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetListSinKouiQuery,
    GetApiReceiptGetListSinKouiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetListSinKouiQuery,
    GetApiReceiptGetListSinKouiQueryVariables
  >(GetApiReceiptGetListSinKouiDocument, options);
}
export type GetApiReceiptGetListSinKouiQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListSinKouiQuery
>;
export type GetApiReceiptGetListSinKouiLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListSinKouiLazyQuery
>;
export type GetApiReceiptGetListSinKouiSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListSinKouiSuspenseQuery
>;
export type GetApiReceiptGetListSinKouiQueryResult = Apollo.QueryResult<
  GetApiReceiptGetListSinKouiQuery,
  GetApiReceiptGetListSinKouiQueryVariables
>;
export const GetApiReceiptGetInsuranceInfDocument = gql`
  query getApiReceiptGetInsuranceInf($ptId: BigInt, $sinYm: Int) {
    getApiReceiptGetInsuranceInf(ptId: $ptId, sinYm: $sinYm) {
      message
      status
      data {
        insuranceInfDtos {
          hokenId
          insuranceName
          bango
          edaNo
          futansyaNoKohi1
          futansyaNoKohi2
          futansyaNoKohi3
          futansyaNoKohi4
          hokenId
          hokenKbn
          hokensyaNo
          ichibuFutan
          insuranceName
          jyukyusyaNoKohi1
          jyukyusyaNoKohi2
          jyukyusyaNoKohi3
          jyukyusyaNoKohi4
          kigo
          kohi1Id
          kohi1ReceKisai
          kohi2Id
          kohi2ReceKisai
          kohi3Id
          kohi3ReceKisai
          kohi4ReceKisai
          nissu
          ptFutan
          tensu
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetInsuranceInfQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetInsuranceInfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetInsuranceInfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetInsuranceInfQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceiptGetInsuranceInfQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetInsuranceInfQuery,
    GetApiReceiptGetInsuranceInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetInsuranceInfQuery,
    GetApiReceiptGetInsuranceInfQueryVariables
  >(GetApiReceiptGetInsuranceInfDocument, options);
}
export function useGetApiReceiptGetInsuranceInfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetInsuranceInfQuery,
    GetApiReceiptGetInsuranceInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetInsuranceInfQuery,
    GetApiReceiptGetInsuranceInfQueryVariables
  >(GetApiReceiptGetInsuranceInfDocument, options);
}
export function useGetApiReceiptGetInsuranceInfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetInsuranceInfQuery,
    GetApiReceiptGetInsuranceInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetInsuranceInfQuery,
    GetApiReceiptGetInsuranceInfQueryVariables
  >(GetApiReceiptGetInsuranceInfDocument, options);
}
export type GetApiReceiptGetInsuranceInfQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetInsuranceInfQuery
>;
export type GetApiReceiptGetInsuranceInfLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetInsuranceInfLazyQuery
>;
export type GetApiReceiptGetInsuranceInfSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetInsuranceInfSuspenseQuery
>;
export type GetApiReceiptGetInsuranceInfQueryResult = Apollo.QueryResult<
  GetApiReceiptGetInsuranceInfQuery,
  GetApiReceiptGetInsuranceInfQueryVariables
>;
export const GetApiReceiptGetSinMeiInMonthListDocument = gql`
  query getApiReceiptGetSinMeiInMonthList(
    $hokenId: Int
    $ptId: BigInt
    $seikyuYm: Int
    $sinYm: Int
  ) {
    getApiReceiptGetSinMeiInMonthList(
      hokenId: $hokenId
      ptId: $ptId
      seikyuYm: $seikyuYm
      sinYm: $sinYm
    ) {
      data {
        holidays
        sinMeiModels {
          sinId
          sinIdBinding
          itemName
          quantity
          tenKai
          inOutKbn
          isRowColorGray
          sinRpNo
          sinSeqNo
          itemCd
          isDrug
          days
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetSinMeiInMonthListQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetSinMeiInMonthListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetSinMeiInMonthListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetSinMeiInMonthListQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *      seikyuYm: // value for 'seikyuYm'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceiptGetSinMeiInMonthListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetSinMeiInMonthListQuery,
    GetApiReceiptGetSinMeiInMonthListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetSinMeiInMonthListQuery,
    GetApiReceiptGetSinMeiInMonthListQueryVariables
  >(GetApiReceiptGetSinMeiInMonthListDocument, options);
}
export function useGetApiReceiptGetSinMeiInMonthListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetSinMeiInMonthListQuery,
    GetApiReceiptGetSinMeiInMonthListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetSinMeiInMonthListQuery,
    GetApiReceiptGetSinMeiInMonthListQueryVariables
  >(GetApiReceiptGetSinMeiInMonthListDocument, options);
}
export function useGetApiReceiptGetSinMeiInMonthListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetSinMeiInMonthListQuery,
    GetApiReceiptGetSinMeiInMonthListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetSinMeiInMonthListQuery,
    GetApiReceiptGetSinMeiInMonthListQueryVariables
  >(GetApiReceiptGetSinMeiInMonthListDocument, options);
}
export type GetApiReceiptGetSinMeiInMonthListQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetSinMeiInMonthListQuery
>;
export type GetApiReceiptGetSinMeiInMonthListLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetSinMeiInMonthListLazyQuery
>;
export type GetApiReceiptGetSinMeiInMonthListSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceiptGetSinMeiInMonthListSuspenseQuery>;
export type GetApiReceiptGetSinMeiInMonthListQueryResult = Apollo.QueryResult<
  GetApiReceiptGetSinMeiInMonthListQuery,
  GetApiReceiptGetSinMeiInMonthListQueryVariables
>;
export const GetApiReceiptGetReceHenReasonDocument = gql`
  query getApiReceiptGetReceHenReason(
    $hokenId: Int
    $ptId: BigInt
    $seikyuYm: Int
    $sinDate: Int
  ) {
    getApiReceiptGetReceHenReason(
      hokenId: $hokenId
      ptId: $ptId
      seikyuYm: $seikyuYm
      sinDate: $sinDate
    ) {
      data {
        receReasonCmt
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetReceHenReasonQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetReceHenReasonQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetReceHenReasonQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetReceHenReasonQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *      seikyuYm: // value for 'seikyuYm'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiReceiptGetReceHenReasonQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetReceHenReasonQuery,
    GetApiReceiptGetReceHenReasonQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetReceHenReasonQuery,
    GetApiReceiptGetReceHenReasonQueryVariables
  >(GetApiReceiptGetReceHenReasonDocument, options);
}
export function useGetApiReceiptGetReceHenReasonLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetReceHenReasonQuery,
    GetApiReceiptGetReceHenReasonQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetReceHenReasonQuery,
    GetApiReceiptGetReceHenReasonQueryVariables
  >(GetApiReceiptGetReceHenReasonDocument, options);
}
export function useGetApiReceiptGetReceHenReasonSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetReceHenReasonQuery,
    GetApiReceiptGetReceHenReasonQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetReceHenReasonQuery,
    GetApiReceiptGetReceHenReasonQueryVariables
  >(GetApiReceiptGetReceHenReasonDocument, options);
}
export type GetApiReceiptGetReceHenReasonQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceHenReasonQuery
>;
export type GetApiReceiptGetReceHenReasonLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceHenReasonLazyQuery
>;
export type GetApiReceiptGetReceHenReasonSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceHenReasonSuspenseQuery
>;
export type GetApiReceiptGetReceHenReasonQueryResult = Apollo.QueryResult<
  GetApiReceiptGetReceHenReasonQuery,
  GetApiReceiptGetReceHenReasonQueryVariables
>;
export const GetApiReceGetSinMeiInMonthListDocument = gql`
  query getApiReceGetSinMeiInMonthList(
    $hokenId: Int
    $ptId: BigInt
    $seikyuYm: Int
    $sinYm: Int
  ) {
    getApiReceiptGetSinMeiInMonthList(
      hokenId: $hokenId
      ptId: $ptId
      seikyuYm: $seikyuYm
      sinYm: $sinYm
    ) {
      data {
        sinMeiModels {
          sinId
          sinIdBinding
          itemName
          suryo
          unitName
          tenKai
          totalTen
          totalKingaku
          kingaku
          futanS
          futanK1
          futanK2
          futanK3
          futanK4
          cdKbn
          jihiSbt
          enTenKbn
          santeiKbn
          inOutKbn
          sinRpNo
          sinSeqNo
          quantity
          sinHoTotalTen
          total
          totalBinding
          futanSBinding
          futanK1Binding
          futanK2Binding
          futanK3Binding
          futanK4Binding
          asterisk
          isRowColorGray
          isForegroundRed
          days
          itemCd
          drugKbn
          isDrug
        }
        holidays
      }
    }
  }
`;

/**
 * __useGetApiReceGetSinMeiInMonthListQuery__
 *
 * To run a query within a React component, call `useGetApiReceGetSinMeiInMonthListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceGetSinMeiInMonthListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceGetSinMeiInMonthListQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *      seikyuYm: // value for 'seikyuYm'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceGetSinMeiInMonthListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceGetSinMeiInMonthListQuery,
    GetApiReceGetSinMeiInMonthListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceGetSinMeiInMonthListQuery,
    GetApiReceGetSinMeiInMonthListQueryVariables
  >(GetApiReceGetSinMeiInMonthListDocument, options);
}
export function useGetApiReceGetSinMeiInMonthListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceGetSinMeiInMonthListQuery,
    GetApiReceGetSinMeiInMonthListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceGetSinMeiInMonthListQuery,
    GetApiReceGetSinMeiInMonthListQueryVariables
  >(GetApiReceGetSinMeiInMonthListDocument, options);
}
export function useGetApiReceGetSinMeiInMonthListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceGetSinMeiInMonthListQuery,
    GetApiReceGetSinMeiInMonthListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceGetSinMeiInMonthListQuery,
    GetApiReceGetSinMeiInMonthListQueryVariables
  >(GetApiReceGetSinMeiInMonthListDocument, options);
}
export type GetApiReceGetSinMeiInMonthListQueryHookResult = ReturnType<
  typeof useGetApiReceGetSinMeiInMonthListQuery
>;
export type GetApiReceGetSinMeiInMonthListLazyQueryHookResult = ReturnType<
  typeof useGetApiReceGetSinMeiInMonthListLazyQuery
>;
export type GetApiReceGetSinMeiInMonthListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceGetSinMeiInMonthListSuspenseQuery
>;
export type GetApiReceGetSinMeiInMonthListQueryResult = Apollo.QueryResult<
  GetApiReceGetSinMeiInMonthListQuery,
  GetApiReceGetSinMeiInMonthListQueryVariables
>;
export const GetApiReceiptGetReceStatusDocument = gql`
  query getApiReceiptGetReceStatus(
    $ptId: BigInt
    $seikyuYm: Int
    $sinYm: Int
    $hokenId: Int
  ) {
    getApiReceiptGetReceStatus(
      ptId: $ptId
      seikyuYm: $seikyuYm
      sinYm: $sinYm
      hokenId: $hokenId
    ) {
      data {
        ptId
        seikyuYm
        hokenId
        sinYm
        fusenKbn
        isPaperRece
        statusKbn
        isPrechecked
        isOutput
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetReceStatusQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetReceStatusQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetReceStatusQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetReceStatusQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      seikyuYm: // value for 'seikyuYm'
 *      sinYm: // value for 'sinYm'
 *      hokenId: // value for 'hokenId'
 *   },
 * });
 */
export function useGetApiReceiptGetReceStatusQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetReceStatusQuery,
    GetApiReceiptGetReceStatusQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetReceStatusQuery,
    GetApiReceiptGetReceStatusQueryVariables
  >(GetApiReceiptGetReceStatusDocument, options);
}
export function useGetApiReceiptGetReceStatusLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetReceStatusQuery,
    GetApiReceiptGetReceStatusQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetReceStatusQuery,
    GetApiReceiptGetReceStatusQueryVariables
  >(GetApiReceiptGetReceStatusDocument, options);
}
export function useGetApiReceiptGetReceStatusSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetReceStatusQuery,
    GetApiReceiptGetReceStatusQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetReceStatusQuery,
    GetApiReceiptGetReceStatusQueryVariables
  >(GetApiReceiptGetReceStatusDocument, options);
}
export type GetApiReceiptGetReceStatusQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceStatusQuery
>;
export type GetApiReceiptGetReceStatusLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceStatusLazyQuery
>;
export type GetApiReceiptGetReceStatusSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceStatusSuspenseQuery
>;
export type GetApiReceiptGetReceStatusQueryResult = Apollo.QueryResult<
  GetApiReceiptGetReceStatusQuery,
  GetApiReceiptGetReceStatusQueryVariables
>;
export const GetApiReceiptGetReceiptEditDocument = gql`
  query getApiReceiptGetReceiptEdit(
    $hokenId: Int
    $ptId: BigInt
    $seikyuYm: Int
    $sinYm: Int
  ) {
    getApiReceiptGetReceiptEdit(
      hokenId: $hokenId
      ptId: $ptId
      seikyuYm: $seikyuYm
      sinYm: $sinYm
    ) {
      data {
        seqNo
        receiptEditOrigin {
          tokki1Id
          tokki2Id
          tokki3Id
          tokki4Id
          tokki5Id
          hokenNissu
          kohi1Nissu
          kohi2Nissu
          kohi3Nissu
          kohi4Nissu
          kohi1ReceKyufu
          kohi2ReceKyufu
          kohi3ReceKyufu
          kohi4ReceKyufu
          hokenReceTensu
          hokenReceFutan
          kohi1ReceTensu
          kohi1ReceFutan
          kohi2ReceTensu
          kohi2ReceFutan
          kohi3ReceTensu
          kohi3ReceFutan
          kohi4ReceTensu
          kohi4ReceFutan
        }
        receiptEditCurrent {
          tokki1Id
          tokki2Id
          tokki3Id
          tokki4Id
          tokki5Id
          hokenNissu
          kohi1Nissu
          kohi2Nissu
          kohi3Nissu
          kohi4Nissu
          kohi1ReceKyufu
          kohi2ReceKyufu
          kohi3ReceKyufu
          kohi4ReceKyufu
          hokenReceTensu
          hokenReceFutan
          kohi1ReceTensu
          kohi1ReceFutan
          kohi2ReceTensu
          kohi2ReceFutan
          kohi3ReceTensu
          kohi3ReceFutan
          kohi4ReceTensu
          kohi4ReceFutan
        }
        tokkiMstDictionary
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetReceiptEditQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetReceiptEditQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetReceiptEditQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetReceiptEditQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *      seikyuYm: // value for 'seikyuYm'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceiptGetReceiptEditQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetReceiptEditQuery,
    GetApiReceiptGetReceiptEditQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetReceiptEditQuery,
    GetApiReceiptGetReceiptEditQueryVariables
  >(GetApiReceiptGetReceiptEditDocument, options);
}
export function useGetApiReceiptGetReceiptEditLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetReceiptEditQuery,
    GetApiReceiptGetReceiptEditQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetReceiptEditQuery,
    GetApiReceiptGetReceiptEditQueryVariables
  >(GetApiReceiptGetReceiptEditDocument, options);
}
export function useGetApiReceiptGetReceiptEditSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetReceiptEditQuery,
    GetApiReceiptGetReceiptEditQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetReceiptEditQuery,
    GetApiReceiptGetReceiptEditQueryVariables
  >(GetApiReceiptGetReceiptEditDocument, options);
}
export type GetApiReceiptGetReceiptEditQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceiptEditQuery
>;
export type GetApiReceiptGetReceiptEditLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceiptEditLazyQuery
>;
export type GetApiReceiptGetReceiptEditSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceiptEditSuspenseQuery
>;
export type GetApiReceiptGetReceiptEditQueryResult = Apollo.QueryResult<
  GetApiReceiptGetReceiptEditQuery,
  GetApiReceiptGetReceiptEditQueryVariables
>;
export const PostApiReceiptSaveReceStatusDocument = gql`
  mutation postApiReceiptSaveReceStatus(
    $input: EmrCloudApiRequestsReceiptSaveReceStatusRequestInput
  ) {
    postApiReceiptSaveReceStatus(
      emrCloudApiRequestsReceiptSaveReceStatusRequestInput: $input
    ) {
      data {
        success
      }
    }
  }
`;
export type PostApiReceiptSaveReceStatusMutationFn = Apollo.MutationFunction<
  PostApiReceiptSaveReceStatusMutation,
  PostApiReceiptSaveReceStatusMutationVariables
>;

/**
 * __usePostApiReceiptSaveReceStatusMutation__
 *
 * To run a mutation, you first call `usePostApiReceiptSaveReceStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceiptSaveReceStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceiptSaveReceStatusMutation, { data, loading, error }] = usePostApiReceiptSaveReceStatusMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceiptSaveReceStatusMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceiptSaveReceStatusMutation,
    PostApiReceiptSaveReceStatusMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceiptSaveReceStatusMutation,
    PostApiReceiptSaveReceStatusMutationVariables
  >(PostApiReceiptSaveReceStatusDocument, options);
}
export type PostApiReceiptSaveReceStatusMutationHookResult = ReturnType<
  typeof usePostApiReceiptSaveReceStatusMutation
>;
export type PostApiReceiptSaveReceStatusMutationResult =
  Apollo.MutationResult<PostApiReceiptSaveReceStatusMutation>;
export type PostApiReceiptSaveReceStatusMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceiptSaveReceStatusMutation,
    PostApiReceiptSaveReceStatusMutationVariables
  >;
export const PostApiReceiptSaveReceiptEditDocument = gql`
  mutation postApiReceiptSaveReceiptEdit(
    $input: EmrCloudApiRequestsReceiptSaveReceiptEditRequestInput
  ) {
    postApiReceiptSaveReceiptEdit(
      emrCloudApiRequestsReceiptSaveReceiptEditRequestInput: $input
    ) {
      data {
        success
      }
    }
  }
`;
export type PostApiReceiptSaveReceiptEditMutationFn = Apollo.MutationFunction<
  PostApiReceiptSaveReceiptEditMutation,
  PostApiReceiptSaveReceiptEditMutationVariables
>;

/**
 * __usePostApiReceiptSaveReceiptEditMutation__
 *
 * To run a mutation, you first call `usePostApiReceiptSaveReceiptEditMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceiptSaveReceiptEditMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceiptSaveReceiptEditMutation, { data, loading, error }] = usePostApiReceiptSaveReceiptEditMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceiptSaveReceiptEditMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceiptSaveReceiptEditMutation,
    PostApiReceiptSaveReceiptEditMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceiptSaveReceiptEditMutation,
    PostApiReceiptSaveReceiptEditMutationVariables
  >(PostApiReceiptSaveReceiptEditDocument, options);
}
export type PostApiReceiptSaveReceiptEditMutationHookResult = ReturnType<
  typeof usePostApiReceiptSaveReceiptEditMutation
>;
export type PostApiReceiptSaveReceiptEditMutationResult =
  Apollo.MutationResult<PostApiReceiptSaveReceiptEditMutation>;
export type PostApiReceiptSaveReceiptEditMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceiptSaveReceiptEditMutation,
    PostApiReceiptSaveReceiptEditMutationVariables
  >;
export const GetApiReceiptCheckExisReceInfEditDocument = gql`
  query getApiReceiptCheckExisReceInfEdit(
    $hokenId: Int
    $ptId: BigInt
    $seikyuYm: Int
    $sinYm: Int
  ) {
    getApiReceiptCheckExisReceInfEdit(
      hokenId: $hokenId
      ptId: $ptId
      seikyuYm: $seikyuYm
      sinYm: $sinYm
    ) {
      data {
        receInfEdit
      }
    }
  }
`;

/**
 * __useGetApiReceiptCheckExisReceInfEditQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptCheckExisReceInfEditQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptCheckExisReceInfEditQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptCheckExisReceInfEditQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *      seikyuYm: // value for 'seikyuYm'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceiptCheckExisReceInfEditQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptCheckExisReceInfEditQuery,
    GetApiReceiptCheckExisReceInfEditQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptCheckExisReceInfEditQuery,
    GetApiReceiptCheckExisReceInfEditQueryVariables
  >(GetApiReceiptCheckExisReceInfEditDocument, options);
}
export function useGetApiReceiptCheckExisReceInfEditLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptCheckExisReceInfEditQuery,
    GetApiReceiptCheckExisReceInfEditQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptCheckExisReceInfEditQuery,
    GetApiReceiptCheckExisReceInfEditQueryVariables
  >(GetApiReceiptCheckExisReceInfEditDocument, options);
}
export function useGetApiReceiptCheckExisReceInfEditSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptCheckExisReceInfEditQuery,
    GetApiReceiptCheckExisReceInfEditQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptCheckExisReceInfEditQuery,
    GetApiReceiptCheckExisReceInfEditQueryVariables
  >(GetApiReceiptCheckExisReceInfEditDocument, options);
}
export type GetApiReceiptCheckExisReceInfEditQueryHookResult = ReturnType<
  typeof useGetApiReceiptCheckExisReceInfEditQuery
>;
export type GetApiReceiptCheckExisReceInfEditLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptCheckExisReceInfEditLazyQuery
>;
export type GetApiReceiptCheckExisReceInfEditSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceiptCheckExisReceInfEditSuspenseQuery>;
export type GetApiReceiptCheckExisReceInfEditQueryResult = Apollo.QueryResult<
  GetApiReceiptCheckExisReceInfEditQuery,
  GetApiReceiptCheckExisReceInfEditQueryVariables
>;
export const GetApiReceiptGetSyoukiInfListDocument = gql`
  query getApiReceiptGetSyoukiInfList(
    $hokenId: Int
    $ptId: BigInt
    $sinYm: Int
  ) {
    getApiReceiptGetSyoukiInfList(
      hokenId: $hokenId
      ptId: $ptId
      sinYm: $sinYm
    ) {
      data {
        syoukiInfList {
          seqNo
          sortNo
          syoukiKbn
          syouki
        }
        syoukiKbnMstList {
          syoukiKbn
          name
          startYm
          syoukiKbnDisplay
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetSyoukiInfListQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetSyoukiInfListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetSyoukiInfListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetSyoukiInfListQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceiptGetSyoukiInfListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetSyoukiInfListQuery,
    GetApiReceiptGetSyoukiInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetSyoukiInfListQuery,
    GetApiReceiptGetSyoukiInfListQueryVariables
  >(GetApiReceiptGetSyoukiInfListDocument, options);
}
export function useGetApiReceiptGetSyoukiInfListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetSyoukiInfListQuery,
    GetApiReceiptGetSyoukiInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetSyoukiInfListQuery,
    GetApiReceiptGetSyoukiInfListQueryVariables
  >(GetApiReceiptGetSyoukiInfListDocument, options);
}
export function useGetApiReceiptGetSyoukiInfListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetSyoukiInfListQuery,
    GetApiReceiptGetSyoukiInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetSyoukiInfListQuery,
    GetApiReceiptGetSyoukiInfListQueryVariables
  >(GetApiReceiptGetSyoukiInfListDocument, options);
}
export type GetApiReceiptGetSyoukiInfListQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetSyoukiInfListQuery
>;
export type GetApiReceiptGetSyoukiInfListLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetSyoukiInfListLazyQuery
>;
export type GetApiReceiptGetSyoukiInfListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetSyoukiInfListSuspenseQuery
>;
export type GetApiReceiptGetSyoukiInfListQueryResult = Apollo.QueryResult<
  GetApiReceiptGetSyoukiInfListQuery,
  GetApiReceiptGetSyoukiInfListQueryVariables
>;
export const PostApiReceiptSaveSyoukiInfListDocument = gql`
  mutation postApiReceiptSaveSyoukiInfList(
    $input: EmrCloudApiRequestsReceiptSaveSyoukiInfListRequestInput
  ) {
    postApiReceiptSaveSyoukiInfList(
      emrCloudApiRequestsReceiptSaveSyoukiInfListRequestInput: $input
    ) {
      data {
        syoukiInfInvalidList {
          seqNo
          sortNo
          syoukiKbn
          syouki
        }
      }
    }
  }
`;
export type PostApiReceiptSaveSyoukiInfListMutationFn = Apollo.MutationFunction<
  PostApiReceiptSaveSyoukiInfListMutation,
  PostApiReceiptSaveSyoukiInfListMutationVariables
>;

/**
 * __usePostApiReceiptSaveSyoukiInfListMutation__
 *
 * To run a mutation, you first call `usePostApiReceiptSaveSyoukiInfListMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceiptSaveSyoukiInfListMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceiptSaveSyoukiInfListMutation, { data, loading, error }] = usePostApiReceiptSaveSyoukiInfListMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceiptSaveSyoukiInfListMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceiptSaveSyoukiInfListMutation,
    PostApiReceiptSaveSyoukiInfListMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceiptSaveSyoukiInfListMutation,
    PostApiReceiptSaveSyoukiInfListMutationVariables
  >(PostApiReceiptSaveSyoukiInfListDocument, options);
}
export type PostApiReceiptSaveSyoukiInfListMutationHookResult = ReturnType<
  typeof usePostApiReceiptSaveSyoukiInfListMutation
>;
export type PostApiReceiptSaveSyoukiInfListMutationResult =
  Apollo.MutationResult<PostApiReceiptSaveSyoukiInfListMutation>;
export type PostApiReceiptSaveSyoukiInfListMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceiptSaveSyoukiInfListMutation,
    PostApiReceiptSaveSyoukiInfListMutationVariables
  >;
export const GetApiReceiptSyoukiInfHistoryDocument = gql`
  query getApiReceiptSyoukiInfHistory($ptId: BigInt, $sinYm: Int) {
    getApiReceiptSyoukiInfHistory(ptId: $ptId, sinYm: $sinYm) {
      data {
        data {
          sinYm
          sinYmDisplay
          hokenId
          hokenName
          syoukiInfList {
            seqNo
            sortNo
            syoukiKbn
            syouki
          }
          syoukiKbnMstList {
            syoukiKbn
            name
            startYm
            syoukiKbnDisplay
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptSyoukiInfHistoryQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptSyoukiInfHistoryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptSyoukiInfHistoryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptSyoukiInfHistoryQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceiptSyoukiInfHistoryQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptSyoukiInfHistoryQuery,
    GetApiReceiptSyoukiInfHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptSyoukiInfHistoryQuery,
    GetApiReceiptSyoukiInfHistoryQueryVariables
  >(GetApiReceiptSyoukiInfHistoryDocument, options);
}
export function useGetApiReceiptSyoukiInfHistoryLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptSyoukiInfHistoryQuery,
    GetApiReceiptSyoukiInfHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptSyoukiInfHistoryQuery,
    GetApiReceiptSyoukiInfHistoryQueryVariables
  >(GetApiReceiptSyoukiInfHistoryDocument, options);
}
export function useGetApiReceiptSyoukiInfHistorySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptSyoukiInfHistoryQuery,
    GetApiReceiptSyoukiInfHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptSyoukiInfHistoryQuery,
    GetApiReceiptSyoukiInfHistoryQueryVariables
  >(GetApiReceiptSyoukiInfHistoryDocument, options);
}
export type GetApiReceiptSyoukiInfHistoryQueryHookResult = ReturnType<
  typeof useGetApiReceiptSyoukiInfHistoryQuery
>;
export type GetApiReceiptSyoukiInfHistoryLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptSyoukiInfHistoryLazyQuery
>;
export type GetApiReceiptSyoukiInfHistorySuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptSyoukiInfHistorySuspenseQuery
>;
export type GetApiReceiptSyoukiInfHistoryQueryResult = Apollo.QueryResult<
  GetApiReceiptSyoukiInfHistoryQuery,
  GetApiReceiptSyoukiInfHistoryQueryVariables
>;
export const GetApiReceiptGetSyobyoKeikaListDocument = gql`
  query getApiReceiptGetSyobyoKeikaList(
    $sinYm: Int
    $ptId: BigInt
    $hokenId: Int
    $hokenKbn: Int
  ) {
    getApiReceiptGetSyobyoKeikaList(
      hokenId: $hokenId
      hokenKbn: $hokenKbn
      ptId: $ptId
      sinYm: $sinYm
    ) {
      data {
        syobyoKeikaList {
          sinDay
          seqNo
          keika
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetSyobyoKeikaListQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetSyobyoKeikaListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetSyobyoKeikaListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetSyobyoKeikaListQuery({
 *   variables: {
 *      sinYm: // value for 'sinYm'
 *      ptId: // value for 'ptId'
 *      hokenId: // value for 'hokenId'
 *      hokenKbn: // value for 'hokenKbn'
 *   },
 * });
 */
export function useGetApiReceiptGetSyobyoKeikaListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetSyobyoKeikaListQuery,
    GetApiReceiptGetSyobyoKeikaListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetSyobyoKeikaListQuery,
    GetApiReceiptGetSyobyoKeikaListQueryVariables
  >(GetApiReceiptGetSyobyoKeikaListDocument, options);
}
export function useGetApiReceiptGetSyobyoKeikaListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetSyobyoKeikaListQuery,
    GetApiReceiptGetSyobyoKeikaListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetSyobyoKeikaListQuery,
    GetApiReceiptGetSyobyoKeikaListQueryVariables
  >(GetApiReceiptGetSyobyoKeikaListDocument, options);
}
export function useGetApiReceiptGetSyobyoKeikaListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetSyobyoKeikaListQuery,
    GetApiReceiptGetSyobyoKeikaListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetSyobyoKeikaListQuery,
    GetApiReceiptGetSyobyoKeikaListQueryVariables
  >(GetApiReceiptGetSyobyoKeikaListDocument, options);
}
export type GetApiReceiptGetSyobyoKeikaListQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetSyobyoKeikaListQuery
>;
export type GetApiReceiptGetSyobyoKeikaListLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetSyobyoKeikaListLazyQuery
>;
export type GetApiReceiptGetSyobyoKeikaListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetSyobyoKeikaListSuspenseQuery
>;
export type GetApiReceiptGetSyobyoKeikaListQueryResult = Apollo.QueryResult<
  GetApiReceiptGetSyobyoKeikaListQuery,
  GetApiReceiptGetSyobyoKeikaListQueryVariables
>;
export const PostApiReceiptSaveSyobyoKeikaListDocument = gql`
  mutation postApiReceiptSaveSyobyoKeikaList(
    $input: EmrCloudApiRequestsReceiptSaveSyobyoKeikaListRequestInput
  ) {
    postApiReceiptSaveSyobyoKeikaList(
      emrCloudApiRequestsReceiptSaveSyobyoKeikaListRequestInput: $input
    ) {
      data {
        syobyoKeikaInvalidList {
          keika
          seqNo
          sinDay
        }
      }
    }
  }
`;
export type PostApiReceiptSaveSyobyoKeikaListMutationFn =
  Apollo.MutationFunction<
    PostApiReceiptSaveSyobyoKeikaListMutation,
    PostApiReceiptSaveSyobyoKeikaListMutationVariables
  >;

/**
 * __usePostApiReceiptSaveSyobyoKeikaListMutation__
 *
 * To run a mutation, you first call `usePostApiReceiptSaveSyobyoKeikaListMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceiptSaveSyobyoKeikaListMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceiptSaveSyobyoKeikaListMutation, { data, loading, error }] = usePostApiReceiptSaveSyobyoKeikaListMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceiptSaveSyobyoKeikaListMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceiptSaveSyobyoKeikaListMutation,
    PostApiReceiptSaveSyobyoKeikaListMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceiptSaveSyobyoKeikaListMutation,
    PostApiReceiptSaveSyobyoKeikaListMutationVariables
  >(PostApiReceiptSaveSyobyoKeikaListDocument, options);
}
export type PostApiReceiptSaveSyobyoKeikaListMutationHookResult = ReturnType<
  typeof usePostApiReceiptSaveSyobyoKeikaListMutation
>;
export type PostApiReceiptSaveSyobyoKeikaListMutationResult =
  Apollo.MutationResult<PostApiReceiptSaveSyobyoKeikaListMutation>;
export type PostApiReceiptSaveSyobyoKeikaListMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceiptSaveSyobyoKeikaListMutation,
    PostApiReceiptSaveSyobyoKeikaListMutationVariables
  >;
export const GetApiReceiptSyobyoKeikaHistoryDocument = gql`
  query getApiReceiptSyobyoKeikaHistory($ptId: BigInt) {
    getApiReceiptSyobyoKeikaHistory(ptId: $ptId) {
      data {
        data {
          sinYm
          sinYmDisplay
          hokenId
          hokenName
          syobyoKeikaList {
            sinDay
            seqNo
            keika
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptSyobyoKeikaHistoryQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptSyobyoKeikaHistoryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptSyobyoKeikaHistoryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptSyobyoKeikaHistoryQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiReceiptSyobyoKeikaHistoryQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptSyobyoKeikaHistoryQuery,
    GetApiReceiptSyobyoKeikaHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptSyobyoKeikaHistoryQuery,
    GetApiReceiptSyobyoKeikaHistoryQueryVariables
  >(GetApiReceiptSyobyoKeikaHistoryDocument, options);
}
export function useGetApiReceiptSyobyoKeikaHistoryLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptSyobyoKeikaHistoryQuery,
    GetApiReceiptSyobyoKeikaHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptSyobyoKeikaHistoryQuery,
    GetApiReceiptSyobyoKeikaHistoryQueryVariables
  >(GetApiReceiptSyobyoKeikaHistoryDocument, options);
}
export function useGetApiReceiptSyobyoKeikaHistorySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptSyobyoKeikaHistoryQuery,
    GetApiReceiptSyobyoKeikaHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptSyobyoKeikaHistoryQuery,
    GetApiReceiptSyobyoKeikaHistoryQueryVariables
  >(GetApiReceiptSyobyoKeikaHistoryDocument, options);
}
export type GetApiReceiptSyobyoKeikaHistoryQueryHookResult = ReturnType<
  typeof useGetApiReceiptSyobyoKeikaHistoryQuery
>;
export type GetApiReceiptSyobyoKeikaHistoryLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptSyobyoKeikaHistoryLazyQuery
>;
export type GetApiReceiptSyobyoKeikaHistorySuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptSyobyoKeikaHistorySuspenseQuery
>;
export type GetApiReceiptSyobyoKeikaHistoryQueryResult = Apollo.QueryResult<
  GetApiReceiptSyobyoKeikaHistoryQuery,
  GetApiReceiptSyobyoKeikaHistoryQueryVariables
>;
export const GetApiReceiptGetSinDateRaiinInfListDocument = gql`
  query getApiReceiptGetSinDateRaiinInfList(
    $sinYm: Int
    $ptId: BigInt
    $hokenId: Int
  ) {
    getApiReceiptGetSinDateRaiinInfList(
      hokenId: $hokenId
      ptId: $ptId
      sinYm: $sinYm
    ) {
      data {
        sinDateList
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetSinDateRaiinInfListQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetSinDateRaiinInfListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetSinDateRaiinInfListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetSinDateRaiinInfListQuery({
 *   variables: {
 *      sinYm: // value for 'sinYm'
 *      ptId: // value for 'ptId'
 *      hokenId: // value for 'hokenId'
 *   },
 * });
 */
export function useGetApiReceiptGetSinDateRaiinInfListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetSinDateRaiinInfListQuery,
    GetApiReceiptGetSinDateRaiinInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetSinDateRaiinInfListQuery,
    GetApiReceiptGetSinDateRaiinInfListQueryVariables
  >(GetApiReceiptGetSinDateRaiinInfListDocument, options);
}
export function useGetApiReceiptGetSinDateRaiinInfListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetSinDateRaiinInfListQuery,
    GetApiReceiptGetSinDateRaiinInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetSinDateRaiinInfListQuery,
    GetApiReceiptGetSinDateRaiinInfListQueryVariables
  >(GetApiReceiptGetSinDateRaiinInfListDocument, options);
}
export function useGetApiReceiptGetSinDateRaiinInfListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetSinDateRaiinInfListQuery,
    GetApiReceiptGetSinDateRaiinInfListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetSinDateRaiinInfListQuery,
    GetApiReceiptGetSinDateRaiinInfListQueryVariables
  >(GetApiReceiptGetSinDateRaiinInfListDocument, options);
}
export type GetApiReceiptGetSinDateRaiinInfListQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetSinDateRaiinInfListQuery
>;
export type GetApiReceiptGetSinDateRaiinInfListLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetSinDateRaiinInfListLazyQuery
>;
export type GetApiReceiptGetSinDateRaiinInfListSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceiptGetSinDateRaiinInfListSuspenseQuery>;
export type GetApiReceiptGetSinDateRaiinInfListQueryResult = Apollo.QueryResult<
  GetApiReceiptGetSinDateRaiinInfListQuery,
  GetApiReceiptGetSinDateRaiinInfListQueryVariables
>;
export const GetApiReceiptGetReceByomeiCheckingDocument = gql`
  query getApiReceiptGetReceByomeiChecking(
    $sinDate: Int
    $ptId: BigInt
    $hokenId: Int
  ) {
    getApiReceiptGetReceByomeiChecking(
      hokenId: $hokenId
      ptId: $ptId
      sinDate: $sinDate
    ) {
      data {
        data {
          displayItemName
          listCheckingItem {
            itemCd
            byomeiCd
            byomei
            fullByomei
            sikkanKbn
            sikkanCd
            isAdopted
            nanbyoCd
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetReceByomeiCheckingQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetReceByomeiCheckingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetReceByomeiCheckingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetReceByomeiCheckingQuery({
 *   variables: {
 *      sinDate: // value for 'sinDate'
 *      ptId: // value for 'ptId'
 *      hokenId: // value for 'hokenId'
 *   },
 * });
 */
export function useGetApiReceiptGetReceByomeiCheckingQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetReceByomeiCheckingQuery,
    GetApiReceiptGetReceByomeiCheckingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetReceByomeiCheckingQuery,
    GetApiReceiptGetReceByomeiCheckingQueryVariables
  >(GetApiReceiptGetReceByomeiCheckingDocument, options);
}
export function useGetApiReceiptGetReceByomeiCheckingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetReceByomeiCheckingQuery,
    GetApiReceiptGetReceByomeiCheckingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetReceByomeiCheckingQuery,
    GetApiReceiptGetReceByomeiCheckingQueryVariables
  >(GetApiReceiptGetReceByomeiCheckingDocument, options);
}
export function useGetApiReceiptGetReceByomeiCheckingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetReceByomeiCheckingQuery,
    GetApiReceiptGetReceByomeiCheckingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetReceByomeiCheckingQuery,
    GetApiReceiptGetReceByomeiCheckingQueryVariables
  >(GetApiReceiptGetReceByomeiCheckingDocument, options);
}
export type GetApiReceiptGetReceByomeiCheckingQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceByomeiCheckingQuery
>;
export type GetApiReceiptGetReceByomeiCheckingLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceByomeiCheckingLazyQuery
>;
export type GetApiReceiptGetReceByomeiCheckingSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceiptGetReceByomeiCheckingSuspenseQuery>;
export type GetApiReceiptGetReceByomeiCheckingQueryResult = Apollo.QueryResult<
  GetApiReceiptGetReceByomeiCheckingQuery,
  GetApiReceiptGetReceByomeiCheckingQueryVariables
>;
export const GetApiReceiptGetReceCmtListDocument = gql`
  query getApiReceiptGetReceCmtList(
    $sinDate: Int
    $sinYm: Int
    $ptId: BigInt
    $hokenId: Int
  ) {
    getApiReceiptGetReceCmtList(
      hokenId: $hokenId
      ptId: $ptId
      sinDate: $sinDate
      sinYm: $sinYm
    ) {
      data {
        headerItemCmtList {
          id
          ptId
          seqNo
          sinYm
          hokenId
          cmtKbn
          cmtSbt
          cmt
          cmtData
          itemCd
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
        }
        footerItemCmtList {
          id
          ptId
          seqNo
          sinYm
          hokenId
          cmtKbn
          cmtSbt
          cmt
          cmtData
          itemCd
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
        }
        headerFreeCmtList {
          id
          ptId
          seqNo
          sinYm
          hokenId
          cmtKbn
          cmtSbt
          cmt
          cmtData
          itemCd
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
        }
        footerFreeCmtList {
          id
          ptId
          seqNo
          sinYm
          hokenId
          cmtKbn
          cmtSbt
          cmt
          cmtData
          itemCd
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetReceCmtListQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetReceCmtListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetReceCmtListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetReceCmtListQuery({
 *   variables: {
 *      sinDate: // value for 'sinDate'
 *      sinYm: // value for 'sinYm'
 *      ptId: // value for 'ptId'
 *      hokenId: // value for 'hokenId'
 *   },
 * });
 */
export function useGetApiReceiptGetReceCmtListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetReceCmtListQuery,
    GetApiReceiptGetReceCmtListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetReceCmtListQuery,
    GetApiReceiptGetReceCmtListQueryVariables
  >(GetApiReceiptGetReceCmtListDocument, options);
}
export function useGetApiReceiptGetReceCmtListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetReceCmtListQuery,
    GetApiReceiptGetReceCmtListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetReceCmtListQuery,
    GetApiReceiptGetReceCmtListQueryVariables
  >(GetApiReceiptGetReceCmtListDocument, options);
}
export function useGetApiReceiptGetReceCmtListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetReceCmtListQuery,
    GetApiReceiptGetReceCmtListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetReceCmtListQuery,
    GetApiReceiptGetReceCmtListQueryVariables
  >(GetApiReceiptGetReceCmtListDocument, options);
}
export type GetApiReceiptGetReceCmtListQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceCmtListQuery
>;
export type GetApiReceiptGetReceCmtListLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceCmtListLazyQuery
>;
export type GetApiReceiptGetReceCmtListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetReceCmtListSuspenseQuery
>;
export type GetApiReceiptGetReceCmtListQueryResult = Apollo.QueryResult<
  GetApiReceiptGetReceCmtListQuery,
  GetApiReceiptGetReceCmtListQueryVariables
>;
export const GetApiReceiptReceCmtHistoryDocument = gql`
  query getApiReceiptReceCmtHistory($ptId: BigInt) {
    getApiReceiptReceCmtHistory(ptId: $ptId) {
      data {
        data {
          sinYm
          sinYmDisplay
          hokenId
          hokenName
          headerItemCmtList {
            id
            ptId
            seqNo
            sinYm
            hokenId
            cmtKbn
            cmtSbt
            cmt
            cmtData
            itemCd
            cmtCol1
            cmtCol2
            cmtCol3
            cmtCol4
            cmtColKeta1
            cmtColKeta2
            cmtColKeta3
            cmtColKeta4
          }
          footerItemCmtList {
            id
            ptId
            seqNo
            sinYm
            hokenId
            cmtKbn
            cmtSbt
            cmt
            cmtData
            itemCd
            cmtCol1
            cmtCol2
            cmtCol3
            cmtCol4
            cmtColKeta1
            cmtColKeta2
            cmtColKeta3
            cmtColKeta4
          }
          headerFreeCmtList {
            id
            ptId
            seqNo
            sinYm
            hokenId
            cmtKbn
            cmtSbt
            cmt
            cmtData
            itemCd
            cmtCol1
            cmtCol2
            cmtCol3
            cmtCol4
            cmtColKeta1
            cmtColKeta2
            cmtColKeta3
            cmtColKeta4
          }
          footerFreeCmtList {
            id
            ptId
            seqNo
            sinYm
            hokenId
            cmtKbn
            cmtSbt
            cmt
            cmtData
            itemCd
            cmtCol1
            cmtCol2
            cmtCol3
            cmtCol4
            cmtColKeta1
            cmtColKeta2
            cmtColKeta3
            cmtColKeta4
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptReceCmtHistoryQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptReceCmtHistoryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptReceCmtHistoryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptReceCmtHistoryQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiReceiptReceCmtHistoryQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptReceCmtHistoryQuery,
    GetApiReceiptReceCmtHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptReceCmtHistoryQuery,
    GetApiReceiptReceCmtHistoryQueryVariables
  >(GetApiReceiptReceCmtHistoryDocument, options);
}
export function useGetApiReceiptReceCmtHistoryLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptReceCmtHistoryQuery,
    GetApiReceiptReceCmtHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptReceCmtHistoryQuery,
    GetApiReceiptReceCmtHistoryQueryVariables
  >(GetApiReceiptReceCmtHistoryDocument, options);
}
export function useGetApiReceiptReceCmtHistorySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptReceCmtHistoryQuery,
    GetApiReceiptReceCmtHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptReceCmtHistoryQuery,
    GetApiReceiptReceCmtHistoryQueryVariables
  >(GetApiReceiptReceCmtHistoryDocument, options);
}
export type GetApiReceiptReceCmtHistoryQueryHookResult = ReturnType<
  typeof useGetApiReceiptReceCmtHistoryQuery
>;
export type GetApiReceiptReceCmtHistoryLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptReceCmtHistoryLazyQuery
>;
export type GetApiReceiptReceCmtHistorySuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptReceCmtHistorySuspenseQuery
>;
export type GetApiReceiptReceCmtHistoryQueryResult = Apollo.QueryResult<
  GetApiReceiptReceCmtHistoryQuery,
  GetApiReceiptReceCmtHistoryQueryVariables
>;
export const PostApiReceiptSaveReceCmtListDocument = gql`
  mutation postApiReceiptSaveReceCmtList(
    $input: EmrCloudApiRequestsReceiptSaveReceCmtListRequestInput
  ) {
    postApiReceiptSaveReceCmtList(
      emrCloudApiRequestsReceiptSaveReceCmtListRequestInput: $input
    ) {
      data {
        headerItemCmtInvalidList {
          id
          ptId
          seqNo
          sinYm
          hokenId
          cmtKbn
          cmtSbt
          cmt
          cmtData
          itemCd
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
        }
        footerItemCmtInvalidList {
          id
          ptId
          seqNo
          sinYm
          hokenId
          cmtKbn
          cmtSbt
          cmt
          cmtData
          itemCd
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
        }
        headerFreeCmtInvalidList {
          id
          ptId
          seqNo
          sinYm
          hokenId
          cmtKbn
          cmtSbt
          cmt
          cmtData
          itemCd
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
        }
        footerFreeCmtInvalidList {
          id
          ptId
          seqNo
          sinYm
          hokenId
          cmtKbn
          cmtSbt
          cmt
          cmtData
          itemCd
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
        }
        status
      }
    }
  }
`;
export type PostApiReceiptSaveReceCmtListMutationFn = Apollo.MutationFunction<
  PostApiReceiptSaveReceCmtListMutation,
  PostApiReceiptSaveReceCmtListMutationVariables
>;

/**
 * __usePostApiReceiptSaveReceCmtListMutation__
 *
 * To run a mutation, you first call `usePostApiReceiptSaveReceCmtListMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceiptSaveReceCmtListMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceiptSaveReceCmtListMutation, { data, loading, error }] = usePostApiReceiptSaveReceCmtListMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceiptSaveReceCmtListMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceiptSaveReceCmtListMutation,
    PostApiReceiptSaveReceCmtListMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceiptSaveReceCmtListMutation,
    PostApiReceiptSaveReceCmtListMutationVariables
  >(PostApiReceiptSaveReceCmtListDocument, options);
}
export type PostApiReceiptSaveReceCmtListMutationHookResult = ReturnType<
  typeof usePostApiReceiptSaveReceCmtListMutation
>;
export type PostApiReceiptSaveReceCmtListMutationResult =
  Apollo.MutationResult<PostApiReceiptSaveReceCmtListMutation>;
export type PostApiReceiptSaveReceCmtListMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceiptSaveReceCmtListMutation,
    PostApiReceiptSaveReceCmtListMutationVariables
  >;
export const GetApiReceiptGetListRaiinInfDocument = gql`
  query getApiReceiptGetListRaiinInf(
    $sinYm: Int
    $ptId: BigInt
    $dayInMonth: Int
    $rpNo: Int
    $seqNo: Int
  ) {
    getApiReceiptGetListRaiinInf(
      sinYm: $sinYm
      ptId: $ptId
      dayInMonth: $dayInMonth
      rpNo: $rpNo
      seqNo: $seqNo
    ) {
      data {
        raiinInfList {
          ptId
          sinDate
          raiinNo
          raiinNoBinding
          uketukeTime
          uketukeTimeBinding
          sinEndTime
          sinEndTimeBinding
          status
        }
      }
    }
  }
`;

/**
 * __useGetApiReceiptGetListRaiinInfQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptGetListRaiinInfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptGetListRaiinInfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptGetListRaiinInfQuery({
 *   variables: {
 *      sinYm: // value for 'sinYm'
 *      ptId: // value for 'ptId'
 *      dayInMonth: // value for 'dayInMonth'
 *      rpNo: // value for 'rpNo'
 *      seqNo: // value for 'seqNo'
 *   },
 * });
 */
export function useGetApiReceiptGetListRaiinInfQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptGetListRaiinInfQuery,
    GetApiReceiptGetListRaiinInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptGetListRaiinInfQuery,
    GetApiReceiptGetListRaiinInfQueryVariables
  >(GetApiReceiptGetListRaiinInfDocument, options);
}
export function useGetApiReceiptGetListRaiinInfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptGetListRaiinInfQuery,
    GetApiReceiptGetListRaiinInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptGetListRaiinInfQuery,
    GetApiReceiptGetListRaiinInfQueryVariables
  >(GetApiReceiptGetListRaiinInfDocument, options);
}
export function useGetApiReceiptGetListRaiinInfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptGetListRaiinInfQuery,
    GetApiReceiptGetListRaiinInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptGetListRaiinInfQuery,
    GetApiReceiptGetListRaiinInfQueryVariables
  >(GetApiReceiptGetListRaiinInfDocument, options);
}
export type GetApiReceiptGetListRaiinInfQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListRaiinInfQuery
>;
export type GetApiReceiptGetListRaiinInfLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListRaiinInfLazyQuery
>;
export type GetApiReceiptGetListRaiinInfSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceiptGetListRaiinInfSuspenseQuery
>;
export type GetApiReceiptGetListRaiinInfQueryResult = Apollo.QueryResult<
  GetApiReceiptGetListRaiinInfQuery,
  GetApiReceiptGetListRaiinInfQueryVariables
>;
export const GetApiReceiptCheckExistSyobyoKeikaDocument = gql`
  query getApiReceiptCheckExistSyobyoKeika(
    $hokenId: Int
    $ptId: BigInt
    $sinDay: Int
    $sinYm: Int
  ) {
    getApiReceiptCheckExistSyobyoKeika(
      hokenId: $hokenId
      ptId: $ptId
      sinDay: $sinDay
      sinYm: $sinYm
    ) {
      data {
        isExisted
      }
    }
  }
`;

/**
 * __useGetApiReceiptCheckExistSyobyoKeikaQuery__
 *
 * To run a query within a React component, call `useGetApiReceiptCheckExistSyobyoKeikaQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceiptCheckExistSyobyoKeikaQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceiptCheckExistSyobyoKeikaQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      ptId: // value for 'ptId'
 *      sinDay: // value for 'sinDay'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceiptCheckExistSyobyoKeikaQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceiptCheckExistSyobyoKeikaQuery,
    GetApiReceiptCheckExistSyobyoKeikaQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceiptCheckExistSyobyoKeikaQuery,
    GetApiReceiptCheckExistSyobyoKeikaQueryVariables
  >(GetApiReceiptCheckExistSyobyoKeikaDocument, options);
}
export function useGetApiReceiptCheckExistSyobyoKeikaLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceiptCheckExistSyobyoKeikaQuery,
    GetApiReceiptCheckExistSyobyoKeikaQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceiptCheckExistSyobyoKeikaQuery,
    GetApiReceiptCheckExistSyobyoKeikaQueryVariables
  >(GetApiReceiptCheckExistSyobyoKeikaDocument, options);
}
export function useGetApiReceiptCheckExistSyobyoKeikaSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceiptCheckExistSyobyoKeikaQuery,
    GetApiReceiptCheckExistSyobyoKeikaQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceiptCheckExistSyobyoKeikaQuery,
    GetApiReceiptCheckExistSyobyoKeikaQueryVariables
  >(GetApiReceiptCheckExistSyobyoKeikaDocument, options);
}
export type GetApiReceiptCheckExistSyobyoKeikaQueryHookResult = ReturnType<
  typeof useGetApiReceiptCheckExistSyobyoKeikaQuery
>;
export type GetApiReceiptCheckExistSyobyoKeikaLazyQueryHookResult = ReturnType<
  typeof useGetApiReceiptCheckExistSyobyoKeikaLazyQuery
>;
export type GetApiReceiptCheckExistSyobyoKeikaSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceiptCheckExistSyobyoKeikaSuspenseQuery>;
export type GetApiReceiptCheckExistSyobyoKeikaQueryResult = Apollo.QueryResult<
  GetApiReceiptCheckExistSyobyoKeikaQuery,
  GetApiReceiptCheckExistSyobyoKeikaQueryVariables
>;
