import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiMainMenuFindPtHokenListQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiMainMenuFindPtHokenListQuery = {
  __typename?: "query_root";
  getApiMainMenuFindPtHokenList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceFindPtHokenListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceFindPtHokenListResponse";
      hokenInfModels?: Array<{
        __typename?: "DomainModelsInsuranceHokenInfModel";
        hokenId?: number;
        hokenSentaku?: string;
      }>;
    };
  };
};

export const GetApiMainMenuFindPtHokenListDocument = gql`
  query getApiMainMenuFindPtHokenList($ptId: BigInt, $sinDate: Int) {
    getApiMainMenuFindPtHokenList(ptId: $ptId, sinDate: $sinDate) {
      data {
        hokenInfModels {
          hokenId
          hokenSentaku
        }
      }
    }
  }
`;

/**
 * __useGetApiMainMenuFindPtHokenListQuery__
 *
 * To run a query within a React component, call `useGetApiMainMenuFindPtHokenListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMainMenuFindPtHokenListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMainMenuFindPtHokenListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiMainMenuFindPtHokenListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMainMenuFindPtHokenListQuery,
    GetApiMainMenuFindPtHokenListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMainMenuFindPtHokenListQuery,
    GetApiMainMenuFindPtHokenListQueryVariables
  >(GetApiMainMenuFindPtHokenListDocument, options);
}
export function useGetApiMainMenuFindPtHokenListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMainMenuFindPtHokenListQuery,
    GetApiMainMenuFindPtHokenListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMainMenuFindPtHokenListQuery,
    GetApiMainMenuFindPtHokenListQueryVariables
  >(GetApiMainMenuFindPtHokenListDocument, options);
}
export function useGetApiMainMenuFindPtHokenListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMainMenuFindPtHokenListQuery,
    GetApiMainMenuFindPtHokenListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMainMenuFindPtHokenListQuery,
    GetApiMainMenuFindPtHokenListQueryVariables
  >(GetApiMainMenuFindPtHokenListDocument, options);
}
export type GetApiMainMenuFindPtHokenListQueryHookResult = ReturnType<
  typeof useGetApiMainMenuFindPtHokenListQuery
>;
export type GetApiMainMenuFindPtHokenListLazyQueryHookResult = ReturnType<
  typeof useGetApiMainMenuFindPtHokenListLazyQuery
>;
export type GetApiMainMenuFindPtHokenListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMainMenuFindPtHokenListSuspenseQuery
>;
export type GetApiMainMenuFindPtHokenListQueryResult = Apollo.QueryResult<
  GetApiMainMenuFindPtHokenListQuery,
  GetApiMainMenuFindPtHokenListQueryVariables
>;
