import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables =
  Types.Exact<{
    ptId: Types.Scalars["BigInt"]["input"];
  }>;

export type GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery = {
  __typename?: "query_root";
  getApiKarteMedicalHistoryGetKarteMedicalHistory?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteMedicalHistoryEmrCloudApiResponsesKarteMedicalHistoryGetKarteMedicalHistoryResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesKarteMedicalHistoryEmrCloudApiResponsesKarteMedicalHistoryGetKarteMedicalHistoryResponse";
      kioRekis?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtKioRekiModel";
        byomei?: string;
        byomeiCd?: string;
        byotaiCd?: string;
        cmt?: string;
        hpId?: number;
        isDeleted?: number;
        onSetDate?: string;
        ptId?: string;
        seqNo?: number;
        sortNo?: number;
        startDate?: number;
      }>;
      octDrugs?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtOtcDrugModel";
        cmt?: string;
        endDate?: number;
        fullEndDate?: number;
        fullStartDate?: number;
        hpId?: number;
        isDeleted?: number;
        ptId?: string;
        seqNo?: string;
        serialNum?: number;
        sortNo?: number;
        startDate?: number;
        tradeName?: string;
      }>;
      otherDrugs?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtOtherDrugModel";
        cmt?: string;
        drugName?: string;
        endDate?: number;
        fullEndDate?: number;
        fullStartDate?: number;
        hpId?: number;
        isDeleted?: number;
        itemCd?: string;
        ptId?: string;
        seqNo?: string;
        sortNo?: number;
        startDate?: number;
      }>;
      pregnants?: Array<{
        __typename?: "DomainModelsKarteMedicalHistoryPtPregnancyRelatedModel";
        hpId?: number;
        breastfeedStatus?: number;
        isDeleted?: number;
        pregnancyStatus?: number;
        ptId?: string;
      }>;
      socialHistorys?: Array<{
        __typename?: "DomainModelsKarteMedicalHistoryPtSmokingRelatedModel";
        smokingDurationUnit?: number;
        smokingDuration?: number;
        smokingDetail?: string;
        seqNo?: string;
        ptId?: string;
        smokingDailyCount?: number;
        isDeleted?: number;
        hpId?: number;
        drinkingFrequency?: number;
        drinkingDetail?: string;
        drinkingAmount?: number;
        smokingStartAge?: number;
        smokingEndAge?: number;
        smokingEndYear?: number;
        smokingStartYear?: number;
        smokingStatus?: number;
        totalSmokingDuration?: number;
        brinkmanNumber?: string;
      }>;
      supples?: Array<{
        __typename?: "DomainModelsSpecialNoteImportantNotePtSuppleModel";
        cmt?: string;
        endDate?: number;
        fullEndDate?: number;
        fullStartDate?: number;
        hpId?: number;
        indexCd?: string;
        indexWord?: string;
        isDeleted?: number;
        ptId?: string;
        seqNo?: number;
        sortNo?: number;
        startDate?: number;
      }>;
      families?: Array<{
        __typename?: "DomainModelsFamilyPtFamilyRekiModel";
        byomei?: string;
        byomeiCd?: string;
        byotaiCd?: string;
        cmt?: string;
        familyId?: string;
        hpId?: number;
        id?: string;
        isDeleted?: boolean;
        ptId?: string;
        seqNo?: string;
        sortNo?: number;
        zokugaraCd?: string;
        zokugaraElse?: string;
      }>;
    };
  };
};

export type SaveKarteMedicalHistoryPastMutationVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  byomei: Types.Scalars["String"]["input"];
  byomeiCd: Types.Scalars["String"]["input"];
  byotaiCd: Types.Scalars["String"]["input"];
  cmt: Types.Scalars["String"]["input"];
  isDeleted: Types.Scalars["Int"]["input"];
  seqNo: Types.Scalars["Int"]["input"];
  sortNo: Types.Scalars["Int"]["input"];
  startDate: Types.Scalars["Int"]["input"];
  onSetDate: Types.Scalars["String"]["input"];
}>;

export type SaveKarteMedicalHistoryPastMutation = {
  __typename?: "mutation_root";
  postApiKarteMedicalHistorySaveKarteMedicalHistoryPast?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteMedicalHistorySaveKarteMedicalHistoryPastResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteMedicalHistorySaveKarteMedicalHistoryPastResponse";
      status?: boolean;
    };
  };
};

export type GetApiKarteMedicalHistoryGetDiseaseListQueryVariables =
  Types.Exact<{
    isByomei?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isHasFreeByomei?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isMisaiyou?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isPrefix?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isSuffix?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    keyword?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    pageIndex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    pageSize?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    sindate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiKarteMedicalHistoryGetDiseaseListQuery = {
  __typename?: "query_root";
  getApiMstItemDiseaseSearch?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemDiseaseSearchDiseaseSearchResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemDiseaseSearchDiseaseSearchResponse";
      data?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemDiseaseSearchDiseaseSearchModel";
        byomeiCd?: string;
        byomeiType?: string;
        icd10?: string;
        icd102013?: string;
        isAdopted?: boolean;
        kanaName1?: string;
        nanByo?: string;
        sbyomei?: string;
        sikkan?: string;
        sikkanCd?: number;
      }>;
    };
  };
};

export type SavePregnantInfoMutationVariables = Types.Exact<{
  breastfeedStatus?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isDeleted: Types.Scalars["Int"]["input"];
  pregnancyStatus?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId: Types.Scalars["BigInt"]["input"];
}>;

export type SavePregnantInfoMutation = {
  __typename?: "mutation_root";
  postApiKarteMedicalHistorySavePregnantInfo?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteMedicalHistorySavePregnantInfoResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteMedicalHistorySavePregnantInfoResponse";
      success?: boolean;
    };
  };
};

export type SaveKarteMedicalHistoryFamilyMutationVariables = Types.Exact<{
  cmt: Types.Scalars["String"]["input"];
  byomei: Types.Scalars["String"]["input"];
  id: Types.Scalars["BigInt"]["input"];
  isDeleted: Types.Scalars["Boolean"]["input"];
  zokugaraElse: Types.Scalars["String"]["input"];
  zokugaraCd: Types.Scalars["String"]["input"];
  ptId: Types.Scalars["BigInt"]["input"];
  seqNo: Types.Scalars["Int"]["input"];
  sortNo: Types.Scalars["Int"]["input"];
  byomeiCd: Types.Scalars["String"]["input"];
}>;

export type SaveKarteMedicalHistoryFamilyMutation = {
  __typename?: "mutation_root";
  postApiKarteMedicalHistorySaveKarteMedicalHistoryFamily?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteMedicalHistorySaveKarteMedicalHistoryFamilyResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteMedicalHistorySaveKarteMedicalHistoryFamilyResponse";
      status?: boolean;
    };
  };
};

export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutationVariables =
  Types.Exact<{
    ptId: Types.Scalars["BigInt"]["input"];
    drinkingAmount?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    drinkingDetail?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    drinkingFrequency: Types.Scalars["Int"]["input"];
    isDeleted: Types.Scalars["Int"]["input"];
    smokingDailyCount?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    smokingDetail?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    smokingDuration?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    smokingStartAge?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    smokingEndAge?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    smokingStatus: Types.Scalars["Int"]["input"];
  }>;

export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation = {
  __typename?: "mutation_root";
  postApiKarteMedicalHistorySaveKarteMedicalHistorySocial?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteMedicalHistorySaveKarteMedicalHistorySocialResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteMedicalHistorySaveKarteMedicalHistorySocialResponse";
      status?: boolean;
    };
  };
};

export type PostApiMstItemSearchSupplementMutationVariables = Types.Exact<{
  pageIndex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  pageSize?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  searchValue?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type PostApiMstItemSearchSupplementMutation = {
  __typename?: "mutation_root";
  postApiMstItemSearchSupplement?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSearchSupplementResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSearchSupplementResponse";
      total?: number;
      listData?: Array<{
        __typename?: "DomainModelsMstItemSearchSupplementModel";
        indexCd?: string;
        seibun?: string;
        indexWord?: string;
        seibunCd?: string;
        seibunGroupByIndexCd?: string;
        tokuhoFlg?: string;
        tokuhoFlgConvert?: string;
      }>;
    };
  };
};

export type PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutationVariables =
  Types.Exact<{
    ptId: Types.Scalars["BigInt"]["input"];
    cmt: Types.Scalars["String"]["input"];
    drugName: Types.Scalars["String"]["input"];
    endDate: Types.Scalars["Int"]["input"];
    isDeleted: Types.Scalars["Int"]["input"];
    itemCd: Types.Scalars["String"]["input"];
    seqNo: Types.Scalars["BigInt"]["input"];
    sortNo: Types.Scalars["Int"]["input"];
    startDate: Types.Scalars["Int"]["input"];
  }>;

export type PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation =
  {
    __typename?: "mutation_root";
    postApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrug?: {
      __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugResponse";
      message?: string;
      status?: number;
      data?: {
        __typename?: "EmrCloudApiResponsesKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugResponse";
        status?: number;
      };
    };
  };

export type SaveKarteMedicineHistoryOtcMedicineMutationVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  tradeName: Types.Scalars["String"]["input"];
  cmt?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  isDeleted: Types.Scalars["Int"]["input"];
  startDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  endDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  seqNo: Types.Scalars["BigInt"]["input"];
}>;

export type SaveKarteMedicineHistoryOtcMedicineMutation = {
  __typename?: "mutation_root";
  postApiKarteMedicalHistorySaveOtcMedicine?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteMedicalHistorySaveOtcMedicineResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteMedicalHistorySaveOtcMedicineResponse";
      success?: boolean;
    };
  };
};

export type SearchMedicineHistoryOtcMasterItemMutationVariables = Types.Exact<{
  pageIndex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  pageSize?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  searchValue?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type SearchMedicineHistoryOtcMasterItemMutation = {
  __typename?: "mutation_root";
  postApiMstItemSearchOTC?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSearchOtcResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSearchOtcResponse";
      total?: number;
      listData?: Array<{
        __typename?: "DomainModelsMstItemOtcItemModel";
        tradeName?: string;
        otcCd?: string;
        makerName?: string;
      }>;
    };
  };
};

export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutationVariables =
  Types.Exact<{
    cmt?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    endDate: Types.Scalars["Int"]["input"];
    indexCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    indexWord?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    ptId: Types.Scalars["BigInt"]["input"];
    isDeleted: Types.Scalars["Int"]["input"];
    seqNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    sortNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    startDate: Types.Scalars["Int"]["input"];
  }>;

export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation = {
  __typename?: "mutation_root";
  postApiKarteMedicalHistorySaveKarteMedicalHistorySupply?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteMedicalHistorySaveKarteMedicalHistorySupplyResponse";
    message?: string;
    status?: number;
  };
};

export type GetApiDiseasesGetListRecentRegisteredDiseaseQueryVariables =
  Types.Exact<{
    request?: Types.InputMaybe<Types.Scalars["JSON"]["input"]>;
  }>;

export type GetApiDiseasesGetListRecentRegisteredDiseaseQuery = {
  __typename?: "query_root";
  getApiDiseasesGetListRecentRegisteredDisease?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDiseasesGetListRecentRegisteredDiseaseResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesDiseasesGetListRecentRegisteredDiseaseResponse";
      data?: Array<{
        __typename?: "DomainModelsDiseasesGetListRecentRegisteredDiseaseModel";
        byomeiCd?: string;
        byomei?: string;
        fullByomei?: string;
        hosokuCmt?: string;
        nanbyoCd?: number;
        sikkanKbn?: number;
      }>;
    };
  };
};

export const GetApiKarteMedicalHistoryGetKarteMedicalHistoryDocument = gql`
  query getApiKarteMedicalHistoryGetKarteMedicalHistory($ptId: BigInt!) {
    getApiKarteMedicalHistoryGetKarteMedicalHistory(ptId: $ptId) {
      data {
        kioRekis {
          byomei
          byomeiCd
          byotaiCd
          cmt
          hpId
          isDeleted
          onSetDate
          ptId
          seqNo
          sortNo
          startDate
        }
        octDrugs {
          cmt
          endDate
          fullEndDate
          fullStartDate
          hpId
          isDeleted
          ptId
          seqNo
          serialNum
          sortNo
          startDate
          tradeName
        }
        otherDrugs {
          cmt
          drugName
          endDate
          fullEndDate
          fullStartDate
          hpId
          isDeleted
          itemCd
          ptId
          seqNo
          sortNo
          startDate
        }
        pregnants {
          hpId
          breastfeedStatus
          isDeleted
          pregnancyStatus
          ptId
        }
        socialHistorys {
          smokingDurationUnit
          smokingDuration
          smokingDetail
          seqNo
          ptId
          smokingDailyCount
          isDeleted
          hpId
          drinkingFrequency
          drinkingDetail
          drinkingAmount
          smokingStartAge
          smokingEndAge
          smokingEndYear
          smokingStartYear
          smokingStatus
          totalSmokingDuration
          brinkmanNumber
        }
        supples {
          cmt
          endDate
          fullEndDate
          fullStartDate
          hpId
          indexCd
          indexWord
          isDeleted
          ptId
          seqNo
          sortNo
          startDate
        }
        families {
          byomei
          byomeiCd
          byotaiCd
          cmt
          familyId
          hpId
          id
          isDeleted
          ptId
          seqNo
          sortNo
          zokugaraCd
          zokugaraElse
        }
      }
    }
  }
`;

/**
 * __useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery__
 *
 * To run a query within a React component, call `useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  > &
    (
      | {
          variables: GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >(GetApiKarteMedicalHistoryGetKarteMedicalHistoryDocument, options);
}
export function useGetApiKarteMedicalHistoryGetKarteMedicalHistoryLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >(GetApiKarteMedicalHistoryGetKarteMedicalHistoryDocument, options);
}
export function useGetApiKarteMedicalHistoryGetKarteMedicalHistorySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >(GetApiKarteMedicalHistoryGetKarteMedicalHistoryDocument, options);
}
export type GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryHookResult =
  ReturnType<typeof useGetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery>;
export type GetApiKarteMedicalHistoryGetKarteMedicalHistoryLazyQueryHookResult =
  ReturnType<
    typeof useGetApiKarteMedicalHistoryGetKarteMedicalHistoryLazyQuery
  >;
export type GetApiKarteMedicalHistoryGetKarteMedicalHistorySuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiKarteMedicalHistoryGetKarteMedicalHistorySuspenseQuery
  >;
export type GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryResult =
  Apollo.QueryResult<
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQuery,
    GetApiKarteMedicalHistoryGetKarteMedicalHistoryQueryVariables
  >;
export const SaveKarteMedicalHistoryPastDocument = gql`
  mutation saveKarteMedicalHistoryPast(
    $ptId: BigInt!
    $byomei: String!
    $byomeiCd: String!
    $byotaiCd: String!
    $cmt: String!
    $isDeleted: Int!
    $seqNo: Int!
    $sortNo: Int!
    $startDate: Int!
    $onSetDate: String!
  ) {
    postApiKarteMedicalHistorySaveKarteMedicalHistoryPast(
      emrCloudApiRequestsKarteMedicalHistorySaveKarteMedicalHistoryPastRequestInput: {
        kioRekiItems: [
          {
            ptId: $ptId
            seqNo: $seqNo
            isDeleted: $isDeleted
            byomei: $byomei
            cmt: $cmt
            sortNo: $sortNo
            startDate: $startDate
            byotaiCd: $byotaiCd
            byomeiCd: $byomeiCd
            onSetDate: $onSetDate
          }
        ]
      }
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type SaveKarteMedicalHistoryPastMutationFn = Apollo.MutationFunction<
  SaveKarteMedicalHistoryPastMutation,
  SaveKarteMedicalHistoryPastMutationVariables
>;

/**
 * __useSaveKarteMedicalHistoryPastMutation__
 *
 * To run a mutation, you first call `useSaveKarteMedicalHistoryPastMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveKarteMedicalHistoryPastMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveKarteMedicalHistoryPastMutation, { data, loading, error }] = useSaveKarteMedicalHistoryPastMutation({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      byomei: // value for 'byomei'
 *      byomeiCd: // value for 'byomeiCd'
 *      byotaiCd: // value for 'byotaiCd'
 *      cmt: // value for 'cmt'
 *      isDeleted: // value for 'isDeleted'
 *      seqNo: // value for 'seqNo'
 *      sortNo: // value for 'sortNo'
 *      startDate: // value for 'startDate'
 *      onSetDate: // value for 'onSetDate'
 *   },
 * });
 */
export function useSaveKarteMedicalHistoryPastMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SaveKarteMedicalHistoryPastMutation,
    SaveKarteMedicalHistoryPastMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SaveKarteMedicalHistoryPastMutation,
    SaveKarteMedicalHistoryPastMutationVariables
  >(SaveKarteMedicalHistoryPastDocument, options);
}
export type SaveKarteMedicalHistoryPastMutationHookResult = ReturnType<
  typeof useSaveKarteMedicalHistoryPastMutation
>;
export type SaveKarteMedicalHistoryPastMutationResult =
  Apollo.MutationResult<SaveKarteMedicalHistoryPastMutation>;
export type SaveKarteMedicalHistoryPastMutationOptions =
  Apollo.BaseMutationOptions<
    SaveKarteMedicalHistoryPastMutation,
    SaveKarteMedicalHistoryPastMutationVariables
  >;
export const GetApiKarteMedicalHistoryGetDiseaseListDocument = gql`
  query getApiKarteMedicalHistoryGetDiseaseList(
    $isByomei: Boolean
    $isHasFreeByomei: Boolean
    $isMisaiyou: Boolean
    $isPrefix: Boolean
    $isSuffix: Boolean
    $keyword: String
    $pageIndex: Int
    $pageSize: Int
    $sindate: Int
  ) {
    getApiMstItemDiseaseSearch(
      isByomei: $isByomei
      isHasFreeByomei: $isHasFreeByomei
      isMisaiyou: $isMisaiyou
      isPrefix: $isPrefix
      isSuffix: $isSuffix
      keyword: $keyword
      pageIndex: $pageIndex
      pageSize: $pageSize
      sindate: $sindate
    ) {
      data {
        data {
          byomeiCd
          byomeiType
          icd10
          icd102013
          isAdopted
          kanaName1
          nanByo
          sbyomei
          sikkan
          sikkanCd
          nanByo
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiKarteMedicalHistoryGetDiseaseListQuery__
 *
 * To run a query within a React component, call `useGetApiKarteMedicalHistoryGetDiseaseListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKarteMedicalHistoryGetDiseaseListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKarteMedicalHistoryGetDiseaseListQuery({
 *   variables: {
 *      isByomei: // value for 'isByomei'
 *      isHasFreeByomei: // value for 'isHasFreeByomei'
 *      isMisaiyou: // value for 'isMisaiyou'
 *      isPrefix: // value for 'isPrefix'
 *      isSuffix: // value for 'isSuffix'
 *      keyword: // value for 'keyword'
 *      pageIndex: // value for 'pageIndex'
 *      pageSize: // value for 'pageSize'
 *      sindate: // value for 'sindate'
 *   },
 * });
 */
export function useGetApiKarteMedicalHistoryGetDiseaseListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiKarteMedicalHistoryGetDiseaseListQuery,
    GetApiKarteMedicalHistoryGetDiseaseListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKarteMedicalHistoryGetDiseaseListQuery,
    GetApiKarteMedicalHistoryGetDiseaseListQueryVariables
  >(GetApiKarteMedicalHistoryGetDiseaseListDocument, options);
}
export function useGetApiKarteMedicalHistoryGetDiseaseListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKarteMedicalHistoryGetDiseaseListQuery,
    GetApiKarteMedicalHistoryGetDiseaseListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKarteMedicalHistoryGetDiseaseListQuery,
    GetApiKarteMedicalHistoryGetDiseaseListQueryVariables
  >(GetApiKarteMedicalHistoryGetDiseaseListDocument, options);
}
export function useGetApiKarteMedicalHistoryGetDiseaseListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKarteMedicalHistoryGetDiseaseListQuery,
    GetApiKarteMedicalHistoryGetDiseaseListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKarteMedicalHistoryGetDiseaseListQuery,
    GetApiKarteMedicalHistoryGetDiseaseListQueryVariables
  >(GetApiKarteMedicalHistoryGetDiseaseListDocument, options);
}
export type GetApiKarteMedicalHistoryGetDiseaseListQueryHookResult = ReturnType<
  typeof useGetApiKarteMedicalHistoryGetDiseaseListQuery
>;
export type GetApiKarteMedicalHistoryGetDiseaseListLazyQueryHookResult =
  ReturnType<typeof useGetApiKarteMedicalHistoryGetDiseaseListLazyQuery>;
export type GetApiKarteMedicalHistoryGetDiseaseListSuspenseQueryHookResult =
  ReturnType<typeof useGetApiKarteMedicalHistoryGetDiseaseListSuspenseQuery>;
export type GetApiKarteMedicalHistoryGetDiseaseListQueryResult =
  Apollo.QueryResult<
    GetApiKarteMedicalHistoryGetDiseaseListQuery,
    GetApiKarteMedicalHistoryGetDiseaseListQueryVariables
  >;
export const SavePregnantInfoDocument = gql`
  mutation savePregnantInfo(
    $breastfeedStatus: Int
    $isDeleted: Int!
    $pregnancyStatus: Int
    $ptId: BigInt!
  ) {
    postApiKarteMedicalHistorySavePregnantInfo(
      emrCloudApiRequestsKarteMedicalHistorySavePregnantInfoRequestInput: {
        breastfeedStatus: $breastfeedStatus
        isDeleted: $isDeleted
        pregnancyStatus: $pregnancyStatus
        ptId: $ptId
      }
    ) {
      data {
        success
      }
      message
      status
    }
  }
`;
export type SavePregnantInfoMutationFn = Apollo.MutationFunction<
  SavePregnantInfoMutation,
  SavePregnantInfoMutationVariables
>;

/**
 * __useSavePregnantInfoMutation__
 *
 * To run a mutation, you first call `useSavePregnantInfoMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSavePregnantInfoMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [savePregnantInfoMutation, { data, loading, error }] = useSavePregnantInfoMutation({
 *   variables: {
 *      breastfeedStatus: // value for 'breastfeedStatus'
 *      isDeleted: // value for 'isDeleted'
 *      pregnancyStatus: // value for 'pregnancyStatus'
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useSavePregnantInfoMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SavePregnantInfoMutation,
    SavePregnantInfoMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SavePregnantInfoMutation,
    SavePregnantInfoMutationVariables
  >(SavePregnantInfoDocument, options);
}
export type SavePregnantInfoMutationHookResult = ReturnType<
  typeof useSavePregnantInfoMutation
>;
export type SavePregnantInfoMutationResult =
  Apollo.MutationResult<SavePregnantInfoMutation>;
export type SavePregnantInfoMutationOptions = Apollo.BaseMutationOptions<
  SavePregnantInfoMutation,
  SavePregnantInfoMutationVariables
>;
export const SaveKarteMedicalHistoryFamilyDocument = gql`
  mutation saveKarteMedicalHistoryFamily(
    $cmt: String!
    $byomei: String!
    $id: BigInt!
    $isDeleted: Boolean!
    $zokugaraElse: String!
    $zokugaraCd: String!
    $ptId: BigInt!
    $seqNo: Int!
    $sortNo: Int!
    $byomeiCd: String!
  ) {
    postApiKarteMedicalHistorySaveKarteMedicalHistoryFamily(
      emrCloudApiRequestsKarteMedicalHistorySaveKarteMedicalHistoryFamilyRequestInput: {
        familyList: {
          cmt: $cmt
          byomei: $byomei
          id: $id
          isDeleted: $isDeleted
          zokugaraElse: $zokugaraElse
          zokugaraCd: $zokugaraCd
          ptId: $ptId
          seqNo: $seqNo
          sortNo: $sortNo
          byomeiCd: $byomeiCd
        }
      }
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type SaveKarteMedicalHistoryFamilyMutationFn = Apollo.MutationFunction<
  SaveKarteMedicalHistoryFamilyMutation,
  SaveKarteMedicalHistoryFamilyMutationVariables
>;

/**
 * __useSaveKarteMedicalHistoryFamilyMutation__
 *
 * To run a mutation, you first call `useSaveKarteMedicalHistoryFamilyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveKarteMedicalHistoryFamilyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveKarteMedicalHistoryFamilyMutation, { data, loading, error }] = useSaveKarteMedicalHistoryFamilyMutation({
 *   variables: {
 *      cmt: // value for 'cmt'
 *      byomei: // value for 'byomei'
 *      id: // value for 'id'
 *      isDeleted: // value for 'isDeleted'
 *      zokugaraElse: // value for 'zokugaraElse'
 *      zokugaraCd: // value for 'zokugaraCd'
 *      ptId: // value for 'ptId'
 *      seqNo: // value for 'seqNo'
 *      sortNo: // value for 'sortNo'
 *      byomeiCd: // value for 'byomeiCd'
 *   },
 * });
 */
export function useSaveKarteMedicalHistoryFamilyMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SaveKarteMedicalHistoryFamilyMutation,
    SaveKarteMedicalHistoryFamilyMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SaveKarteMedicalHistoryFamilyMutation,
    SaveKarteMedicalHistoryFamilyMutationVariables
  >(SaveKarteMedicalHistoryFamilyDocument, options);
}
export type SaveKarteMedicalHistoryFamilyMutationHookResult = ReturnType<
  typeof useSaveKarteMedicalHistoryFamilyMutation
>;
export type SaveKarteMedicalHistoryFamilyMutationResult =
  Apollo.MutationResult<SaveKarteMedicalHistoryFamilyMutation>;
export type SaveKarteMedicalHistoryFamilyMutationOptions =
  Apollo.BaseMutationOptions<
    SaveKarteMedicalHistoryFamilyMutation,
    SaveKarteMedicalHistoryFamilyMutationVariables
  >;
export const PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialDocument = gql`
  mutation postApiKarteMedicalHistorySaveKarteMedicalHistorySocial(
    $ptId: BigInt!
    $drinkingAmount: Int
    $drinkingDetail: String
    $drinkingFrequency: Int!
    $isDeleted: Int!
    $smokingDailyCount: Int
    $smokingDetail: String
    $smokingDuration: Int
    $smokingStartAge: Int
    $smokingEndAge: Int
    $smokingStatus: Int!
  ) {
    postApiKarteMedicalHistorySaveKarteMedicalHistorySocial(
      emrCloudApiRequestsKarteMedicalHistorySaveKarteMedicalHistorySocialRequestInput: {
        ptId: $ptId
        ptSocialHistoryRequest: {
          drinkingAmount: $drinkingAmount
          drinkingDetail: $drinkingDetail
          drinkingFrequency: $drinkingFrequency
          isDeleted: $isDeleted
          smokingDailyCount: $smokingDailyCount
          smokingDuration: $smokingDuration
          smokingDetail: $smokingDetail
          smokingStatus: $smokingStatus
          smokingStartAge: $smokingStartAge
          smokingEndAge: $smokingEndAge
        }
      }
    ) {
      message
      status
      data {
        status
      }
    }
  }
`;
export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutationFn =
  Apollo.MutationFunction<
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutationVariables
  >;

/**
 * __usePostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation__
 *
 * To run a mutation, you first call `usePostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation, { data, loading, error }] = usePostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      drinkingAmount: // value for 'drinkingAmount'
 *      drinkingDetail: // value for 'drinkingDetail'
 *      drinkingFrequency: // value for 'drinkingFrequency'
 *      isDeleted: // value for 'isDeleted'
 *      smokingDailyCount: // value for 'smokingDailyCount'
 *      smokingDetail: // value for 'smokingDetail'
 *      smokingDuration: // value for 'smokingDuration'
 *      smokingStartAge: // value for 'smokingStartAge'
 *      smokingEndAge: // value for 'smokingEndAge'
 *      smokingStatus: // value for 'smokingStatus'
 *   },
 * });
 */
export function usePostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutationVariables
  >(PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialDocument, options);
}
export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutationHookResult =
  ReturnType<
    typeof usePostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation
  >;
export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutationResult =
  Apollo.MutationResult<PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation>;
export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySocialMutationVariables
  >;
export const PostApiMstItemSearchSupplementDocument = gql`
  mutation postApiMstItemSearchSupplement(
    $pageIndex: Int
    $pageSize: Int
    $searchValue: String
  ) {
    postApiMstItemSearchSupplement(
      emrCloudApiRequestsMstItemSearchSupplementRequestInput: {
        pageIndex: $pageIndex
        pageSize: $pageSize
        searchValue: $searchValue
      }
    ) {
      data {
        listData {
          indexCd
          seibun
          indexWord
          seibunCd
          seibunGroupByIndexCd
          tokuhoFlg
          tokuhoFlgConvert
        }
        total
      }
      message
      status
    }
  }
`;
export type PostApiMstItemSearchSupplementMutationFn = Apollo.MutationFunction<
  PostApiMstItemSearchSupplementMutation,
  PostApiMstItemSearchSupplementMutationVariables
>;

/**
 * __usePostApiMstItemSearchSupplementMutation__
 *
 * To run a mutation, you first call `usePostApiMstItemSearchSupplementMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMstItemSearchSupplementMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMstItemSearchSupplementMutation, { data, loading, error }] = usePostApiMstItemSearchSupplementMutation({
 *   variables: {
 *      pageIndex: // value for 'pageIndex'
 *      pageSize: // value for 'pageSize'
 *      searchValue: // value for 'searchValue'
 *   },
 * });
 */
export function usePostApiMstItemSearchSupplementMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMstItemSearchSupplementMutation,
    PostApiMstItemSearchSupplementMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMstItemSearchSupplementMutation,
    PostApiMstItemSearchSupplementMutationVariables
  >(PostApiMstItemSearchSupplementDocument, options);
}
export type PostApiMstItemSearchSupplementMutationHookResult = ReturnType<
  typeof usePostApiMstItemSearchSupplementMutation
>;
export type PostApiMstItemSearchSupplementMutationResult =
  Apollo.MutationResult<PostApiMstItemSearchSupplementMutation>;
export type PostApiMstItemSearchSupplementMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMstItemSearchSupplementMutation,
    PostApiMstItemSearchSupplementMutationVariables
  >;
export const PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugDocument = gql`
  mutation postApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrug(
    $ptId: BigInt!
    $cmt: String!
    $drugName: String!
    $endDate: Int!
    $isDeleted: Int!
    $itemCd: String!
    $seqNo: BigInt!
    $sortNo: Int!
    $startDate: Int!
  ) {
    postApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrug(
      emrCloudApiRequestsKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugRequestInput: {
        ptId: $ptId
        saveMedicalHistoryOtherDrugInputItems: {
          cmt: $cmt
          drugName: $drugName
          endDate: $endDate
          isDeleted: $isDeleted
          itemCd: $itemCd
          ptId: $ptId
          seqNo: $seqNo
          sortNo: $sortNo
          startDate: $startDate
        }
      }
    ) {
      message
      status
      data {
        status
      }
    }
  }
`;
export type PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutationFn =
  Apollo.MutationFunction<
    PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutationVariables
  >;

/**
 * __usePostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation__
 *
 * To run a mutation, you first call `usePostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation, { data, loading, error }] = usePostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      cmt: // value for 'cmt'
 *      drugName: // value for 'drugName'
 *      endDate: // value for 'endDate'
 *      isDeleted: // value for 'isDeleted'
 *      itemCd: // value for 'itemCd'
 *      seqNo: // value for 'seqNo'
 *      sortNo: // value for 'sortNo'
 *      startDate: // value for 'startDate'
 *   },
 * });
 */
export function usePostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutationVariables
  >(
    PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugDocument,
    options,
  );
}
export type PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutationHookResult =
  ReturnType<
    typeof usePostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation
  >;
export type PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutationResult =
  Apollo.MutationResult<PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation>;
export type PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistoryOtherDrugMutationVariables
  >;
export const SaveKarteMedicineHistoryOtcMedicineDocument = gql`
  mutation saveKarteMedicineHistoryOtcMedicine(
    $ptId: BigInt!
    $tradeName: String!
    $cmt: String
    $isDeleted: Int!
    $startDate: Int
    $endDate: Int
    $seqNo: BigInt!
  ) {
    postApiKarteMedicalHistorySaveOtcMedicine(
      emrCloudApiRequestsKarteMedicalHistorySaveOtcMedicineRequestInput: {
        ptId: $ptId
        otcDrugItems: {
          ptId: $ptId
          cmt: $cmt
          tradeName: $tradeName
          isDeleted: $isDeleted
          seqNo: $seqNo
          startDate: $startDate
          endDate: $endDate
        }
      }
    ) {
      data {
        success
      }
      status
      message
    }
  }
`;
export type SaveKarteMedicineHistoryOtcMedicineMutationFn =
  Apollo.MutationFunction<
    SaveKarteMedicineHistoryOtcMedicineMutation,
    SaveKarteMedicineHistoryOtcMedicineMutationVariables
  >;

/**
 * __useSaveKarteMedicineHistoryOtcMedicineMutation__
 *
 * To run a mutation, you first call `useSaveKarteMedicineHistoryOtcMedicineMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveKarteMedicineHistoryOtcMedicineMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveKarteMedicineHistoryOtcMedicineMutation, { data, loading, error }] = useSaveKarteMedicineHistoryOtcMedicineMutation({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      tradeName: // value for 'tradeName'
 *      cmt: // value for 'cmt'
 *      isDeleted: // value for 'isDeleted'
 *      startDate: // value for 'startDate'
 *      endDate: // value for 'endDate'
 *      seqNo: // value for 'seqNo'
 *   },
 * });
 */
export function useSaveKarteMedicineHistoryOtcMedicineMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SaveKarteMedicineHistoryOtcMedicineMutation,
    SaveKarteMedicineHistoryOtcMedicineMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SaveKarteMedicineHistoryOtcMedicineMutation,
    SaveKarteMedicineHistoryOtcMedicineMutationVariables
  >(SaveKarteMedicineHistoryOtcMedicineDocument, options);
}
export type SaveKarteMedicineHistoryOtcMedicineMutationHookResult = ReturnType<
  typeof useSaveKarteMedicineHistoryOtcMedicineMutation
>;
export type SaveKarteMedicineHistoryOtcMedicineMutationResult =
  Apollo.MutationResult<SaveKarteMedicineHistoryOtcMedicineMutation>;
export type SaveKarteMedicineHistoryOtcMedicineMutationOptions =
  Apollo.BaseMutationOptions<
    SaveKarteMedicineHistoryOtcMedicineMutation,
    SaveKarteMedicineHistoryOtcMedicineMutationVariables
  >;
export const SearchMedicineHistoryOtcMasterItemDocument = gql`
  mutation searchMedicineHistoryOtcMasterItem(
    $pageIndex: Int
    $pageSize: Int
    $searchValue: String
  ) {
    postApiMstItemSearchOTC(
      emrCloudApiRequestsMstItemSearchOTCRequestInput: {
        pageIndex: $pageIndex
        pageSize: $pageSize
        searchValue: $searchValue
      }
    ) {
      data {
        listData {
          tradeName
          otcCd
          makerName
        }
        total
      }
      message
      status
    }
  }
`;
export type SearchMedicineHistoryOtcMasterItemMutationFn =
  Apollo.MutationFunction<
    SearchMedicineHistoryOtcMasterItemMutation,
    SearchMedicineHistoryOtcMasterItemMutationVariables
  >;

/**
 * __useSearchMedicineHistoryOtcMasterItemMutation__
 *
 * To run a mutation, you first call `useSearchMedicineHistoryOtcMasterItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSearchMedicineHistoryOtcMasterItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [searchMedicineHistoryOtcMasterItemMutation, { data, loading, error }] = useSearchMedicineHistoryOtcMasterItemMutation({
 *   variables: {
 *      pageIndex: // value for 'pageIndex'
 *      pageSize: // value for 'pageSize'
 *      searchValue: // value for 'searchValue'
 *   },
 * });
 */
export function useSearchMedicineHistoryOtcMasterItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SearchMedicineHistoryOtcMasterItemMutation,
    SearchMedicineHistoryOtcMasterItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SearchMedicineHistoryOtcMasterItemMutation,
    SearchMedicineHistoryOtcMasterItemMutationVariables
  >(SearchMedicineHistoryOtcMasterItemDocument, options);
}
export type SearchMedicineHistoryOtcMasterItemMutationHookResult = ReturnType<
  typeof useSearchMedicineHistoryOtcMasterItemMutation
>;
export type SearchMedicineHistoryOtcMasterItemMutationResult =
  Apollo.MutationResult<SearchMedicineHistoryOtcMasterItemMutation>;
export type SearchMedicineHistoryOtcMasterItemMutationOptions =
  Apollo.BaseMutationOptions<
    SearchMedicineHistoryOtcMasterItemMutation,
    SearchMedicineHistoryOtcMasterItemMutationVariables
  >;
export const PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyDocument = gql`
  mutation postApiKarteMedicalHistorySaveKarteMedicalHistorySupply(
    $cmt: String
    $endDate: Int!
    $indexCd: String
    $indexWord: String
    $ptId: BigInt!
    $isDeleted: Int!
    $seqNo: Int
    $sortNo: Int
    $startDate: Int!
  ) {
    postApiKarteMedicalHistorySaveKarteMedicalHistorySupply(
      emrCloudApiRequestsKarteMedicalHistorySaveKarteMedicalHistorySupplyRequestInput: {
        ptSupples: {
          cmt: $cmt
          endDate: $endDate
          indexCd: $indexCd
          indexWord: $indexWord
          isDeleted: $isDeleted
          ptId: $ptId
          seqNo: $seqNo
          sortNo: $sortNo
          startDate: $startDate
        }
      }
    ) {
      message
      status
    }
  }
`;
export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutationFn =
  Apollo.MutationFunction<
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutationVariables
  >;

/**
 * __usePostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation__
 *
 * To run a mutation, you first call `usePostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation, { data, loading, error }] = usePostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation({
 *   variables: {
 *      cmt: // value for 'cmt'
 *      endDate: // value for 'endDate'
 *      indexCd: // value for 'indexCd'
 *      indexWord: // value for 'indexWord'
 *      ptId: // value for 'ptId'
 *      isDeleted: // value for 'isDeleted'
 *      seqNo: // value for 'seqNo'
 *      sortNo: // value for 'sortNo'
 *      startDate: // value for 'startDate'
 *   },
 * });
 */
export function usePostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutationVariables
  >(PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyDocument, options);
}
export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutationHookResult =
  ReturnType<
    typeof usePostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation
  >;
export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutationResult =
  Apollo.MutationResult<PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation>;
export type PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutation,
    PostApiKarteMedicalHistorySaveKarteMedicalHistorySupplyMutationVariables
  >;
export const GetApiDiseasesGetListRecentRegisteredDiseaseDocument = gql`
  query getApiDiseasesGetListRecentRegisteredDisease($request: JSON) {
    getApiDiseasesGetListRecentRegisteredDisease(request: $request) {
      data {
        data {
          byomeiCd
          byomei
          fullByomei
          hosokuCmt
          nanbyoCd
          sikkanKbn
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiDiseasesGetListRecentRegisteredDiseaseQuery__
 *
 * To run a query within a React component, call `useGetApiDiseasesGetListRecentRegisteredDiseaseQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiDiseasesGetListRecentRegisteredDiseaseQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiDiseasesGetListRecentRegisteredDiseaseQuery({
 *   variables: {
 *      request: // value for 'request'
 *   },
 * });
 */
export function useGetApiDiseasesGetListRecentRegisteredDiseaseQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiDiseasesGetListRecentRegisteredDiseaseQuery,
    GetApiDiseasesGetListRecentRegisteredDiseaseQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiDiseasesGetListRecentRegisteredDiseaseQuery,
    GetApiDiseasesGetListRecentRegisteredDiseaseQueryVariables
  >(GetApiDiseasesGetListRecentRegisteredDiseaseDocument, options);
}
export function useGetApiDiseasesGetListRecentRegisteredDiseaseLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiDiseasesGetListRecentRegisteredDiseaseQuery,
    GetApiDiseasesGetListRecentRegisteredDiseaseQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiDiseasesGetListRecentRegisteredDiseaseQuery,
    GetApiDiseasesGetListRecentRegisteredDiseaseQueryVariables
  >(GetApiDiseasesGetListRecentRegisteredDiseaseDocument, options);
}
export function useGetApiDiseasesGetListRecentRegisteredDiseaseSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiDiseasesGetListRecentRegisteredDiseaseQuery,
    GetApiDiseasesGetListRecentRegisteredDiseaseQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiDiseasesGetListRecentRegisteredDiseaseQuery,
    GetApiDiseasesGetListRecentRegisteredDiseaseQueryVariables
  >(GetApiDiseasesGetListRecentRegisteredDiseaseDocument, options);
}
export type GetApiDiseasesGetListRecentRegisteredDiseaseQueryHookResult =
  ReturnType<typeof useGetApiDiseasesGetListRecentRegisteredDiseaseQuery>;
export type GetApiDiseasesGetListRecentRegisteredDiseaseLazyQueryHookResult =
  ReturnType<typeof useGetApiDiseasesGetListRecentRegisteredDiseaseLazyQuery>;
export type GetApiDiseasesGetListRecentRegisteredDiseaseSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiDiseasesGetListRecentRegisteredDiseaseSuspenseQuery
  >;
export type GetApiDiseasesGetListRecentRegisteredDiseaseQueryResult =
  Apollo.QueryResult<
    GetApiDiseasesGetListRecentRegisteredDiseaseQuery,
    GetApiDiseasesGetListRecentRegisteredDiseaseQueryVariables
  >;
