import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiPatientInforGetInsuranceCardImageQueryVariables =
  Types.Exact<{
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  }>;

export type GetApiPatientInforGetInsuranceCardImageQuery = {
  __typename?: "query_root";
  getApiPatientInforGetInsuranceCardImage?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforGetInsuranceCardImageResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforGetInsuranceCardImageResponse";
      listPortalCustomerFileModels?: Array<{
        __typename?: "DomainModelsPortalCustomerFilePortalCustomerFileModel";
        customerId?: number;
        originalFileName?: string;
        pathFile?: string;
        type?: number;
      }>;
    };
  };
};

export const GetApiPatientInforGetInsuranceCardImageDocument = gql`
  query getApiPatientInforGetInsuranceCardImage($ptId: BigInt) {
    getApiPatientInforGetInsuranceCardImage(ptId: $ptId) {
      data {
        listPortalCustomerFileModels {
          customerId
          originalFileName
          pathFile
          type
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiPatientInforGetInsuranceCardImageQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetInsuranceCardImageQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetInsuranceCardImageQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetInsuranceCardImageQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiPatientInforGetInsuranceCardImageQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetInsuranceCardImageQuery,
    GetApiPatientInforGetInsuranceCardImageQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetInsuranceCardImageQuery,
    GetApiPatientInforGetInsuranceCardImageQueryVariables
  >(GetApiPatientInforGetInsuranceCardImageDocument, options);
}
export function useGetApiPatientInforGetInsuranceCardImageLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetInsuranceCardImageQuery,
    GetApiPatientInforGetInsuranceCardImageQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetInsuranceCardImageQuery,
    GetApiPatientInforGetInsuranceCardImageQueryVariables
  >(GetApiPatientInforGetInsuranceCardImageDocument, options);
}
export function useGetApiPatientInforGetInsuranceCardImageSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetInsuranceCardImageQuery,
    GetApiPatientInforGetInsuranceCardImageQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetInsuranceCardImageQuery,
    GetApiPatientInforGetInsuranceCardImageQueryVariables
  >(GetApiPatientInforGetInsuranceCardImageDocument, options);
}
export type GetApiPatientInforGetInsuranceCardImageQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetInsuranceCardImageQuery
>;
export type GetApiPatientInforGetInsuranceCardImageLazyQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetInsuranceCardImageLazyQuery>;
export type GetApiPatientInforGetInsuranceCardImageSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetInsuranceCardImageSuspenseQuery>;
export type GetApiPatientInforGetInsuranceCardImageQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforGetInsuranceCardImageQuery,
    GetApiPatientInforGetInsuranceCardImageQueryVariables
  >;
