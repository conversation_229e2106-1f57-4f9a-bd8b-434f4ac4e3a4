import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type OperatorLoginMutationVariables = Types.Exact<{
  input: Types.OperatorLoginReq;
}>;

export type OperatorLoginMutation = {
  __typename?: "mutation_root";
  operatorLogin: {
    __typename?: "OperatorLoginRes";
    challengeName: string;
    sessionValue?: string;
    pharmacyFlg: boolean;
    karteStatus: number;
  };
};

export type OperatorVerifyMfaCodeMutationVariables = Types.Exact<{
  input: Types.OperatorVerifyMfaCodeReq;
}>;

export type OperatorVerifyMfaCodeMutation = {
  __typename?: "mutation_root";
  operatorVerifyMFACode: {
    __typename?: "OperatorVerifyMFACodeRes";
    challengeName: string;
    sessionValue?: string;
    pharmacyFlg: boolean;
  };
};

export type OperatorChangePasswordMutationVariables = Types.Exact<{
  input: Types.OperatorChangePasswordReq;
}>;

export type OperatorChangePasswordMutation = {
  __typename?: "mutation_root";
  operatorChangePassword: {
    __typename?: "OperatorChangePasswordRes";
    challengeName: string;
    sessionValue?: string;
    pharmacyFlg: boolean;
  };
};

export const OperatorLoginDocument = gql`
  mutation operatorLogin($input: OperatorLoginReq!) {
    operatorLogin(input: $input) {
      challengeName
      sessionValue
      pharmacyFlg
      karteStatus
    }
  }
`;
export type OperatorLoginMutationFn = Apollo.MutationFunction<
  OperatorLoginMutation,
  OperatorLoginMutationVariables
>;

/**
 * __useOperatorLoginMutation__
 *
 * To run a mutation, you first call `useOperatorLoginMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useOperatorLoginMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [operatorLoginMutation, { data, loading, error }] = useOperatorLoginMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useOperatorLoginMutation(
  baseOptions?: Apollo.MutationHookOptions<
    OperatorLoginMutation,
    OperatorLoginMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    OperatorLoginMutation,
    OperatorLoginMutationVariables
  >(OperatorLoginDocument, options);
}
export type OperatorLoginMutationHookResult = ReturnType<
  typeof useOperatorLoginMutation
>;
export type OperatorLoginMutationResult =
  Apollo.MutationResult<OperatorLoginMutation>;
export type OperatorLoginMutationOptions = Apollo.BaseMutationOptions<
  OperatorLoginMutation,
  OperatorLoginMutationVariables
>;
export const OperatorVerifyMfaCodeDocument = gql`
  mutation operatorVerifyMFACode($input: OperatorVerifyMFACodeReq!) {
    operatorVerifyMFACode(input: $input) {
      challengeName
      sessionValue
      pharmacyFlg
    }
  }
`;
export type OperatorVerifyMfaCodeMutationFn = Apollo.MutationFunction<
  OperatorVerifyMfaCodeMutation,
  OperatorVerifyMfaCodeMutationVariables
>;

/**
 * __useOperatorVerifyMfaCodeMutation__
 *
 * To run a mutation, you first call `useOperatorVerifyMfaCodeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useOperatorVerifyMfaCodeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [operatorVerifyMfaCodeMutation, { data, loading, error }] = useOperatorVerifyMfaCodeMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useOperatorVerifyMfaCodeMutation(
  baseOptions?: Apollo.MutationHookOptions<
    OperatorVerifyMfaCodeMutation,
    OperatorVerifyMfaCodeMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    OperatorVerifyMfaCodeMutation,
    OperatorVerifyMfaCodeMutationVariables
  >(OperatorVerifyMfaCodeDocument, options);
}
export type OperatorVerifyMfaCodeMutationHookResult = ReturnType<
  typeof useOperatorVerifyMfaCodeMutation
>;
export type OperatorVerifyMfaCodeMutationResult =
  Apollo.MutationResult<OperatorVerifyMfaCodeMutation>;
export type OperatorVerifyMfaCodeMutationOptions = Apollo.BaseMutationOptions<
  OperatorVerifyMfaCodeMutation,
  OperatorVerifyMfaCodeMutationVariables
>;
export const OperatorChangePasswordDocument = gql`
  mutation operatorChangePassword($input: OperatorChangePasswordReq!) {
    operatorChangePassword(input: $input) {
      challengeName
      sessionValue
      pharmacyFlg
    }
  }
`;
export type OperatorChangePasswordMutationFn = Apollo.MutationFunction<
  OperatorChangePasswordMutation,
  OperatorChangePasswordMutationVariables
>;

/**
 * __useOperatorChangePasswordMutation__
 *
 * To run a mutation, you first call `useOperatorChangePasswordMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useOperatorChangePasswordMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [operatorChangePasswordMutation, { data, loading, error }] = useOperatorChangePasswordMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useOperatorChangePasswordMutation(
  baseOptions?: Apollo.MutationHookOptions<
    OperatorChangePasswordMutation,
    OperatorChangePasswordMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    OperatorChangePasswordMutation,
    OperatorChangePasswordMutationVariables
  >(OperatorChangePasswordDocument, options);
}
export type OperatorChangePasswordMutationHookResult = ReturnType<
  typeof useOperatorChangePasswordMutation
>;
export type OperatorChangePasswordMutationResult =
  Apollo.MutationResult<OperatorChangePasswordMutation>;
export type OperatorChangePasswordMutationOptions = Apollo.BaseMutationOptions<
  OperatorChangePasswordMutation,
  OperatorChangePasswordMutationVariables
>;
