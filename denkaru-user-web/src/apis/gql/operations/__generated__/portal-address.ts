import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetAddressByPostcodeQueryVariables = Types.Exact<{
  postcode: Types.Scalars["String"]["input"];
}>;

export type GetAddressByPostcodeQuery = {
  __typename?: "query_root";
  getAddressByPostcode?: Array<{
    __typename?: "PortalAddress";
    postcode: string;
    cityName: string;
    prefectureName: string;
    prefectureId: number;
    banti: string;
    cityId: number;
  }>;
};

export const GetAddressByPostcodeDocument = gql`
  query getAddressByPostcode($postcode: String!) {
    getAddressByPostcode(postcode: $postcode) {
      postcode
      cityName
      prefectureName
      prefectureId
      banti
      cityId
    }
  }
`;

/**
 * __useGetAddressByPostcodeQuery__
 *
 * To run a query within a React component, call `useGetAddressByPostcodeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAddressByPostcodeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAddressByPostcodeQuery({
 *   variables: {
 *      postcode: // value for 'postcode'
 *   },
 * });
 */
export function useGetAddressByPostcodeQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetAddressByPostcodeQuery,
    GetAddressByPostcodeQueryVariables
  > &
    (
      | { variables: GetAddressByPostcodeQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetAddressByPostcodeQuery,
    GetAddressByPostcodeQueryVariables
  >(GetAddressByPostcodeDocument, options);
}
export function useGetAddressByPostcodeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetAddressByPostcodeQuery,
    GetAddressByPostcodeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetAddressByPostcodeQuery,
    GetAddressByPostcodeQueryVariables
  >(GetAddressByPostcodeDocument, options);
}
export function useGetAddressByPostcodeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetAddressByPostcodeQuery,
    GetAddressByPostcodeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetAddressByPostcodeQuery,
    GetAddressByPostcodeQueryVariables
  >(GetAddressByPostcodeDocument, options);
}
export type GetAddressByPostcodeQueryHookResult = ReturnType<
  typeof useGetAddressByPostcodeQuery
>;
export type GetAddressByPostcodeLazyQueryHookResult = ReturnType<
  typeof useGetAddressByPostcodeLazyQuery
>;
export type GetAddressByPostcodeSuspenseQueryHookResult = ReturnType<
  typeof useGetAddressByPostcodeSuspenseQuery
>;
export type GetAddressByPostcodeQueryResult = Apollo.QueryResult<
  GetAddressByPostcodeQuery,
  GetAddressByPostcodeQueryVariables
>;
