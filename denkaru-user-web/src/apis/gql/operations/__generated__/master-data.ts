import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiMstItemGetTenMstListByItemTypeQueryVariables = Types.Exact<{
  itemType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiMstItemGetTenMstListByItemTypeQuery = {
  __typename?: "query_root";
  getApiMstItemGetTenMstListByItemType?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetTenMstListByItemTypeResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetTenMstListByItemTypeResponse";
      tenMsts?: Array<{
        __typename?: "DomainModelsMstItemTenMstMaintenanceModel";
        itemCd?: string;
        name?: string;
        itemType?: string;
        masterSbt?: string;
        kanaName1?: string;
        kanaName2?: string;
        receName?: string;
        hpId?: number;
        startDate?: number;
        endDate?: number;
        santeiItemCd?: string;
        ten?: number;
        odrUnitName?: string;
        receUnitName?: string;
        sansoKbn?: number;
        fukuyoRise?: number;
        fukuyoMorning?: number;
        fukuyoDaytime?: number;
        fukuyoNight?: number;
        fukuyoSleep?: number;
        sinKouiKbn?: number;
        yohoKbn?: number;
        kazeiKbn?: number;
        jihiSbt?: number;
      }>;
    };
  };
};

export type GetApiMstItemGetTenMstOriginInfoCreateQueryVariables = Types.Exact<{
  type?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiMstItemGetTenMstOriginInfoCreateQuery = {
  __typename?: "query_root";
  getApiMstItemGetTenMstOriginInfoCreate?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetTenMstOriginInfoCreateResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetTenMstOriginInfoCreateResponse";
      itemCd?: string;
      jihiSbt?: number;
      tenMstOriginModel?: {
        __typename?: "DomainModelsMstItemTenMstOriginModel";
        itemCd?: string;
        startDate?: number;
        endDate?: number;
        masterSbt?: string;
        sinKouiKbn?: number;
        name?: string;
        kanaName1?: string;
        kanaName2?: string;
        kanaName3?: string;
        kanaName4?: string;
        kanaName5?: string;
        kanaName6?: string;
        kanaName7?: string;
        ryosyuName?: string;
        receName?: string;
        tenId?: number;
        ten?: number;
        receUnitCd?: string;
        receUnitName?: string;
        odrUnitName?: string;
        cnvUnitName?: string;
        odrTermVal?: number;
        cnvTermVal?: number;
        defaultVal?: number;
        isAdopted?: number;
        koukiKbn?: number;
        hokatuKensa?: number;
        byomeiKbn?: number;
        igakukanri?: number;
        jitudayCount?: number;
        jituday?: number;
        dayCount?: number;
        drugKanrenKbn?: number;
        kizamiId?: number;
        kizamiMin?: number;
        kizamiMax?: number;
        kizamiVal?: number;
        kizamiTen?: number;
        kizamiErr?: number;
        maxCount?: number;
        maxCountErr?: number;
        tyuCd?: string;
        tyuSeq?: string;
        tusokuAge?: number;
        minAge?: string;
        maxAge?: string;
        ageCheck?: number;
        timeKasanKbn?: number;
        futekiKbn?: number;
        futekiSisetuKbn?: number;
        syotiNyuyojiKbn?: number;
        lowWeightKbn?: number;
        handanKbn?: number;
        handanGrpKbn?: number;
        teigenKbn?: number;
        sekituiKbn?: number;
        keibuKbn?: number;
        autoHougouKbn?: number;
        gairaiKanriKbn?: number;
        tusokuTargetKbn?: number;
        hokatuKbn?: number;
        tyoonpaNaisiKbn?: number;
        autoFungoKbn?: number;
        tyoonpaGyokoKbn?: number;
        gazoKasan?: number;
        kansatuKbn?: number;
        masuiKbn?: number;
        fukubikuNaisiKasan?: number;
        fukubikuKotunanKasan?: number;
        masuiKasan?: number;
        moniterKasan?: number;
        toketuKasan?: number;
        tenKbnNo?: string;
        shortstayOpe?: number;
        buiKbn?: number;
        sisetucd1?: number;
        sisetucd2?: number;
        sisetucd3?: number;
        sisetucd4?: number;
        sisetucd5?: number;
        sisetucd6?: number;
        sisetucd7?: number;
        sisetucd8?: number;
        sisetucd9?: number;
        sisetucd10?: number;
        agekasanMin1?: string;
        agekasanMax1?: string;
        agekasanCd1?: string;
        agekasanCd1Note?: string;
        agekasanMin2?: string;
        agekasanMax2?: string;
        agekasanCd2?: string;
        agekasanCd2Note?: string;
        agekasanMin3?: string;
        agekasanMax3?: string;
        agekasanCd3?: string;
        agekasanCd3Note?: string;
        agekasanMin4?: string;
        agekasanMax4?: string;
        agekasanCd4?: string;
        agekasanCd4Note?: string;
        kensaCmt?: number;
        madokuKbn?: number;
        sinkeiKbn?: number;
        seibutuKbn?: number;
        zoueiKbn?: number;
        drugKbn?: number;
        zaiKbn?: number;
        zaikeiPoint?: number;
        capacity?: number;
        kohatuKbn?: number;
        tokuzaiAgeKbn?: number;
        sansoKbn?: number;
        tokuzaiSbt?: number;
        maxPrice?: number;
        maxTen?: number;
        syukeiSaki?: string;
        cdKbn?: string;
        cdSyo?: number;
        cdBu?: number;
        cdKbnno?: number;
        cdEdano?: number;
        cdKouno?: number;
        kokujiKbn?: string;
        kokujiSyo?: number;
        kokujiBu?: number;
        kokujiKbnNo?: number;
        kokujiEdaNo?: number;
        kokujiKouNo?: number;
        kokuji1?: string;
        kokuji2?: string;
        kohyoJun?: number;
        yjCd?: string;
        yakkaCd?: string;
        syusaiSbt?: number;
        syohinKanren?: string;
        updDate?: number;
        delDate?: number;
        keikaDate?: number;
        rousaiKbn?: number;
        sisiKbn?: number;
        shotCnt?: number;
        isNosearch?: number;
        isNodspPaperRece?: number;
        isNodspRece?: number;
        isNodspRyosyu?: number;
        isNodspKarte?: number;
        isNodspYakutai?: number;
        jihiSbt?: number;
        kazeiKbn?: number;
        yohoKbn?: number;
        ipnNameCd?: string;
        fukuyoRise?: number;
        fukuyoMorning?: number;
        fukuyoDaytime?: number;
        fukuyoNight?: number;
        fukuyoSleep?: number;
        suryoRoundupKbn?: number;
        kouseisinKbn?: number;
        chusyaDrugSbt?: number;
        kensaFukusuSantei?: number;
        santeiItemCd?: string;
        santeigaiKbn?: number;
        kensaItemCd?: string;
        kensaItemSeqNo?: number;
        renkeiCd1?: string;
        renkeiCd2?: string;
        saiketuKbn?: number;
        cmtKbn?: number;
        cmtCol1?: number;
        cmtColKeta1?: number;
        cmtCol2?: number;
        cmtColKeta2?: number;
        cmtCol3?: number;
        cmtColKeta3?: number;
        cmtCol4?: number;
        cmtColKeta4?: number;
        selectCmtId?: number;
        kensaLabel?: number;
        isUpdated?: boolean;
        isAddNew?: boolean;
        isDeleted?: number;
        isUsage?: boolean;
        isNotUsage?: boolean;
        isLastItem?: boolean;
        isStartDateKeyUpdated?: boolean;
        originStartDate?: number;
        isSelected?: boolean;
        hpId?: number;
        createId?: number;
      };
    };
  };
};

export type PostApiMstItemSaveSetDataTenMstMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsMstItemSaveSetDataTenMstRequestInput>;
}>;

export type PostApiMstItemSaveSetDataTenMstMutation = {
  __typename?: "mutation_root";
  postApiMstItemSaveSetDataTenMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSaveSetDataTenMstResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSaveSetDataTenMstResponse";
      status?: number;
    };
  };
};

export type GetApiMstItemGetListTenMstOriginQueryVariables = Types.Exact<{
  itemCd: Types.Scalars["String"]["input"];
}>;

export type GetApiMstItemGetListTenMstOriginQuery = {
  __typename?: "query_root";
  getApiMstItemGetListTenMstOrigin?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetListTenMstOriginResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetListTenMstOriginResponse";
      startDateDisplay?: Array<number>;
      tenMsts?: Array<{
        __typename?: "EmrCloudApiRequestsMstItemTenMstOriginModelDto";
        ageCheck?: number;
        agekasanCd1?: string;
        agekasanCd1Note?: string;
        agekasanCd2?: string;
        agekasanCd2Note?: string;
        agekasanCd3?: string;
        agekasanCd3Note?: string;
        agekasanCd4?: string;
        agekasanCd4Note?: string;
        agekasanMax1?: string;
        agekasanMax2?: string;
        agekasanMax3?: string;
        agekasanMax4?: string;
        agekasanMin1?: string;
        agekasanMin2?: string;
        agekasanMin3?: string;
        agekasanMin4?: string;
        autoFungoKbn?: number;
        autoHougouKbn?: number;
        buiKbn?: number;
        byomeiKbn?: number;
        capacity?: number;
        cdBu?: number;
        cdEdano?: number;
        cdKbn?: string;
        cdKbnno?: number;
        cdKouno?: number;
        cdSyo?: number;
        chusyaDrugSbt?: number;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
        cmtKbn?: number;
        cnvTermVal?: number;
        cnvUnitName?: string;
        dayCount?: number;
        defaultVal?: number;
        delDate?: number;
        drugKanrenKbn?: number;
        drugKbn?: number;
        endDate?: number;
        fukubikuKotunanKasan?: number;
        fukubikuNaisiKasan?: number;
        fukuyoDaytime?: number;
        fukuyoMorning?: number;
        fukuyoNight?: number;
        fukuyoRise?: number;
        fukuyoSleep?: number;
        futekiKbn?: number;
        futekiSisetuKbn?: number;
        gairaiKanriKbn?: number;
        gazoKasan?: number;
        handanGrpKbn?: number;
        handanKbn?: number;
        hokatuKbn?: number;
        hokatuKensa?: number;
        igakukanri?: number;
        ipnNameCd?: string;
        isAddNew?: boolean;
        isAdopted?: number;
        isDeleted?: number;
        isLastItem?: boolean;
        isNodspKarte?: number;
        isNodspPaperRece?: number;
        isNodspRece?: number;
        isNodspRyosyu?: number;
        isNodspYakutai?: number;
        isNosearch?: number;
        isNotUsage?: boolean;
        isSelected?: boolean;
        isStartDateKeyUpdated?: boolean;
        isUpdated?: boolean;
        isUsage?: boolean;
        itemCd?: string;
        jihiSbt?: number;
        jituday?: number;
        jitudayCount?: number;
        kanaName1?: string;
        kanaName2?: string;
        kanaName3?: string;
        kanaName4?: string;
        kanaName5?: string;
        kanaName6?: string;
        kanaName7?: string;
        kansatuKbn?: number;
        kazeiKbn?: number;
        keibuKbn?: number;
        keikaDate?: number;
        kensaCmt?: number;
        kensaFukusuSantei?: number;
        kensaItemCd?: string;
        kensaItemSeqNo?: number;
        kensaLabel?: number;
        kizamiErr?: number;
        kizamiId?: number;
        kizamiMax?: number;
        kizamiMin?: number;
        kizamiTen?: number;
        kizamiVal?: number;
        kohatuKbn?: number;
        kohyoJun?: number;
        kokuji1?: string;
        kokuji2?: string;
        kokujiBu?: number;
        kokujiEdaNo?: number;
        kokujiKbn?: string;
        kokujiKbnNo?: number;
        kokujiKouNo?: number;
        kokujiSyo?: number;
        koukiKbn?: number;
        kouseisinKbn?: number;
        lowWeightKbn?: number;
        madokuKbn?: number;
        masterSbt?: string;
        masuiKasan?: number;
        masuiKbn?: number;
        maxAge?: string;
        maxCount?: number;
        maxCountErr?: number;
        maxPrice?: number;
        maxTen?: number;
        minAge?: string;
        moniterKasan?: number;
        name?: string;
        odrTermVal?: number;
        odrUnitName?: string;
        originStartDate?: number;
        receName?: string;
        receUnitCd?: string;
        receUnitName?: string;
        renkeiCd1?: string;
        renkeiCd2?: string;
        rousaiKbn?: number;
        ryosyuName?: string;
        saiketuKbn?: number;
        sansoKbn?: number;
        santeiItemCd?: string;
        santeigaiKbn?: number;
        seibutuKbn?: number;
        sekituiKbn?: number;
        selectCmtId?: number;
        shortstayOpe?: number;
        shotCnt?: number;
        sinKouiKbn?: number;
        sinkeiKbn?: number;
        sisetucd1?: number;
        sisetucd2?: number;
        sisetucd3?: number;
        sisetucd4?: number;
        sisetucd5?: number;
        sisetucd6?: number;
        sisetucd7?: number;
        sisetucd8?: number;
        sisetucd9?: number;
        sisetucd10?: number;
        sisiKbn?: number;
        startDate?: number;
        suryoRoundupKbn?: number;
        syohinKanren?: string;
        syotiNyuyojiKbn?: number;
        syukeiSaki?: string;
        syusaiSbt?: number;
        teigenKbn?: number;
        ten?: number;
        tenId?: number;
        tenKbnNo?: string;
        timeKasanKbn?: number;
        toketuKasan?: number;
        tokuzaiAgeKbn?: number;
        tokuzaiSbt?: number;
        tusokuAge?: number;
        tusokuTargetKbn?: number;
        tyoonpaGyokoKbn?: number;
        tyoonpaNaisiKbn?: number;
        tyuCd?: string;
        tyuSeq?: string;
        updDate?: number;
        yakkaCd?: string;
        yjCd?: string;
        yohoHosokuKbn?: number;
        yohoHosokuRec?: number;
        yohoKbn?: number;
        zaiKbn?: number;
        zaikeiPoint?: number;
        zoueiKbn?: number;
      }>;
    };
  };
};

export type GetApiMstItemGetJihiMstListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiMstItemGetJihiMstListQuery = {
  __typename?: "query_root";
  getApiMstItemGetJihiMstList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetJihiMstsResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetJihiMstsResponse";
      listData?: Array<{
        __typename?: "DomainModelsMstItemJihiSbtMstModel";
        hpId?: number;
        isDeleted?: number;
        isYobo?: number;
        jihiSbt?: number;
        name?: string;
        sortNo?: number;
        status?: number;
      }>;
    };
  };
};

export type GetApiMstItemGetSetDataTenMstQueryVariables = Types.Exact<{
  agekasanCd1Note?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  agekasanCd2Note?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  agekasanCd3Note?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  agekasanCd4Note?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  ipnNameCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  jiCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  santeiItemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiMstItemGetSetDataTenMstQuery = {
  __typename?: "query_root";
  getApiMstItemGetSetDataTenMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetSetDataTenMstResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetSetDataTenMstResponse";
      setData?: {
        __typename?: "DomainModelsMstItemSetDataTenMstOriginModel";
        basicSettingTab?: {
          __typename?: "DomainModelsMstItemBasicSettingTabModel";
          cmtKbnMstModels?: Array<{
            __typename?: "DomainModelsMstItemCmtKbnMstModel";
            cmtKbn?: number;
            endDate?: number;
            endDateBinding?: string;
            id?: string;
            isDeleted?: boolean;
            itemCd?: string;
            startDate?: number;
            startDateBinding?: string;
          }>;
        };
        ijiSettingTab?: {
          __typename?: "DomainModelsMstItemIjiSettingTabModel";
          searchItemName?: string;
          agekasanCd1Note?: string;
          agekasanCd2Note?: string;
          agekasanCd3Note?: string;
          agekasanCd4Note?: string;
        };
        precriptionSettingTab?: {
          __typename?: "DomainModelsMstItemPrecriptionSettingTabModel";
          m10DayLimits?: Array<{
            __typename?: "DomainModelsMstItemM10DayLimitModel";
            cmt?: string;
            edDate?: string;
            endDateBinding?: string;
            limitDay?: number;
            limitDayBinding?: string;
            seqNo?: number;
            stDate?: string;
            startDateBinding?: string;
            yjCd?: string;
          }>;
          ipnMinYakkaMsts?: Array<{
            __typename?: "DomainModelsOrdInfIpnMinYakkaMstModel";
            endDate?: number;
            hpId?: number;
            id?: number;
            ipnNameCd?: string;
            isDeleted?: number;
            modelModified?: boolean;
            seqNo?: number;
            startDate?: number;
            yakka?: number;
          }>;
          drugDayLimits?: Array<{
            __typename?: "DomainModelsMstItemDrugDayLimitModel";
            endDate?: number;
            hpId?: number;
            id?: number;
            isDeleted?: number;
            itemCd?: string;
            limitDay?: number;
            modelModified?: boolean;
            seqNo?: number;
            startDate?: number;
          }>;
          dosageMst?: {
            __typename?: "DomainModelsMstItemDosageMstModel";
            dayLimit?: number;
            dayMax?: number;
            dayMin?: number;
            dayUnit?: number;
            hpId?: number;
            id?: number;
            isDeleted?: number;
            itemCd?: string;
            modelModified?: boolean;
            onceLimit?: number;
            onceMax?: number;
            onceMin?: number;
            onceUnit?: number;
            seqNo?: number;
          };
          ipnNameMst?: {
            __typename?: "DomainModelsMstItemIpnNameMstModel";
            endDate?: number;
            hpId?: number;
            ipnName?: string;
            ipnNameCd?: string;
            ipnNameCdOrigin?: string;
            isDeleted?: number;
            modelModified?: boolean;
            seqNo?: number;
            startDate?: number;
          };
        };
        usageSettingTab?: {
          __typename?: "DomainModelsMstItemUsageSettingTabModel";
          yohoInfMstPrefix?: string;
        };
        drugInfomationTab?: {
          __typename?: "DomainModelsMstItemDrugInfomationTabModel";
          drugInfs?: Array<{
            __typename?: "DomainModelsMstItemDrugInfModel";
            drugInfo?: string;
            hpId?: number;
            infKbn?: number;
            isDeleted?: number;
            isModified?: boolean;
            itemCd?: string;
            oldDrugInfo?: string;
            seqNo?: string;
          }>;
          zaiImage?: {
            __typename?: "DomainModelsMstItemPiImageModel";
            fileName?: string;
            hpId?: number;
            imageType?: number;
            isDeleted?: boolean;
            isModified?: boolean;
            itemCd?: string;
          };
          houImage?: {
            __typename?: "DomainModelsMstItemPiImageModel";
            fileName?: string;
            hpId?: number;
            imageType?: number;
            isDeleted?: boolean;
            isModified?: boolean;
            itemCd?: string;
          };
        };
        teikyoByomeiTab?: {
          __typename?: "DomainModelsMstItemTeikyoByomeiTabModel";
          teikyoByomeis?: Array<{
            __typename?: "DomainModelsMstItemTeikyoByomeiModel";
            byomei?: string;
            byomeiCd?: string;
            editKbn?: number;
            endYM?: number;
            endYMDisplay?: string;
            hpId?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isInvalidTokusyo?: number;
            isModified?: boolean;
            itemCd?: string;
            kanaName?: string;
            sbyomeiOrigin?: string;
            sikkanCd?: number;
            startYM?: number;
            startYMDisplay?: string;
            systemData?: number;
          }>;
          tekiouByomeiMstExcluded?: {
            __typename?: "DomainModelsMstItemTekiouByomeiMstExcludedModel";
            hpId?: number;
            isDeleted?: number;
            itemCd?: string;
            seqNo?: number;
          };
        };
        santeiKaishuTab?: {
          __typename?: "DomainModelsMstItemSanteiKaishuTabModel";
          densiSanteiKaisus?: Array<{
            __typename?: "DomainModelsTodayOdrDensiSanteiKaisuModel";
            endDate?: number;
            hpId?: number;
            id?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isModified?: boolean;
            itemCd?: string;
            itemGrpCd?: string;
            maxCount?: number;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            targetKbn?: number;
            termCount?: number;
            termSbt?: number;
            unitCd?: number;
            userSetting?: number;
          }>;
        };
        haihanTab?: {
          __typename?: "DomainModelsMstItemHaihanTabModel";
          densiHaihanModel1s?: Array<{
            __typename?: "DomainModelsMstItemDensiHaihanModel";
            endDate?: number;
            haihanKbn?: number;
            hpId?: number;
            id?: number;
            initModelType?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isModified?: boolean;
            isReadOnly?: boolean;
            itemCd1?: string;
            itemCd2?: string;
            modelType?: number;
            name?: string;
            originItemCd2?: string;
            prevItemCd2?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            targetKbn?: number;
            termCnt?: number;
            termSbt?: number;
            userSetting?: number;
          }>;
          densiHaihanModel2s?: Array<{
            __typename?: "DomainModelsMstItemDensiHaihanModel";
            endDate?: number;
            haihanKbn?: number;
            hpId?: number;
            id?: number;
            initModelType?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isModified?: boolean;
            isReadOnly?: boolean;
            itemCd1?: string;
            itemCd2?: string;
            modelType?: number;
            name?: string;
            originItemCd2?: string;
            prevItemCd2?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            targetKbn?: number;
            termCnt?: number;
            termSbt?: number;
            userSetting?: number;
          }>;
          densiHaihanModel3s?: Array<{
            __typename?: "DomainModelsMstItemDensiHaihanModel";
            endDate?: number;
            haihanKbn?: number;
            hpId?: number;
            id?: number;
            initModelType?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isModified?: boolean;
            isReadOnly?: boolean;
            itemCd1?: string;
            itemCd2?: string;
            modelType?: number;
            name?: string;
            originItemCd2?: string;
            prevItemCd2?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            targetKbn?: number;
            termCnt?: number;
            termSbt?: number;
            userSetting?: number;
          }>;
        };
        houkatsuTab?: {
          __typename?: "DomainModelsMstItemHoukatsuTabModel";
          listDensiHoukatuModels?: Array<{
            __typename?: "DomainModelsMstItemDensiHoukatuModel";
            endDate?: number;
            houkatuGrpItemCd?: string;
            houkatuGrpNo?: string;
            houkatuTerm?: number;
            hpId?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isInvalidBinding?: boolean;
            isModified?: boolean;
            itemCd?: string;
            name?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            targetKbn?: number;
            userSetting?: number;
          }>;
          listDensiHoukatuGrpModels?: Array<{
            __typename?: "DomainModelsMstItemDensiHoukatuGrpModel";
            endDate?: number;
            houkatuGrpNo?: string;
            houkatuItemCd?: string;
            houkatuTerm?: number;
            hpId?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isUpdate?: boolean;
            itemCd?: string;
            name?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            targetKbn?: number;
            userSetting?: number;
          }>;
          listDensiHoukatuMaster?: Array<{
            __typename?: "DomainModelsMstItemDensiHoukatuModel";
            endDate?: number;
            houkatuGrpItemCd?: string;
            houkatuGrpNo?: string;
            houkatuTerm?: number;
            hpId?: number;
            isDeleted?: boolean;
            isInvalid?: number;
            isInvalidBinding?: boolean;
            isModified?: boolean;
            itemCd?: string;
            name?: string;
            seqNo?: string;
            spJyoken?: number;
            startDate?: number;
            targetKbn?: number;
            userSetting?: number;
          }>;
        };
        combinedContraindicationTab?: {
          __typename?: "DomainModelsMstItemCombinedContraindicationTabModel";
          combinedContraindications?: Array<{
            __typename?: "DomainModelsMstItemCombinedContraindicationModel";
            aCd?: string;
            bCd?: string;
            hpId?: number;
            id?: string;
            isAddNew?: boolean;
            isDeleted?: boolean;
            isUpdated?: boolean;
            name?: string;
            originBCd?: string;
            seqNo?: number;
          }>;
        };
      };
    };
  };
};

export type GetApiMstItemCheckIsTenMstUsedQueryVariables = Types.Exact<{
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  startDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  endDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiMstItemCheckIsTenMstUsedQuery = {
  __typename?: "query_root";
  getApiMstItemCheckIsTenMstUsed?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemCheckIsTenMstUsedResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemCheckIsTenMstUsedResponse";
      status?: number;
    };
  };
};

export type PostApiMstItemDeleteOrRecoverTenMstMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsMstItemDeleteOrRecoverTenMstRequestInput>;
}>;

export type PostApiMstItemDeleteOrRecoverTenMstMutation = {
  __typename?: "mutation_root";
  postApiMstItemDeleteOrRecoverTenMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemDeleteOrRecoverTenMstResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemDeleteOrRecoverTenMstResponse";
      status?: number;
    };
  };
};

export type GetApiMstItemGetTeikyoByomeiQueryVariables = Types.Exact<{
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiMstItemGetTeikyoByomeiQuery = {
  __typename?: "query_root";
  getApiMstItemGetTeikyoByomei?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetTeikyoByomeiResponse";
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetTeikyoByomeiResponse";
      teikyoByomeis?: Array<{
        __typename?: "DomainModelsMstItemTeikyoByomeiModel";
        byomei?: string;
        byomeiCd?: string;
        editKbn?: number;
        endYM?: number;
        isDeleted?: boolean;
        isInvalid?: number;
        isInvalidTokusyo?: number;
        isModified?: boolean;
        itemCd?: string;
        kanaName?: string;
        sikkanCd?: number;
        startYM?: number;
        systemData?: number;
        hpId?: number;
      }>;
    };
  };
};

export const GetApiMstItemGetTenMstListByItemTypeDocument = gql`
  query getApiMstItemGetTenMstListByItemType($itemType: Int, $sinDate: Int) {
    getApiMstItemGetTenMstListByItemType(
      itemType: $itemType
      sinDate: $sinDate
    ) {
      data {
        tenMsts {
          itemCd
          name
          itemType
          masterSbt
          kanaName1
          kanaName2
          receName
          hpId
          startDate
          endDate
          santeiItemCd
          ten
          odrUnitName
          receUnitName
          sansoKbn
          fukuyoRise
          fukuyoMorning
          fukuyoDaytime
          fukuyoNight
          fukuyoSleep
          sinKouiKbn
          yohoKbn
          kazeiKbn
          jihiSbt
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiMstItemGetTenMstListByItemTypeQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemGetTenMstListByItemTypeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemGetTenMstListByItemTypeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemGetTenMstListByItemTypeQuery({
 *   variables: {
 *      itemType: // value for 'itemType'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiMstItemGetTenMstListByItemTypeQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemGetTenMstListByItemTypeQuery,
    GetApiMstItemGetTenMstListByItemTypeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemGetTenMstListByItemTypeQuery,
    GetApiMstItemGetTenMstListByItemTypeQueryVariables
  >(GetApiMstItemGetTenMstListByItemTypeDocument, options);
}
export function useGetApiMstItemGetTenMstListByItemTypeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemGetTenMstListByItemTypeQuery,
    GetApiMstItemGetTenMstListByItemTypeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemGetTenMstListByItemTypeQuery,
    GetApiMstItemGetTenMstListByItemTypeQueryVariables
  >(GetApiMstItemGetTenMstListByItemTypeDocument, options);
}
export function useGetApiMstItemGetTenMstListByItemTypeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemGetTenMstListByItemTypeQuery,
    GetApiMstItemGetTenMstListByItemTypeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemGetTenMstListByItemTypeQuery,
    GetApiMstItemGetTenMstListByItemTypeQueryVariables
  >(GetApiMstItemGetTenMstListByItemTypeDocument, options);
}
export type GetApiMstItemGetTenMstListByItemTypeQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetTenMstListByItemTypeQuery
>;
export type GetApiMstItemGetTenMstListByItemTypeLazyQueryHookResult =
  ReturnType<typeof useGetApiMstItemGetTenMstListByItemTypeLazyQuery>;
export type GetApiMstItemGetTenMstListByItemTypeSuspenseQueryHookResult =
  ReturnType<typeof useGetApiMstItemGetTenMstListByItemTypeSuspenseQuery>;
export type GetApiMstItemGetTenMstListByItemTypeQueryResult =
  Apollo.QueryResult<
    GetApiMstItemGetTenMstListByItemTypeQuery,
    GetApiMstItemGetTenMstListByItemTypeQueryVariables
  >;
export const GetApiMstItemGetTenMstOriginInfoCreateDocument = gql`
  query getApiMstItemGetTenMstOriginInfoCreate($type: Int, $itemCd: String) {
    getApiMstItemGetTenMstOriginInfoCreate(type: $type, itemCd: $itemCd) {
      data {
        itemCd
        jihiSbt
        tenMstOriginModel {
          itemCd
          startDate
          endDate
          masterSbt
          sinKouiKbn
          name
          kanaName1
          kanaName2
          kanaName3
          kanaName4
          kanaName5
          kanaName6
          kanaName7
          ryosyuName
          receName
          tenId
          ten
          receUnitCd
          receUnitName
          odrUnitName
          cnvUnitName
          odrTermVal
          cnvTermVal
          defaultVal
          isAdopted
          koukiKbn
          hokatuKensa
          byomeiKbn
          igakukanri
          jitudayCount
          jituday
          dayCount
          drugKanrenKbn
          kizamiId
          kizamiMin
          kizamiMax
          kizamiVal
          kizamiTen
          kizamiErr
          maxCount
          maxCountErr
          tyuCd
          tyuSeq
          tusokuAge
          minAge
          maxAge
          ageCheck
          timeKasanKbn
          futekiKbn
          futekiSisetuKbn
          syotiNyuyojiKbn
          lowWeightKbn
          handanKbn
          handanGrpKbn
          teigenKbn
          sekituiKbn
          keibuKbn
          autoHougouKbn
          gairaiKanriKbn
          tusokuTargetKbn
          hokatuKbn
          tyoonpaNaisiKbn
          autoFungoKbn
          tyoonpaGyokoKbn
          gazoKasan
          kansatuKbn
          masuiKbn
          fukubikuNaisiKasan
          fukubikuKotunanKasan
          masuiKasan
          moniterKasan
          toketuKasan
          tenKbnNo
          shortstayOpe
          buiKbn
          sisetucd1
          sisetucd2
          sisetucd3
          sisetucd4
          sisetucd5
          sisetucd6
          sisetucd7
          sisetucd8
          sisetucd9
          sisetucd10
          agekasanMin1
          agekasanMax1
          agekasanCd1
          agekasanCd1Note
          agekasanMin2
          agekasanMax2
          agekasanCd2
          agekasanCd2Note
          agekasanMin3
          agekasanMax3
          agekasanCd3
          agekasanCd3Note
          agekasanMin4
          agekasanMax4
          agekasanCd4
          agekasanCd4Note
          kensaCmt
          madokuKbn
          sinkeiKbn
          seibutuKbn
          zoueiKbn
          drugKbn
          zaiKbn
          zaikeiPoint
          capacity
          kohatuKbn
          tokuzaiAgeKbn
          sansoKbn
          tokuzaiSbt
          maxPrice
          maxTen
          syukeiSaki
          cdKbn
          cdSyo
          cdBu
          cdKbnno
          cdEdano
          cdKouno
          kokujiKbn
          kokujiSyo
          kokujiBu
          kokujiKbnNo
          kokujiEdaNo
          kokujiKouNo
          kokuji1
          kokuji2
          kohyoJun
          yjCd
          yakkaCd
          syusaiSbt
          syohinKanren
          updDate
          delDate
          keikaDate
          rousaiKbn
          sisiKbn
          shotCnt
          isNosearch
          isNodspPaperRece
          isNodspRece
          isNodspRyosyu
          isNodspKarte
          isNodspYakutai
          jihiSbt
          kazeiKbn
          yohoKbn
          ipnNameCd
          fukuyoRise
          fukuyoMorning
          fukuyoDaytime
          fukuyoNight
          fukuyoSleep
          suryoRoundupKbn
          kouseisinKbn
          chusyaDrugSbt
          kensaFukusuSantei
          santeiItemCd
          santeigaiKbn
          kensaItemCd
          kensaItemSeqNo
          renkeiCd1
          renkeiCd2
          saiketuKbn
          cmtKbn
          cmtCol1
          cmtColKeta1
          cmtCol2
          cmtColKeta2
          cmtCol3
          cmtColKeta3
          cmtCol4
          cmtColKeta4
          selectCmtId
          kensaLabel
          isUpdated
          isAddNew
          isDeleted
          isUsage
          isNotUsage
          isLastItem
          isStartDateKeyUpdated
          originStartDate
          isSelected
          hpId
          createId
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiMstItemGetTenMstOriginInfoCreateQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemGetTenMstOriginInfoCreateQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemGetTenMstOriginInfoCreateQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemGetTenMstOriginInfoCreateQuery({
 *   variables: {
 *      type: // value for 'type'
 *      itemCd: // value for 'itemCd'
 *   },
 * });
 */
export function useGetApiMstItemGetTenMstOriginInfoCreateQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemGetTenMstOriginInfoCreateQuery,
    GetApiMstItemGetTenMstOriginInfoCreateQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemGetTenMstOriginInfoCreateQuery,
    GetApiMstItemGetTenMstOriginInfoCreateQueryVariables
  >(GetApiMstItemGetTenMstOriginInfoCreateDocument, options);
}
export function useGetApiMstItemGetTenMstOriginInfoCreateLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemGetTenMstOriginInfoCreateQuery,
    GetApiMstItemGetTenMstOriginInfoCreateQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemGetTenMstOriginInfoCreateQuery,
    GetApiMstItemGetTenMstOriginInfoCreateQueryVariables
  >(GetApiMstItemGetTenMstOriginInfoCreateDocument, options);
}
export function useGetApiMstItemGetTenMstOriginInfoCreateSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemGetTenMstOriginInfoCreateQuery,
    GetApiMstItemGetTenMstOriginInfoCreateQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemGetTenMstOriginInfoCreateQuery,
    GetApiMstItemGetTenMstOriginInfoCreateQueryVariables
  >(GetApiMstItemGetTenMstOriginInfoCreateDocument, options);
}
export type GetApiMstItemGetTenMstOriginInfoCreateQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetTenMstOriginInfoCreateQuery
>;
export type GetApiMstItemGetTenMstOriginInfoCreateLazyQueryHookResult =
  ReturnType<typeof useGetApiMstItemGetTenMstOriginInfoCreateLazyQuery>;
export type GetApiMstItemGetTenMstOriginInfoCreateSuspenseQueryHookResult =
  ReturnType<typeof useGetApiMstItemGetTenMstOriginInfoCreateSuspenseQuery>;
export type GetApiMstItemGetTenMstOriginInfoCreateQueryResult =
  Apollo.QueryResult<
    GetApiMstItemGetTenMstOriginInfoCreateQuery,
    GetApiMstItemGetTenMstOriginInfoCreateQueryVariables
  >;
export const PostApiMstItemSaveSetDataTenMstDocument = gql`
  mutation postApiMstItemSaveSetDataTenMst(
    $input: EmrCloudApiRequestsMstItemSaveSetDataTenMstRequestInput
  ) {
    postApiMstItemSaveSetDataTenMst(
      emrCloudApiRequestsMstItemSaveSetDataTenMstRequestInput: $input
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiMstItemSaveSetDataTenMstMutationFn = Apollo.MutationFunction<
  PostApiMstItemSaveSetDataTenMstMutation,
  PostApiMstItemSaveSetDataTenMstMutationVariables
>;

/**
 * __usePostApiMstItemSaveSetDataTenMstMutation__
 *
 * To run a mutation, you first call `usePostApiMstItemSaveSetDataTenMstMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMstItemSaveSetDataTenMstMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMstItemSaveSetDataTenMstMutation, { data, loading, error }] = usePostApiMstItemSaveSetDataTenMstMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiMstItemSaveSetDataTenMstMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMstItemSaveSetDataTenMstMutation,
    PostApiMstItemSaveSetDataTenMstMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMstItemSaveSetDataTenMstMutation,
    PostApiMstItemSaveSetDataTenMstMutationVariables
  >(PostApiMstItemSaveSetDataTenMstDocument, options);
}
export type PostApiMstItemSaveSetDataTenMstMutationHookResult = ReturnType<
  typeof usePostApiMstItemSaveSetDataTenMstMutation
>;
export type PostApiMstItemSaveSetDataTenMstMutationResult =
  Apollo.MutationResult<PostApiMstItemSaveSetDataTenMstMutation>;
export type PostApiMstItemSaveSetDataTenMstMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMstItemSaveSetDataTenMstMutation,
    PostApiMstItemSaveSetDataTenMstMutationVariables
  >;
export const GetApiMstItemGetListTenMstOriginDocument = gql`
  query getApiMstItemGetListTenMstOrigin($itemCd: String!) {
    getApiMstItemGetListTenMstOrigin(itemCd: $itemCd) {
      data {
        startDateDisplay
        tenMsts {
          ageCheck
          agekasanCd1
          agekasanCd1Note
          agekasanCd2
          agekasanCd2Note
          agekasanCd3
          agekasanCd3Note
          agekasanCd4
          agekasanCd4Note
          agekasanMax1
          agekasanMax2
          agekasanMax3
          agekasanMax4
          agekasanMin1
          agekasanMin2
          agekasanMin3
          agekasanMin4
          autoFungoKbn
          autoHougouKbn
          buiKbn
          byomeiKbn
          capacity
          cdBu
          cdEdano
          cdKbn
          cdKbnno
          cdKouno
          cdSyo
          chusyaDrugSbt
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
          cmtKbn
          cnvTermVal
          cnvUnitName
          dayCount
          defaultVal
          delDate
          drugKanrenKbn
          drugKbn
          endDate
          fukubikuKotunanKasan
          fukubikuNaisiKasan
          fukuyoDaytime
          fukuyoMorning
          fukuyoNight
          fukuyoRise
          fukuyoSleep
          futekiKbn
          futekiSisetuKbn
          gairaiKanriKbn
          gazoKasan
          handanGrpKbn
          handanKbn
          hokatuKbn
          hokatuKensa
          igakukanri
          ipnNameCd
          isAddNew
          isAdopted
          isDeleted
          isLastItem
          isNodspKarte
          isNodspPaperRece
          isNodspRece
          isNodspRyosyu
          isNodspYakutai
          isNosearch
          isNotUsage
          isSelected
          isStartDateKeyUpdated
          isUpdated
          isUsage
          itemCd
          jihiSbt
          jituday
          jitudayCount
          kanaName1
          kanaName2
          kanaName3
          kanaName4
          kanaName5
          kanaName6
          kanaName7
          kansatuKbn
          kazeiKbn
          keibuKbn
          keikaDate
          kensaCmt
          kensaFukusuSantei
          kensaItemCd
          kensaItemSeqNo
          kensaLabel
          kizamiErr
          kizamiId
          kizamiMax
          kizamiMin
          kizamiTen
          kizamiVal
          kohatuKbn
          kohyoJun
          kokuji1
          kokuji2
          kokujiBu
          kokujiEdaNo
          kokujiKbn
          kokujiKbnNo
          kokujiKouNo
          kokujiSyo
          koukiKbn
          kouseisinKbn
          lowWeightKbn
          madokuKbn
          masterSbt
          masuiKasan
          masuiKbn
          maxAge
          maxCount
          maxCountErr
          maxPrice
          maxTen
          minAge
          moniterKasan
          name
          odrTermVal
          odrUnitName
          originStartDate
          receName
          receUnitCd
          receUnitName
          renkeiCd1
          renkeiCd2
          rousaiKbn
          ryosyuName
          saiketuKbn
          sansoKbn
          santeiItemCd
          santeigaiKbn
          seibutuKbn
          sekituiKbn
          selectCmtId
          shortstayOpe
          shotCnt
          sinKouiKbn
          sinkeiKbn
          sisetucd1
          sisetucd2
          sisetucd3
          sisetucd4
          sisetucd5
          sisetucd6
          sisetucd7
          sisetucd8
          sisetucd9
          sisetucd10
          sisiKbn
          startDate
          suryoRoundupKbn
          syohinKanren
          syotiNyuyojiKbn
          syukeiSaki
          syusaiSbt
          teigenKbn
          ten
          tenId
          tenKbnNo
          timeKasanKbn
          toketuKasan
          tokuzaiAgeKbn
          tokuzaiSbt
          tusokuAge
          tusokuTargetKbn
          tyoonpaGyokoKbn
          tyoonpaNaisiKbn
          tyuCd
          tyuSeq
          updDate
          yakkaCd
          yjCd
          yohoHosokuKbn
          yohoHosokuRec
          yohoKbn
          zaiKbn
          zaikeiPoint
          zoueiKbn
        }
      }
    }
  }
`;

/**
 * __useGetApiMstItemGetListTenMstOriginQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemGetListTenMstOriginQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemGetListTenMstOriginQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemGetListTenMstOriginQuery({
 *   variables: {
 *      itemCd: // value for 'itemCd'
 *   },
 * });
 */
export function useGetApiMstItemGetListTenMstOriginQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiMstItemGetListTenMstOriginQuery,
    GetApiMstItemGetListTenMstOriginQueryVariables
  > &
    (
      | {
          variables: GetApiMstItemGetListTenMstOriginQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemGetListTenMstOriginQuery,
    GetApiMstItemGetListTenMstOriginQueryVariables
  >(GetApiMstItemGetListTenMstOriginDocument, options);
}
export function useGetApiMstItemGetListTenMstOriginLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemGetListTenMstOriginQuery,
    GetApiMstItemGetListTenMstOriginQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemGetListTenMstOriginQuery,
    GetApiMstItemGetListTenMstOriginQueryVariables
  >(GetApiMstItemGetListTenMstOriginDocument, options);
}
export function useGetApiMstItemGetListTenMstOriginSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemGetListTenMstOriginQuery,
    GetApiMstItemGetListTenMstOriginQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemGetListTenMstOriginQuery,
    GetApiMstItemGetListTenMstOriginQueryVariables
  >(GetApiMstItemGetListTenMstOriginDocument, options);
}
export type GetApiMstItemGetListTenMstOriginQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetListTenMstOriginQuery
>;
export type GetApiMstItemGetListTenMstOriginLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetListTenMstOriginLazyQuery
>;
export type GetApiMstItemGetListTenMstOriginSuspenseQueryHookResult =
  ReturnType<typeof useGetApiMstItemGetListTenMstOriginSuspenseQuery>;
export type GetApiMstItemGetListTenMstOriginQueryResult = Apollo.QueryResult<
  GetApiMstItemGetListTenMstOriginQuery,
  GetApiMstItemGetListTenMstOriginQueryVariables
>;
export const GetApiMstItemGetJihiMstListDocument = gql`
  query getApiMstItemGetJihiMstList {
    getApiMstItemGetJihiMstList {
      data {
        listData {
          hpId
          isDeleted
          isYobo
          jihiSbt
          name
          sortNo
          status
        }
      }
      status
      message
    }
  }
`;

/**
 * __useGetApiMstItemGetJihiMstListQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemGetJihiMstListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemGetJihiMstListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemGetJihiMstListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiMstItemGetJihiMstListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemGetJihiMstListQuery,
    GetApiMstItemGetJihiMstListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemGetJihiMstListQuery,
    GetApiMstItemGetJihiMstListQueryVariables
  >(GetApiMstItemGetJihiMstListDocument, options);
}
export function useGetApiMstItemGetJihiMstListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemGetJihiMstListQuery,
    GetApiMstItemGetJihiMstListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemGetJihiMstListQuery,
    GetApiMstItemGetJihiMstListQueryVariables
  >(GetApiMstItemGetJihiMstListDocument, options);
}
export function useGetApiMstItemGetJihiMstListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemGetJihiMstListQuery,
    GetApiMstItemGetJihiMstListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemGetJihiMstListQuery,
    GetApiMstItemGetJihiMstListQueryVariables
  >(GetApiMstItemGetJihiMstListDocument, options);
}
export type GetApiMstItemGetJihiMstListQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetJihiMstListQuery
>;
export type GetApiMstItemGetJihiMstListLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetJihiMstListLazyQuery
>;
export type GetApiMstItemGetJihiMstListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetJihiMstListSuspenseQuery
>;
export type GetApiMstItemGetJihiMstListQueryResult = Apollo.QueryResult<
  GetApiMstItemGetJihiMstListQuery,
  GetApiMstItemGetJihiMstListQueryVariables
>;
export const GetApiMstItemGetSetDataTenMstDocument = gql`
  query getApiMstItemGetSetDataTenMst(
    $agekasanCd1Note: String
    $agekasanCd2Note: String
    $agekasanCd3Note: String
    $agekasanCd4Note: String
    $ipnNameCd: String
    $itemCd: String
    $jiCd: String
    $santeiItemCd: String
    $sinDate: Int
  ) {
    getApiMstItemGetSetDataTenMst(
      agekasanCd1Note: $agekasanCd1Note
      agekasanCd2Note: $agekasanCd2Note
      agekasanCd3Note: $agekasanCd3Note
      agekasanCd4Note: $agekasanCd4Note
      ipnNameCd: $ipnNameCd
      itemCd: $itemCd
      jiCd: $jiCd
      santeiItemCd: $santeiItemCd
      sinDate: $sinDate
    ) {
      message
      status
      data {
        setData {
          basicSettingTab {
            cmtKbnMstModels {
              cmtKbn
              endDate
              endDateBinding
              id
              isDeleted
              itemCd
              startDate
              startDateBinding
            }
          }
          ijiSettingTab {
            searchItemName
            agekasanCd1Note
            agekasanCd2Note
            agekasanCd3Note
            agekasanCd4Note
          }
          precriptionSettingTab {
            m10DayLimits {
              cmt
              edDate
              endDateBinding
              limitDay
              limitDayBinding
              seqNo
              stDate
              startDateBinding
              yjCd
            }
            ipnMinYakkaMsts {
              endDate
              hpId
              id
              ipnNameCd
              isDeleted
              modelModified
              seqNo
              startDate
              yakka
            }
            drugDayLimits {
              endDate
              hpId
              id
              isDeleted
              itemCd
              limitDay
              modelModified
              seqNo
              startDate
            }
            dosageMst {
              dayLimit
              dayMax
              dayMin
              dayUnit
              hpId
              id
              isDeleted
              itemCd
              modelModified
              onceLimit
              onceMax
              onceMin
              onceUnit
              seqNo
            }
            ipnNameMst {
              endDate
              hpId
              ipnName
              ipnNameCd
              ipnNameCdOrigin
              isDeleted
              modelModified
              seqNo
              startDate
            }
          }
          usageSettingTab {
            yohoInfMstPrefix
          }
          drugInfomationTab {
            drugInfs {
              drugInfo
              hpId
              infKbn
              isDeleted
              isModified
              itemCd
              oldDrugInfo
              seqNo
            }
            zaiImage {
              fileName
              hpId
              imageType
              isDeleted
              isModified
              itemCd
            }
            houImage {
              fileName
              hpId
              imageType
              isDeleted
              isModified
              itemCd
            }
          }
          teikyoByomeiTab {
            teikyoByomeis {
              byomei
              byomeiCd
              editKbn
              endYM
              endYMDisplay
              hpId
              isDeleted
              isInvalid
              isInvalidTokusyo
              isModified
              itemCd
              kanaName
              sbyomeiOrigin
              sikkanCd
              startYM
              startYMDisplay
              systemData
            }
            tekiouByomeiMstExcluded {
              hpId
              isDeleted
              itemCd
              seqNo
            }
          }
          santeiKaishuTab {
            densiSanteiKaisus {
              endDate
              hpId
              id
              isDeleted
              isInvalid
              isModified
              itemCd
              itemGrpCd
              maxCount
              seqNo
              spJyoken
              startDate
              targetKbn
              termCount
              termSbt
              unitCd
              userSetting
            }
          }
          haihanTab {
            densiHaihanModel1s {
              endDate
              haihanKbn
              hpId
              id
              initModelType
              isDeleted
              isInvalid
              isModified
              isReadOnly
              itemCd1
              itemCd2
              modelType
              name
              originItemCd2
              prevItemCd2
              seqNo
              spJyoken
              startDate
              targetKbn
              termCnt
              termSbt
              userSetting
            }
            densiHaihanModel2s {
              endDate
              haihanKbn
              hpId
              id
              initModelType
              isDeleted
              isInvalid
              isModified
              isReadOnly
              itemCd1
              itemCd2
              modelType
              name
              originItemCd2
              prevItemCd2
              seqNo
              spJyoken
              startDate
              targetKbn
              termCnt
              termSbt
              userSetting
            }
            densiHaihanModel3s {
              endDate
              haihanKbn
              hpId
              id
              initModelType
              isDeleted
              isInvalid
              isModified
              isReadOnly
              itemCd1
              itemCd2
              modelType
              name
              originItemCd2
              prevItemCd2
              seqNo
              spJyoken
              startDate
              targetKbn
              termCnt
              termSbt
              userSetting
            }
          }
          houkatsuTab {
            listDensiHoukatuModels {
              endDate
              houkatuGrpItemCd
              houkatuGrpNo
              houkatuTerm
              hpId
              isDeleted
              isInvalid
              isInvalidBinding
              isModified
              itemCd
              name
              seqNo
              spJyoken
              startDate
              targetKbn
              userSetting
            }
            listDensiHoukatuGrpModels {
              endDate
              houkatuGrpNo
              houkatuItemCd
              houkatuTerm
              hpId
              isDeleted
              isInvalid
              isUpdate
              itemCd
              name
              seqNo
              spJyoken
              startDate
              targetKbn
              userSetting
            }
            listDensiHoukatuMaster {
              endDate
              houkatuGrpItemCd
              houkatuGrpNo
              houkatuTerm
              hpId
              isDeleted
              isInvalid
              isInvalidBinding
              isModified
              itemCd
              name
              seqNo
              spJyoken
              startDate
              targetKbn
              userSetting
            }
          }
          combinedContraindicationTab {
            combinedContraindications {
              aCd
              bCd
              hpId
              id
              isAddNew
              isDeleted
              isUpdated
              name
              originBCd
              seqNo
            }
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiMstItemGetSetDataTenMstQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemGetSetDataTenMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemGetSetDataTenMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemGetSetDataTenMstQuery({
 *   variables: {
 *      agekasanCd1Note: // value for 'agekasanCd1Note'
 *      agekasanCd2Note: // value for 'agekasanCd2Note'
 *      agekasanCd3Note: // value for 'agekasanCd3Note'
 *      agekasanCd4Note: // value for 'agekasanCd4Note'
 *      ipnNameCd: // value for 'ipnNameCd'
 *      itemCd: // value for 'itemCd'
 *      jiCd: // value for 'jiCd'
 *      santeiItemCd: // value for 'santeiItemCd'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiMstItemGetSetDataTenMstQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemGetSetDataTenMstQuery,
    GetApiMstItemGetSetDataTenMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemGetSetDataTenMstQuery,
    GetApiMstItemGetSetDataTenMstQueryVariables
  >(GetApiMstItemGetSetDataTenMstDocument, options);
}
export function useGetApiMstItemGetSetDataTenMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemGetSetDataTenMstQuery,
    GetApiMstItemGetSetDataTenMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemGetSetDataTenMstQuery,
    GetApiMstItemGetSetDataTenMstQueryVariables
  >(GetApiMstItemGetSetDataTenMstDocument, options);
}
export function useGetApiMstItemGetSetDataTenMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemGetSetDataTenMstQuery,
    GetApiMstItemGetSetDataTenMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemGetSetDataTenMstQuery,
    GetApiMstItemGetSetDataTenMstQueryVariables
  >(GetApiMstItemGetSetDataTenMstDocument, options);
}
export type GetApiMstItemGetSetDataTenMstQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetSetDataTenMstQuery
>;
export type GetApiMstItemGetSetDataTenMstLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetSetDataTenMstLazyQuery
>;
export type GetApiMstItemGetSetDataTenMstSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetSetDataTenMstSuspenseQuery
>;
export type GetApiMstItemGetSetDataTenMstQueryResult = Apollo.QueryResult<
  GetApiMstItemGetSetDataTenMstQuery,
  GetApiMstItemGetSetDataTenMstQueryVariables
>;
export const GetApiMstItemCheckIsTenMstUsedDocument = gql`
  query getApiMstItemCheckIsTenMstUsed(
    $itemCd: String
    $startDate: Int
    $endDate: Int
  ) {
    getApiMstItemCheckIsTenMstUsed(
      itemCd: $itemCd
      startDate: $startDate
      endDate: $endDate
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiMstItemCheckIsTenMstUsedQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemCheckIsTenMstUsedQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemCheckIsTenMstUsedQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemCheckIsTenMstUsedQuery({
 *   variables: {
 *      itemCd: // value for 'itemCd'
 *      startDate: // value for 'startDate'
 *      endDate: // value for 'endDate'
 *   },
 * });
 */
export function useGetApiMstItemCheckIsTenMstUsedQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemCheckIsTenMstUsedQuery,
    GetApiMstItemCheckIsTenMstUsedQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemCheckIsTenMstUsedQuery,
    GetApiMstItemCheckIsTenMstUsedQueryVariables
  >(GetApiMstItemCheckIsTenMstUsedDocument, options);
}
export function useGetApiMstItemCheckIsTenMstUsedLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemCheckIsTenMstUsedQuery,
    GetApiMstItemCheckIsTenMstUsedQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemCheckIsTenMstUsedQuery,
    GetApiMstItemCheckIsTenMstUsedQueryVariables
  >(GetApiMstItemCheckIsTenMstUsedDocument, options);
}
export function useGetApiMstItemCheckIsTenMstUsedSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemCheckIsTenMstUsedQuery,
    GetApiMstItemCheckIsTenMstUsedQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemCheckIsTenMstUsedQuery,
    GetApiMstItemCheckIsTenMstUsedQueryVariables
  >(GetApiMstItemCheckIsTenMstUsedDocument, options);
}
export type GetApiMstItemCheckIsTenMstUsedQueryHookResult = ReturnType<
  typeof useGetApiMstItemCheckIsTenMstUsedQuery
>;
export type GetApiMstItemCheckIsTenMstUsedLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemCheckIsTenMstUsedLazyQuery
>;
export type GetApiMstItemCheckIsTenMstUsedSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemCheckIsTenMstUsedSuspenseQuery
>;
export type GetApiMstItemCheckIsTenMstUsedQueryResult = Apollo.QueryResult<
  GetApiMstItemCheckIsTenMstUsedQuery,
  GetApiMstItemCheckIsTenMstUsedQueryVariables
>;
export const PostApiMstItemDeleteOrRecoverTenMstDocument = gql`
  mutation postApiMstItemDeleteOrRecoverTenMst(
    $input: EmrCloudApiRequestsMstItemDeleteOrRecoverTenMstRequestInput
  ) {
    postApiMstItemDeleteOrRecoverTenMst(
      emrCloudApiRequestsMstItemDeleteOrRecoverTenMstRequestInput: $input
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiMstItemDeleteOrRecoverTenMstMutationFn =
  Apollo.MutationFunction<
    PostApiMstItemDeleteOrRecoverTenMstMutation,
    PostApiMstItemDeleteOrRecoverTenMstMutationVariables
  >;

/**
 * __usePostApiMstItemDeleteOrRecoverTenMstMutation__
 *
 * To run a mutation, you first call `usePostApiMstItemDeleteOrRecoverTenMstMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMstItemDeleteOrRecoverTenMstMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMstItemDeleteOrRecoverTenMstMutation, { data, loading, error }] = usePostApiMstItemDeleteOrRecoverTenMstMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiMstItemDeleteOrRecoverTenMstMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMstItemDeleteOrRecoverTenMstMutation,
    PostApiMstItemDeleteOrRecoverTenMstMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMstItemDeleteOrRecoverTenMstMutation,
    PostApiMstItemDeleteOrRecoverTenMstMutationVariables
  >(PostApiMstItemDeleteOrRecoverTenMstDocument, options);
}
export type PostApiMstItemDeleteOrRecoverTenMstMutationHookResult = ReturnType<
  typeof usePostApiMstItemDeleteOrRecoverTenMstMutation
>;
export type PostApiMstItemDeleteOrRecoverTenMstMutationResult =
  Apollo.MutationResult<PostApiMstItemDeleteOrRecoverTenMstMutation>;
export type PostApiMstItemDeleteOrRecoverTenMstMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMstItemDeleteOrRecoverTenMstMutation,
    PostApiMstItemDeleteOrRecoverTenMstMutationVariables
  >;
export const GetApiMstItemGetTeikyoByomeiDocument = gql`
  query getApiMstItemGetTeikyoByomei($itemCd: String) {
    getApiMstItemGetTeikyoByomei(itemCd: $itemCd) {
      data {
        teikyoByomeis {
          byomei
          byomeiCd
          editKbn
          endYM
          isDeleted
          isInvalid
          isInvalidTokusyo
          isModified
          itemCd
          kanaName
          sikkanCd
          startYM
          systemData
          hpId
        }
      }
      status
    }
  }
`;

/**
 * __useGetApiMstItemGetTeikyoByomeiQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemGetTeikyoByomeiQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemGetTeikyoByomeiQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemGetTeikyoByomeiQuery({
 *   variables: {
 *      itemCd: // value for 'itemCd'
 *   },
 * });
 */
export function useGetApiMstItemGetTeikyoByomeiQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemGetTeikyoByomeiQuery,
    GetApiMstItemGetTeikyoByomeiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemGetTeikyoByomeiQuery,
    GetApiMstItemGetTeikyoByomeiQueryVariables
  >(GetApiMstItemGetTeikyoByomeiDocument, options);
}
export function useGetApiMstItemGetTeikyoByomeiLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemGetTeikyoByomeiQuery,
    GetApiMstItemGetTeikyoByomeiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemGetTeikyoByomeiQuery,
    GetApiMstItemGetTeikyoByomeiQueryVariables
  >(GetApiMstItemGetTeikyoByomeiDocument, options);
}
export function useGetApiMstItemGetTeikyoByomeiSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemGetTeikyoByomeiQuery,
    GetApiMstItemGetTeikyoByomeiQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemGetTeikyoByomeiQuery,
    GetApiMstItemGetTeikyoByomeiQueryVariables
  >(GetApiMstItemGetTeikyoByomeiDocument, options);
}
export type GetApiMstItemGetTeikyoByomeiQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetTeikyoByomeiQuery
>;
export type GetApiMstItemGetTeikyoByomeiLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetTeikyoByomeiLazyQuery
>;
export type GetApiMstItemGetTeikyoByomeiSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetTeikyoByomeiSuspenseQuery
>;
export type GetApiMstItemGetTeikyoByomeiQueryResult = Apollo.QueryResult<
  GetApiMstItemGetTeikyoByomeiQuery,
  GetApiMstItemGetTeikyoByomeiQueryVariables
>;
