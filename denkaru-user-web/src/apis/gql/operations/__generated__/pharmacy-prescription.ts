import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type FindPrescriptionReceptionsQueryVariables = Types.Exact<{
  input: Types.FindPrescriptionReceptionsInput;
}>;

export type FindPrescriptionReceptionsQuery = {
  __typename?: "query_root";
  findPrescriptionReceptions: Array<{
    __typename?: "PrescriptionReception";
    prescriptionReceptionId: number;
    hpId: number;
    receptionTimestamp: string;
    receptionStatus: number;
    phoneNumber: string;
    genericDrugDesire: number;
    prescriptionRecordBring: number;
    otherRequest?: string;
    printStatus: number;
    smsStatus: number;
    memo?: string;
    isNew: boolean;
  }>;
};

export type FindPrescriptionImagesQueryVariables = Types.Exact<{
  input: Types.FindPrescriptionImagesInput;
}>;

export type FindPrescriptionImagesQuery = {
  __typename?: "query_root";
  findPrescriptionImages: Array<{
    __typename?: "PrescriptionImage";
    prescriptionImageId: number;
    imageUrl: string;
    mimeType?: string;
  }>;
};

export type UpdatePrescriptionReceptionStatusMutationVariables = Types.Exact<{
  input: Types.UpdatePrescriptionReceptionStatusInput;
}>;

export type UpdatePrescriptionReceptionStatusMutation = {
  __typename?: "mutation_root";
  updatePrescriptionReceptionStatus: boolean;
};

export type UpdatePrescriptionReceptionMemoMutationVariables = Types.Exact<{
  input: Types.UpdatePrescriptionReceptionMemoInput;
}>;

export type UpdatePrescriptionReceptionMemoMutation = {
  __typename?: "mutation_root";
  updatePrescriptionReceptionMemo: boolean;
};

export type UpdatePrescriptionReceptionPrintStatusMutationVariables =
  Types.Exact<{
    input: Types.UpdatePrescriptionReceptionPrintStatusInput;
  }>;

export type UpdatePrescriptionReceptionPrintStatusMutation = {
  __typename?: "mutation_root";
  updatePrescriptionReceptionPrintStatus: boolean;
};

export const FindPrescriptionReceptionsDocument = gql`
  query findPrescriptionReceptions($input: FindPrescriptionReceptionsInput!) {
    findPrescriptionReceptions(input: $input) {
      prescriptionReceptionId
      hpId
      receptionTimestamp
      receptionStatus
      phoneNumber
      genericDrugDesire
      prescriptionRecordBring
      otherRequest
      printStatus
      smsStatus
      memo
      isNew
    }
  }
`;

/**
 * __useFindPrescriptionReceptionsQuery__
 *
 * To run a query within a React component, call `useFindPrescriptionReceptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useFindPrescriptionReceptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFindPrescriptionReceptionsQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useFindPrescriptionReceptionsQuery(
  baseOptions: Apollo.QueryHookOptions<
    FindPrescriptionReceptionsQuery,
    FindPrescriptionReceptionsQueryVariables
  > &
    (
      | { variables: FindPrescriptionReceptionsQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    FindPrescriptionReceptionsQuery,
    FindPrescriptionReceptionsQueryVariables
  >(FindPrescriptionReceptionsDocument, options);
}
export function useFindPrescriptionReceptionsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FindPrescriptionReceptionsQuery,
    FindPrescriptionReceptionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    FindPrescriptionReceptionsQuery,
    FindPrescriptionReceptionsQueryVariables
  >(FindPrescriptionReceptionsDocument, options);
}
export function useFindPrescriptionReceptionsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    FindPrescriptionReceptionsQuery,
    FindPrescriptionReceptionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    FindPrescriptionReceptionsQuery,
    FindPrescriptionReceptionsQueryVariables
  >(FindPrescriptionReceptionsDocument, options);
}
export type FindPrescriptionReceptionsQueryHookResult = ReturnType<
  typeof useFindPrescriptionReceptionsQuery
>;
export type FindPrescriptionReceptionsLazyQueryHookResult = ReturnType<
  typeof useFindPrescriptionReceptionsLazyQuery
>;
export type FindPrescriptionReceptionsSuspenseQueryHookResult = ReturnType<
  typeof useFindPrescriptionReceptionsSuspenseQuery
>;
export type FindPrescriptionReceptionsQueryResult = Apollo.QueryResult<
  FindPrescriptionReceptionsQuery,
  FindPrescriptionReceptionsQueryVariables
>;
export const FindPrescriptionImagesDocument = gql`
  query findPrescriptionImages($input: FindPrescriptionImagesInput!) {
    findPrescriptionImages(input: $input) {
      prescriptionImageId
      imageUrl
      mimeType
    }
  }
`;

/**
 * __useFindPrescriptionImagesQuery__
 *
 * To run a query within a React component, call `useFindPrescriptionImagesQuery` and pass it any options that fit your needs.
 * When your component renders, `useFindPrescriptionImagesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFindPrescriptionImagesQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useFindPrescriptionImagesQuery(
  baseOptions: Apollo.QueryHookOptions<
    FindPrescriptionImagesQuery,
    FindPrescriptionImagesQueryVariables
  > &
    (
      | { variables: FindPrescriptionImagesQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    FindPrescriptionImagesQuery,
    FindPrescriptionImagesQueryVariables
  >(FindPrescriptionImagesDocument, options);
}
export function useFindPrescriptionImagesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FindPrescriptionImagesQuery,
    FindPrescriptionImagesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    FindPrescriptionImagesQuery,
    FindPrescriptionImagesQueryVariables
  >(FindPrescriptionImagesDocument, options);
}
export function useFindPrescriptionImagesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    FindPrescriptionImagesQuery,
    FindPrescriptionImagesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    FindPrescriptionImagesQuery,
    FindPrescriptionImagesQueryVariables
  >(FindPrescriptionImagesDocument, options);
}
export type FindPrescriptionImagesQueryHookResult = ReturnType<
  typeof useFindPrescriptionImagesQuery
>;
export type FindPrescriptionImagesLazyQueryHookResult = ReturnType<
  typeof useFindPrescriptionImagesLazyQuery
>;
export type FindPrescriptionImagesSuspenseQueryHookResult = ReturnType<
  typeof useFindPrescriptionImagesSuspenseQuery
>;
export type FindPrescriptionImagesQueryResult = Apollo.QueryResult<
  FindPrescriptionImagesQuery,
  FindPrescriptionImagesQueryVariables
>;
export const UpdatePrescriptionReceptionStatusDocument = gql`
  mutation updatePrescriptionReceptionStatus(
    $input: UpdatePrescriptionReceptionStatusInput!
  ) {
    updatePrescriptionReceptionStatus(input: $input)
  }
`;
export type UpdatePrescriptionReceptionStatusMutationFn =
  Apollo.MutationFunction<
    UpdatePrescriptionReceptionStatusMutation,
    UpdatePrescriptionReceptionStatusMutationVariables
  >;

/**
 * __useUpdatePrescriptionReceptionStatusMutation__
 *
 * To run a mutation, you first call `useUpdatePrescriptionReceptionStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePrescriptionReceptionStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePrescriptionReceptionStatusMutation, { data, loading, error }] = useUpdatePrescriptionReceptionStatusMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePrescriptionReceptionStatusMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePrescriptionReceptionStatusMutation,
    UpdatePrescriptionReceptionStatusMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePrescriptionReceptionStatusMutation,
    UpdatePrescriptionReceptionStatusMutationVariables
  >(UpdatePrescriptionReceptionStatusDocument, options);
}
export type UpdatePrescriptionReceptionStatusMutationHookResult = ReturnType<
  typeof useUpdatePrescriptionReceptionStatusMutation
>;
export type UpdatePrescriptionReceptionStatusMutationResult =
  Apollo.MutationResult<UpdatePrescriptionReceptionStatusMutation>;
export type UpdatePrescriptionReceptionStatusMutationOptions =
  Apollo.BaseMutationOptions<
    UpdatePrescriptionReceptionStatusMutation,
    UpdatePrescriptionReceptionStatusMutationVariables
  >;
export const UpdatePrescriptionReceptionMemoDocument = gql`
  mutation updatePrescriptionReceptionMemo(
    $input: UpdatePrescriptionReceptionMemoInput!
  ) {
    updatePrescriptionReceptionMemo(input: $input)
  }
`;
export type UpdatePrescriptionReceptionMemoMutationFn = Apollo.MutationFunction<
  UpdatePrescriptionReceptionMemoMutation,
  UpdatePrescriptionReceptionMemoMutationVariables
>;

/**
 * __useUpdatePrescriptionReceptionMemoMutation__
 *
 * To run a mutation, you first call `useUpdatePrescriptionReceptionMemoMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePrescriptionReceptionMemoMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePrescriptionReceptionMemoMutation, { data, loading, error }] = useUpdatePrescriptionReceptionMemoMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePrescriptionReceptionMemoMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePrescriptionReceptionMemoMutation,
    UpdatePrescriptionReceptionMemoMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePrescriptionReceptionMemoMutation,
    UpdatePrescriptionReceptionMemoMutationVariables
  >(UpdatePrescriptionReceptionMemoDocument, options);
}
export type UpdatePrescriptionReceptionMemoMutationHookResult = ReturnType<
  typeof useUpdatePrescriptionReceptionMemoMutation
>;
export type UpdatePrescriptionReceptionMemoMutationResult =
  Apollo.MutationResult<UpdatePrescriptionReceptionMemoMutation>;
export type UpdatePrescriptionReceptionMemoMutationOptions =
  Apollo.BaseMutationOptions<
    UpdatePrescriptionReceptionMemoMutation,
    UpdatePrescriptionReceptionMemoMutationVariables
  >;
export const UpdatePrescriptionReceptionPrintStatusDocument = gql`
  mutation updatePrescriptionReceptionPrintStatus(
    $input: UpdatePrescriptionReceptionPrintStatusInput!
  ) {
    updatePrescriptionReceptionPrintStatus(input: $input)
  }
`;
export type UpdatePrescriptionReceptionPrintStatusMutationFn =
  Apollo.MutationFunction<
    UpdatePrescriptionReceptionPrintStatusMutation,
    UpdatePrescriptionReceptionPrintStatusMutationVariables
  >;

/**
 * __useUpdatePrescriptionReceptionPrintStatusMutation__
 *
 * To run a mutation, you first call `useUpdatePrescriptionReceptionPrintStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePrescriptionReceptionPrintStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePrescriptionReceptionPrintStatusMutation, { data, loading, error }] = useUpdatePrescriptionReceptionPrintStatusMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePrescriptionReceptionPrintStatusMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePrescriptionReceptionPrintStatusMutation,
    UpdatePrescriptionReceptionPrintStatusMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePrescriptionReceptionPrintStatusMutation,
    UpdatePrescriptionReceptionPrintStatusMutationVariables
  >(UpdatePrescriptionReceptionPrintStatusDocument, options);
}
export type UpdatePrescriptionReceptionPrintStatusMutationHookResult =
  ReturnType<typeof useUpdatePrescriptionReceptionPrintStatusMutation>;
export type UpdatePrescriptionReceptionPrintStatusMutationResult =
  Apollo.MutationResult<UpdatePrescriptionReceptionPrintStatusMutation>;
export type UpdatePrescriptionReceptionPrintStatusMutationOptions =
  Apollo.BaseMutationOptions<
    UpdatePrescriptionReceptionPrintStatusMutation,
    UpdatePrescriptionReceptionPrintStatusMutationVariables
  >;
