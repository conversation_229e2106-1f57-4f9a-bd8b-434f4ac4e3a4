import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetPatientInforGetHokenInfByPtIdQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetPatientInforGetHokenInfByPtIdQuery = {
  __typename?: "query_root";
  getApiPatientInforGetHokenInfByPtId?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceAiChatGetHokenInfByPtIdResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceAiChatGetHokenInfByPtIdResponse";
      data?: Array<{
        __typename?: "DomainModelsInsuranceAiChatHokenInfDto";
        bango?: string;
        checkDate?: number;
        edaNo?: string;
        endDate?: number;
        hokenEdaNo?: number;
        hokenEdaNoMaster?: number;
        hokenId?: number;
        hokenKbn?: number;
        hokenMstId?: number;
        hokenNo?: number;
        hokenNoMaster?: number;
        hokenSbtKbn?: number;
        hokenSentaku?: string;
        hokenSName?: string;
        hokensyaNo?: string;
        honkeKbn?: number;
        houbetu?: string;
        hpId?: number;
        isDeleted?: number;
        isExpirated?: boolean;
        isHoken?: boolean;
        isJibai?: boolean;
        isJihi?: boolean;
        isKokuho?: boolean;
        isNashi?: boolean;
        isRousai?: boolean;
        isShaho?: boolean;
        jibaiHokenName?: string;
        kigo?: string;
        kofuDate?: number;
        onlineConfirmCheckDate?: number;
        ptId?: string;
        rousaiKofuNo?: string;
        seqNo?: string;
        sinDate?: number;
        startDate?: number;
        hokenCheck?: Array<{
          __typename?: "DomainModelsInsuranceAiChatHokenCheckDto";
          checkComment?: string;
          checkDate?: number;
          checkId?: number;
          checkMachine?: string;
          checkName?: string;
          hokenGrp?: number;
          hokenId?: number;
          isDeleted?: number;
          ptId?: string;
          seqNo?: string;
        }>;
      }>;
    };
  };
};

export type GetApiOnlineGetOnlineConsentQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  isContainAgreedConsent?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiOnlineGetOnlineConsentQuery = {
  __typename?: "query_root";
  getApiOnlineGetOnlineConsent?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineGetOnlineConsentResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineGetOnlineConsentResponse";
      onlineConsentList?: Array<{
        __typename?: "EmrCloudApiResponsesOnlineDtoOnlineConsentDto";
        consDate?: string;
        consKbn?: number;
        isFromOnlineAgreedConsent?: boolean;
        limitDate?: string;
        ptId?: string;
      }>;
    };
  };
};

export type GetApiSystemConfGetListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiSystemConfGetListQuery = {
  __typename?: "query_root";
  getApiSystemConfGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSystemConfGetSystemConfListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSystemConfGetSystemConfListResponse";
      systemConfList?: Array<{
        __typename?: "EmrCloudApiResponsesSystemConfSystemConfDto";
        biko?: string;
        grpCd?: number;
        grpEdaNo?: number;
        param?: string;
        val?: number;
      }>;
    };
  };
};

export type GetApiUserGetAllPermissionQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiUserGetAllPermissionQuery = {
  __typename?: "query_root";
  getApiUserGetAllPermission?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesUserGetAllPermissionResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesUserGetAllPermissionResponse";
      userPermissionModels?: Array<{
        __typename?: "DomainModelsUserUserPermissionModel";
        functionCd?: string;
        hpId?: number;
        isDefault?: boolean;
        permission?: number;
        userId?: number;
      }>;
    };
  };
};

export const GetPatientInforGetHokenInfByPtIdDocument = gql`
  query getPatientInforGetHokenInfByPtId($ptId: BigInt, $sinDate: Int) {
    getApiPatientInforGetHokenInfByPtId(ptId: $ptId, sinDate: $sinDate) {
      data {
        data {
          bango
          checkDate
          edaNo
          endDate
          hokenCheck {
            checkComment
            checkDate
            checkId
            checkMachine
            checkName
            hokenGrp
            hokenId
            isDeleted
            ptId
            seqNo
          }
          hokenEdaNo
          hokenEdaNoMaster
          hokenId
          hokenKbn
          hokenMstId
          hokenNo
          hokenNoMaster
          hokenSbtKbn
          hokenSentaku
          hokenSName
          hokensyaNo
          honkeKbn
          houbetu
          hpId
          isDeleted
          isExpirated
          isHoken
          isJibai
          isJihi
          isKokuho
          isNashi
          isRousai
          isShaho
          jibaiHokenName
          kigo
          kofuDate
          onlineConfirmCheckDate
          ptId
          rousaiKofuNo
          seqNo
          sinDate
          startDate
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetPatientInforGetHokenInfByPtIdQuery__
 *
 * To run a query within a React component, call `useGetPatientInforGetHokenInfByPtIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPatientInforGetHokenInfByPtIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPatientInforGetHokenInfByPtIdQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetPatientInforGetHokenInfByPtIdQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetPatientInforGetHokenInfByPtIdQuery,
    GetPatientInforGetHokenInfByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPatientInforGetHokenInfByPtIdQuery,
    GetPatientInforGetHokenInfByPtIdQueryVariables
  >(GetPatientInforGetHokenInfByPtIdDocument, options);
}
export function useGetPatientInforGetHokenInfByPtIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPatientInforGetHokenInfByPtIdQuery,
    GetPatientInforGetHokenInfByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPatientInforGetHokenInfByPtIdQuery,
    GetPatientInforGetHokenInfByPtIdQueryVariables
  >(GetPatientInforGetHokenInfByPtIdDocument, options);
}
export function useGetPatientInforGetHokenInfByPtIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPatientInforGetHokenInfByPtIdQuery,
    GetPatientInforGetHokenInfByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPatientInforGetHokenInfByPtIdQuery,
    GetPatientInforGetHokenInfByPtIdQueryVariables
  >(GetPatientInforGetHokenInfByPtIdDocument, options);
}
export type GetPatientInforGetHokenInfByPtIdQueryHookResult = ReturnType<
  typeof useGetPatientInforGetHokenInfByPtIdQuery
>;
export type GetPatientInforGetHokenInfByPtIdLazyQueryHookResult = ReturnType<
  typeof useGetPatientInforGetHokenInfByPtIdLazyQuery
>;
export type GetPatientInforGetHokenInfByPtIdSuspenseQueryHookResult =
  ReturnType<typeof useGetPatientInforGetHokenInfByPtIdSuspenseQuery>;
export type GetPatientInforGetHokenInfByPtIdQueryResult = Apollo.QueryResult<
  GetPatientInforGetHokenInfByPtIdQuery,
  GetPatientInforGetHokenInfByPtIdQueryVariables
>;
export const GetApiOnlineGetOnlineConsentDocument = gql`
  query getApiOnlineGetOnlineConsent(
    $ptId: BigInt
    $isContainAgreedConsent: Boolean
  ) {
    getApiOnlineGetOnlineConsent(
      ptId: $ptId
      isContainAgreedConsent: $isContainAgreedConsent
    ) {
      data {
        onlineConsentList {
          consDate
          consKbn
          isFromOnlineAgreedConsent
          limitDate
          ptId
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiOnlineGetOnlineConsentQuery__
 *
 * To run a query within a React component, call `useGetApiOnlineGetOnlineConsentQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiOnlineGetOnlineConsentQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiOnlineGetOnlineConsentQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      isContainAgreedConsent: // value for 'isContainAgreedConsent'
 *   },
 * });
 */
export function useGetApiOnlineGetOnlineConsentQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiOnlineGetOnlineConsentQuery,
    GetApiOnlineGetOnlineConsentQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiOnlineGetOnlineConsentQuery,
    GetApiOnlineGetOnlineConsentQueryVariables
  >(GetApiOnlineGetOnlineConsentDocument, options);
}
export function useGetApiOnlineGetOnlineConsentLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiOnlineGetOnlineConsentQuery,
    GetApiOnlineGetOnlineConsentQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiOnlineGetOnlineConsentQuery,
    GetApiOnlineGetOnlineConsentQueryVariables
  >(GetApiOnlineGetOnlineConsentDocument, options);
}
export function useGetApiOnlineGetOnlineConsentSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiOnlineGetOnlineConsentQuery,
    GetApiOnlineGetOnlineConsentQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiOnlineGetOnlineConsentQuery,
    GetApiOnlineGetOnlineConsentQueryVariables
  >(GetApiOnlineGetOnlineConsentDocument, options);
}
export type GetApiOnlineGetOnlineConsentQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetOnlineConsentQuery
>;
export type GetApiOnlineGetOnlineConsentLazyQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetOnlineConsentLazyQuery
>;
export type GetApiOnlineGetOnlineConsentSuspenseQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetOnlineConsentSuspenseQuery
>;
export type GetApiOnlineGetOnlineConsentQueryResult = Apollo.QueryResult<
  GetApiOnlineGetOnlineConsentQuery,
  GetApiOnlineGetOnlineConsentQueryVariables
>;
export const GetApiSystemConfGetListDocument = gql`
  query getApiSystemConfGetList {
    getApiSystemConfGetList {
      data {
        systemConfList {
          biko
          grpCd
          grpEdaNo
          param
          val
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiSystemConfGetListQuery__
 *
 * To run a query within a React component, call `useGetApiSystemConfGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiSystemConfGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiSystemConfGetListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiSystemConfGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiSystemConfGetListQuery,
    GetApiSystemConfGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiSystemConfGetListQuery,
    GetApiSystemConfGetListQueryVariables
  >(GetApiSystemConfGetListDocument, options);
}
export function useGetApiSystemConfGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiSystemConfGetListQuery,
    GetApiSystemConfGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiSystemConfGetListQuery,
    GetApiSystemConfGetListQueryVariables
  >(GetApiSystemConfGetListDocument, options);
}
export function useGetApiSystemConfGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiSystemConfGetListQuery,
    GetApiSystemConfGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiSystemConfGetListQuery,
    GetApiSystemConfGetListQueryVariables
  >(GetApiSystemConfGetListDocument, options);
}
export type GetApiSystemConfGetListQueryHookResult = ReturnType<
  typeof useGetApiSystemConfGetListQuery
>;
export type GetApiSystemConfGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiSystemConfGetListLazyQuery
>;
export type GetApiSystemConfGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiSystemConfGetListSuspenseQuery
>;
export type GetApiSystemConfGetListQueryResult = Apollo.QueryResult<
  GetApiSystemConfGetListQuery,
  GetApiSystemConfGetListQueryVariables
>;
export const GetApiUserGetAllPermissionDocument = gql`
  query getApiUserGetAllPermission {
    getApiUserGetAllPermission {
      data {
        userPermissionModels {
          functionCd
          hpId
          isDefault
          permission
          userId
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiUserGetAllPermissionQuery__
 *
 * To run a query within a React component, call `useGetApiUserGetAllPermissionQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiUserGetAllPermissionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiUserGetAllPermissionQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiUserGetAllPermissionQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiUserGetAllPermissionQuery,
    GetApiUserGetAllPermissionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiUserGetAllPermissionQuery,
    GetApiUserGetAllPermissionQueryVariables
  >(GetApiUserGetAllPermissionDocument, options);
}
export function useGetApiUserGetAllPermissionLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiUserGetAllPermissionQuery,
    GetApiUserGetAllPermissionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiUserGetAllPermissionQuery,
    GetApiUserGetAllPermissionQueryVariables
  >(GetApiUserGetAllPermissionDocument, options);
}
export function useGetApiUserGetAllPermissionSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiUserGetAllPermissionQuery,
    GetApiUserGetAllPermissionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiUserGetAllPermissionQuery,
    GetApiUserGetAllPermissionQueryVariables
  >(GetApiUserGetAllPermissionDocument, options);
}
export type GetApiUserGetAllPermissionQueryHookResult = ReturnType<
  typeof useGetApiUserGetAllPermissionQuery
>;
export type GetApiUserGetAllPermissionLazyQueryHookResult = ReturnType<
  typeof useGetApiUserGetAllPermissionLazyQuery
>;
export type GetApiUserGetAllPermissionSuspenseQueryHookResult = ReturnType<
  typeof useGetApiUserGetAllPermissionSuspenseQuery
>;
export type GetApiUserGetAllPermissionQueryResult = Apollo.QueryResult<
  GetApiUserGetAllPermissionQuery,
  GetApiUserGetAllPermissionQueryVariables
>;
