import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiKensaMstGetKensaMstsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiKensaMstGetKensaMstsQuery = {
  __typename?: "query_root";
  getApiKensaMstGetKensaMsts?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaMstKensaMstListResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesKensaMstKensaMstListResponse";
      listData?: Array<{
        __typename?: "DomainModelsKensaMstKensaMstModel";
        analyte?: string;
        analyteName?: string;
        centerCode?: string;
        centerItemCd1?: string;
        centerItemCd2?: string;
        createDate?: string;
        createId?: number;
        createMachine?: string;
        digit?: number;
        displayName?: string;
        femaleStd?: string;
        femaleStdHigh?: string;
        femaleStdLow?: string;
        hpId?: number;
        identification?: string;
        identificationName?: string;
        isDelete?: number;
        jlac10?: string;
        kensaItemCd?: string;
        kensaItemSeqNo?: number;
        kensaKana?: string;
        kensaName?: string;
        maleStd?: string;
        maleStdHigh?: string;
        maleStdLow?: string;
        materialCd?: number;
        methodology?: string;
        methodologyName?: string;
        oyaItemCd?: string;
        oyaItemSeqNo?: number;
        resultIdCommon?: string;
        resultIdCommonName?: string;
        resultIdUnique?: string;
        resultIdUniqueName?: string;
        santeiItemCd?: string;
        sortNo?: string;
        specimen?: string;
        specimenName?: string;
        unit?: string;
        updateDate?: string;
        updateMachine?: string;
        updateId?: number;
      }>;
    };
  };
};

export type PostApiKensaMstCreateKensaMstMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsKensaMstSaveKensaMstRequestInput;
}>;

export type PostApiKensaMstCreateKensaMstMutation = {
  __typename?: "mutation_root";
  postApiKensaMstCreateKensaMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaMstKensaMstResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesKensaMstKensaMstResponse";
      data?: {
        __typename?: "DomainModelsKensaMstKensaMstModel";
        analyte?: string;
        analyteName?: string;
        centerCode?: string;
        centerItemCd1?: string;
        centerItemCd2?: string;
        createDate?: string;
        createId?: number;
        createMachine?: string;
        digit?: number;
        displayName?: string;
        femaleStd?: string;
        femaleStdHigh?: string;
        femaleStdLow?: string;
        hpId?: number;
        identification?: string;
        identificationName?: string;
        isDelete?: number;
        jlac10?: string;
        kensaItemCd?: string;
        kensaItemSeqNo?: number;
        kensaKana?: string;
        kensaName?: string;
        maleStd?: string;
        maleStdHigh?: string;
        maleStdLow?: string;
        materialCd?: number;
        methodology?: string;
        methodologyName?: string;
        oyaItemCd?: string;
        oyaItemSeqNo?: number;
        resultIdCommon?: string;
        resultIdCommonName?: string;
        resultIdUnique?: string;
        resultIdUniqueName?: string;
        santeiItemCd?: string;
        sortNo?: string;
        specimen?: string;
        specimenName?: string;
        unit?: string;
        updateDate?: string;
        updateMachine?: string;
        updateId?: number;
      };
    };
  };
};

export type PostApiKensaMstUpdateKensaMstMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsKensaMstSaveKensaMstRequestInput;
}>;

export type PostApiKensaMstUpdateKensaMstMutation = {
  __typename?: "mutation_root";
  postApiKensaMstUpdateKensaMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaMstKensaMstResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesKensaMstKensaMstResponse";
      data?: {
        __typename?: "DomainModelsKensaMstKensaMstModel";
        analyte?: string;
        analyteName?: string;
        centerCode?: string;
        centerItemCd1?: string;
        centerItemCd2?: string;
        createDate?: string;
        createId?: number;
        createMachine?: string;
        digit?: number;
        displayName?: string;
        femaleStd?: string;
        femaleStdHigh?: string;
        femaleStdLow?: string;
        hpId?: number;
        identification?: string;
        identificationName?: string;
        isDelete?: number;
        jlac10?: string;
        kensaItemCd?: string;
        kensaItemSeqNo?: number;
        kensaKana?: string;
        kensaName?: string;
        maleStd?: string;
        maleStdHigh?: string;
        maleStdLow?: string;
        materialCd?: number;
        methodology?: string;
        methodologyName?: string;
        oyaItemCd?: string;
        oyaItemSeqNo?: number;
        resultIdCommon?: string;
        resultIdCommonName?: string;
        resultIdUnique?: string;
        resultIdUniqueName?: string;
        santeiItemCd?: string;
        sortNo?: string;
        specimen?: string;
        specimenName?: string;
        unit?: string;
        updateDate?: string;
        updateMachine?: string;
        updateId?: number;
      };
    };
  };
};

export type PostApiKensaMstDeleteKensaMstMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsKensaMstDeleteKensaMstRequestInput>;
}>;

export type PostApiKensaMstDeleteKensaMstMutation = {
  __typename?: "mutation_root";
  postApiKensaMstDeleteKensaMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKensaMstKensaMstResponse";
    status?: number;
    message?: string;
  };
};

export const GetApiKensaMstGetKensaMstsDocument = gql`
  query getApiKensaMstGetKensaMsts {
    getApiKensaMstGetKensaMsts {
      status
      message
      data {
        listData {
          analyte
          analyteName
          centerCode
          centerItemCd1
          centerItemCd2
          createDate
          createId
          createMachine
          digit
          displayName
          femaleStd
          femaleStdHigh
          femaleStdLow
          hpId
          identification
          identificationName
          isDelete
          jlac10
          kensaItemCd
          kensaItemSeqNo
          kensaKana
          kensaName
          maleStd
          maleStdHigh
          maleStdLow
          materialCd
          methodology
          methodologyName
          oyaItemCd
          oyaItemSeqNo
          resultIdCommon
          resultIdCommonName
          resultIdUnique
          resultIdUniqueName
          santeiItemCd
          sortNo
          specimen
          specimenName
          unit
          updateDate
          updateMachine
          updateId
        }
      }
    }
  }
`;

/**
 * __useGetApiKensaMstGetKensaMstsQuery__
 *
 * To run a query within a React component, call `useGetApiKensaMstGetKensaMstsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKensaMstGetKensaMstsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKensaMstGetKensaMstsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiKensaMstGetKensaMstsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiKensaMstGetKensaMstsQuery,
    GetApiKensaMstGetKensaMstsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKensaMstGetKensaMstsQuery,
    GetApiKensaMstGetKensaMstsQueryVariables
  >(GetApiKensaMstGetKensaMstsDocument, options);
}
export function useGetApiKensaMstGetKensaMstsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKensaMstGetKensaMstsQuery,
    GetApiKensaMstGetKensaMstsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKensaMstGetKensaMstsQuery,
    GetApiKensaMstGetKensaMstsQueryVariables
  >(GetApiKensaMstGetKensaMstsDocument, options);
}
export function useGetApiKensaMstGetKensaMstsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKensaMstGetKensaMstsQuery,
    GetApiKensaMstGetKensaMstsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKensaMstGetKensaMstsQuery,
    GetApiKensaMstGetKensaMstsQueryVariables
  >(GetApiKensaMstGetKensaMstsDocument, options);
}
export type GetApiKensaMstGetKensaMstsQueryHookResult = ReturnType<
  typeof useGetApiKensaMstGetKensaMstsQuery
>;
export type GetApiKensaMstGetKensaMstsLazyQueryHookResult = ReturnType<
  typeof useGetApiKensaMstGetKensaMstsLazyQuery
>;
export type GetApiKensaMstGetKensaMstsSuspenseQueryHookResult = ReturnType<
  typeof useGetApiKensaMstGetKensaMstsSuspenseQuery
>;
export type GetApiKensaMstGetKensaMstsQueryResult = Apollo.QueryResult<
  GetApiKensaMstGetKensaMstsQuery,
  GetApiKensaMstGetKensaMstsQueryVariables
>;
export const PostApiKensaMstCreateKensaMstDocument = gql`
  mutation PostApiKensaMstCreateKensaMst(
    $input: EmrCloudApiRequestsKensaMstSaveKensaMstRequestInput!
  ) {
    postApiKensaMstCreateKensaMst(
      emrCloudApiRequestsKensaMstSaveKensaMstRequestInput: $input
    ) {
      data {
        data {
          analyte
          analyteName
          centerCode
          centerItemCd1
          centerItemCd2
          createDate
          createId
          createMachine
          digit
          displayName
          femaleStd
          femaleStdHigh
          femaleStdLow
          hpId
          identification
          identificationName
          isDelete
          jlac10
          kensaItemCd
          kensaItemSeqNo
          kensaKana
          kensaName
          maleStd
          maleStdHigh
          maleStdLow
          materialCd
          methodology
          methodologyName
          oyaItemCd
          oyaItemSeqNo
          resultIdCommon
          resultIdCommonName
          resultIdUnique
          resultIdUniqueName
          santeiItemCd
          sortNo
          specimen
          specimenName
          unit
          updateDate
          updateMachine
          updateId
        }
      }
      status
      message
    }
  }
`;
export type PostApiKensaMstCreateKensaMstMutationFn = Apollo.MutationFunction<
  PostApiKensaMstCreateKensaMstMutation,
  PostApiKensaMstCreateKensaMstMutationVariables
>;

/**
 * __usePostApiKensaMstCreateKensaMstMutation__
 *
 * To run a mutation, you first call `usePostApiKensaMstCreateKensaMstMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKensaMstCreateKensaMstMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKensaMstCreateKensaMstMutation, { data, loading, error }] = usePostApiKensaMstCreateKensaMstMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiKensaMstCreateKensaMstMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKensaMstCreateKensaMstMutation,
    PostApiKensaMstCreateKensaMstMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKensaMstCreateKensaMstMutation,
    PostApiKensaMstCreateKensaMstMutationVariables
  >(PostApiKensaMstCreateKensaMstDocument, options);
}
export type PostApiKensaMstCreateKensaMstMutationHookResult = ReturnType<
  typeof usePostApiKensaMstCreateKensaMstMutation
>;
export type PostApiKensaMstCreateKensaMstMutationResult =
  Apollo.MutationResult<PostApiKensaMstCreateKensaMstMutation>;
export type PostApiKensaMstCreateKensaMstMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKensaMstCreateKensaMstMutation,
    PostApiKensaMstCreateKensaMstMutationVariables
  >;
export const PostApiKensaMstUpdateKensaMstDocument = gql`
  mutation PostApiKensaMstUpdateKensaMst(
    $input: EmrCloudApiRequestsKensaMstSaveKensaMstRequestInput!
  ) {
    postApiKensaMstUpdateKensaMst(
      emrCloudApiRequestsKensaMstSaveKensaMstRequestInput: $input
    ) {
      data {
        data {
          analyte
          analyteName
          centerCode
          centerItemCd1
          centerItemCd2
          createDate
          createId
          createMachine
          digit
          displayName
          femaleStd
          femaleStdHigh
          femaleStdLow
          hpId
          identification
          identificationName
          isDelete
          jlac10
          kensaItemCd
          kensaItemSeqNo
          kensaKana
          kensaName
          maleStd
          maleStdHigh
          maleStdLow
          materialCd
          methodology
          methodologyName
          oyaItemCd
          oyaItemSeqNo
          resultIdCommon
          resultIdCommonName
          resultIdUnique
          resultIdUniqueName
          santeiItemCd
          sortNo
          specimen
          specimenName
          unit
          updateDate
          updateMachine
          updateId
        }
      }
      status
      message
    }
  }
`;
export type PostApiKensaMstUpdateKensaMstMutationFn = Apollo.MutationFunction<
  PostApiKensaMstUpdateKensaMstMutation,
  PostApiKensaMstUpdateKensaMstMutationVariables
>;

/**
 * __usePostApiKensaMstUpdateKensaMstMutation__
 *
 * To run a mutation, you first call `usePostApiKensaMstUpdateKensaMstMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKensaMstUpdateKensaMstMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKensaMstUpdateKensaMstMutation, { data, loading, error }] = usePostApiKensaMstUpdateKensaMstMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiKensaMstUpdateKensaMstMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKensaMstUpdateKensaMstMutation,
    PostApiKensaMstUpdateKensaMstMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKensaMstUpdateKensaMstMutation,
    PostApiKensaMstUpdateKensaMstMutationVariables
  >(PostApiKensaMstUpdateKensaMstDocument, options);
}
export type PostApiKensaMstUpdateKensaMstMutationHookResult = ReturnType<
  typeof usePostApiKensaMstUpdateKensaMstMutation
>;
export type PostApiKensaMstUpdateKensaMstMutationResult =
  Apollo.MutationResult<PostApiKensaMstUpdateKensaMstMutation>;
export type PostApiKensaMstUpdateKensaMstMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKensaMstUpdateKensaMstMutation,
    PostApiKensaMstUpdateKensaMstMutationVariables
  >;
export const PostApiKensaMstDeleteKensaMstDocument = gql`
  mutation PostApiKensaMstDeleteKensaMst(
    $input: EmrCloudApiRequestsKensaMstDeleteKensaMstRequestInput
  ) {
    postApiKensaMstDeleteKensaMst(
      emrCloudApiRequestsKensaMstDeleteKensaMstRequestInput: $input
    ) {
      status
      message
    }
  }
`;
export type PostApiKensaMstDeleteKensaMstMutationFn = Apollo.MutationFunction<
  PostApiKensaMstDeleteKensaMstMutation,
  PostApiKensaMstDeleteKensaMstMutationVariables
>;

/**
 * __usePostApiKensaMstDeleteKensaMstMutation__
 *
 * To run a mutation, you first call `usePostApiKensaMstDeleteKensaMstMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiKensaMstDeleteKensaMstMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiKensaMstDeleteKensaMstMutation, { data, loading, error }] = usePostApiKensaMstDeleteKensaMstMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiKensaMstDeleteKensaMstMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiKensaMstDeleteKensaMstMutation,
    PostApiKensaMstDeleteKensaMstMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiKensaMstDeleteKensaMstMutation,
    PostApiKensaMstDeleteKensaMstMutationVariables
  >(PostApiKensaMstDeleteKensaMstDocument, options);
}
export type PostApiKensaMstDeleteKensaMstMutationHookResult = ReturnType<
  typeof usePostApiKensaMstDeleteKensaMstMutation
>;
export type PostApiKensaMstDeleteKensaMstMutationResult =
  Apollo.MutationResult<PostApiKensaMstDeleteKensaMstMutation>;
export type PostApiKensaMstDeleteKensaMstMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiKensaMstDeleteKensaMstMutation,
    PostApiKensaMstDeleteKensaMstMutationVariables
  >;
