import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type LoginMutationVariables = Types.Exact<{
  input: Types.LoginReq;
}>;

export type LoginMutation = {
  __typename?: "mutation_root";
  login: {
    __typename?: "LoginRes";
    hospitalId: number;
    karteStatus: number;
    success: number;
    isLoginIdInitialized: number;
    isPasswordInitialized: number;
    pharmacyFlg: boolean;
    staffInfo: {
      __typename?: "StaffInfo";
      hospitalID: number;
      loginId: string;
      managerKbn: number;
      staffId: number;
      staffKana: string;
      staffName: string;
      staffType: number;
      status: number;
      permissions: Array<{
        __typename?: "StaffPermission";
        functionCd: string;
        permission: number;
      }>;
    };
  };
};

export type LogoutMutationVariables = Types.Exact<{ [key: string]: never }>;

export type LogoutMutation = {
  __typename?: "mutation_root";
  logout: {
    __typename?: "LogoutRes";
    success: number;
    isSessionRemaining: boolean;
  };
};

export type LoginInitialMutationVariables = Types.Exact<{
  input: Types.LoginInitialReq;
}>;

export type LoginInitialMutation = {
  __typename?: "mutation_root";
  loginInitial: { __typename?: "LoginInitialRes"; success: number };
};

export const LoginDocument = gql`
  mutation login($input: LoginReq!) {
    login(input: $input) {
      hospitalId
      karteStatus
      success
      isLoginIdInitialized
      isPasswordInitialized
      pharmacyFlg
      staffInfo {
        hospitalID
        loginId
        managerKbn
        permissions {
          functionCd
          permission
        }
        staffId
        staffKana
        staffName
        staffType
        status
      }
    }
  }
`;
export type LoginMutationFn = Apollo.MutationFunction<
  LoginMutation,
  LoginMutationVariables
>;

/**
 * __useLoginMutation__
 *
 * To run a mutation, you first call `useLoginMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useLoginMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [loginMutation, { data, loading, error }] = useLoginMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useLoginMutation(
  baseOptions?: Apollo.MutationHookOptions<
    LoginMutation,
    LoginMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<LoginMutation, LoginMutationVariables>(
    LoginDocument,
    options,
  );
}
export type LoginMutationHookResult = ReturnType<typeof useLoginMutation>;
export type LoginMutationResult = Apollo.MutationResult<LoginMutation>;
export type LoginMutationOptions = Apollo.BaseMutationOptions<
  LoginMutation,
  LoginMutationVariables
>;
export const LogoutDocument = gql`
  mutation logout {
    logout {
      success
      isSessionRemaining
    }
  }
`;
export type LogoutMutationFn = Apollo.MutationFunction<
  LogoutMutation,
  LogoutMutationVariables
>;

/**
 * __useLogoutMutation__
 *
 * To run a mutation, you first call `useLogoutMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useLogoutMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [logoutMutation, { data, loading, error }] = useLogoutMutation({
 *   variables: {
 *   },
 * });
 */
export function useLogoutMutation(
  baseOptions?: Apollo.MutationHookOptions<
    LogoutMutation,
    LogoutMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<LogoutMutation, LogoutMutationVariables>(
    LogoutDocument,
    options,
  );
}
export type LogoutMutationHookResult = ReturnType<typeof useLogoutMutation>;
export type LogoutMutationResult = Apollo.MutationResult<LogoutMutation>;
export type LogoutMutationOptions = Apollo.BaseMutationOptions<
  LogoutMutation,
  LogoutMutationVariables
>;
export const LoginInitialDocument = gql`
  mutation loginInitial($input: LoginInitialReq!) {
    loginInitial(input: $input) {
      success
    }
  }
`;
export type LoginInitialMutationFn = Apollo.MutationFunction<
  LoginInitialMutation,
  LoginInitialMutationVariables
>;

/**
 * __useLoginInitialMutation__
 *
 * To run a mutation, you first call `useLoginInitialMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useLoginInitialMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [loginInitialMutation, { data, loading, error }] = useLoginInitialMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useLoginInitialMutation(
  baseOptions?: Apollo.MutationHookOptions<
    LoginInitialMutation,
    LoginInitialMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    LoginInitialMutation,
    LoginInitialMutationVariables
  >(LoginInitialDocument, options);
}
export type LoginInitialMutationHookResult = ReturnType<
  typeof useLoginInitialMutation
>;
export type LoginInitialMutationResult =
  Apollo.MutationResult<LoginInitialMutation>;
export type LoginInitialMutationOptions = Apollo.BaseMutationOptions<
  LoginInitialMutation,
  LoginInitialMutationVariables
>;
