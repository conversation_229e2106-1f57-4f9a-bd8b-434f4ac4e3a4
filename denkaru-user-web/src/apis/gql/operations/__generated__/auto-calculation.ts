import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiMedicalExaminationGetCheckedOrderMutationVariables =
  Types.Exact<{
    input: Types.EmrCloudApiRequestsMedicalExaminationGetCheckedOrderRequestInput;
  }>;

export type PostApiMedicalExaminationGetCheckedOrderMutation = {
  __typename?: "mutation_root";
  postApiMedicalExaminationGetCheckedOrder?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationGetCheckedOrderResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationGetCheckedOrderResponse";
      checkedOrderModels?: Array<{
        __typename?: "DomainModelsMedicalExaminationCheckedOrderModel";
        buiKbn?: number;
        centerCd?: string;
        centerName?: string;
        checkingContent?: string;
        checkingType?: number;
        checkingTypeDisplay?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
        cnvUnitName?: string;
        defaultValue?: number;
        drugKbn?: number;
        inOutKbn?: number;
        ipnCD?: string;
        ipnName?: string;
        ipnNameCd?: string;
        isAdopted?: number;
        isEnableSantei?: boolean;
        isSelectiveComment?: boolean;
        itemCd?: string;
        itemName?: string;
        kohatuKbn?: number;
        kokuji1?: string;
        kokuji2?: string;
        masterSbt?: string;
        odrUnitName?: string;
        santei?: boolean;
        rousaiKbn?: number;
        senteiRyoyoKbn?: number;
        sinKouiKbn?: number;
        startDate?: number;
        unitName?: string;
        yjCd?: string;
        yohoKbn?: number;
      }>;
    };
  };
};

export type GetApiEpsValidateBeforePrintingQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiEpsValidateBeforePrintingQuery = {
  __typename?: "query_root";
  getApiEpsValidateBeforePrinting?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsValidateBeforePrintingResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsValidateBeforePrintingResponse";
      epsPrescriptions?: Array<{
        __typename?: "DomainModelsEpsPrescriptionEpsPrescriptionModel";
        accessCode?: string;
      }>;
      epsReferences?: Array<{
        __typename?: "DomainModelsEpsReferenceEpsReferenceModel";
        raiinNo?: string;
      }>;
      ptHokenPatterns?: Array<{
        __typename?: "DomainModelsEpsPtHokenPtn";
        hokenId?: number;
      }>;
      raiinInf?: {
        __typename?: "DomainModelsEpsRaiinInfModel";
        raiinNo?: string;
      };
    };
  };
};

export type GetApiMedicalExaminationGetKensaAuditTrailLogQueryVariables =
  Types.Exact<{
    eventCd: Types.Scalars["String"]["input"];
    isOperator: Types.Scalars["Int"]["input"];
    operatorName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    ptId: Types.Scalars["BigInt"]["input"];
    raiinNo: Types.Scalars["BigInt"]["input"];
    sinDate: Types.Scalars["Int"]["input"];
  }>;

export type GetApiMedicalExaminationGetKensaAuditTrailLogQuery = {
  __typename?: "query_root";
  getApiMedicalExaminationGetKensaAuditTrailLog?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationGetKensaAuditTrailLogResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationGetKensaAuditTrailLogResponse";
      auditTrailLogItems?: Array<{
        __typename?: "UseCaseMedicalExaminationGetKensaAuditTrailLogAuditTrailLogItem";
        eventCd?: string;
        hosuke?: string;
        hpId?: number;
        isOperator?: number;
        logDate?: string;
        logId?: string;
        machine?: string;
        operatorName?: string;
        ptId?: string;
        raiinNo?: string;
        sinDate?: number;
        userId?: number;
      }>;
    };
  };
};

export const PostApiMedicalExaminationGetCheckedOrderDocument = gql`
  mutation postApiMedicalExaminationGetCheckedOrder(
    $input: EmrCloudApiRequestsMedicalExaminationGetCheckedOrderRequestInput!
  ) {
    postApiMedicalExaminationGetCheckedOrder(
      emrCloudApiRequestsMedicalExaminationGetCheckedOrderRequestInput: $input
    ) {
      message
      status
      data {
        checkedOrderModels {
          buiKbn
          centerCd
          centerName
          checkingContent
          checkingType
          checkingTypeDisplay
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
          cnvUnitName
          defaultValue
          drugKbn
          inOutKbn
          ipnCD
          ipnName
          ipnNameCd
          isAdopted
          isEnableSantei
          isSelectiveComment
          itemCd
          itemName
          kohatuKbn
          kokuji1
          kokuji2
          masterSbt
          odrUnitName
          santei
          rousaiKbn
          senteiRyoyoKbn
          sinKouiKbn
          startDate
          unitName
          yjCd
          yohoKbn
        }
      }
    }
  }
`;
export type PostApiMedicalExaminationGetCheckedOrderMutationFn =
  Apollo.MutationFunction<
    PostApiMedicalExaminationGetCheckedOrderMutation,
    PostApiMedicalExaminationGetCheckedOrderMutationVariables
  >;

/**
 * __usePostApiMedicalExaminationGetCheckedOrderMutation__
 *
 * To run a mutation, you first call `usePostApiMedicalExaminationGetCheckedOrderMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMedicalExaminationGetCheckedOrderMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMedicalExaminationGetCheckedOrderMutation, { data, loading, error }] = usePostApiMedicalExaminationGetCheckedOrderMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiMedicalExaminationGetCheckedOrderMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMedicalExaminationGetCheckedOrderMutation,
    PostApiMedicalExaminationGetCheckedOrderMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMedicalExaminationGetCheckedOrderMutation,
    PostApiMedicalExaminationGetCheckedOrderMutationVariables
  >(PostApiMedicalExaminationGetCheckedOrderDocument, options);
}
export type PostApiMedicalExaminationGetCheckedOrderMutationHookResult =
  ReturnType<typeof usePostApiMedicalExaminationGetCheckedOrderMutation>;
export type PostApiMedicalExaminationGetCheckedOrderMutationResult =
  Apollo.MutationResult<PostApiMedicalExaminationGetCheckedOrderMutation>;
export type PostApiMedicalExaminationGetCheckedOrderMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMedicalExaminationGetCheckedOrderMutation,
    PostApiMedicalExaminationGetCheckedOrderMutationVariables
  >;
export const GetApiEpsValidateBeforePrintingDocument = gql`
  query getApiEpsValidateBeforePrinting(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiEpsValidateBeforePrinting(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        epsPrescriptions {
          accessCode
        }
        epsReferences {
          raiinNo
        }
        ptHokenPatterns {
          hokenId
        }
        raiinInf {
          raiinNo
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiEpsValidateBeforePrintingQuery__
 *
 * To run a query within a React component, call `useGetApiEpsValidateBeforePrintingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiEpsValidateBeforePrintingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiEpsValidateBeforePrintingQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiEpsValidateBeforePrintingQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiEpsValidateBeforePrintingQuery,
    GetApiEpsValidateBeforePrintingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiEpsValidateBeforePrintingQuery,
    GetApiEpsValidateBeforePrintingQueryVariables
  >(GetApiEpsValidateBeforePrintingDocument, options);
}
export function useGetApiEpsValidateBeforePrintingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiEpsValidateBeforePrintingQuery,
    GetApiEpsValidateBeforePrintingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiEpsValidateBeforePrintingQuery,
    GetApiEpsValidateBeforePrintingQueryVariables
  >(GetApiEpsValidateBeforePrintingDocument, options);
}
export function useGetApiEpsValidateBeforePrintingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiEpsValidateBeforePrintingQuery,
    GetApiEpsValidateBeforePrintingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiEpsValidateBeforePrintingQuery,
    GetApiEpsValidateBeforePrintingQueryVariables
  >(GetApiEpsValidateBeforePrintingDocument, options);
}
export type GetApiEpsValidateBeforePrintingQueryHookResult = ReturnType<
  typeof useGetApiEpsValidateBeforePrintingQuery
>;
export type GetApiEpsValidateBeforePrintingLazyQueryHookResult = ReturnType<
  typeof useGetApiEpsValidateBeforePrintingLazyQuery
>;
export type GetApiEpsValidateBeforePrintingSuspenseQueryHookResult = ReturnType<
  typeof useGetApiEpsValidateBeforePrintingSuspenseQuery
>;
export type GetApiEpsValidateBeforePrintingQueryResult = Apollo.QueryResult<
  GetApiEpsValidateBeforePrintingQuery,
  GetApiEpsValidateBeforePrintingQueryVariables
>;
export const GetApiMedicalExaminationGetKensaAuditTrailLogDocument = gql`
  query getApiMedicalExaminationGetKensaAuditTrailLog(
    $eventCd: String!
    $isOperator: Int!
    $operatorName: String
    $ptId: BigInt!
    $raiinNo: BigInt!
    $sinDate: Int!
  ) {
    getApiMedicalExaminationGetKensaAuditTrailLog(
      eventCd: $eventCd
      isOperator: $isOperator
      operatorName: $operatorName
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        auditTrailLogItems {
          eventCd
          hosuke
          hpId
          isOperator
          logDate
          logId
          machine
          operatorName
          ptId
          raiinNo
          sinDate
          userId
        }
      }
      status
      message
    }
  }
`;

/**
 * __useGetApiMedicalExaminationGetKensaAuditTrailLogQuery__
 *
 * To run a query within a React component, call `useGetApiMedicalExaminationGetKensaAuditTrailLogQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMedicalExaminationGetKensaAuditTrailLogQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMedicalExaminationGetKensaAuditTrailLogQuery({
 *   variables: {
 *      eventCd: // value for 'eventCd'
 *      isOperator: // value for 'isOperator'
 *      operatorName: // value for 'operatorName'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiMedicalExaminationGetKensaAuditTrailLogQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiMedicalExaminationGetKensaAuditTrailLogQuery,
    GetApiMedicalExaminationGetKensaAuditTrailLogQueryVariables
  > &
    (
      | {
          variables: GetApiMedicalExaminationGetKensaAuditTrailLogQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMedicalExaminationGetKensaAuditTrailLogQuery,
    GetApiMedicalExaminationGetKensaAuditTrailLogQueryVariables
  >(GetApiMedicalExaminationGetKensaAuditTrailLogDocument, options);
}
export function useGetApiMedicalExaminationGetKensaAuditTrailLogLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMedicalExaminationGetKensaAuditTrailLogQuery,
    GetApiMedicalExaminationGetKensaAuditTrailLogQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMedicalExaminationGetKensaAuditTrailLogQuery,
    GetApiMedicalExaminationGetKensaAuditTrailLogQueryVariables
  >(GetApiMedicalExaminationGetKensaAuditTrailLogDocument, options);
}
export function useGetApiMedicalExaminationGetKensaAuditTrailLogSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMedicalExaminationGetKensaAuditTrailLogQuery,
    GetApiMedicalExaminationGetKensaAuditTrailLogQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMedicalExaminationGetKensaAuditTrailLogQuery,
    GetApiMedicalExaminationGetKensaAuditTrailLogQueryVariables
  >(GetApiMedicalExaminationGetKensaAuditTrailLogDocument, options);
}
export type GetApiMedicalExaminationGetKensaAuditTrailLogQueryHookResult =
  ReturnType<typeof useGetApiMedicalExaminationGetKensaAuditTrailLogQuery>;
export type GetApiMedicalExaminationGetKensaAuditTrailLogLazyQueryHookResult =
  ReturnType<typeof useGetApiMedicalExaminationGetKensaAuditTrailLogLazyQuery>;
export type GetApiMedicalExaminationGetKensaAuditTrailLogSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiMedicalExaminationGetKensaAuditTrailLogSuspenseQuery
  >;
export type GetApiMedicalExaminationGetKensaAuditTrailLogQueryResult =
  Apollo.QueryResult<
    GetApiMedicalExaminationGetKensaAuditTrailLogQuery,
    GetApiMedicalExaminationGetKensaAuditTrailLogQueryVariables
  >;
