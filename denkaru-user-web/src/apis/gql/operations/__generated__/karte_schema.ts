import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiSchemaGetListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiSchemaGetListQuery = {
  __typename?: "query_root";
  getApiSchemaGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSchemaGetListImageTemplatesResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSchemaGetListImageTemplatesResponse";
      data?: Array<{
        __typename?: "UseCaseSchemaGetListImageTemplatesGetListImageTemplatesOutputItem";
        folderName?: string;
        listItems?: Array<{
          __typename?: "UseCaseSchemaGetListImageTemplatesGetListImageTemplatesOutputItemImageItem";
          fileName?: string;
          key?: string;
          url?: string;
        }>;
      }>;
    };
  };
};

export const GetApiSchemaGetListDocument = gql`
  query getApiSchemaGetList {
    getApiSchemaGetList {
      data {
        data {
          folderName
          listItems {
            fileName
            key
            url
          }
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiSchemaGetListQuery__
 *
 * To run a query within a React component, call `useGetApiSchemaGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiSchemaGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiSchemaGetListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiSchemaGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiSchemaGetListQuery,
    GetApiSchemaGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiSchemaGetListQuery,
    GetApiSchemaGetListQueryVariables
  >(GetApiSchemaGetListDocument, options);
}
export function useGetApiSchemaGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiSchemaGetListQuery,
    GetApiSchemaGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiSchemaGetListQuery,
    GetApiSchemaGetListQueryVariables
  >(GetApiSchemaGetListDocument, options);
}
export function useGetApiSchemaGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiSchemaGetListQuery,
    GetApiSchemaGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiSchemaGetListQuery,
    GetApiSchemaGetListQueryVariables
  >(GetApiSchemaGetListDocument, options);
}
export type GetApiSchemaGetListQueryHookResult = ReturnType<
  typeof useGetApiSchemaGetListQuery
>;
export type GetApiSchemaGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiSchemaGetListLazyQuery
>;
export type GetApiSchemaGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiSchemaGetListSuspenseQuery
>;
export type GetApiSchemaGetListQueryResult = Apollo.QueryResult<
  GetApiSchemaGetListQuery,
  GetApiSchemaGetListQueryVariables
>;
