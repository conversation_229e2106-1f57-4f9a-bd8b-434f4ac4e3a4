import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetClinicPaymentListQueryVariables = Types.Exact<{
  input: Types.GetClinicPaymentListInput;
}>;

export type GetClinicPaymentListQuery = {
  __typename?: "query_root";
  getClinicPaymentList: Array<{
    __typename?: "ClinicPayment";
    reserveDetailId: number;
    paymentClinicDetailId: string;
    paymentStatus: number;
    paymentType: number;
    billingAmount: number;
    depositAmount: number;
    paymentDate: string;
    examDate: string;
    treatmentCategoryName: string;
    treatmentDepartmentName: string;
  }>;
};

export type RequestClinicPaymentMutationVariables = Types.Exact<{
  input: Types.RequestClinicPaymentInput;
}>;

export type RequestClinicPaymentMutation = {
  __typename?: "mutation_root";
  requestClinicPayment: {
    __typename?: "RequestPaymentRes";
    isSuccess: boolean;
  };
};

export type UpdateClinicPaymentMutationVariables = Types.Exact<{
  input: Types.RequestClinicPaymentInput;
}>;

export type UpdateClinicPaymentMutation = {
  __typename?: "mutation_root";
  updateClinicPayment: { __typename?: "RequestPaymentRes"; isSuccess: boolean };
};

export type CancelClinicPaymentMutationVariables = Types.Exact<{
  input: Types.CancelClinicPaymentInput;
}>;

export type CancelClinicPaymentMutation = {
  __typename?: "mutation_root";
  cancelClinicPayment: { __typename?: "RequestPaymentRes"; isSuccess: boolean };
};

export type StopBillingClinicPaymentMutationVariables = Types.Exact<{
  input: Types.StopBillingClinicPaymentInput;
}>;

export type StopBillingClinicPaymentMutation = {
  __typename?: "mutation_root";
  stopBillingClinicPayment: {
    __typename?: "RequestPaymentRes";
    isSuccess: boolean;
  };
};

export type GetPharmacyPaymentQueryVariables = Types.Exact<{
  input: Types.GetPharmacyPaymentInput;
}>;

export type GetPharmacyPaymentQuery = {
  __typename?: "query_root";
  getPharmacyPayment: {
    __typename?: "GetPharmacyPaymentRes";
    paymentPharmacyDetailId: string;
    paymentStatus: number;
    paymentType: number;
    medicationCost: number;
    deliveryFee: number;
    errorMessage: string;
    firstSuccessDate?: string;
  };
};

export type RequestPharmacyPaymentMutationVariables = Types.Exact<{
  input: Types.RequestPharmacyPaymentInput;
}>;

export type RequestPharmacyPaymentMutation = {
  __typename?: "mutation_root";
  requestPharmacyPayment: {
    __typename?: "RequestPaymentRes";
    isSuccess: boolean;
  };
};

export type UpdatePharmacyPaymentMutationVariables = Types.Exact<{
  input: Types.RequestPharmacyPaymentInput;
}>;

export type UpdatePharmacyPaymentMutation = {
  __typename?: "mutation_root";
  updatePharmacyPayment: {
    __typename?: "RequestPaymentRes";
    isSuccess: boolean;
  };
};

export type CancelPharmacyPaymentMutationVariables = Types.Exact<{
  input: Types.CancelPharmacyPaymentInput;
}>;

export type CancelPharmacyPaymentMutation = {
  __typename?: "mutation_root";
  cancelPharmacyPayment: {
    __typename?: "RequestPaymentRes";
    isSuccess: boolean;
  };
};

export type StopBillingPharmacyPaymentMutationVariables = Types.Exact<{
  input: Types.StopBillingPharmacyPaymentInput;
}>;

export type StopBillingPharmacyPaymentMutation = {
  __typename?: "mutation_root";
  stopBillingPharmacyPayment: {
    __typename?: "RequestPaymentRes";
    isSuccess: boolean;
  };
};

export type GetFinCodeTenantShopContractQueryVariables = Types.Exact<{
  input: Types.GetFinCodeTenantShopContractInput;
}>;

export type GetFinCodeTenantShopContractQuery = {
  __typename?: "query_root";
  getFinCodeTenantShopContract: {
    __typename?: "FinCodeTenantShopContract";
    statusCode: number;
    shopName: string;
  };
};

export type GetHpFincodeInfoByHospitalQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetHpFincodeInfoByHospitalQuery = {
  __typename?: "query_root";
  getHpFincodeInfoByHospital: {
    __typename?: "HpFincodeInfo";
    hpTenantShopId: string;
    platformId: number;
    isFincodeRelation: boolean;
    isHtFincodeRelation: boolean;
    isFincodeRelationAvailableStatus: boolean;
    isInsuranceMedicalInstitution: boolean;
  };
};

export type RegistFincodeShopIdMutationVariables = Types.Exact<{
  input: Types.RegistFincodeShopIdInput;
}>;

export type RegistFincodeShopIdMutation = {
  __typename?: "mutation_root";
  registFincodeShopId: boolean;
};

export type IsPaymentMethodRegisteredForPatientQueryVariables = Types.Exact<{
  input: Types.IsPaymentMethodRegisteredForPatientInput;
}>;

export type IsPaymentMethodRegisteredForPatientQuery = {
  __typename?: "query_root";
  isPaymentMethodRegisteredForPatient: boolean;
};

export type GetApiAccountingGetSystemConfigQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
  sumAdjust: Types.Scalars["Int"]["input"];
}>;

export type GetApiAccountingGetSystemConfigQuery = {
  __typename?: "query_root";
  getApiAccountingGetSystemConfig?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingGetAccountingConfigResponse";
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingGetAccountingConfigResponse";
      accountingConfig?: {
        __typename?: "UseCaseAccountingGetAccountingSystemConfAccountingConfigDto";
        isVisiblePrintDrgLabel?: boolean;
        isCheckedPrintDrgLabel?: boolean;
        isCheckedPrintDetail?: boolean;
        isVisiblePrintOutDrg?: boolean;
        isCheckedPrintOutDrg?: boolean;
        isCheckedPrintReceipt?: boolean;
        isVisiblePrintDrgInf?: boolean;
        isCheckedPrintDrgInf?: boolean;
        isVisiblePrintDrgNote?: boolean;
        isCheckedPrintDrgNote?: boolean;
        isRyosyoDetail?: boolean;
      };
    };
  };
};

export type PostApiAccountingCheckAccountingMutationVariables = Types.Exact<{
  emrCloudApiRequestsAccountingCheckAccountingStatusRequestInput: Types.EmrCloudApiRequestsAccountingCheckAccountingStatusRequestInput;
}>;

export type PostApiAccountingCheckAccountingMutation = {
  __typename?: "mutation_root";
  postApiAccountingCheckAccounting?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingCheckAccountingStatusResponse";
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingCheckAccountingStatusResponse";
      errorType?: string;
      message?: string;
    };
  };
};

export type PostApiAccountingSaveAccountingMutationVariables = Types.Exact<{
  emrCloudApiRequestsAccountingSaveAccountingRequestInput: Types.EmrCloudApiRequestsAccountingSaveAccountingRequestInput;
}>;

export type PostApiAccountingSaveAccountingMutation = {
  __typename?: "mutation_root";
  postApiAccountingSaveAccounting?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingSaveAccountingResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingSaveAccountingResponse";
      success?: boolean;
    };
  };
};

export type GetApiAccountDueGetListQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
}>;

export type GetApiAccountDueGetListQuery = {
  __typename?: "query_root";
  getApiAccountDueGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountDueGetAccountDueListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesAccountDueGetAccountDueListResponse";
      listPaymentMethod?: any;
      listUketsukeSbt?: any;
      accountDueList?: Array<{
        __typename?: "EmrCloudApiResponsesAccountDueAccountDueDto";
        adjustFutan?: number;
        hokenPatternName?: string;
        hokenPid?: number;
        hpId?: number;
        isMenjo?: boolean;
        isNewAdjustFutanDisplay?: boolean;
        isNotPayment?: boolean;
        isSeikyuRow?: boolean;
        isShinSeikyuGaku?: boolean;
        kaDisplay?: string;
        month?: number;
        newAdjustFutan?: number;
        newAdjustFutanDisplay?: string;
        newSeikyuDetail?: string;
        newSeikyuGaku?: number;
        newSeikyuGakuDisplay?: string;
        newSeikyuTensu?: number;
        nyukinCmt?: string;
        nyukinDate?: number;
        nyukinGaku?: number;
        nyukinKbn?: number;
        oyaRaiinNo?: string;
        paymentMethodCd?: number;
        ptId?: string;
        raiinInfStatus?: number;
        raiinNo?: string;
        seikyuAdjustFutan?: number;
        seikyuAdjustFutanDisplay?: string;
        seikyuDetail?: string;
        seikyuGaku?: number;
        seikyuGakuDisplay?: string;
        seikyuSinDate?: number;
        seikyuTensu?: number;
        seqNo?: string;
        sinDateDisplay?: string;
        sortNo?: number;
        stateDisplay?: string;
        uketukeSbt?: number;
        unPaid?: number;
        treatmentDepartmentId?: number;
      }>;
    };
  };
};

export type PostApiAccountDueSaveListMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsAccountDueSaveAccountDueListRequestInput;
}>;

export type PostApiAccountDueSaveListMutation = {
  __typename?: "mutation_root";
  postApiAccountDueSaveList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountDueSaveAccountDueListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesAccountDueSaveAccountDueListResponse";
      accountDueList?: Array<{
        __typename?: "EmrCloudApiResponsesAccountDueAccountDueDto";
        adjustFutan?: number;
        hokenPatternName?: string;
        hokenPid?: number;
        hpId?: number;
        isMenjo?: boolean;
        isNewAdjustFutanDisplay?: boolean;
        isNotPayment?: boolean;
        isSeikyuRow?: boolean;
        isShinSeikyuGaku?: boolean;
        kaDisplay?: string;
        month?: number;
        newAdjustFutan?: number;
        newAdjustFutanDisplay?: string;
        newSeikyuDetail?: string;
        newSeikyuGaku?: number;
        newSeikyuGakuDisplay?: string;
        newSeikyuTensu?: number;
        nyukinCmt?: string;
        nyukinDate?: number;
        nyukinGaku?: number;
        nyukinKbn?: number;
        oyaRaiinNo?: string;
        paymentMethodCd?: number;
        ptId?: string;
        raiinInfStatus?: number;
        raiinNo?: string;
        seikyuAdjustFutan?: number;
        seikyuAdjustFutanDisplay?: string;
        seikyuDetail?: string;
        seikyuGaku?: number;
        seikyuGakuDisplay?: string;
        seikyuSinDate?: number;
        seikyuTensu?: number;
        seqNo?: string;
        sinDateDisplay?: string;
        sortNo?: number;
        stateDisplay?: string;
        uketukeSbt?: number;
        unPaid?: number;
      }>;
    };
  };
};

export const GetClinicPaymentListDocument = gql`
  query getClinicPaymentList($input: GetClinicPaymentListInput!) {
    getClinicPaymentList(input: $input) {
      reserveDetailId
      paymentClinicDetailId
      paymentStatus
      paymentType
      billingAmount
      depositAmount
      paymentDate
      examDate
      treatmentCategoryName
      treatmentDepartmentName
    }
  }
`;

/**
 * __useGetClinicPaymentListQuery__
 *
 * To run a query within a React component, call `useGetClinicPaymentListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetClinicPaymentListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetClinicPaymentListQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetClinicPaymentListQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetClinicPaymentListQuery,
    GetClinicPaymentListQueryVariables
  > &
    (
      | { variables: GetClinicPaymentListQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetClinicPaymentListQuery,
    GetClinicPaymentListQueryVariables
  >(GetClinicPaymentListDocument, options);
}
export function useGetClinicPaymentListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetClinicPaymentListQuery,
    GetClinicPaymentListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetClinicPaymentListQuery,
    GetClinicPaymentListQueryVariables
  >(GetClinicPaymentListDocument, options);
}
export function useGetClinicPaymentListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetClinicPaymentListQuery,
    GetClinicPaymentListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetClinicPaymentListQuery,
    GetClinicPaymentListQueryVariables
  >(GetClinicPaymentListDocument, options);
}
export type GetClinicPaymentListQueryHookResult = ReturnType<
  typeof useGetClinicPaymentListQuery
>;
export type GetClinicPaymentListLazyQueryHookResult = ReturnType<
  typeof useGetClinicPaymentListLazyQuery
>;
export type GetClinicPaymentListSuspenseQueryHookResult = ReturnType<
  typeof useGetClinicPaymentListSuspenseQuery
>;
export type GetClinicPaymentListQueryResult = Apollo.QueryResult<
  GetClinicPaymentListQuery,
  GetClinicPaymentListQueryVariables
>;
export const RequestClinicPaymentDocument = gql`
  mutation requestClinicPayment($input: RequestClinicPaymentInput!) {
    requestClinicPayment(input: $input) {
      isSuccess
    }
  }
`;
export type RequestClinicPaymentMutationFn = Apollo.MutationFunction<
  RequestClinicPaymentMutation,
  RequestClinicPaymentMutationVariables
>;

/**
 * __useRequestClinicPaymentMutation__
 *
 * To run a mutation, you first call `useRequestClinicPaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRequestClinicPaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [requestClinicPaymentMutation, { data, loading, error }] = useRequestClinicPaymentMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useRequestClinicPaymentMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RequestClinicPaymentMutation,
    RequestClinicPaymentMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    RequestClinicPaymentMutation,
    RequestClinicPaymentMutationVariables
  >(RequestClinicPaymentDocument, options);
}
export type RequestClinicPaymentMutationHookResult = ReturnType<
  typeof useRequestClinicPaymentMutation
>;
export type RequestClinicPaymentMutationResult =
  Apollo.MutationResult<RequestClinicPaymentMutation>;
export type RequestClinicPaymentMutationOptions = Apollo.BaseMutationOptions<
  RequestClinicPaymentMutation,
  RequestClinicPaymentMutationVariables
>;
export const UpdateClinicPaymentDocument = gql`
  mutation updateClinicPayment($input: RequestClinicPaymentInput!) {
    updateClinicPayment(input: $input) {
      isSuccess
    }
  }
`;
export type UpdateClinicPaymentMutationFn = Apollo.MutationFunction<
  UpdateClinicPaymentMutation,
  UpdateClinicPaymentMutationVariables
>;

/**
 * __useUpdateClinicPaymentMutation__
 *
 * To run a mutation, you first call `useUpdateClinicPaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateClinicPaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateClinicPaymentMutation, { data, loading, error }] = useUpdateClinicPaymentMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateClinicPaymentMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateClinicPaymentMutation,
    UpdateClinicPaymentMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateClinicPaymentMutation,
    UpdateClinicPaymentMutationVariables
  >(UpdateClinicPaymentDocument, options);
}
export type UpdateClinicPaymentMutationHookResult = ReturnType<
  typeof useUpdateClinicPaymentMutation
>;
export type UpdateClinicPaymentMutationResult =
  Apollo.MutationResult<UpdateClinicPaymentMutation>;
export type UpdateClinicPaymentMutationOptions = Apollo.BaseMutationOptions<
  UpdateClinicPaymentMutation,
  UpdateClinicPaymentMutationVariables
>;
export const CancelClinicPaymentDocument = gql`
  mutation cancelClinicPayment($input: CancelClinicPaymentInput!) {
    cancelClinicPayment(input: $input) {
      isSuccess
    }
  }
`;
export type CancelClinicPaymentMutationFn = Apollo.MutationFunction<
  CancelClinicPaymentMutation,
  CancelClinicPaymentMutationVariables
>;

/**
 * __useCancelClinicPaymentMutation__
 *
 * To run a mutation, you first call `useCancelClinicPaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCancelClinicPaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [cancelClinicPaymentMutation, { data, loading, error }] = useCancelClinicPaymentMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCancelClinicPaymentMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CancelClinicPaymentMutation,
    CancelClinicPaymentMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CancelClinicPaymentMutation,
    CancelClinicPaymentMutationVariables
  >(CancelClinicPaymentDocument, options);
}
export type CancelClinicPaymentMutationHookResult = ReturnType<
  typeof useCancelClinicPaymentMutation
>;
export type CancelClinicPaymentMutationResult =
  Apollo.MutationResult<CancelClinicPaymentMutation>;
export type CancelClinicPaymentMutationOptions = Apollo.BaseMutationOptions<
  CancelClinicPaymentMutation,
  CancelClinicPaymentMutationVariables
>;
export const StopBillingClinicPaymentDocument = gql`
  mutation stopBillingClinicPayment($input: StopBillingClinicPaymentInput!) {
    stopBillingClinicPayment(input: $input) {
      isSuccess
    }
  }
`;
export type StopBillingClinicPaymentMutationFn = Apollo.MutationFunction<
  StopBillingClinicPaymentMutation,
  StopBillingClinicPaymentMutationVariables
>;

/**
 * __useStopBillingClinicPaymentMutation__
 *
 * To run a mutation, you first call `useStopBillingClinicPaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useStopBillingClinicPaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [stopBillingClinicPaymentMutation, { data, loading, error }] = useStopBillingClinicPaymentMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useStopBillingClinicPaymentMutation(
  baseOptions?: Apollo.MutationHookOptions<
    StopBillingClinicPaymentMutation,
    StopBillingClinicPaymentMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    StopBillingClinicPaymentMutation,
    StopBillingClinicPaymentMutationVariables
  >(StopBillingClinicPaymentDocument, options);
}
export type StopBillingClinicPaymentMutationHookResult = ReturnType<
  typeof useStopBillingClinicPaymentMutation
>;
export type StopBillingClinicPaymentMutationResult =
  Apollo.MutationResult<StopBillingClinicPaymentMutation>;
export type StopBillingClinicPaymentMutationOptions =
  Apollo.BaseMutationOptions<
    StopBillingClinicPaymentMutation,
    StopBillingClinicPaymentMutationVariables
  >;
export const GetPharmacyPaymentDocument = gql`
  query getPharmacyPayment($input: GetPharmacyPaymentInput!) {
    getPharmacyPayment(input: $input) {
      paymentPharmacyDetailId
      paymentStatus
      paymentType
      medicationCost
      deliveryFee
      errorMessage
      firstSuccessDate
    }
  }
`;

/**
 * __useGetPharmacyPaymentQuery__
 *
 * To run a query within a React component, call `useGetPharmacyPaymentQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPharmacyPaymentQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPharmacyPaymentQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPharmacyPaymentQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPharmacyPaymentQuery,
    GetPharmacyPaymentQueryVariables
  > &
    (
      | { variables: GetPharmacyPaymentQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPharmacyPaymentQuery,
    GetPharmacyPaymentQueryVariables
  >(GetPharmacyPaymentDocument, options);
}
export function useGetPharmacyPaymentLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPharmacyPaymentQuery,
    GetPharmacyPaymentQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPharmacyPaymentQuery,
    GetPharmacyPaymentQueryVariables
  >(GetPharmacyPaymentDocument, options);
}
export function useGetPharmacyPaymentSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPharmacyPaymentQuery,
    GetPharmacyPaymentQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPharmacyPaymentQuery,
    GetPharmacyPaymentQueryVariables
  >(GetPharmacyPaymentDocument, options);
}
export type GetPharmacyPaymentQueryHookResult = ReturnType<
  typeof useGetPharmacyPaymentQuery
>;
export type GetPharmacyPaymentLazyQueryHookResult = ReturnType<
  typeof useGetPharmacyPaymentLazyQuery
>;
export type GetPharmacyPaymentSuspenseQueryHookResult = ReturnType<
  typeof useGetPharmacyPaymentSuspenseQuery
>;
export type GetPharmacyPaymentQueryResult = Apollo.QueryResult<
  GetPharmacyPaymentQuery,
  GetPharmacyPaymentQueryVariables
>;
export const RequestPharmacyPaymentDocument = gql`
  mutation requestPharmacyPayment($input: RequestPharmacyPaymentInput!) {
    requestPharmacyPayment(input: $input) {
      isSuccess
    }
  }
`;
export type RequestPharmacyPaymentMutationFn = Apollo.MutationFunction<
  RequestPharmacyPaymentMutation,
  RequestPharmacyPaymentMutationVariables
>;

/**
 * __useRequestPharmacyPaymentMutation__
 *
 * To run a mutation, you first call `useRequestPharmacyPaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRequestPharmacyPaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [requestPharmacyPaymentMutation, { data, loading, error }] = useRequestPharmacyPaymentMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useRequestPharmacyPaymentMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RequestPharmacyPaymentMutation,
    RequestPharmacyPaymentMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    RequestPharmacyPaymentMutation,
    RequestPharmacyPaymentMutationVariables
  >(RequestPharmacyPaymentDocument, options);
}
export type RequestPharmacyPaymentMutationHookResult = ReturnType<
  typeof useRequestPharmacyPaymentMutation
>;
export type RequestPharmacyPaymentMutationResult =
  Apollo.MutationResult<RequestPharmacyPaymentMutation>;
export type RequestPharmacyPaymentMutationOptions = Apollo.BaseMutationOptions<
  RequestPharmacyPaymentMutation,
  RequestPharmacyPaymentMutationVariables
>;
export const UpdatePharmacyPaymentDocument = gql`
  mutation updatePharmacyPayment($input: RequestPharmacyPaymentInput!) {
    updatePharmacyPayment(input: $input) {
      isSuccess
    }
  }
`;
export type UpdatePharmacyPaymentMutationFn = Apollo.MutationFunction<
  UpdatePharmacyPaymentMutation,
  UpdatePharmacyPaymentMutationVariables
>;

/**
 * __useUpdatePharmacyPaymentMutation__
 *
 * To run a mutation, you first call `useUpdatePharmacyPaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePharmacyPaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePharmacyPaymentMutation, { data, loading, error }] = useUpdatePharmacyPaymentMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePharmacyPaymentMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePharmacyPaymentMutation,
    UpdatePharmacyPaymentMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePharmacyPaymentMutation,
    UpdatePharmacyPaymentMutationVariables
  >(UpdatePharmacyPaymentDocument, options);
}
export type UpdatePharmacyPaymentMutationHookResult = ReturnType<
  typeof useUpdatePharmacyPaymentMutation
>;
export type UpdatePharmacyPaymentMutationResult =
  Apollo.MutationResult<UpdatePharmacyPaymentMutation>;
export type UpdatePharmacyPaymentMutationOptions = Apollo.BaseMutationOptions<
  UpdatePharmacyPaymentMutation,
  UpdatePharmacyPaymentMutationVariables
>;
export const CancelPharmacyPaymentDocument = gql`
  mutation cancelPharmacyPayment($input: CancelPharmacyPaymentInput!) {
    cancelPharmacyPayment(input: $input) {
      isSuccess
    }
  }
`;
export type CancelPharmacyPaymentMutationFn = Apollo.MutationFunction<
  CancelPharmacyPaymentMutation,
  CancelPharmacyPaymentMutationVariables
>;

/**
 * __useCancelPharmacyPaymentMutation__
 *
 * To run a mutation, you first call `useCancelPharmacyPaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCancelPharmacyPaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [cancelPharmacyPaymentMutation, { data, loading, error }] = useCancelPharmacyPaymentMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCancelPharmacyPaymentMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CancelPharmacyPaymentMutation,
    CancelPharmacyPaymentMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CancelPharmacyPaymentMutation,
    CancelPharmacyPaymentMutationVariables
  >(CancelPharmacyPaymentDocument, options);
}
export type CancelPharmacyPaymentMutationHookResult = ReturnType<
  typeof useCancelPharmacyPaymentMutation
>;
export type CancelPharmacyPaymentMutationResult =
  Apollo.MutationResult<CancelPharmacyPaymentMutation>;
export type CancelPharmacyPaymentMutationOptions = Apollo.BaseMutationOptions<
  CancelPharmacyPaymentMutation,
  CancelPharmacyPaymentMutationVariables
>;
export const StopBillingPharmacyPaymentDocument = gql`
  mutation stopBillingPharmacyPayment(
    $input: StopBillingPharmacyPaymentInput!
  ) {
    stopBillingPharmacyPayment(input: $input) {
      isSuccess
    }
  }
`;
export type StopBillingPharmacyPaymentMutationFn = Apollo.MutationFunction<
  StopBillingPharmacyPaymentMutation,
  StopBillingPharmacyPaymentMutationVariables
>;

/**
 * __useStopBillingPharmacyPaymentMutation__
 *
 * To run a mutation, you first call `useStopBillingPharmacyPaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useStopBillingPharmacyPaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [stopBillingPharmacyPaymentMutation, { data, loading, error }] = useStopBillingPharmacyPaymentMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useStopBillingPharmacyPaymentMutation(
  baseOptions?: Apollo.MutationHookOptions<
    StopBillingPharmacyPaymentMutation,
    StopBillingPharmacyPaymentMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    StopBillingPharmacyPaymentMutation,
    StopBillingPharmacyPaymentMutationVariables
  >(StopBillingPharmacyPaymentDocument, options);
}
export type StopBillingPharmacyPaymentMutationHookResult = ReturnType<
  typeof useStopBillingPharmacyPaymentMutation
>;
export type StopBillingPharmacyPaymentMutationResult =
  Apollo.MutationResult<StopBillingPharmacyPaymentMutation>;
export type StopBillingPharmacyPaymentMutationOptions =
  Apollo.BaseMutationOptions<
    StopBillingPharmacyPaymentMutation,
    StopBillingPharmacyPaymentMutationVariables
  >;
export const GetFinCodeTenantShopContractDocument = gql`
  query getFinCodeTenantShopContract(
    $input: GetFinCodeTenantShopContractInput!
  ) {
    getFinCodeTenantShopContract(input: $input) {
      statusCode
      shopName
    }
  }
`;

/**
 * __useGetFinCodeTenantShopContractQuery__
 *
 * To run a query within a React component, call `useGetFinCodeTenantShopContractQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetFinCodeTenantShopContractQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetFinCodeTenantShopContractQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetFinCodeTenantShopContractQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetFinCodeTenantShopContractQuery,
    GetFinCodeTenantShopContractQueryVariables
  > &
    (
      | {
          variables: GetFinCodeTenantShopContractQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetFinCodeTenantShopContractQuery,
    GetFinCodeTenantShopContractQueryVariables
  >(GetFinCodeTenantShopContractDocument, options);
}
export function useGetFinCodeTenantShopContractLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetFinCodeTenantShopContractQuery,
    GetFinCodeTenantShopContractQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetFinCodeTenantShopContractQuery,
    GetFinCodeTenantShopContractQueryVariables
  >(GetFinCodeTenantShopContractDocument, options);
}
export function useGetFinCodeTenantShopContractSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetFinCodeTenantShopContractQuery,
    GetFinCodeTenantShopContractQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetFinCodeTenantShopContractQuery,
    GetFinCodeTenantShopContractQueryVariables
  >(GetFinCodeTenantShopContractDocument, options);
}
export type GetFinCodeTenantShopContractQueryHookResult = ReturnType<
  typeof useGetFinCodeTenantShopContractQuery
>;
export type GetFinCodeTenantShopContractLazyQueryHookResult = ReturnType<
  typeof useGetFinCodeTenantShopContractLazyQuery
>;
export type GetFinCodeTenantShopContractSuspenseQueryHookResult = ReturnType<
  typeof useGetFinCodeTenantShopContractSuspenseQuery
>;
export type GetFinCodeTenantShopContractQueryResult = Apollo.QueryResult<
  GetFinCodeTenantShopContractQuery,
  GetFinCodeTenantShopContractQueryVariables
>;
export const GetHpFincodeInfoByHospitalDocument = gql`
  query getHpFincodeInfoByHospital {
    getHpFincodeInfoByHospital {
      hpTenantShopId
      platformId
      isFincodeRelation
      isHtFincodeRelation
      isFincodeRelationAvailableStatus
      isInsuranceMedicalInstitution
    }
  }
`;

/**
 * __useGetHpFincodeInfoByHospitalQuery__
 *
 * To run a query within a React component, call `useGetHpFincodeInfoByHospitalQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetHpFincodeInfoByHospitalQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetHpFincodeInfoByHospitalQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetHpFincodeInfoByHospitalQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetHpFincodeInfoByHospitalQuery,
    GetHpFincodeInfoByHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetHpFincodeInfoByHospitalQuery,
    GetHpFincodeInfoByHospitalQueryVariables
  >(GetHpFincodeInfoByHospitalDocument, options);
}
export function useGetHpFincodeInfoByHospitalLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetHpFincodeInfoByHospitalQuery,
    GetHpFincodeInfoByHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetHpFincodeInfoByHospitalQuery,
    GetHpFincodeInfoByHospitalQueryVariables
  >(GetHpFincodeInfoByHospitalDocument, options);
}
export function useGetHpFincodeInfoByHospitalSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetHpFincodeInfoByHospitalQuery,
    GetHpFincodeInfoByHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetHpFincodeInfoByHospitalQuery,
    GetHpFincodeInfoByHospitalQueryVariables
  >(GetHpFincodeInfoByHospitalDocument, options);
}
export type GetHpFincodeInfoByHospitalQueryHookResult = ReturnType<
  typeof useGetHpFincodeInfoByHospitalQuery
>;
export type GetHpFincodeInfoByHospitalLazyQueryHookResult = ReturnType<
  typeof useGetHpFincodeInfoByHospitalLazyQuery
>;
export type GetHpFincodeInfoByHospitalSuspenseQueryHookResult = ReturnType<
  typeof useGetHpFincodeInfoByHospitalSuspenseQuery
>;
export type GetHpFincodeInfoByHospitalQueryResult = Apollo.QueryResult<
  GetHpFincodeInfoByHospitalQuery,
  GetHpFincodeInfoByHospitalQueryVariables
>;
export const RegistFincodeShopIdDocument = gql`
  mutation registFincodeShopId($input: RegistFincodeShopIdInput!) {
    registFincodeShopId(input: $input)
  }
`;
export type RegistFincodeShopIdMutationFn = Apollo.MutationFunction<
  RegistFincodeShopIdMutation,
  RegistFincodeShopIdMutationVariables
>;

/**
 * __useRegistFincodeShopIdMutation__
 *
 * To run a mutation, you first call `useRegistFincodeShopIdMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRegistFincodeShopIdMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [registFincodeShopIdMutation, { data, loading, error }] = useRegistFincodeShopIdMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useRegistFincodeShopIdMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RegistFincodeShopIdMutation,
    RegistFincodeShopIdMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    RegistFincodeShopIdMutation,
    RegistFincodeShopIdMutationVariables
  >(RegistFincodeShopIdDocument, options);
}
export type RegistFincodeShopIdMutationHookResult = ReturnType<
  typeof useRegistFincodeShopIdMutation
>;
export type RegistFincodeShopIdMutationResult =
  Apollo.MutationResult<RegistFincodeShopIdMutation>;
export type RegistFincodeShopIdMutationOptions = Apollo.BaseMutationOptions<
  RegistFincodeShopIdMutation,
  RegistFincodeShopIdMutationVariables
>;
export const IsPaymentMethodRegisteredForPatientDocument = gql`
  query isPaymentMethodRegisteredForPatient(
    $input: IsPaymentMethodRegisteredForPatientInput!
  ) {
    isPaymentMethodRegisteredForPatient(input: $input)
  }
`;

/**
 * __useIsPaymentMethodRegisteredForPatientQuery__
 *
 * To run a query within a React component, call `useIsPaymentMethodRegisteredForPatientQuery` and pass it any options that fit your needs.
 * When your component renders, `useIsPaymentMethodRegisteredForPatientQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useIsPaymentMethodRegisteredForPatientQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useIsPaymentMethodRegisteredForPatientQuery(
  baseOptions: Apollo.QueryHookOptions<
    IsPaymentMethodRegisteredForPatientQuery,
    IsPaymentMethodRegisteredForPatientQueryVariables
  > &
    (
      | {
          variables: IsPaymentMethodRegisteredForPatientQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    IsPaymentMethodRegisteredForPatientQuery,
    IsPaymentMethodRegisteredForPatientQueryVariables
  >(IsPaymentMethodRegisteredForPatientDocument, options);
}
export function useIsPaymentMethodRegisteredForPatientLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    IsPaymentMethodRegisteredForPatientQuery,
    IsPaymentMethodRegisteredForPatientQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    IsPaymentMethodRegisteredForPatientQuery,
    IsPaymentMethodRegisteredForPatientQueryVariables
  >(IsPaymentMethodRegisteredForPatientDocument, options);
}
export function useIsPaymentMethodRegisteredForPatientSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    IsPaymentMethodRegisteredForPatientQuery,
    IsPaymentMethodRegisteredForPatientQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    IsPaymentMethodRegisteredForPatientQuery,
    IsPaymentMethodRegisteredForPatientQueryVariables
  >(IsPaymentMethodRegisteredForPatientDocument, options);
}
export type IsPaymentMethodRegisteredForPatientQueryHookResult = ReturnType<
  typeof useIsPaymentMethodRegisteredForPatientQuery
>;
export type IsPaymentMethodRegisteredForPatientLazyQueryHookResult = ReturnType<
  typeof useIsPaymentMethodRegisteredForPatientLazyQuery
>;
export type IsPaymentMethodRegisteredForPatientSuspenseQueryHookResult =
  ReturnType<typeof useIsPaymentMethodRegisteredForPatientSuspenseQuery>;
export type IsPaymentMethodRegisteredForPatientQueryResult = Apollo.QueryResult<
  IsPaymentMethodRegisteredForPatientQuery,
  IsPaymentMethodRegisteredForPatientQueryVariables
>;
export const GetApiAccountingGetSystemConfigDocument = gql`
  query getApiAccountingGetSystemConfig(
    $ptId: BigInt!
    $raiinNo: BigInt!
    $sumAdjust: Int!
  ) {
    getApiAccountingGetSystemConfig(
      ptId: $ptId
      raiinNo: $raiinNo
      sumAdjust: $sumAdjust
    ) {
      data {
        accountingConfig {
          isVisiblePrintDrgLabel
          isCheckedPrintDrgLabel
          isCheckedPrintDetail
          isVisiblePrintOutDrg
          isCheckedPrintOutDrg
          isCheckedPrintReceipt
          isCheckedPrintDetail
          isVisiblePrintDrgInf
          isCheckedPrintDrgInf
          isVisiblePrintDrgNote
          isCheckedPrintDrgNote
          isRyosyoDetail
        }
      }
      status
    }
  }
`;

/**
 * __useGetApiAccountingGetSystemConfigQuery__
 *
 * To run a query within a React component, call `useGetApiAccountingGetSystemConfigQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiAccountingGetSystemConfigQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiAccountingGetSystemConfigQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sumAdjust: // value for 'sumAdjust'
 *   },
 * });
 */
export function useGetApiAccountingGetSystemConfigQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiAccountingGetSystemConfigQuery,
    GetApiAccountingGetSystemConfigQueryVariables
  > &
    (
      | {
          variables: GetApiAccountingGetSystemConfigQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiAccountingGetSystemConfigQuery,
    GetApiAccountingGetSystemConfigQueryVariables
  >(GetApiAccountingGetSystemConfigDocument, options);
}
export function useGetApiAccountingGetSystemConfigLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiAccountingGetSystemConfigQuery,
    GetApiAccountingGetSystemConfigQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiAccountingGetSystemConfigQuery,
    GetApiAccountingGetSystemConfigQueryVariables
  >(GetApiAccountingGetSystemConfigDocument, options);
}
export function useGetApiAccountingGetSystemConfigSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiAccountingGetSystemConfigQuery,
    GetApiAccountingGetSystemConfigQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiAccountingGetSystemConfigQuery,
    GetApiAccountingGetSystemConfigQueryVariables
  >(GetApiAccountingGetSystemConfigDocument, options);
}
export type GetApiAccountingGetSystemConfigQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetSystemConfigQuery
>;
export type GetApiAccountingGetSystemConfigLazyQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetSystemConfigLazyQuery
>;
export type GetApiAccountingGetSystemConfigSuspenseQueryHookResult = ReturnType<
  typeof useGetApiAccountingGetSystemConfigSuspenseQuery
>;
export type GetApiAccountingGetSystemConfigQueryResult = Apollo.QueryResult<
  GetApiAccountingGetSystemConfigQuery,
  GetApiAccountingGetSystemConfigQueryVariables
>;
export const PostApiAccountingCheckAccountingDocument = gql`
  mutation postApiAccountingCheckAccounting(
    $emrCloudApiRequestsAccountingCheckAccountingStatusRequestInput: EmrCloudApiRequestsAccountingCheckAccountingStatusRequestInput!
  ) {
    postApiAccountingCheckAccounting(
      emrCloudApiRequestsAccountingCheckAccountingStatusRequestInput: $emrCloudApiRequestsAccountingCheckAccountingStatusRequestInput
    ) {
      data {
        errorType
        message
      }
      status
    }
  }
`;
export type PostApiAccountingCheckAccountingMutationFn =
  Apollo.MutationFunction<
    PostApiAccountingCheckAccountingMutation,
    PostApiAccountingCheckAccountingMutationVariables
  >;

/**
 * __usePostApiAccountingCheckAccountingMutation__
 *
 * To run a mutation, you first call `usePostApiAccountingCheckAccountingMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiAccountingCheckAccountingMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiAccountingCheckAccountingMutation, { data, loading, error }] = usePostApiAccountingCheckAccountingMutation({
 *   variables: {
 *      emrCloudApiRequestsAccountingCheckAccountingStatusRequestInput: // value for 'emrCloudApiRequestsAccountingCheckAccountingStatusRequestInput'
 *   },
 * });
 */
export function usePostApiAccountingCheckAccountingMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiAccountingCheckAccountingMutation,
    PostApiAccountingCheckAccountingMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiAccountingCheckAccountingMutation,
    PostApiAccountingCheckAccountingMutationVariables
  >(PostApiAccountingCheckAccountingDocument, options);
}
export type PostApiAccountingCheckAccountingMutationHookResult = ReturnType<
  typeof usePostApiAccountingCheckAccountingMutation
>;
export type PostApiAccountingCheckAccountingMutationResult =
  Apollo.MutationResult<PostApiAccountingCheckAccountingMutation>;
export type PostApiAccountingCheckAccountingMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiAccountingCheckAccountingMutation,
    PostApiAccountingCheckAccountingMutationVariables
  >;
export const PostApiAccountingSaveAccountingDocument = gql`
  mutation postApiAccountingSaveAccounting(
    $emrCloudApiRequestsAccountingSaveAccountingRequestInput: EmrCloudApiRequestsAccountingSaveAccountingRequestInput!
  ) {
    postApiAccountingSaveAccounting(
      emrCloudApiRequestsAccountingSaveAccountingRequestInput: $emrCloudApiRequestsAccountingSaveAccountingRequestInput
    ) {
      data {
        success
      }
      status
      message
    }
  }
`;
export type PostApiAccountingSaveAccountingMutationFn = Apollo.MutationFunction<
  PostApiAccountingSaveAccountingMutation,
  PostApiAccountingSaveAccountingMutationVariables
>;

/**
 * __usePostApiAccountingSaveAccountingMutation__
 *
 * To run a mutation, you first call `usePostApiAccountingSaveAccountingMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiAccountingSaveAccountingMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiAccountingSaveAccountingMutation, { data, loading, error }] = usePostApiAccountingSaveAccountingMutation({
 *   variables: {
 *      emrCloudApiRequestsAccountingSaveAccountingRequestInput: // value for 'emrCloudApiRequestsAccountingSaveAccountingRequestInput'
 *   },
 * });
 */
export function usePostApiAccountingSaveAccountingMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiAccountingSaveAccountingMutation,
    PostApiAccountingSaveAccountingMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiAccountingSaveAccountingMutation,
    PostApiAccountingSaveAccountingMutationVariables
  >(PostApiAccountingSaveAccountingDocument, options);
}
export type PostApiAccountingSaveAccountingMutationHookResult = ReturnType<
  typeof usePostApiAccountingSaveAccountingMutation
>;
export type PostApiAccountingSaveAccountingMutationResult =
  Apollo.MutationResult<PostApiAccountingSaveAccountingMutation>;
export type PostApiAccountingSaveAccountingMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiAccountingSaveAccountingMutation,
    PostApiAccountingSaveAccountingMutationVariables
  >;
export const GetApiAccountDueGetListDocument = gql`
  query getApiAccountDueGetList($ptId: BigInt!, $sinDate: Int!) {
    getApiAccountDueGetList(ptId: $ptId, sinDate: $sinDate) {
      data {
        accountDueList {
          adjustFutan
          hokenPatternName
          hokenPid
          hpId
          isMenjo
          isNewAdjustFutanDisplay
          isNotPayment
          isSeikyuRow
          isShinSeikyuGaku
          kaDisplay
          month
          newAdjustFutan
          newAdjustFutanDisplay
          newSeikyuDetail
          newSeikyuGaku
          newSeikyuGakuDisplay
          newSeikyuTensu
          nyukinCmt
          nyukinDate
          nyukinGaku
          nyukinKbn
          oyaRaiinNo
          paymentMethodCd
          ptId
          raiinInfStatus
          raiinNo
          seikyuAdjustFutan
          seikyuAdjustFutanDisplay
          seikyuDetail
          seikyuGaku
          seikyuGakuDisplay
          seikyuSinDate
          seikyuTensu
          seqNo
          sinDateDisplay
          sortNo
          stateDisplay
          uketukeSbt
          unPaid
          treatmentDepartmentId
        }
        listPaymentMethod
        listUketsukeSbt
      }
    }
  }
`;

/**
 * __useGetApiAccountDueGetListQuery__
 *
 * To run a query within a React component, call `useGetApiAccountDueGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiAccountDueGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiAccountDueGetListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiAccountDueGetListQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiAccountDueGetListQuery,
    GetApiAccountDueGetListQueryVariables
  > &
    (
      | { variables: GetApiAccountDueGetListQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiAccountDueGetListQuery,
    GetApiAccountDueGetListQueryVariables
  >(GetApiAccountDueGetListDocument, options);
}
export function useGetApiAccountDueGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiAccountDueGetListQuery,
    GetApiAccountDueGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiAccountDueGetListQuery,
    GetApiAccountDueGetListQueryVariables
  >(GetApiAccountDueGetListDocument, options);
}
export function useGetApiAccountDueGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiAccountDueGetListQuery,
    GetApiAccountDueGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiAccountDueGetListQuery,
    GetApiAccountDueGetListQueryVariables
  >(GetApiAccountDueGetListDocument, options);
}
export type GetApiAccountDueGetListQueryHookResult = ReturnType<
  typeof useGetApiAccountDueGetListQuery
>;
export type GetApiAccountDueGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiAccountDueGetListLazyQuery
>;
export type GetApiAccountDueGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiAccountDueGetListSuspenseQuery
>;
export type GetApiAccountDueGetListQueryResult = Apollo.QueryResult<
  GetApiAccountDueGetListQuery,
  GetApiAccountDueGetListQueryVariables
>;
export const PostApiAccountDueSaveListDocument = gql`
  mutation postApiAccountDueSaveList(
    $input: EmrCloudApiRequestsAccountDueSaveAccountDueListRequestInput!
  ) {
    postApiAccountDueSaveList(
      emrCloudApiRequestsAccountDueSaveAccountDueListRequestInput: $input
    ) {
      data {
        accountDueList {
          adjustFutan
          hokenPatternName
          hokenPid
          hpId
          isMenjo
          isNewAdjustFutanDisplay
          isNotPayment
          isSeikyuRow
          isShinSeikyuGaku
          kaDisplay
          month
          newAdjustFutan
          newAdjustFutanDisplay
          newSeikyuDetail
          newSeikyuGaku
          newSeikyuGakuDisplay
          newSeikyuTensu
          nyukinCmt
          nyukinDate
          nyukinGaku
          nyukinKbn
          oyaRaiinNo
          paymentMethodCd
          ptId
          raiinInfStatus
          raiinNo
          seikyuAdjustFutan
          seikyuAdjustFutanDisplay
          seikyuDetail
          seikyuGaku
          seikyuGakuDisplay
          seikyuSinDate
          seikyuTensu
          seqNo
          sinDateDisplay
          sortNo
          stateDisplay
          uketukeSbt
          unPaid
        }
      }
    }
  }
`;
export type PostApiAccountDueSaveListMutationFn = Apollo.MutationFunction<
  PostApiAccountDueSaveListMutation,
  PostApiAccountDueSaveListMutationVariables
>;

/**
 * __usePostApiAccountDueSaveListMutation__
 *
 * To run a mutation, you first call `usePostApiAccountDueSaveListMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiAccountDueSaveListMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiAccountDueSaveListMutation, { data, loading, error }] = usePostApiAccountDueSaveListMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiAccountDueSaveListMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiAccountDueSaveListMutation,
    PostApiAccountDueSaveListMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiAccountDueSaveListMutation,
    PostApiAccountDueSaveListMutationVariables
  >(PostApiAccountDueSaveListDocument, options);
}
export type PostApiAccountDueSaveListMutationHookResult = ReturnType<
  typeof usePostApiAccountDueSaveListMutation
>;
export type PostApiAccountDueSaveListMutationResult =
  Apollo.MutationResult<PostApiAccountDueSaveListMutation>;
export type PostApiAccountDueSaveListMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiAccountDueSaveListMutation,
    PostApiAccountDueSaveListMutationVariables
  >;
