import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type FindPharmacyDeliveryHistoriesQueryVariables = Types.Exact<{
  input: Types.PharmacyDeliveryHistoriesInput;
}>;

export type FindPharmacyDeliveryHistoriesQuery = {
  __typename?: "query_root";
  findPharmacyDeliveryHistories: Array<{
    __typename?: "PharmacyDeliveryHistories";
    pharmacyReserveId: number;
    deliveryAt: string;
    deliveryInquiryNumber: string;
  }>;
};

export type GetPharmacyDeliveryHistoryQueryVariables = Types.Exact<{
  input: Types.PharmacyDeliveryHistoryInput;
}>;

export type GetPharmacyDeliveryHistoryQuery = {
  __typename?: "query_root";
  getPharmacyDeliveryHistory: {
    __typename?: "PharmacyDeliveryHistory";
    pharmacyReserveId: number;
    deliveryAt: string;
    deliveryName: string;
    deliveryPostCode: string;
    deliveryAddress: string;
    deliveryPhoneNumber: string;
    deliveryInquiryNumber: string;
  };
};

export type GetPharmacyPaymentHistoriesQueryVariables = Types.Exact<{
  input: Types.GetPharmacyPaymentHistoriesInput;
}>;

export type GetPharmacyPaymentHistoriesQuery = {
  __typename?: "query_root";
  getPharmacyPaymentHistories: Array<{
    __typename?: "PharmacyPaymentHistory";
    paymentPharmacyDetailHistoryId: string;
    paymentDate: string;
    paymentStatus: number;
    actionType: number;
    paymentType: number;
    totalAmount: number;
  }>;
};

export type GetPharmacyPaymentDetailHistoryQueryVariables = Types.Exact<{
  input: Types.GetPharmacyPaymentDetailHistoryInput;
}>;

export type GetPharmacyPaymentDetailHistoryQuery = {
  __typename?: "query_root";
  getPharmacyPaymentDetailHistory: {
    __typename?: "PharmacyPaymentDetailHistory";
    paymentPharmacyDetailHistoryId: string;
    paymentDate: string;
    paymentStatus: number;
    actionType: number;
    paymentType: number;
    medicationCost: number;
    deliveryFee: number;
    previousMedicationCost: number;
    previousDeliveryFee: number;
    errorMessage: string;
    cardInfo?: {
      __typename?: "CardInfo";
      card_no: string;
      brand: string;
      expire: string;
      holderName: string;
    };
  };
};

export type FindPharmacyReserveStatusHistoriesQueryVariables = Types.Exact<{
  input: Types.FindPharmacyReserveStatusHistoriesInput;
}>;

export type FindPharmacyReserveStatusHistoriesQuery = {
  __typename?: "query_root";
  findPharmacyReserveStatusHistories: Array<{
    __typename?: "PharmacyReserveStatusHistory";
    pharmacyReserveStatusHistoryId: number;
    pharmacyReserveDetailId: number;
    status?: number;
    statusCancelType?: number;
    guidanceStatus?: number;
    pharmacistId?: number;
    pharmacistName?: string;
    pharmacistMemo: string;
    createdAt: string;
  }>;
};

export const FindPharmacyDeliveryHistoriesDocument = gql`
  query FindPharmacyDeliveryHistories($input: pharmacyDeliveryHistoriesInput!) {
    findPharmacyDeliveryHistories(input: $input) {
      pharmacyReserveId
      deliveryAt
      deliveryInquiryNumber
    }
  }
`;

/**
 * __useFindPharmacyDeliveryHistoriesQuery__
 *
 * To run a query within a React component, call `useFindPharmacyDeliveryHistoriesQuery` and pass it any options that fit your needs.
 * When your component renders, `useFindPharmacyDeliveryHistoriesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFindPharmacyDeliveryHistoriesQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useFindPharmacyDeliveryHistoriesQuery(
  baseOptions: Apollo.QueryHookOptions<
    FindPharmacyDeliveryHistoriesQuery,
    FindPharmacyDeliveryHistoriesQueryVariables
  > &
    (
      | {
          variables: FindPharmacyDeliveryHistoriesQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    FindPharmacyDeliveryHistoriesQuery,
    FindPharmacyDeliveryHistoriesQueryVariables
  >(FindPharmacyDeliveryHistoriesDocument, options);
}
export function useFindPharmacyDeliveryHistoriesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FindPharmacyDeliveryHistoriesQuery,
    FindPharmacyDeliveryHistoriesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    FindPharmacyDeliveryHistoriesQuery,
    FindPharmacyDeliveryHistoriesQueryVariables
  >(FindPharmacyDeliveryHistoriesDocument, options);
}
export function useFindPharmacyDeliveryHistoriesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    FindPharmacyDeliveryHistoriesQuery,
    FindPharmacyDeliveryHistoriesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    FindPharmacyDeliveryHistoriesQuery,
    FindPharmacyDeliveryHistoriesQueryVariables
  >(FindPharmacyDeliveryHistoriesDocument, options);
}
export type FindPharmacyDeliveryHistoriesQueryHookResult = ReturnType<
  typeof useFindPharmacyDeliveryHistoriesQuery
>;
export type FindPharmacyDeliveryHistoriesLazyQueryHookResult = ReturnType<
  typeof useFindPharmacyDeliveryHistoriesLazyQuery
>;
export type FindPharmacyDeliveryHistoriesSuspenseQueryHookResult = ReturnType<
  typeof useFindPharmacyDeliveryHistoriesSuspenseQuery
>;
export type FindPharmacyDeliveryHistoriesQueryResult = Apollo.QueryResult<
  FindPharmacyDeliveryHistoriesQuery,
  FindPharmacyDeliveryHistoriesQueryVariables
>;
export const GetPharmacyDeliveryHistoryDocument = gql`
  query GetPharmacyDeliveryHistory($input: pharmacyDeliveryHistoryInput!) {
    getPharmacyDeliveryHistory(input: $input) {
      pharmacyReserveId
      deliveryAt
      deliveryName
      deliveryPostCode
      deliveryAddress
      deliveryPhoneNumber
      deliveryInquiryNumber
    }
  }
`;

/**
 * __useGetPharmacyDeliveryHistoryQuery__
 *
 * To run a query within a React component, call `useGetPharmacyDeliveryHistoryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPharmacyDeliveryHistoryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPharmacyDeliveryHistoryQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPharmacyDeliveryHistoryQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPharmacyDeliveryHistoryQuery,
    GetPharmacyDeliveryHistoryQueryVariables
  > &
    (
      | { variables: GetPharmacyDeliveryHistoryQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPharmacyDeliveryHistoryQuery,
    GetPharmacyDeliveryHistoryQueryVariables
  >(GetPharmacyDeliveryHistoryDocument, options);
}
export function useGetPharmacyDeliveryHistoryLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPharmacyDeliveryHistoryQuery,
    GetPharmacyDeliveryHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPharmacyDeliveryHistoryQuery,
    GetPharmacyDeliveryHistoryQueryVariables
  >(GetPharmacyDeliveryHistoryDocument, options);
}
export function useGetPharmacyDeliveryHistorySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPharmacyDeliveryHistoryQuery,
    GetPharmacyDeliveryHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPharmacyDeliveryHistoryQuery,
    GetPharmacyDeliveryHistoryQueryVariables
  >(GetPharmacyDeliveryHistoryDocument, options);
}
export type GetPharmacyDeliveryHistoryQueryHookResult = ReturnType<
  typeof useGetPharmacyDeliveryHistoryQuery
>;
export type GetPharmacyDeliveryHistoryLazyQueryHookResult = ReturnType<
  typeof useGetPharmacyDeliveryHistoryLazyQuery
>;
export type GetPharmacyDeliveryHistorySuspenseQueryHookResult = ReturnType<
  typeof useGetPharmacyDeliveryHistorySuspenseQuery
>;
export type GetPharmacyDeliveryHistoryQueryResult = Apollo.QueryResult<
  GetPharmacyDeliveryHistoryQuery,
  GetPharmacyDeliveryHistoryQueryVariables
>;
export const GetPharmacyPaymentHistoriesDocument = gql`
  query GetPharmacyPaymentHistories($input: GetPharmacyPaymentHistoriesInput!) {
    getPharmacyPaymentHistories(input: $input) {
      paymentPharmacyDetailHistoryId
      paymentDate
      paymentStatus
      actionType
      paymentType
      totalAmount
    }
  }
`;

/**
 * __useGetPharmacyPaymentHistoriesQuery__
 *
 * To run a query within a React component, call `useGetPharmacyPaymentHistoriesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPharmacyPaymentHistoriesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPharmacyPaymentHistoriesQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPharmacyPaymentHistoriesQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPharmacyPaymentHistoriesQuery,
    GetPharmacyPaymentHistoriesQueryVariables
  > &
    (
      | { variables: GetPharmacyPaymentHistoriesQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPharmacyPaymentHistoriesQuery,
    GetPharmacyPaymentHistoriesQueryVariables
  >(GetPharmacyPaymentHistoriesDocument, options);
}
export function useGetPharmacyPaymentHistoriesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPharmacyPaymentHistoriesQuery,
    GetPharmacyPaymentHistoriesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPharmacyPaymentHistoriesQuery,
    GetPharmacyPaymentHistoriesQueryVariables
  >(GetPharmacyPaymentHistoriesDocument, options);
}
export function useGetPharmacyPaymentHistoriesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPharmacyPaymentHistoriesQuery,
    GetPharmacyPaymentHistoriesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPharmacyPaymentHistoriesQuery,
    GetPharmacyPaymentHistoriesQueryVariables
  >(GetPharmacyPaymentHistoriesDocument, options);
}
export type GetPharmacyPaymentHistoriesQueryHookResult = ReturnType<
  typeof useGetPharmacyPaymentHistoriesQuery
>;
export type GetPharmacyPaymentHistoriesLazyQueryHookResult = ReturnType<
  typeof useGetPharmacyPaymentHistoriesLazyQuery
>;
export type GetPharmacyPaymentHistoriesSuspenseQueryHookResult = ReturnType<
  typeof useGetPharmacyPaymentHistoriesSuspenseQuery
>;
export type GetPharmacyPaymentHistoriesQueryResult = Apollo.QueryResult<
  GetPharmacyPaymentHistoriesQuery,
  GetPharmacyPaymentHistoriesQueryVariables
>;
export const GetPharmacyPaymentDetailHistoryDocument = gql`
  query GetPharmacyPaymentDetailHistory(
    $input: GetPharmacyPaymentDetailHistoryInput!
  ) {
    getPharmacyPaymentDetailHistory(input: $input) {
      paymentPharmacyDetailHistoryId
      paymentDate
      paymentStatus
      actionType
      paymentType
      medicationCost
      deliveryFee
      previousMedicationCost
      previousDeliveryFee
      errorMessage
      cardInfo {
        card_no
        brand
        expire
        holderName
      }
    }
  }
`;

/**
 * __useGetPharmacyPaymentDetailHistoryQuery__
 *
 * To run a query within a React component, call `useGetPharmacyPaymentDetailHistoryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPharmacyPaymentDetailHistoryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPharmacyPaymentDetailHistoryQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPharmacyPaymentDetailHistoryQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPharmacyPaymentDetailHistoryQuery,
    GetPharmacyPaymentDetailHistoryQueryVariables
  > &
    (
      | {
          variables: GetPharmacyPaymentDetailHistoryQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPharmacyPaymentDetailHistoryQuery,
    GetPharmacyPaymentDetailHistoryQueryVariables
  >(GetPharmacyPaymentDetailHistoryDocument, options);
}
export function useGetPharmacyPaymentDetailHistoryLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPharmacyPaymentDetailHistoryQuery,
    GetPharmacyPaymentDetailHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPharmacyPaymentDetailHistoryQuery,
    GetPharmacyPaymentDetailHistoryQueryVariables
  >(GetPharmacyPaymentDetailHistoryDocument, options);
}
export function useGetPharmacyPaymentDetailHistorySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPharmacyPaymentDetailHistoryQuery,
    GetPharmacyPaymentDetailHistoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPharmacyPaymentDetailHistoryQuery,
    GetPharmacyPaymentDetailHistoryQueryVariables
  >(GetPharmacyPaymentDetailHistoryDocument, options);
}
export type GetPharmacyPaymentDetailHistoryQueryHookResult = ReturnType<
  typeof useGetPharmacyPaymentDetailHistoryQuery
>;
export type GetPharmacyPaymentDetailHistoryLazyQueryHookResult = ReturnType<
  typeof useGetPharmacyPaymentDetailHistoryLazyQuery
>;
export type GetPharmacyPaymentDetailHistorySuspenseQueryHookResult = ReturnType<
  typeof useGetPharmacyPaymentDetailHistorySuspenseQuery
>;
export type GetPharmacyPaymentDetailHistoryQueryResult = Apollo.QueryResult<
  GetPharmacyPaymentDetailHistoryQuery,
  GetPharmacyPaymentDetailHistoryQueryVariables
>;
export const FindPharmacyReserveStatusHistoriesDocument = gql`
  query FindPharmacyReserveStatusHistories(
    $input: FindPharmacyReserveStatusHistoriesInput!
  ) {
    findPharmacyReserveStatusHistories(input: $input) {
      pharmacyReserveStatusHistoryId
      pharmacyReserveDetailId
      status
      statusCancelType
      guidanceStatus
      pharmacistId
      pharmacistName
      pharmacistMemo
      createdAt
    }
  }
`;

/**
 * __useFindPharmacyReserveStatusHistoriesQuery__
 *
 * To run a query within a React component, call `useFindPharmacyReserveStatusHistoriesQuery` and pass it any options that fit your needs.
 * When your component renders, `useFindPharmacyReserveStatusHistoriesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFindPharmacyReserveStatusHistoriesQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useFindPharmacyReserveStatusHistoriesQuery(
  baseOptions: Apollo.QueryHookOptions<
    FindPharmacyReserveStatusHistoriesQuery,
    FindPharmacyReserveStatusHistoriesQueryVariables
  > &
    (
      | {
          variables: FindPharmacyReserveStatusHistoriesQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    FindPharmacyReserveStatusHistoriesQuery,
    FindPharmacyReserveStatusHistoriesQueryVariables
  >(FindPharmacyReserveStatusHistoriesDocument, options);
}
export function useFindPharmacyReserveStatusHistoriesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FindPharmacyReserveStatusHistoriesQuery,
    FindPharmacyReserveStatusHistoriesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    FindPharmacyReserveStatusHistoriesQuery,
    FindPharmacyReserveStatusHistoriesQueryVariables
  >(FindPharmacyReserveStatusHistoriesDocument, options);
}
export function useFindPharmacyReserveStatusHistoriesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    FindPharmacyReserveStatusHistoriesQuery,
    FindPharmacyReserveStatusHistoriesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    FindPharmacyReserveStatusHistoriesQuery,
    FindPharmacyReserveStatusHistoriesQueryVariables
  >(FindPharmacyReserveStatusHistoriesDocument, options);
}
export type FindPharmacyReserveStatusHistoriesQueryHookResult = ReturnType<
  typeof useFindPharmacyReserveStatusHistoriesQuery
>;
export type FindPharmacyReserveStatusHistoriesLazyQueryHookResult = ReturnType<
  typeof useFindPharmacyReserveStatusHistoriesLazyQuery
>;
export type FindPharmacyReserveStatusHistoriesSuspenseQueryHookResult =
  ReturnType<typeof useFindPharmacyReserveStatusHistoriesSuspenseQuery>;
export type FindPharmacyReserveStatusHistoriesQueryResult = Apollo.QueryResult<
  FindPharmacyReserveStatusHistoriesQuery,
  FindPharmacyReserveStatusHistoriesQueryVariables
>;
