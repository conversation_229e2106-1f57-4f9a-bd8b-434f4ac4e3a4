import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiHistoryGetListQueryVariables = Types.Exact<{
  keyWord?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  offset?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  limit?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isShowApproval?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  userId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  odrKouiKbns?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars["Int"]["input"]>>
    | Types.InputMaybe<Types.Scalars["Int"]["input"]>
  >;
  treatmentDepartmentIds?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars["Int"]["input"]>>
    | Types.InputMaybe<Types.Scalars["Int"]["input"]>
  >;
  tantoIds?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars["Int"]["input"]>>
    | Types.InputMaybe<Types.Scalars["Int"]["input"]>
  >;
  hasSOAP?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiHistoryGetListQuery = {
  __typename?: "query_root";
  getApiHistoryGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationGetMedicalExaminationHistoryResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationGetMedicalExaminationHistoryResponse";
      keyWord?: string;
      startPage?: number;
      total?: number;
      totalKeyWordMatched?: number;
      currentKeyWordMatched?: number;
      karteOrdRaiins?: Array<{
        __typename?: "UseCaseMedicalExaminationGetHistoryHistoryKarteOdrRaiinItem";
        raiinNo?: string;
        santeiKbn?: number;
        santeiKbnDisplay?: string;
        sinDate?: number;
        sinEndTime?: string;
        sinStartTime?: string;
        sinryoTitle?: string;
        status?: number;
        syosaisinDisplay?: string;
        syosaisinKbn?: number;
        tagNo?: number;
        tantoFullName?: string;
        tantoId?: number;
        tantoName?: string;
        treatmentDepartmentId?: number;
        treatmentDepartmentTitle?: string;
        uketsukeName?: string;
        uketukeTime?: string;
        kaName?: string;
        kaId?: number;
        jikanKbn?: number;
        jikanDisplay?: string;
        hokenType?: number;
        hokenTitle?: string;
        hokenRate?: string;
        hokenPid?: number;
        karteEdition?: {
          __typename?: "UseCaseMedicalExaminationGetHistoryKarteEditionItem";
          karteStatus?: number;
          karteStatusText?: string;
          ptId?: string;
          raiinNo?: string;
          updateDate?: string;
          updateId?: number;
          updateName?: string;
          isDeleted?: number;
          hpId?: number;
          edition?: number;
          createId?: number;
          createDate?: string;
          approvalId?: number;
          approvalDate?: string;
          approvalName?: string;
          hokenGroups?: Array<{
            __typename?: "UseCaseMedicalExaminationGetHistoryHokenGroupHistoryItem";
            hokenTitle?: string;
            hokenPid?: number;
            groupOdrItems?: Array<{
              __typename?: "UseCaseMedicalExaminationGetHistoryGroupOdrGHistoryItem";
              tosekiKbn?: number;
              syohoSbt?: number;
              sinkyuName?: string;
              sikyuName?: string;
              sikyuKbn?: number;
              santeiName?: string;
              santeiKbn?: number;
              kouiCode?: number;
              isKensa?: boolean;
              isDrug?: boolean;
              inOutName?: string;
              inOutKbn?: number;
              hokenPid?: number;
              groupName?: string;
              groupKouiCode?: number;
              odrInfs?: Array<{
                __typename?: "UseCaseMedicalExaminationGetHistoryOdrInfHistoryItem";
                updateName?: string;
                updateMachine?: string;
                updateDateDisplay?: string;
                updateDate?: string;
                tosekiKbn?: number;
                syohoSbt?: number;
                sortNo?: number;
                sinDate?: number;
                sikyuKbn?: number;
                santeiKbn?: number;
                rpNo?: string;
                rpName?: string;
                rpEdaNo?: string;
                raiinNo?: string;
                ptId?: string;
                odrKouiKbn?: number;
                isDeleted?: number;
                inoutKbn?: number;
                id?: string;
                hpId?: number;
                hokenPid?: number;
                groupOdrKouiKbn?: number;
                daysCnt?: number;
                createName?: string;
                createMachine?: string;
                createId?: number;
                createDateDisplay?: string;
                createDate?: string;
                odrDetails?: Array<{
                  __typename?: "UseCaseOrdInfsGetListTreesOdrInfDetailItem";
                  youkaiekiCd?: string;
                  rousaiKbn?: number;
                  centerName?: string;
                  centerCd?: string;
                  yohoKbn?: number;
                  yjCd?: string;
                  yakkaiUnit?: string;
                  yakka?: number;
                  unitSbt?: number;
                  unitName?: string;
                  termVal?: number;
                  ten?: number;
                  syohoLimitKbn?: number;
                  syohoKbn?: number;
                  suryo?: number;
                  sinKouiKbn?: number;
                  sinDate?: number;
                  rpNo?: string;
                  rpEdaNo?: string;
                  rowNo?: number;
                  rikikaUnit?: string;
                  rikikaRate?: number;
                  reqCd?: string;
                  raiinNo?: string;
                  ptId?: string;
                  odrUnitName?: string;
                  odrTermVal?: number;
                  memoItem?: string;
                  masterSbt?: string;
                  kokuji2?: string;
                  kokuji1?: string;
                  kohatuKbn?: number;
                  kikakiUnit?: string;
                  kensaGaichu?: number;
                  itemName?: string;
                  itemCd?: string;
                  isNodspRece?: number;
                  isKensaMstEmpty?: boolean;
                  isGetPriceInYakka?: boolean;
                  ipnName?: string;
                  ipnCd?: string;
                  hpId?: number;
                  hasCmtName?: boolean;
                  handanGrpKbn?: number;
                  drugKbn?: number;
                  fontColor?: string;
                  displayItemName?: string;
                  commentNewline?: number;
                  cnvUnitName?: string;
                  cnvTermVal?: number;
                  cmtOpt?: string;
                  cmtName?: string;
                  alternationIndex?: number;
                  bunkatu?: string;
                  bunkatuKoui?: number;
                  centerItemCd1?: string;
                  centerItemCd2?: string;
                  bikoComment?: number;
                  buiKbn?: number;
                  isAdopted?: number;
                  senteiRyoyoKbn?: number;
                }>;
              }>;
            }>;
          }>;
          listKarteFiles?: Array<{
            __typename?: "UseCaseMedicalExaminationGetHistoryFileInfOutputItem";
            createDate?: string;
            createDateDisplay?: string;
            createName?: string;
            isDeleted?: boolean;
            isSchema?: boolean;
            dspFileName?: string;
            linkFile?: string;
            seqNo?: string;
            updateDate?: string;
            updateDateDisplay?: string;
            updateName?: string;
            fileName?: string;
            categoryCd?: number;
            memo?: string;
          }>;
          karteHistories?: Array<{
            __typename?: "UseCaseMedicalExaminationGetHistoryGrpKarteHistoryItem";
            karteKbn?: number;
            kbnName?: string;
            kbnShortName?: string;
            sortNo?: number;
            canImage?: number;
            karteData?: Array<{
              __typename?: "UseCaseMedicalExaminationGetHistoryKarteInfHistoryItem";
              createDate?: string;
              createDateDisplay?: string;
              createName?: string;
              hpId?: number;
              isDeleted?: number;
              karteKbn?: number;
              ptId?: string;
              raiinNo?: string;
              richText?: string;
              seqNo?: string;
              sinDate?: number;
              text?: string;
              updateDate?: string;
              updateDateDisplay?: string;
            }>;
          }>;
          headerOrderModels?: Array<{
            __typename?: "DomainModelsHistoryOrderHeaderOrderModel";
            updateUserName?: string;
            syosaisinKbn?: number;
            syosaishinBinding?: string;
            jikanKbn?: number;
            jikanBinding?: string;
            isDeleted?: number;
            hokenPattentName?: string;
            createDateBinding?: string;
          }>;
        };
      }>;
    };
  };
};

export type GetApiKarteFilterGetListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiKarteFilterGetListQuery = {
  __typename?: "query_root";
  getApiKarteFilterGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteFilterGetKarteFilterMstResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesKarteFilterGetKarteFilterMstResponse";
      karteFilters?: Array<{
        __typename?: "UseCaseKarteFilterGetListKarteFilterKarteFilterMstOutputItem";
        autoApply?: number;
        filterId?: string;
        filterName?: string;
        sortNo?: number;
        karteFilterDetailModel?: {
          __typename?: "UseCaseKarteFilterGetListKarteFilterKarteFilterDetailOutputItem";
          bookMarkChecked?: boolean;
          listHokenId?: Array<number>;
          listKaId?: Array<number>;
          listUserId?: Array<number>;
        };
      }>;
    };
  };
};

export type GetApiHistoryGetListVersionQueryVariables = Types.Exact<{
  limit?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  offset?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  userId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiHistoryGetListVersionQuery = {
  __typename?: "query_root";
  getApiHistoryGetListVersion?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationGetMedicalExaminationHistoryResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationGetMedicalExaminationHistoryResponse";
      startPage?: number;
      total?: number;
      karteOrdRaiins?: Array<{
        __typename?: "UseCaseMedicalExaminationGetHistoryHistoryKarteOdrRaiinItem";
        raiinNo?: string;
        santeiKbn?: number;
        santeiKbnDisplay?: string;
        sinDate?: number;
        sinEndTime?: string;
        sinStartTime?: string;
        sinryoTitle?: string;
        status?: number;
        syosaisinDisplay?: string;
        syosaisinKbn?: number;
        tagNo?: number;
        tantoFullName?: string;
        tantoId?: number;
        tantoName?: string;
        treatmentDepartmentId?: number;
        treatmentDepartmentTitle?: string;
        uketsukeName?: string;
        uketukeTime?: string;
        kaName?: string;
        kaId?: number;
        jikanKbn?: number;
        jikanDisplay?: string;
        hokenType?: number;
        hokenTitle?: string;
        hokenRate?: string;
        hokenPid?: number;
        karteEdition?: {
          __typename?: "UseCaseMedicalExaminationGetHistoryKarteEditionItem";
          karteStatus?: number;
          karteStatusText?: string;
          ptId?: string;
          raiinNo?: string;
          updateDate?: string;
          updateId?: number;
          updateName?: string;
          isDeleted?: number;
          hpId?: number;
          edition?: number;
          createId?: number;
          createDate?: string;
          approvalId?: number;
          approvalDate?: string;
          approvalName?: string;
          hokenGroups?: Array<{
            __typename?: "UseCaseMedicalExaminationGetHistoryHokenGroupHistoryItem";
            hokenTitle?: string;
            hokenPid?: number;
            groupOdrItems?: Array<{
              __typename?: "UseCaseMedicalExaminationGetHistoryGroupOdrGHistoryItem";
              tosekiKbn?: number;
              syohoSbt?: number;
              sinkyuName?: string;
              sikyuName?: string;
              sikyuKbn?: number;
              santeiName?: string;
              santeiKbn?: number;
              kouiCode?: number;
              isKensa?: boolean;
              isDrug?: boolean;
              inOutName?: string;
              inOutKbn?: number;
              hokenPid?: number;
              groupName?: string;
              groupKouiCode?: number;
              odrInfs?: Array<{
                __typename?: "UseCaseMedicalExaminationGetHistoryOdrInfHistoryItem";
                updateName?: string;
                updateMachine?: string;
                updateDateDisplay?: string;
                updateDate?: string;
                tosekiKbn?: number;
                syohoSbt?: number;
                sortNo?: number;
                sinDate?: number;
                sikyuKbn?: number;
                santeiKbn?: number;
                rpNo?: string;
                rpName?: string;
                rpEdaNo?: string;
                raiinNo?: string;
                ptId?: string;
                odrKouiKbn?: number;
                isDeleted?: number;
                inoutKbn?: number;
                id?: string;
                hpId?: number;
                hokenPid?: number;
                groupOdrKouiKbn?: number;
                daysCnt?: number;
                createName?: string;
                createMachine?: string;
                createId?: number;
                createDateDisplay?: string;
                createDate?: string;
                odrDetails?: Array<{
                  __typename?: "UseCaseOrdInfsGetListTreesOdrInfDetailItem";
                  youkaiekiCd?: string;
                  yohoKbn?: number;
                  yjCd?: string;
                  yakkaiUnit?: string;
                  yakka?: number;
                  unitSbt?: number;
                  unitName?: string;
                  termVal?: number;
                  ten?: number;
                  syohoLimitKbn?: number;
                  syohoKbn?: number;
                  suryo?: number;
                  sinKouiKbn?: number;
                  sinDate?: number;
                  rpNo?: string;
                  rpEdaNo?: string;
                  rowNo?: number;
                  rikikaUnit?: string;
                  rikikaRate?: number;
                  reqCd?: string;
                  raiinNo?: string;
                  ptId?: string;
                  odrUnitName?: string;
                  odrTermVal?: number;
                  memoItem?: string;
                  masterSbt?: string;
                  kokuji2?: string;
                  kokuji1?: string;
                  kohatuKbn?: number;
                  kikakiUnit?: string;
                  kensaGaichu?: number;
                  kasan2?: number;
                  kasan1?: number;
                  jissiMachine?: string;
                  jissiKbn?: number;
                  jissiId?: number;
                  jissiDate?: string;
                  itemName?: string;
                  itemCd?: string;
                  isNodspRece?: number;
                  isKensaMstEmpty?: boolean;
                  isGetPriceInYakka?: boolean;
                  ipnName?: string;
                  ipnCd?: string;
                  hpId?: number;
                  hasCmtName?: boolean;
                  handanGrpKbn?: number;
                  drugKbn?: number;
                  fontColor?: string;
                  displayItemName?: string;
                  commentNewline?: number;
                  cnvUnitName?: string;
                  cnvTermVal?: number;
                  cmtOpt?: string;
                  cmtName?: string;
                  cmtColKeta4?: number;
                  cmtColKeta3?: number;
                  cmtColKeta2?: number;
                  cmtColKeta1?: number;
                  cmtCol4?: number;
                  cmtCol3?: number;
                  cmtCol2?: number;
                  cmtCol1?: number;
                  alternationIndex?: number;
                  bunkatu?: string;
                  buiKbn?: number;
                  rousaiKbn?: number;
                  centerName?: string;
                  centerCd?: string;
                  bunkatuKoui?: number;
                  centerItemCd1?: string;
                  centerItemCd2?: string;
                  senteiRyoyoKbn?: number;
                  yohoSets?: Array<{
                    __typename?: "DomainModelsOrdInfDetailsYohoSetMstModel";
                    createDate?: string;
                    createId?: number;
                    createMachine?: string;
                    hpId?: number;
                    isDeleted?: number;
                    isModified?: boolean;
                    itemCd?: string;
                    itemname?: string;
                    setId?: number;
                    sortNo?: number;
                    updateDate?: string;
                    updateId?: number;
                    updateMachine?: string;
                    userId?: number;
                    yohoKbn?: number;
                  }>;
                }>;
              }>;
            }>;
          }>;
          listKarteFiles?: Array<{
            __typename?: "UseCaseMedicalExaminationGetHistoryFileInfOutputItem";
            createDate?: string;
            createDateDisplay?: string;
            createName?: string;
            isDeleted?: boolean;
            isSchema?: boolean;
            dspFileName?: string;
            linkFile?: string;
            seqNo?: string;
            updateDate?: string;
            updateDateDisplay?: string;
            updateName?: string;
            fileName?: string;
          }>;
          karteHistories?: Array<{
            __typename?: "UseCaseMedicalExaminationGetHistoryGrpKarteHistoryItem";
            karteKbn?: number;
            kbnName?: string;
            kbnShortName?: string;
            sortNo?: number;
            canImage?: number;
            karteData?: Array<{
              __typename?: "UseCaseMedicalExaminationGetHistoryKarteInfHistoryItem";
              createDate?: string;
              createDateDisplay?: string;
              createName?: string;
              hpId?: number;
              isDeleted?: number;
              karteKbn?: number;
              ptId?: string;
              raiinNo?: string;
              richText?: string;
              seqNo?: string;
              sinDate?: number;
              text?: string;
              updateDate?: string;
              updateDateDisplay?: string;
            }>;
          }>;
          headerOrderModels?: Array<{
            __typename?: "DomainModelsHistoryOrderHeaderOrderModel";
            updateUserName?: string;
            syosaisinKbn?: number;
            syosaishinBinding?: string;
            jikanKbn?: number;
            jikanBinding?: string;
            isDeleted?: number;
            hokenPattentName?: string;
            createDateBinding?: string;
          }>;
        };
      }>;
    };
  };
};

export const GetApiHistoryGetListDocument = gql`
  query getApiHistoryGetList(
    $keyWord: String
    $offset: Int
    $limit: Int
    $ptId: BigInt
    $sinDate: Int
    $isShowApproval: Int
    $userId: Int
    $odrKouiKbns: [Int]
    $treatmentDepartmentIds: [Int]
    $tantoIds: [Int]
    $hasSOAP: Boolean
  ) {
    getApiHistoryGetList(
      keyWord: $keyWord
      offset: $offset
      limit: $limit
      ptId: $ptId
      sinDate: $sinDate
      isShowApproval: $isShowApproval
      userId: $userId
      odrKouiKbns: $odrKouiKbns
      treatmentDepartmentIds: $treatmentDepartmentIds
      tantoIds: $tantoIds
      hasSOAP: $hasSOAP
    ) {
      data {
        karteOrdRaiins {
          karteEdition {
            hokenGroups {
              groupOdrItems {
                odrInfs {
                  odrDetails {
                    youkaiekiCd
                    rousaiKbn
                    centerName
                    centerCd
                    yohoKbn
                    yjCd
                    yakkaiUnit
                    yakka
                    unitSbt
                    unitName
                    termVal
                    ten
                    syohoLimitKbn
                    syohoKbn
                    suryo
                    sinKouiKbn
                    sinDate
                    rpNo
                    rpEdaNo
                    rowNo
                    rikikaUnit
                    rikikaRate
                    reqCd
                    raiinNo
                    ptId
                    odrUnitName
                    odrTermVal
                    memoItem
                    masterSbt
                    kokuji2
                    kokuji1
                    kohatuKbn
                    kikakiUnit
                    kensaGaichu
                    itemName
                    itemCd
                    isNodspRece
                    isKensaMstEmpty
                    isGetPriceInYakka
                    ipnName
                    ipnCd
                    hpId
                    hasCmtName
                    handanGrpKbn
                    drugKbn
                    fontColor
                    displayItemName
                    commentNewline
                    cnvUnitName
                    cnvTermVal
                    cmtOpt
                    cmtName
                    alternationIndex
                    bunkatu
                    bunkatuKoui
                    centerItemCd1
                    centerItemCd2
                    bikoComment
                    buiKbn
                    isAdopted
                    senteiRyoyoKbn
                  }
                  updateName
                  updateMachine
                  updateDateDisplay
                  updateDate
                  tosekiKbn
                  syohoSbt
                  sortNo
                  sinDate
                  sikyuKbn
                  santeiKbn
                  rpNo
                  rpName
                  rpEdaNo
                  raiinNo
                  ptId
                  odrKouiKbn
                  isDeleted
                  inoutKbn
                  id
                  hpId
                  hokenPid
                  groupOdrKouiKbn
                  daysCnt
                  createName
                  createMachine
                  createId
                  createDateDisplay
                  createDate
                }
                tosekiKbn
                syohoSbt
                sinkyuName
                sikyuName
                sikyuKbn
                santeiName
                santeiKbn
                kouiCode
                isKensa
                isDrug
                inOutName
                inOutKbn
                hokenPid
                groupName
                groupKouiCode
              }
              hokenTitle
              hokenPid
            }
            karteStatus
            karteStatusText
            listKarteFiles {
              createDate
              createDateDisplay
              createName
              isDeleted
              isSchema
              dspFileName
              linkFile
              seqNo
              updateDate
              updateDateDisplay
              updateName
              fileName
              categoryCd
              memo
            }
            ptId
            raiinNo
            updateDate
            updateId
            updateName
            karteHistories {
              karteData {
                createDate
                createDateDisplay
                createName
                hpId
                isDeleted
                karteKbn
                ptId
                raiinNo
                richText
                seqNo
                sinDate
                text
                updateDate
                updateDateDisplay
              }
              karteKbn
              kbnName
              kbnShortName
              sortNo
              canImage
            }
            isDeleted
            hpId
            headerOrderModels {
              updateUserName
              syosaisinKbn
              syosaishinBinding
              jikanKbn
              jikanBinding
              isDeleted
              hokenPattentName
              createDateBinding
            }
            edition
            createId
            createDate
            approvalId
            approvalDate
            approvalName
          }
          raiinNo
          santeiKbn
          santeiKbnDisplay
          sinDate
          sinEndTime
          sinStartTime
          sinryoTitle
          status
          syosaisinDisplay
          syosaisinKbn
          tagNo
          tantoFullName
          tantoId
          tantoName
          treatmentDepartmentId
          treatmentDepartmentTitle
          uketsukeName
          uketukeTime
          kaName
          kaId
          jikanKbn
          jikanDisplay
          hokenType
          hokenTitle
          hokenRate
          hokenPid
        }
        keyWord
        startPage
        total
        totalKeyWordMatched
        currentKeyWordMatched
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiHistoryGetListQuery__
 *
 * To run a query within a React component, call `useGetApiHistoryGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiHistoryGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiHistoryGetListQuery({
 *   variables: {
 *      keyWord: // value for 'keyWord'
 *      offset: // value for 'offset'
 *      limit: // value for 'limit'
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      isShowApproval: // value for 'isShowApproval'
 *      userId: // value for 'userId'
 *      odrKouiKbns: // value for 'odrKouiKbns'
 *      treatmentDepartmentIds: // value for 'treatmentDepartmentIds'
 *      tantoIds: // value for 'tantoIds'
 *      hasSOAP: // value for 'hasSOAP'
 *   },
 * });
 */
export function useGetApiHistoryGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiHistoryGetListQuery,
    GetApiHistoryGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiHistoryGetListQuery,
    GetApiHistoryGetListQueryVariables
  >(GetApiHistoryGetListDocument, options);
}
export function useGetApiHistoryGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiHistoryGetListQuery,
    GetApiHistoryGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiHistoryGetListQuery,
    GetApiHistoryGetListQueryVariables
  >(GetApiHistoryGetListDocument, options);
}
export function useGetApiHistoryGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiHistoryGetListQuery,
    GetApiHistoryGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiHistoryGetListQuery,
    GetApiHistoryGetListQueryVariables
  >(GetApiHistoryGetListDocument, options);
}
export type GetApiHistoryGetListQueryHookResult = ReturnType<
  typeof useGetApiHistoryGetListQuery
>;
export type GetApiHistoryGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiHistoryGetListLazyQuery
>;
export type GetApiHistoryGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiHistoryGetListSuspenseQuery
>;
export type GetApiHistoryGetListQueryResult = Apollo.QueryResult<
  GetApiHistoryGetListQuery,
  GetApiHistoryGetListQueryVariables
>;
export const GetApiKarteFilterGetListDocument = gql`
  query getApiKarteFilterGetList {
    getApiKarteFilterGetList {
      data {
        karteFilters {
          autoApply
          filterId
          filterName
          karteFilterDetailModel {
            bookMarkChecked
            listHokenId
            listKaId
            listUserId
          }
          sortNo
        }
      }
    }
  }
`;

/**
 * __useGetApiKarteFilterGetListQuery__
 *
 * To run a query within a React component, call `useGetApiKarteFilterGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKarteFilterGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKarteFilterGetListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiKarteFilterGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiKarteFilterGetListQuery,
    GetApiKarteFilterGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKarteFilterGetListQuery,
    GetApiKarteFilterGetListQueryVariables
  >(GetApiKarteFilterGetListDocument, options);
}
export function useGetApiKarteFilterGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKarteFilterGetListQuery,
    GetApiKarteFilterGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKarteFilterGetListQuery,
    GetApiKarteFilterGetListQueryVariables
  >(GetApiKarteFilterGetListDocument, options);
}
export function useGetApiKarteFilterGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKarteFilterGetListQuery,
    GetApiKarteFilterGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKarteFilterGetListQuery,
    GetApiKarteFilterGetListQueryVariables
  >(GetApiKarteFilterGetListDocument, options);
}
export type GetApiKarteFilterGetListQueryHookResult = ReturnType<
  typeof useGetApiKarteFilterGetListQuery
>;
export type GetApiKarteFilterGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiKarteFilterGetListLazyQuery
>;
export type GetApiKarteFilterGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiKarteFilterGetListSuspenseQuery
>;
export type GetApiKarteFilterGetListQueryResult = Apollo.QueryResult<
  GetApiKarteFilterGetListQuery,
  GetApiKarteFilterGetListQueryVariables
>;
export const GetApiHistoryGetListVersionDocument = gql`
  query getApiHistoryGetListVersion(
    $limit: Int
    $offset: Int
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
    $userId: Int
  ) {
    getApiHistoryGetListVersion(
      limit: $limit
      offset: $offset
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
      userId: $userId
    ) {
      message
      status
      data {
        karteOrdRaiins {
          karteEdition {
            hokenGroups {
              groupOdrItems {
                odrInfs {
                  odrDetails {
                    youkaiekiCd
                    yohoSets {
                      createDate
                      createId
                      createMachine
                      hpId
                      isDeleted
                      isModified
                      itemCd
                      itemname
                      setId
                      sortNo
                      updateDate
                      updateId
                      updateMachine
                      userId
                      yohoKbn
                    }
                    yohoKbn
                    yjCd
                    yakkaiUnit
                    yakka
                    unitSbt
                    unitName
                    termVal
                    ten
                    syohoLimitKbn
                    syohoKbn
                    suryo
                    sinKouiKbn
                    sinDate
                    rpNo
                    rpEdaNo
                    rowNo
                    rikikaUnit
                    rikikaRate
                    reqCd
                    raiinNo
                    ptId
                    odrUnitName
                    odrTermVal
                    memoItem
                    masterSbt
                    kokuji2
                    kokuji1
                    kohatuKbn
                    kikakiUnit
                    kensaGaichu
                    kasan2
                    kasan1
                    jissiMachine
                    jissiKbn
                    jissiId
                    jissiDate
                    itemName
                    itemCd
                    isNodspRece
                    isKensaMstEmpty
                    isGetPriceInYakka
                    ipnName
                    ipnCd
                    hpId
                    hasCmtName
                    handanGrpKbn
                    drugKbn
                    fontColor
                    displayItemName
                    commentNewline
                    cnvUnitName
                    cnvTermVal
                    cmtOpt
                    cmtName
                    cmtColKeta4
                    cmtColKeta3
                    cmtColKeta2
                    cmtColKeta1
                    cmtCol4
                    cmtCol3
                    cmtCol2
                    cmtCol1
                    alternationIndex
                    bunkatu
                    buiKbn
                    rousaiKbn
                    centerName
                    centerCd
                    bunkatuKoui
                    centerItemCd1
                    centerItemCd2
                    senteiRyoyoKbn
                  }
                  updateName
                  updateMachine
                  updateDateDisplay
                  updateDate
                  tosekiKbn
                  syohoSbt
                  sortNo
                  sinDate
                  sikyuKbn
                  santeiKbn
                  rpNo
                  rpName
                  rpEdaNo
                  raiinNo
                  ptId
                  odrKouiKbn
                  isDeleted
                  inoutKbn
                  id
                  hpId
                  hokenPid
                  groupOdrKouiKbn
                  daysCnt
                  createName
                  createMachine
                  createId
                  createDateDisplay
                  createDate
                }
                tosekiKbn
                syohoSbt
                sinkyuName
                sikyuName
                sikyuKbn
                santeiName
                santeiKbn
                kouiCode
                isKensa
                isDrug
                inOutName
                inOutKbn
                hokenPid
                groupName
                groupKouiCode
              }
              hokenTitle
              hokenPid
            }
            karteStatus
            karteStatusText
            listKarteFiles {
              createDate
              createDateDisplay
              createName
              isDeleted
              isSchema
              dspFileName
              linkFile
              seqNo
              updateDate
              updateDateDisplay
              updateName
              fileName
            }
            ptId
            raiinNo
            updateDate
            updateId
            updateName
            karteHistories {
              karteData {
                createDate
                createDateDisplay
                createName
                hpId
                isDeleted
                karteKbn
                ptId
                raiinNo
                richText
                seqNo
                sinDate
                text
                updateDate
                updateDateDisplay
              }
              karteKbn
              kbnName
              kbnShortName
              sortNo
              canImage
            }
            isDeleted
            hpId
            headerOrderModels {
              updateUserName
              syosaisinKbn
              syosaishinBinding
              jikanKbn
              jikanBinding
              isDeleted
              hokenPattentName
              createDateBinding
            }
            edition
            createId
            createDate
            approvalId
            approvalDate
            approvalName
          }
          raiinNo
          santeiKbn
          santeiKbnDisplay
          sinDate
          sinEndTime
          sinStartTime
          sinryoTitle
          status
          syosaisinDisplay
          syosaisinKbn
          tagNo
          tantoFullName
          tantoId
          tantoName
          treatmentDepartmentId
          treatmentDepartmentTitle
          uketsukeName
          uketukeTime
          kaName
          kaId
          jikanKbn
          jikanDisplay
          hokenType
          hokenTitle
          hokenRate
          hokenPid
        }
        startPage
        total
      }
    }
  }
`;

/**
 * __useGetApiHistoryGetListVersionQuery__
 *
 * To run a query within a React component, call `useGetApiHistoryGetListVersionQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiHistoryGetListVersionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiHistoryGetListVersionQuery({
 *   variables: {
 *      limit: // value for 'limit'
 *      offset: // value for 'offset'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useGetApiHistoryGetListVersionQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiHistoryGetListVersionQuery,
    GetApiHistoryGetListVersionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiHistoryGetListVersionQuery,
    GetApiHistoryGetListVersionQueryVariables
  >(GetApiHistoryGetListVersionDocument, options);
}
export function useGetApiHistoryGetListVersionLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiHistoryGetListVersionQuery,
    GetApiHistoryGetListVersionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiHistoryGetListVersionQuery,
    GetApiHistoryGetListVersionQueryVariables
  >(GetApiHistoryGetListVersionDocument, options);
}
export function useGetApiHistoryGetListVersionSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiHistoryGetListVersionQuery,
    GetApiHistoryGetListVersionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiHistoryGetListVersionQuery,
    GetApiHistoryGetListVersionQueryVariables
  >(GetApiHistoryGetListVersionDocument, options);
}
export type GetApiHistoryGetListVersionQueryHookResult = ReturnType<
  typeof useGetApiHistoryGetListVersionQuery
>;
export type GetApiHistoryGetListVersionLazyQueryHookResult = ReturnType<
  typeof useGetApiHistoryGetListVersionLazyQuery
>;
export type GetApiHistoryGetListVersionSuspenseQueryHookResult = ReturnType<
  typeof useGetApiHistoryGetListVersionSuspenseQuery
>;
export type GetApiHistoryGetListVersionQueryResult = Apollo.QueryResult<
  GetApiHistoryGetListVersionQuery,
  GetApiHistoryGetListVersionQueryVariables
>;
