import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetPortalHospitalUploadFileUrlsQueryVariables = Types.Exact<{
  input:
    | Array<Types.GetPortalHospitalUploadFileUrlInput>
    | Types.GetPortalHospitalUploadFileUrlInput;
}>;

export type GetPortalHospitalUploadFileUrlsQuery = {
  __typename?: "query_root";
  getPortalHospitalUploadFileUrls: Array<{
    __typename?: "GetPortalHospitalUploadFileUrlRes";
    fileNameWithExtension: string;
    url: string;
  }>;
};

export type GetPortalHospitalStaffUploadFileUrlsQueryVariables = Types.Exact<{
  hospitalStaffId: Types.Scalars["Int"]["input"];
  input:
    | Array<Types.GetPortalHospitalStaffUploadFileUrlInput>
    | Types.GetPortalHospitalStaffUploadFileUrlInput;
}>;

export type GetPortalHospitalStaffUploadFileUrlsQuery = {
  __typename?: "query_root";
  getPortalHospitalStaffUploadFileUrls: Array<{
    __typename?: "GetPortalHospitalStaffUploadFileUrlRes";
    fileNameWithExtension: string;
    url: string;
  }>;
};

export type GetPortalMasterStationByKeywordQueryVariables = Types.Exact<{
  input: Types.Scalars["String"]["input"];
}>;

export type GetPortalMasterStationByKeywordQuery = {
  __typename?: "query_root";
  getPortalMasterStationByKeyword: Array<{
    __typename?: "PortalMasterStation";
    stationId: number;
    name: string;
    railline: {
      __typename?: "PortalMasterRailLine";
      raillineId: number;
      name: string;
      raillineCompanyId: number;
    };
  }>;
};

export type GetPortalHospitalQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetPortalHospitalQuery = {
  __typename?: "query_root";
  getPortalHospitalById: {
    __typename?: "GetPortalHospitalRes";
    portalHospital?: {
      __typename?: "PortalHospital";
      name: string;
      postCode: string;
      telephone: string;
      address1: string;
      address2?: string;
      accessDetail?: string;
      hospitalId: number;
      homePage?: string;
      directorName?: string;
      mailAddress?: string;
      isCarpark: boolean;
      paymentDetails: string;
      carparkDetail?: string;
      descriptionTitle: string;
      description?: string;
      isActive: boolean;
      timelineDescription?: string;
      holidayDetail?: string;
      specialists: Array<{
        __typename?: "PortalHospitalSpecialist";
        specialistId: number;
      }>;
      hospitalStations?: Array<{
        __typename?: "PortalHospitalStation";
        stationId: number;
        walkingMinute: number;
        stationName: string;
      }>;
      businessTimes: Array<{
        __typename?: "PortalBusinessTime";
        businessTimeId: number;
        startTime: string;
        endTime: string;
        monFlag: number;
        tueFlag: number;
        wedFlag: number;
        thuFlag: number;
        friFlag: number;
        satFlag: number;
        sunFlag: number;
      }>;
      tags: Array<{ __typename?: "PortalHospitalTag"; tagId: number }>;
      examinations: Array<{
        __typename?: "PortalHospitalExamination";
        examinationId: number;
      }>;
      files?: Array<{
        __typename?: "PortalHospitalFile";
        fileId?: number;
        originalFileName: string;
        s3Key: string;
        createdAt?: string;
      }>;
    };
  };
};

export type UpdatePortalHospitalMutationVariables = Types.Exact<{
  input: Types.UpdatePortalHospitalInput;
}>;

export type UpdatePortalHospitalMutation = {
  __typename?: "mutation_root";
  updatePortalHospital: boolean;
};

export type GetHospitalInfoByHomepageUrlQueryVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.GetHospitalInfoByHomepageUrlInput>;
}>;

export type GetHospitalInfoByHomepageUrlQuery = {
  __typename?: "query_root";
  getHospitalInfoByHomepageUrl: {
    __typename?: "GetHospitalInfoByHomepageUrlRes";
    description: string;
  };
};

export type GetApiReceptionGetHpInfQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiReceptionGetHpInfQuery = {
  __typename?: "query_root";
  getApiReceptionGetHpInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionGetHpInfResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionGetHpInfResponse";
      hpInf?: {
        __typename?: "EmrCloudApiResponsesReceptionHpInfDto";
        address1?: string;
        address2?: string;
        faxNo?: string;
        hpCd?: string;
        hpId?: number;
        hpName?: string;
        kaisetuName?: string;
        otherContacts?: string;
        prefNo?: number;
        receHpName?: string;
        rousaiHpCd?: string;
        startDate?: number;
        tel?: string;
        updateId?: number;
        postCd?: string;
      };
    };
  };
};

export const GetPortalHospitalUploadFileUrlsDocument = gql`
  query getPortalHospitalUploadFileUrls(
    $input: [GetPortalHospitalUploadFileUrlInput!]!
  ) {
    getPortalHospitalUploadFileUrls(input: $input) {
      fileNameWithExtension
      url
    }
  }
`;

/**
 * __useGetPortalHospitalUploadFileUrlsQuery__
 *
 * To run a query within a React component, call `useGetPortalHospitalUploadFileUrlsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPortalHospitalUploadFileUrlsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPortalHospitalUploadFileUrlsQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPortalHospitalUploadFileUrlsQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPortalHospitalUploadFileUrlsQuery,
    GetPortalHospitalUploadFileUrlsQueryVariables
  > &
    (
      | {
          variables: GetPortalHospitalUploadFileUrlsQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPortalHospitalUploadFileUrlsQuery,
    GetPortalHospitalUploadFileUrlsQueryVariables
  >(GetPortalHospitalUploadFileUrlsDocument, options);
}
export function useGetPortalHospitalUploadFileUrlsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPortalHospitalUploadFileUrlsQuery,
    GetPortalHospitalUploadFileUrlsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPortalHospitalUploadFileUrlsQuery,
    GetPortalHospitalUploadFileUrlsQueryVariables
  >(GetPortalHospitalUploadFileUrlsDocument, options);
}
export function useGetPortalHospitalUploadFileUrlsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPortalHospitalUploadFileUrlsQuery,
    GetPortalHospitalUploadFileUrlsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPortalHospitalUploadFileUrlsQuery,
    GetPortalHospitalUploadFileUrlsQueryVariables
  >(GetPortalHospitalUploadFileUrlsDocument, options);
}
export type GetPortalHospitalUploadFileUrlsQueryHookResult = ReturnType<
  typeof useGetPortalHospitalUploadFileUrlsQuery
>;
export type GetPortalHospitalUploadFileUrlsLazyQueryHookResult = ReturnType<
  typeof useGetPortalHospitalUploadFileUrlsLazyQuery
>;
export type GetPortalHospitalUploadFileUrlsSuspenseQueryHookResult = ReturnType<
  typeof useGetPortalHospitalUploadFileUrlsSuspenseQuery
>;
export type GetPortalHospitalUploadFileUrlsQueryResult = Apollo.QueryResult<
  GetPortalHospitalUploadFileUrlsQuery,
  GetPortalHospitalUploadFileUrlsQueryVariables
>;
export const GetPortalHospitalStaffUploadFileUrlsDocument = gql`
  query getPortalHospitalStaffUploadFileUrls(
    $hospitalStaffId: Int!
    $input: [GetPortalHospitalStaffUploadFileUrlInput!]!
  ) {
    getPortalHospitalStaffUploadFileUrls(
      hospitalStaffId: $hospitalStaffId
      input: $input
    ) {
      fileNameWithExtension
      url
    }
  }
`;

/**
 * __useGetPortalHospitalStaffUploadFileUrlsQuery__
 *
 * To run a query within a React component, call `useGetPortalHospitalStaffUploadFileUrlsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPortalHospitalStaffUploadFileUrlsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPortalHospitalStaffUploadFileUrlsQuery({
 *   variables: {
 *      hospitalStaffId: // value for 'hospitalStaffId'
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPortalHospitalStaffUploadFileUrlsQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPortalHospitalStaffUploadFileUrlsQuery,
    GetPortalHospitalStaffUploadFileUrlsQueryVariables
  > &
    (
      | {
          variables: GetPortalHospitalStaffUploadFileUrlsQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPortalHospitalStaffUploadFileUrlsQuery,
    GetPortalHospitalStaffUploadFileUrlsQueryVariables
  >(GetPortalHospitalStaffUploadFileUrlsDocument, options);
}
export function useGetPortalHospitalStaffUploadFileUrlsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPortalHospitalStaffUploadFileUrlsQuery,
    GetPortalHospitalStaffUploadFileUrlsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPortalHospitalStaffUploadFileUrlsQuery,
    GetPortalHospitalStaffUploadFileUrlsQueryVariables
  >(GetPortalHospitalStaffUploadFileUrlsDocument, options);
}
export function useGetPortalHospitalStaffUploadFileUrlsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPortalHospitalStaffUploadFileUrlsQuery,
    GetPortalHospitalStaffUploadFileUrlsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPortalHospitalStaffUploadFileUrlsQuery,
    GetPortalHospitalStaffUploadFileUrlsQueryVariables
  >(GetPortalHospitalStaffUploadFileUrlsDocument, options);
}
export type GetPortalHospitalStaffUploadFileUrlsQueryHookResult = ReturnType<
  typeof useGetPortalHospitalStaffUploadFileUrlsQuery
>;
export type GetPortalHospitalStaffUploadFileUrlsLazyQueryHookResult =
  ReturnType<typeof useGetPortalHospitalStaffUploadFileUrlsLazyQuery>;
export type GetPortalHospitalStaffUploadFileUrlsSuspenseQueryHookResult =
  ReturnType<typeof useGetPortalHospitalStaffUploadFileUrlsSuspenseQuery>;
export type GetPortalHospitalStaffUploadFileUrlsQueryResult =
  Apollo.QueryResult<
    GetPortalHospitalStaffUploadFileUrlsQuery,
    GetPortalHospitalStaffUploadFileUrlsQueryVariables
  >;
export const GetPortalMasterStationByKeywordDocument = gql`
  query getPortalMasterStationByKeyword($input: String!) {
    getPortalMasterStationByKeyword(input: $input) {
      stationId
      name
      railline {
        raillineId
        name
        raillineCompanyId
      }
    }
  }
`;

/**
 * __useGetPortalMasterStationByKeywordQuery__
 *
 * To run a query within a React component, call `useGetPortalMasterStationByKeywordQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPortalMasterStationByKeywordQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPortalMasterStationByKeywordQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPortalMasterStationByKeywordQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPortalMasterStationByKeywordQuery,
    GetPortalMasterStationByKeywordQueryVariables
  > &
    (
      | {
          variables: GetPortalMasterStationByKeywordQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPortalMasterStationByKeywordQuery,
    GetPortalMasterStationByKeywordQueryVariables
  >(GetPortalMasterStationByKeywordDocument, options);
}
export function useGetPortalMasterStationByKeywordLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPortalMasterStationByKeywordQuery,
    GetPortalMasterStationByKeywordQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPortalMasterStationByKeywordQuery,
    GetPortalMasterStationByKeywordQueryVariables
  >(GetPortalMasterStationByKeywordDocument, options);
}
export function useGetPortalMasterStationByKeywordSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPortalMasterStationByKeywordQuery,
    GetPortalMasterStationByKeywordQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPortalMasterStationByKeywordQuery,
    GetPortalMasterStationByKeywordQueryVariables
  >(GetPortalMasterStationByKeywordDocument, options);
}
export type GetPortalMasterStationByKeywordQueryHookResult = ReturnType<
  typeof useGetPortalMasterStationByKeywordQuery
>;
export type GetPortalMasterStationByKeywordLazyQueryHookResult = ReturnType<
  typeof useGetPortalMasterStationByKeywordLazyQuery
>;
export type GetPortalMasterStationByKeywordSuspenseQueryHookResult = ReturnType<
  typeof useGetPortalMasterStationByKeywordSuspenseQuery
>;
export type GetPortalMasterStationByKeywordQueryResult = Apollo.QueryResult<
  GetPortalMasterStationByKeywordQuery,
  GetPortalMasterStationByKeywordQueryVariables
>;
export const GetPortalHospitalDocument = gql`
  query getPortalHospital {
    getPortalHospitalById {
      portalHospital {
        name
        postCode
        telephone
        address1
        address2
        accessDetail
        hospitalId
        homePage
        directorName
        mailAddress
        specialists {
          specialistId
        }
        isCarpark
        paymentDetails
        carparkDetail
        descriptionTitle
        description
        isActive
        timelineDescription
        holidayDetail
        hospitalStations {
          stationId
          walkingMinute
          stationName
        }
        businessTimes {
          businessTimeId
          startTime
          endTime
          monFlag
          tueFlag
          wedFlag
          thuFlag
          friFlag
          satFlag
          sunFlag
        }
        tags {
          tagId
        }
        examinations {
          examinationId
        }
        files {
          fileId
          originalFileName
          s3Key
          createdAt
        }
      }
    }
  }
`;

/**
 * __useGetPortalHospitalQuery__
 *
 * To run a query within a React component, call `useGetPortalHospitalQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPortalHospitalQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPortalHospitalQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetPortalHospitalQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetPortalHospitalQuery,
    GetPortalHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPortalHospitalQuery,
    GetPortalHospitalQueryVariables
  >(GetPortalHospitalDocument, options);
}
export function useGetPortalHospitalLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPortalHospitalQuery,
    GetPortalHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPortalHospitalQuery,
    GetPortalHospitalQueryVariables
  >(GetPortalHospitalDocument, options);
}
export function useGetPortalHospitalSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPortalHospitalQuery,
    GetPortalHospitalQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPortalHospitalQuery,
    GetPortalHospitalQueryVariables
  >(GetPortalHospitalDocument, options);
}
export type GetPortalHospitalQueryHookResult = ReturnType<
  typeof useGetPortalHospitalQuery
>;
export type GetPortalHospitalLazyQueryHookResult = ReturnType<
  typeof useGetPortalHospitalLazyQuery
>;
export type GetPortalHospitalSuspenseQueryHookResult = ReturnType<
  typeof useGetPortalHospitalSuspenseQuery
>;
export type GetPortalHospitalQueryResult = Apollo.QueryResult<
  GetPortalHospitalQuery,
  GetPortalHospitalQueryVariables
>;
export const UpdatePortalHospitalDocument = gql`
  mutation updatePortalHospital($input: UpdatePortalHospitalInput!) {
    updatePortalHospital(input: $input)
  }
`;
export type UpdatePortalHospitalMutationFn = Apollo.MutationFunction<
  UpdatePortalHospitalMutation,
  UpdatePortalHospitalMutationVariables
>;

/**
 * __useUpdatePortalHospitalMutation__
 *
 * To run a mutation, you first call `useUpdatePortalHospitalMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePortalHospitalMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePortalHospitalMutation, { data, loading, error }] = useUpdatePortalHospitalMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePortalHospitalMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePortalHospitalMutation,
    UpdatePortalHospitalMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePortalHospitalMutation,
    UpdatePortalHospitalMutationVariables
  >(UpdatePortalHospitalDocument, options);
}
export type UpdatePortalHospitalMutationHookResult = ReturnType<
  typeof useUpdatePortalHospitalMutation
>;
export type UpdatePortalHospitalMutationResult =
  Apollo.MutationResult<UpdatePortalHospitalMutation>;
export type UpdatePortalHospitalMutationOptions = Apollo.BaseMutationOptions<
  UpdatePortalHospitalMutation,
  UpdatePortalHospitalMutationVariables
>;
export const GetHospitalInfoByHomepageUrlDocument = gql`
  query getHospitalInfoByHomepageUrl(
    $input: GetHospitalInfoByHomepageUrlInput
  ) {
    getHospitalInfoByHomepageUrl(input: $input) {
      description
    }
  }
`;

/**
 * __useGetHospitalInfoByHomepageUrlQuery__
 *
 * To run a query within a React component, call `useGetHospitalInfoByHomepageUrlQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetHospitalInfoByHomepageUrlQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetHospitalInfoByHomepageUrlQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetHospitalInfoByHomepageUrlQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetHospitalInfoByHomepageUrlQuery,
    GetHospitalInfoByHomepageUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetHospitalInfoByHomepageUrlQuery,
    GetHospitalInfoByHomepageUrlQueryVariables
  >(GetHospitalInfoByHomepageUrlDocument, options);
}
export function useGetHospitalInfoByHomepageUrlLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetHospitalInfoByHomepageUrlQuery,
    GetHospitalInfoByHomepageUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetHospitalInfoByHomepageUrlQuery,
    GetHospitalInfoByHomepageUrlQueryVariables
  >(GetHospitalInfoByHomepageUrlDocument, options);
}
export function useGetHospitalInfoByHomepageUrlSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetHospitalInfoByHomepageUrlQuery,
    GetHospitalInfoByHomepageUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetHospitalInfoByHomepageUrlQuery,
    GetHospitalInfoByHomepageUrlQueryVariables
  >(GetHospitalInfoByHomepageUrlDocument, options);
}
export type GetHospitalInfoByHomepageUrlQueryHookResult = ReturnType<
  typeof useGetHospitalInfoByHomepageUrlQuery
>;
export type GetHospitalInfoByHomepageUrlLazyQueryHookResult = ReturnType<
  typeof useGetHospitalInfoByHomepageUrlLazyQuery
>;
export type GetHospitalInfoByHomepageUrlSuspenseQueryHookResult = ReturnType<
  typeof useGetHospitalInfoByHomepageUrlSuspenseQuery
>;
export type GetHospitalInfoByHomepageUrlQueryResult = Apollo.QueryResult<
  GetHospitalInfoByHomepageUrlQuery,
  GetHospitalInfoByHomepageUrlQueryVariables
>;
export const GetApiReceptionGetHpInfDocument = gql`
  query getApiReceptionGetHpInf {
    getApiReceptionGetHpInf {
      data {
        hpInf {
          address1
          address2
          faxNo
          hpCd
          hpId
          hpName
          kaisetuName
          otherContacts
          prefNo
          receHpName
          rousaiHpCd
          startDate
          tel
          updateId
          postCd
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiReceptionGetHpInfQuery__
 *
 * To run a query within a React component, call `useGetApiReceptionGetHpInfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceptionGetHpInfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceptionGetHpInfQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiReceptionGetHpInfQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceptionGetHpInfQuery,
    GetApiReceptionGetHpInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceptionGetHpInfQuery,
    GetApiReceptionGetHpInfQueryVariables
  >(GetApiReceptionGetHpInfDocument, options);
}
export function useGetApiReceptionGetHpInfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceptionGetHpInfQuery,
    GetApiReceptionGetHpInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceptionGetHpInfQuery,
    GetApiReceptionGetHpInfQueryVariables
  >(GetApiReceptionGetHpInfDocument, options);
}
export function useGetApiReceptionGetHpInfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceptionGetHpInfQuery,
    GetApiReceptionGetHpInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceptionGetHpInfQuery,
    GetApiReceptionGetHpInfQueryVariables
  >(GetApiReceptionGetHpInfDocument, options);
}
export type GetApiReceptionGetHpInfQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetHpInfQuery
>;
export type GetApiReceptionGetHpInfLazyQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetHpInfLazyQuery
>;
export type GetApiReceptionGetHpInfSuspenseQueryHookResult = ReturnType<
  typeof useGetApiReceptionGetHpInfSuspenseQuery
>;
export type GetApiReceptionGetHpInfQueryResult = Apollo.QueryResult<
  GetApiReceptionGetHpInfQuery,
  GetApiReceptionGetHpInfQueryVariables
>;
