import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiEpsCreateEpsReferenceMutationVariables = Types.Exact<{
  emrCloudApiRequestsEpsCreateEpsReferenceRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsEpsCreateEpsReferenceRequestInput>;
}>;

export type PostApiEpsCreateEpsReferenceMutation = {
  __typename?: "mutation_root";
  postApiEpsCreateEpsReference?: {
    __typename?: "EmrCloudApiResponsesResponse1CreateEpsReferenceResponse";
    message?: string;
    status?: number;
    data?: { __typename?: "CreateEpsReferenceResponse"; isSuccess?: boolean };
  };
};

export type GetApiEpsValidateBeforePrintingOutPatientQueryVariables =
  Types.Exact<{
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiEpsValidateBeforePrintingOutPatientQuery = {
  __typename?: "query_root";
  getApiEpsValidateBeforePrinting?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsValidateBeforePrintingResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsValidateBeforePrintingResponse";
      epsReferences?: Array<{
        __typename?: "DomainModelsEpsReferenceEpsReferenceModel";
        createDate?: string;
        createId?: number;
        createMachine?: string;
        hpId?: number;
        prescriptionId?: string;
        prescriptionReferenceInformation?: string;
        ptId?: string;
        raiinNo?: string;
        sinDate?: number;
      }>;
      epsPrescriptions?: Array<{
        __typename?: "DomainModelsEpsPrescriptionEpsPrescriptionModel";
        accessCode?: string;
        bango?: string;
        createDate?: string;
        createId?: number;
        deleteReasonDisplay?: string;
        deletedDate?: string;
        deletedReason?: number;
        dispensingDate?: number;
        dispensingDateDisplay?: string;
        edaNo?: string;
        epsUpdateDateTime?: string;
        hokenDisplay?: string;
        hokensyaNo?: string;
        hpId?: number;
        id?: number;
        issueType?: number;
        issueTypeDisplay?: string;
        kaId?: number;
        kaSName?: string;
        kigo?: string;
        kohiFutansyaNo?: string;
        kohiJyukyusyaNo?: string;
        messageFlag?: string;
        pharmacyName?: string;
        prescriptionDocument?: string;
        prescriptionId?: string;
        ptId?: string;
        ptName?: string;
        ptNum?: string;
        ptNumDisplay?: string;
        raiinNo?: string;
        refileCount?: number;
        refill?: string;
        resultType?: number;
        resultTypeDisplay?: string;
        seqNo?: string;
        sinDate?: number;
        sinDateDisplay?: string;
        status?: number;
        statusDisplay?: string;
        tantoId?: number;
        tantoName?: string;
        updateDate?: string;
        updateId?: number;
        hokenKbns?: Array<number>;
        epsDispensingModel?: Array<{
          __typename?: "DomainModelsEpsDispensingEpsDispensingModel";
          bango?: string;
          cancelReason?: string;
          createDate?: string;
          createId?: number;
          createMachine?: string;
          dispensingDate?: number;
          dispensingDocument?: string;
          dispensingResultId?: string;
          dispensingTimes?: number;
          edaNo?: string;
          epsUpdateDateTime?: string;
          hokensyaNo?: string;
          hpId?: number;
          id?: string;
          isDeleted?: number;
          kigo?: string;
          kohiFutansyaNo?: string;
          kohiJyukyusyaNo?: string;
          messageFlg?: number;
          prescriptionId?: string;
          ptId?: string;
          receptionPharmacyName?: string;
          resultType?: number;
          updateDate?: string;
          updateId?: number;
          updateMachine?: string;
        }>;
      }>;
      ptHokenPatterns?: Array<{
        __typename?: "DomainModelsEpsPtHokenPtn";
        endDate?: number;
        hokenId?: number;
        hokenKbn?: number;
        hokenMemo?: string;
        hokenPid?: number;
        hokenSbtCd?: number;
        kohi1Id?: number;
        kohi2Id?: number;
        kohi3Id?: number;
        kohi4Id?: number;
        ptId?: string;
        seqNo?: string;
        startDate?: number;
      }>;
      raiinInf?: {
        __typename?: "DomainModelsEpsRaiinInfModel";
        hpId?: number;
        prescriptionIssueSelect?: number;
        printEpsReference?: number;
        ptId?: string;
        raiinNo?: string;
        sinDate?: number;
      };
    };
  };
};

export const PostApiEpsCreateEpsReferenceDocument = gql`
  mutation postApiEpsCreateEpsReference(
    $emrCloudApiRequestsEpsCreateEpsReferenceRequestInput: EmrCloudApiRequestsEpsCreateEpsReferenceRequestInput
  ) {
    postApiEpsCreateEpsReference(
      emrCloudApiRequestsEpsCreateEpsReferenceRequestInput: $emrCloudApiRequestsEpsCreateEpsReferenceRequestInput
    ) {
      data {
        isSuccess
      }
      message
      status
    }
  }
`;
export type PostApiEpsCreateEpsReferenceMutationFn = Apollo.MutationFunction<
  PostApiEpsCreateEpsReferenceMutation,
  PostApiEpsCreateEpsReferenceMutationVariables
>;

/**
 * __usePostApiEpsCreateEpsReferenceMutation__
 *
 * To run a mutation, you first call `usePostApiEpsCreateEpsReferenceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsCreateEpsReferenceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsCreateEpsReferenceMutation, { data, loading, error }] = usePostApiEpsCreateEpsReferenceMutation({
 *   variables: {
 *      emrCloudApiRequestsEpsCreateEpsReferenceRequestInput: // value for 'emrCloudApiRequestsEpsCreateEpsReferenceRequestInput'
 *   },
 * });
 */
export function usePostApiEpsCreateEpsReferenceMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsCreateEpsReferenceMutation,
    PostApiEpsCreateEpsReferenceMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsCreateEpsReferenceMutation,
    PostApiEpsCreateEpsReferenceMutationVariables
  >(PostApiEpsCreateEpsReferenceDocument, options);
}
export type PostApiEpsCreateEpsReferenceMutationHookResult = ReturnType<
  typeof usePostApiEpsCreateEpsReferenceMutation
>;
export type PostApiEpsCreateEpsReferenceMutationResult =
  Apollo.MutationResult<PostApiEpsCreateEpsReferenceMutation>;
export type PostApiEpsCreateEpsReferenceMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsCreateEpsReferenceMutation,
    PostApiEpsCreateEpsReferenceMutationVariables
  >;
export const GetApiEpsValidateBeforePrintingOutPatientDocument = gql`
  query getApiEpsValidateBeforePrintingOutPatient(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiEpsValidateBeforePrinting(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      message
      status
      data {
        epsReferences {
          createDate
          createId
          createMachine
          hpId
          prescriptionId
          prescriptionReferenceInformation
          ptId
          raiinNo
          sinDate
        }
        epsPrescriptions {
          accessCode
          bango
          createDate
          createId
          deleteReasonDisplay
          deletedDate
          deletedReason
          dispensingDate
          dispensingDateDisplay
          edaNo
          epsUpdateDateTime
          hokenDisplay
          hokensyaNo
          hpId
          id
          issueType
          issueTypeDisplay
          kaId
          kaSName
          kigo
          kohiFutansyaNo
          kohiJyukyusyaNo
          messageFlag
          pharmacyName
          prescriptionDocument
          prescriptionId
          ptId
          ptName
          ptNum
          ptNumDisplay
          raiinNo
          refileCount
          refill
          resultType
          resultTypeDisplay
          seqNo
          sinDate
          sinDateDisplay
          status
          statusDisplay
          tantoId
          tantoName
          updateDate
          updateId
          hokenKbns
          epsDispensingModel {
            bango
            cancelReason
            createDate
            createId
            createMachine
            dispensingDate
            dispensingDocument
            dispensingResultId
            dispensingTimes
            edaNo
            epsUpdateDateTime
            hokensyaNo
            hpId
            id
            isDeleted
            kigo
            kohiFutansyaNo
            kohiJyukyusyaNo
            messageFlg
            prescriptionId
            ptId
            receptionPharmacyName
            resultType
            updateDate
            updateId
            updateMachine
          }
        }
        ptHokenPatterns {
          endDate
          hokenId
          hokenKbn
          hokenMemo
          hokenPid
          hokenSbtCd
          kohi1Id
          kohi2Id
          kohi3Id
          kohi4Id
          ptId
          seqNo
          startDate
        }
        raiinInf {
          hpId
          prescriptionIssueSelect
          printEpsReference
          ptId
          raiinNo
          sinDate
        }
      }
    }
  }
`;

/**
 * __useGetApiEpsValidateBeforePrintingOutPatientQuery__
 *
 * To run a query within a React component, call `useGetApiEpsValidateBeforePrintingOutPatientQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiEpsValidateBeforePrintingOutPatientQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiEpsValidateBeforePrintingOutPatientQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiEpsValidateBeforePrintingOutPatientQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiEpsValidateBeforePrintingOutPatientQuery,
    GetApiEpsValidateBeforePrintingOutPatientQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiEpsValidateBeforePrintingOutPatientQuery,
    GetApiEpsValidateBeforePrintingOutPatientQueryVariables
  >(GetApiEpsValidateBeforePrintingOutPatientDocument, options);
}
export function useGetApiEpsValidateBeforePrintingOutPatientLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiEpsValidateBeforePrintingOutPatientQuery,
    GetApiEpsValidateBeforePrintingOutPatientQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiEpsValidateBeforePrintingOutPatientQuery,
    GetApiEpsValidateBeforePrintingOutPatientQueryVariables
  >(GetApiEpsValidateBeforePrintingOutPatientDocument, options);
}
export function useGetApiEpsValidateBeforePrintingOutPatientSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiEpsValidateBeforePrintingOutPatientQuery,
    GetApiEpsValidateBeforePrintingOutPatientQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiEpsValidateBeforePrintingOutPatientQuery,
    GetApiEpsValidateBeforePrintingOutPatientQueryVariables
  >(GetApiEpsValidateBeforePrintingOutPatientDocument, options);
}
export type GetApiEpsValidateBeforePrintingOutPatientQueryHookResult =
  ReturnType<typeof useGetApiEpsValidateBeforePrintingOutPatientQuery>;
export type GetApiEpsValidateBeforePrintingOutPatientLazyQueryHookResult =
  ReturnType<typeof useGetApiEpsValidateBeforePrintingOutPatientLazyQuery>;
export type GetApiEpsValidateBeforePrintingOutPatientSuspenseQueryHookResult =
  ReturnType<typeof useGetApiEpsValidateBeforePrintingOutPatientSuspenseQuery>;
export type GetApiEpsValidateBeforePrintingOutPatientQueryResult =
  Apollo.QueryResult<
    GetApiEpsValidateBeforePrintingOutPatientQuery,
    GetApiEpsValidateBeforePrintingOutPatientQueryVariables
  >;
