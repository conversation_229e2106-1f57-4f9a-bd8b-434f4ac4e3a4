import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiSmartKartePortGetListQueryVariables = Types.Exact<{
  onlyCommon?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiSmartKartePortGetListQuery = {
  __typename?: "query_root";
  getApiSmartKartePortGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSmartKartePortGetPortListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSmartKartePortGetPortListResponse";
      agentSettings?: Array<{
        __typename?: "DomainModelsAgentSettingAgentSettingModel";
        commonHost?: string;
        host?: string;
        hpId?: number;
        isCommon?: number;
        port?: string;
        settingId?: number;
        updateDate?: string;
      }>;
    };
  };
};

export const GetApiSmartKartePortGetListDocument = gql`
  query getApiSmartKartePortGetList($onlyCommon: Boolean = false) {
    getApiSmartKartePortGetList(onlyCommon: $onlyCommon) {
      data {
        agentSettings {
          commonHost
          host
          hpId
          isCommon
          port
          settingId
          updateDate
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiSmartKartePortGetListQuery__
 *
 * To run a query within a React component, call `useGetApiSmartKartePortGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiSmartKartePortGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiSmartKartePortGetListQuery({
 *   variables: {
 *      onlyCommon: // value for 'onlyCommon'
 *   },
 * });
 */
export function useGetApiSmartKartePortGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiSmartKartePortGetListQuery,
    GetApiSmartKartePortGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiSmartKartePortGetListQuery,
    GetApiSmartKartePortGetListQueryVariables
  >(GetApiSmartKartePortGetListDocument, options);
}
export function useGetApiSmartKartePortGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiSmartKartePortGetListQuery,
    GetApiSmartKartePortGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiSmartKartePortGetListQuery,
    GetApiSmartKartePortGetListQueryVariables
  >(GetApiSmartKartePortGetListDocument, options);
}
export function useGetApiSmartKartePortGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiSmartKartePortGetListQuery,
    GetApiSmartKartePortGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiSmartKartePortGetListQuery,
    GetApiSmartKartePortGetListQueryVariables
  >(GetApiSmartKartePortGetListDocument, options);
}
export type GetApiSmartKartePortGetListQueryHookResult = ReturnType<
  typeof useGetApiSmartKartePortGetListQuery
>;
export type GetApiSmartKartePortGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiSmartKartePortGetListLazyQuery
>;
export type GetApiSmartKartePortGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiSmartKartePortGetListSuspenseQuery
>;
export type GetApiSmartKartePortGetListQueryResult = Apollo.QueryResult<
  GetApiSmartKartePortGetListQuery,
  GetApiSmartKartePortGetListQueryVariables
>;
