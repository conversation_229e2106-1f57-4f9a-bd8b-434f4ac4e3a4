import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiChartApprovalSaveMutationVariables = Types.Exact<{
  emrCloudApiRequestsChartApprovalSaveApprovalInfListRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsChartApprovalSaveApprovalInfListRequestInput>;
}>;

export type PostApiChartApprovalSaveMutation = {
  __typename?: "mutation_root";
  postApiChartApprovalSave?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesChartApprovalSaveApprovalInfListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesChartApprovalSaveApprovalInfListResponse";
      status?: number;
    };
  };
};

export const PostApiChartApprovalSaveDocument = gql`
  mutation postApiChartApprovalSave(
    $emrCloudApiRequestsChartApprovalSaveApprovalInfListRequestInput: EmrCloudApiRequestsChartApprovalSaveApprovalInfListRequestInput
  ) {
    postApiChartApprovalSave(
      emrCloudApiRequestsChartApprovalSaveApprovalInfListRequestInput: $emrCloudApiRequestsChartApprovalSaveApprovalInfListRequestInput
    ) {
      data {
        status
      }
    }
  }
`;
export type PostApiChartApprovalSaveMutationFn = Apollo.MutationFunction<
  PostApiChartApprovalSaveMutation,
  PostApiChartApprovalSaveMutationVariables
>;

/**
 * __usePostApiChartApprovalSaveMutation__
 *
 * To run a mutation, you first call `usePostApiChartApprovalSaveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiChartApprovalSaveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiChartApprovalSaveMutation, { data, loading, error }] = usePostApiChartApprovalSaveMutation({
 *   variables: {
 *      emrCloudApiRequestsChartApprovalSaveApprovalInfListRequestInput: // value for 'emrCloudApiRequestsChartApprovalSaveApprovalInfListRequestInput'
 *   },
 * });
 */
export function usePostApiChartApprovalSaveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiChartApprovalSaveMutation,
    PostApiChartApprovalSaveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiChartApprovalSaveMutation,
    PostApiChartApprovalSaveMutationVariables
  >(PostApiChartApprovalSaveDocument, options);
}
export type PostApiChartApprovalSaveMutationHookResult = ReturnType<
  typeof usePostApiChartApprovalSaveMutation
>;
export type PostApiChartApprovalSaveMutationResult =
  Apollo.MutationResult<PostApiChartApprovalSaveMutation>;
export type PostApiChartApprovalSaveMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiChartApprovalSaveMutation,
    PostApiChartApprovalSaveMutationVariables
  >;
