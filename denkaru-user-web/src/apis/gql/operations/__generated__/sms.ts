import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type SendSmsForPharmacyReserveMutationVariables = Types.Exact<{
  input: Types.SendSmsForPharmacyReserveReq;
}>;

export type SendSmsForPharmacyReserveMutation = {
  __typename?: "mutation_root";
  sendSMSForPharmacyReserve: boolean;
};

export type GetSmsTemplateForPharmacyReserveQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetSmsTemplateForPharmacyReserveQuery = {
  __typename?: "query_root";
  getInventoryShortagePharmacyReserveTemplate: {
    __typename?: "smsOtherTemplateRes";
    smsCode: string;
    smsTemplateBody: string;
  };
  getPrescriptionNotYetReceivedTemplate: {
    __typename?: "smsOtherTemplateRes";
    smsCode: string;
    smsTemplateBody: string;
  };
  getReturnedDeliveryItemsTemplate: {
    __typename?: "smsOtherTemplateRes";
    smsCode: string;
    smsTemplateBody: string;
  };
  getFreeFormatPharmacyReserveTemplate: {
    __typename?: "smsOtherTemplateRes";
    smsCode: string;
    smsTemplateBody: string;
  };
};

export type SendSmsForPrescriptionReceptionMutationVariables = Types.Exact<{
  input: Types.SendSmsPrescriptionReceptionInput;
}>;

export type SendSmsForPrescriptionReceptionMutation = {
  __typename?: "mutation_root";
  sendSMSForPrescriptionReception: boolean;
};

export type GetSmsTemplateForPrescriptionReceptionQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetSmsTemplateForPrescriptionReceptionQuery = {
  __typename?: "query_root";
  getMedicationReadyTemplate: {
    __typename?: "smsOtherTemplateRes";
    smsCode: string;
    smsTemplateBody: string;
  };
  getInventoryShortagePrescriptionReceptionTemplate: {
    __typename?: "smsOtherTemplateRes";
    smsCode: string;
    smsTemplateBody: string;
  };
  getFreeFormatPrescriptionReceptionTemplate: {
    __typename?: "smsOtherTemplateRes";
    smsCode: string;
    smsTemplateBody: string;
  };
};

export type SendSmsForSignupMutationVariables = Types.Exact<{
  input: Types.SendSmsForSignupReq;
}>;

export type SendSmsForSignupMutation = {
  __typename?: "mutation_root";
  sendSMSForSignup: boolean;
};

export const SendSmsForPharmacyReserveDocument = gql`
  mutation sendSMSForPharmacyReserve($input: sendSMSForPharmacyReserveReq!) {
    sendSMSForPharmacyReserve(input: $input)
  }
`;
export type SendSmsForPharmacyReserveMutationFn = Apollo.MutationFunction<
  SendSmsForPharmacyReserveMutation,
  SendSmsForPharmacyReserveMutationVariables
>;

/**
 * __useSendSmsForPharmacyReserveMutation__
 *
 * To run a mutation, you first call `useSendSmsForPharmacyReserveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSendSmsForPharmacyReserveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sendSmsForPharmacyReserveMutation, { data, loading, error }] = useSendSmsForPharmacyReserveMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSendSmsForPharmacyReserveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SendSmsForPharmacyReserveMutation,
    SendSmsForPharmacyReserveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SendSmsForPharmacyReserveMutation,
    SendSmsForPharmacyReserveMutationVariables
  >(SendSmsForPharmacyReserveDocument, options);
}
export type SendSmsForPharmacyReserveMutationHookResult = ReturnType<
  typeof useSendSmsForPharmacyReserveMutation
>;
export type SendSmsForPharmacyReserveMutationResult =
  Apollo.MutationResult<SendSmsForPharmacyReserveMutation>;
export type SendSmsForPharmacyReserveMutationOptions =
  Apollo.BaseMutationOptions<
    SendSmsForPharmacyReserveMutation,
    SendSmsForPharmacyReserveMutationVariables
  >;
export const GetSmsTemplateForPharmacyReserveDocument = gql`
  query getSMSTemplateForPharmacyReserve {
    getInventoryShortagePharmacyReserveTemplate: getSMSTemplateForPharmacyReserve(
      input: { smsCode: "InventoryShortagePharmacyReserve" }
    ) {
      smsCode
      smsTemplateBody
    }
    getPrescriptionNotYetReceivedTemplate: getSMSTemplateForPharmacyReserve(
      input: { smsCode: "PrescriptionNotYetReceived" }
    ) {
      smsCode
      smsTemplateBody
    }
    getReturnedDeliveryItemsTemplate: getSMSTemplateForPharmacyReserve(
      input: { smsCode: "ReturnedDeliveryItems" }
    ) {
      smsCode
      smsTemplateBody
    }
    getFreeFormatPharmacyReserveTemplate: getSMSTemplateForPharmacyReserve(
      input: { smsCode: "FreeFormatPharmacyReserve" }
    ) {
      smsCode
      smsTemplateBody
    }
  }
`;

/**
 * __useGetSmsTemplateForPharmacyReserveQuery__
 *
 * To run a query within a React component, call `useGetSmsTemplateForPharmacyReserveQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSmsTemplateForPharmacyReserveQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSmsTemplateForPharmacyReserveQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetSmsTemplateForPharmacyReserveQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetSmsTemplateForPharmacyReserveQuery,
    GetSmsTemplateForPharmacyReserveQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSmsTemplateForPharmacyReserveQuery,
    GetSmsTemplateForPharmacyReserveQueryVariables
  >(GetSmsTemplateForPharmacyReserveDocument, options);
}
export function useGetSmsTemplateForPharmacyReserveLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSmsTemplateForPharmacyReserveQuery,
    GetSmsTemplateForPharmacyReserveQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSmsTemplateForPharmacyReserveQuery,
    GetSmsTemplateForPharmacyReserveQueryVariables
  >(GetSmsTemplateForPharmacyReserveDocument, options);
}
export function useGetSmsTemplateForPharmacyReserveSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSmsTemplateForPharmacyReserveQuery,
    GetSmsTemplateForPharmacyReserveQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSmsTemplateForPharmacyReserveQuery,
    GetSmsTemplateForPharmacyReserveQueryVariables
  >(GetSmsTemplateForPharmacyReserveDocument, options);
}
export type GetSmsTemplateForPharmacyReserveQueryHookResult = ReturnType<
  typeof useGetSmsTemplateForPharmacyReserveQuery
>;
export type GetSmsTemplateForPharmacyReserveLazyQueryHookResult = ReturnType<
  typeof useGetSmsTemplateForPharmacyReserveLazyQuery
>;
export type GetSmsTemplateForPharmacyReserveSuspenseQueryHookResult =
  ReturnType<typeof useGetSmsTemplateForPharmacyReserveSuspenseQuery>;
export type GetSmsTemplateForPharmacyReserveQueryResult = Apollo.QueryResult<
  GetSmsTemplateForPharmacyReserveQuery,
  GetSmsTemplateForPharmacyReserveQueryVariables
>;
export const SendSmsForPrescriptionReceptionDocument = gql`
  mutation sendSMSForPrescriptionReception(
    $input: sendSMSPrescriptionReceptionInput!
  ) {
    sendSMSForPrescriptionReception(input: $input)
  }
`;
export type SendSmsForPrescriptionReceptionMutationFn = Apollo.MutationFunction<
  SendSmsForPrescriptionReceptionMutation,
  SendSmsForPrescriptionReceptionMutationVariables
>;

/**
 * __useSendSmsForPrescriptionReceptionMutation__
 *
 * To run a mutation, you first call `useSendSmsForPrescriptionReceptionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSendSmsForPrescriptionReceptionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sendSmsForPrescriptionReceptionMutation, { data, loading, error }] = useSendSmsForPrescriptionReceptionMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSendSmsForPrescriptionReceptionMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SendSmsForPrescriptionReceptionMutation,
    SendSmsForPrescriptionReceptionMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SendSmsForPrescriptionReceptionMutation,
    SendSmsForPrescriptionReceptionMutationVariables
  >(SendSmsForPrescriptionReceptionDocument, options);
}
export type SendSmsForPrescriptionReceptionMutationHookResult = ReturnType<
  typeof useSendSmsForPrescriptionReceptionMutation
>;
export type SendSmsForPrescriptionReceptionMutationResult =
  Apollo.MutationResult<SendSmsForPrescriptionReceptionMutation>;
export type SendSmsForPrescriptionReceptionMutationOptions =
  Apollo.BaseMutationOptions<
    SendSmsForPrescriptionReceptionMutation,
    SendSmsForPrescriptionReceptionMutationVariables
  >;
export const GetSmsTemplateForPrescriptionReceptionDocument = gql`
  query getSMSTemplateForPrescriptionReception {
    getMedicationReadyTemplate: getSMSTemplateForPrescriptionReception(
      input: { smsCode: "MedicationReady" }
    ) {
      smsCode
      smsTemplateBody
    }
    getInventoryShortagePrescriptionReceptionTemplate: getSMSTemplateForPrescriptionReception(
      input: { smsCode: "InventoryShortagePrescriptionReception" }
    ) {
      smsCode
      smsTemplateBody
    }
    getFreeFormatPrescriptionReceptionTemplate: getSMSTemplateForPrescriptionReception(
      input: { smsCode: "FreeFormatPrescriptionReception" }
    ) {
      smsCode
      smsTemplateBody
    }
  }
`;

/**
 * __useGetSmsTemplateForPrescriptionReceptionQuery__
 *
 * To run a query within a React component, call `useGetSmsTemplateForPrescriptionReceptionQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSmsTemplateForPrescriptionReceptionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSmsTemplateForPrescriptionReceptionQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetSmsTemplateForPrescriptionReceptionQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetSmsTemplateForPrescriptionReceptionQuery,
    GetSmsTemplateForPrescriptionReceptionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSmsTemplateForPrescriptionReceptionQuery,
    GetSmsTemplateForPrescriptionReceptionQueryVariables
  >(GetSmsTemplateForPrescriptionReceptionDocument, options);
}
export function useGetSmsTemplateForPrescriptionReceptionLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSmsTemplateForPrescriptionReceptionQuery,
    GetSmsTemplateForPrescriptionReceptionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSmsTemplateForPrescriptionReceptionQuery,
    GetSmsTemplateForPrescriptionReceptionQueryVariables
  >(GetSmsTemplateForPrescriptionReceptionDocument, options);
}
export function useGetSmsTemplateForPrescriptionReceptionSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSmsTemplateForPrescriptionReceptionQuery,
    GetSmsTemplateForPrescriptionReceptionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSmsTemplateForPrescriptionReceptionQuery,
    GetSmsTemplateForPrescriptionReceptionQueryVariables
  >(GetSmsTemplateForPrescriptionReceptionDocument, options);
}
export type GetSmsTemplateForPrescriptionReceptionQueryHookResult = ReturnType<
  typeof useGetSmsTemplateForPrescriptionReceptionQuery
>;
export type GetSmsTemplateForPrescriptionReceptionLazyQueryHookResult =
  ReturnType<typeof useGetSmsTemplateForPrescriptionReceptionLazyQuery>;
export type GetSmsTemplateForPrescriptionReceptionSuspenseQueryHookResult =
  ReturnType<typeof useGetSmsTemplateForPrescriptionReceptionSuspenseQuery>;
export type GetSmsTemplateForPrescriptionReceptionQueryResult =
  Apollo.QueryResult<
    GetSmsTemplateForPrescriptionReceptionQuery,
    GetSmsTemplateForPrescriptionReceptionQueryVariables
  >;
export const SendSmsForSignupDocument = gql`
  mutation sendSMSForSignup($input: sendSMSForSignupReq!) {
    sendSMSForSignup(input: $input)
  }
`;
export type SendSmsForSignupMutationFn = Apollo.MutationFunction<
  SendSmsForSignupMutation,
  SendSmsForSignupMutationVariables
>;

/**
 * __useSendSmsForSignupMutation__
 *
 * To run a mutation, you first call `useSendSmsForSignupMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSendSmsForSignupMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sendSmsForSignupMutation, { data, loading, error }] = useSendSmsForSignupMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSendSmsForSignupMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SendSmsForSignupMutation,
    SendSmsForSignupMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SendSmsForSignupMutation,
    SendSmsForSignupMutationVariables
  >(SendSmsForSignupDocument, options);
}
export type SendSmsForSignupMutationHookResult = ReturnType<
  typeof useSendSmsForSignupMutation
>;
export type SendSmsForSignupMutationResult =
  Apollo.MutationResult<SendSmsForSignupMutation>;
export type SendSmsForSignupMutationOptions = Apollo.BaseMutationOptions<
  SendSmsForSignupMutation,
  SendSmsForSignupMutationVariables
>;
