import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiSetSendaiGenerationGetListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiSetSendaiGenerationGetListQuery = {
  __typename?: "query_root";
  getApiSetSendaiGenerationGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesGetSendaiGenerationSetSendaiGenerationGetListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesGetSendaiGenerationSetSendaiGenerationGetListResponse";
      listData?: Array<{
        __typename?: "DomainModelsSetGenerationMstSetSendaiGenerationModel";
        setSendaiGenerationPersons?: Array<{
          __typename?: "DomainModelsSetGenerationMstSetSendaiGenerationPersonModel";
          createDate?: string;
          createDateStr?: string;
          endDate?: number;
          endDateStr?: string;
          generationId?: number;
          hpId?: number;
          indexRow?: number;
          setSbt?: number;
          startDate?: number;
          startDateStr?: string;
          userId?: number;
        }>;
        setSendaiGenerationShares?: Array<{
          __typename?: "DomainModelsSetGenerationMstSetSendaiGenerationShareModel";
          createDate?: string;
          createDateStr?: string;
          endDate?: number;
          endDateStr?: string;
          generationId?: number;
          hpId?: number;
          indexRow?: number;
          setSbt?: number;
          startDate?: number;
          startDateStr?: string;
          userId?: number;
        }>;
      }>;
    };
  };
};

export type SetTreeFieldsFragment = {
  __typename?: "UseCaseSetMstGetListGetSetMstListOutputItem";
  color?: number;
  generationId?: number;
  hpId?: number;
  isDeleted?: number;
  isGroup?: number;
  level1?: number;
  level2?: number;
  level3?: number;
  setCd?: number;
  setKana?: string;
  setKbn?: number;
  setKbnEdaNo?: number;
  setName?: string;
  userId?: number;
  weightKbn?: number;
};

export type GetApiSetGetListQueryVariables = Types.Exact<{
  generationId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isPersonal?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  textSearch?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiSetGetListQuery = {
  __typename?: "query_root";
  getApiSetGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSetMstGetSetMstListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesSetMstGetSetMstListResponse";
      data?: Array<{
        __typename?: "UseCaseSetMstGetListGetSetMstListOutputItem";
        color?: number;
        generationId?: number;
        hpId?: number;
        isDeleted?: number;
        isGroup?: number;
        level1?: number;
        level2?: number;
        level3?: number;
        setCd?: number;
        setKana?: string;
        setKbn?: number;
        setKbnEdaNo?: number;
        setName?: string;
        userId?: number;
        weightKbn?: number;
        childrens?: Array<{
          __typename?: "UseCaseSetMstGetListGetSetMstListOutputItem";
          color?: number;
          generationId?: number;
          hpId?: number;
          isDeleted?: number;
          isGroup?: number;
          level1?: number;
          level2?: number;
          level3?: number;
          setCd?: number;
          setKana?: string;
          setKbn?: number;
          setKbnEdaNo?: number;
          setName?: string;
          userId?: number;
          weightKbn?: number;
          childrens?: Array<{
            __typename?: "UseCaseSetMstGetListGetSetMstListOutputItem";
            color?: number;
            generationId?: number;
            hpId?: number;
            isDeleted?: number;
            isGroup?: number;
            level1?: number;
            level2?: number;
            level3?: number;
            setCd?: number;
            setKana?: string;
            setKbn?: number;
            setKbnEdaNo?: number;
            setName?: string;
            userId?: number;
            weightKbn?: number;
          }>;
        }>;
      }>;
    };
  };
};

export type FolderTreeFieldsFragment = {
  __typename?: "UseCaseSetMstGetListGetSetMstListOutputItem";
  color?: number;
  generationId?: number;
  hpId?: number;
  isDeleted?: number;
  isGroup?: number;
  level1?: number;
  level2?: number;
  level3?: number;
  setCd?: number;
  setKana?: string;
  setKbn?: number;
  setKbnEdaNo?: number;
  setName?: string;
  userId?: number;
  weightKbn?: number;
};

export type PostApiSetSaveMutationVariables = Types.Exact<{
  emrCloudApiRequestsSetMstSaveSetMstRequestInput: Types.EmrCloudApiRequestsSetMstSaveSetMstRequestInput;
}>;

export type PostApiSetSaveMutation = {
  __typename?: "mutation_root";
  postApiSetSave?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSetMstSaveSetMstResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSetMstSaveSetMstResponse";
      data?: Array<{
        __typename?: "UseCaseSetMstGetListGetSetMstListOutputItem";
        color?: number;
        generationId?: number;
        hpId?: number;
        isDeleted?: number;
        isGroup?: number;
        level1?: number;
        level2?: number;
        level3?: number;
        setCd?: number;
        setKana?: string;
        setKbn?: number;
        setKbnEdaNo?: number;
        setName?: string;
        userId?: number;
        weightKbn?: number;
        childrens?: Array<{
          __typename?: "UseCaseSetMstGetListGetSetMstListOutputItem";
          color?: number;
          generationId?: number;
          hpId?: number;
          isDeleted?: number;
          isGroup?: number;
          level1?: number;
          level2?: number;
          level3?: number;
          setCd?: number;
          setKana?: string;
          setKbn?: number;
          setKbnEdaNo?: number;
          setName?: string;
          userId?: number;
          weightKbn?: number;
          childrens?: Array<{
            __typename?: "UseCaseSetMstGetListGetSetMstListOutputItem";
            color?: number;
            generationId?: number;
            hpId?: number;
            isDeleted?: number;
            isGroup?: number;
            level1?: number;
            level2?: number;
            level3?: number;
            setCd?: number;
            setKana?: string;
            setKbn?: number;
            setKbnEdaNo?: number;
            setName?: string;
            userId?: number;
            weightKbn?: number;
          }>;
        }>;
      }>;
    };
  };
};

export type GetApiSetGetToolTipQueryVariables = Types.Exact<{
  setCd?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiSetGetToolTipQuery = {
  __typename?: "query_root";
  getApiSetGetToolTip?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSetMstGetSetMstToolTipResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSetMstGetSetMstToolTipResponse";
      data?: {
        __typename?: "DomainModelsSetMstSetMstTooltipModel";
        dictionaryOrders?: any;
        listByomeis?: Array<string>;
        listKarteNames?: Array<string>;
      };
    };
  };
};

export type PostApiSetSendaiGenerationInsertMutationVariables = Types.Exact<{
  emrCloudApiRequestsSetSendaiGenerationAddSetSendaiGenerationRequestInput: Types.EmrCloudApiRequestsSetSendaiGenerationAddSetSendaiGenerationRequestInput;
}>;

export type PostApiSetSendaiGenerationInsertMutation = {
  __typename?: "mutation_root";
  postApiSetSendaiGenerationInsert: string;
};

export type PostApiSetSendaiGenerationDeleteMutationVariables = Types.Exact<{
  emrCloudApiRequestsSetSendaiGenerationDeleteSetSendaiGenerationRequestInput: Types.EmrCloudApiRequestsSetSendaiGenerationDeleteSetSendaiGenerationRequestInput;
}>;

export type PostApiSetSendaiGenerationDeleteMutation = {
  __typename?: "mutation_root";
  postApiSetSendaiGenerationDelete?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSetSendaiGenerationDeleteSetSendaiGenerationResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSetSendaiGenerationDeleteSetSendaiGenerationResponse";
      result?: boolean;
    };
  };
};

export type PostApiSetSaveSuperSetDetailMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsSetMstSaveSuperSetDetailRequestInput>;
}>;

export type PostApiSetSaveSuperSetDetailMutation = {
  __typename?: "mutation_root";
  postApiSetSaveSuperSetDetail?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSetMstSaveSuperSetDetailResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSetMstSaveSuperSetDetailResponse";
      status?: boolean;
    };
  };
};

export type GetApiSetGetSuperSetDetailQueryVariables = Types.Exact<{
  setCd?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiSetGetSuperSetDetailQuery = {
  __typename?: "query_root";
  getApiSetGetSuperSetDetail?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSetMstGetSuperSetDetailResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesSetMstGetSuperSetDetailResponse";
      data?: {
        __typename?: "UseCaseSuperSetDetailGetSuperSetDetailSuperSetDetailItem";
        setByomeiList?: Array<{
          __typename?: "UseCaseSuperSetDetailGetSuperSetDetailSetByomeiItem";
          byomeiCd?: string;
          byomeiCmt?: string;
          displayByomei?: string;
          fullByomei?: string;
          icd10?: string;
          icd1012013?: string;
          icd102013?: string;
          icd1022013?: string;
          id?: string;
          isDspKarte?: boolean;
          isDspRece?: boolean;
          isSuspected?: boolean;
          isSyobyoKbn?: boolean;
          nanByoCd?: number;
          sikkanKbn?: number;
          prefixSuffixList?: Array<{
            __typename?: "DomainModelsSuperSetDetailPrefixSuffixModel";
            code?: string;
            name?: string;
          }>;
        }>;
        setGroupOrderInfList?: Array<{
          __typename?: "UseCaseSuperSetDetailGetSuperSetDetailSetGroupOrderInfItem";
          groupKouiCode?: number;
          groupName?: string;
          guid?: string;
          inOutKbn?: number;
          inOutName?: string;
          isDrug?: boolean;
          isKensa?: boolean;
          isShowInOut?: boolean;
          isShowSantei?: boolean;
          isShowSikyu?: boolean;
          kouiCode?: number;
          santeiKbn?: number;
          santeiName?: string;
          sikyuKbn?: number;
          sikyuName?: string;
          syohoSbt?: number;
          tosekiKbn?: number;
          listSetOrdInfModels?: Array<{
            __typename?: "UseCaseSuperSetDetailGetSuperSetDetailSetOrderInfItem";
            createDate?: string;
            createId?: number;
            createName?: string;
            daysCnt?: number;
            groupKoui?: number;
            hpId?: number;
            id?: string;
            inoutKbn?: number;
            isDeleted?: number;
            isDrug?: boolean;
            isInjection?: boolean;
            isKensa?: boolean;
            isSelfInjection?: boolean;
            isShohoBiko?: boolean;
            isShohoComment?: boolean;
            isShohosenComment?: boolean;
            odrKouiKbn?: number;
            rpEdaNo?: string;
            rpName?: string;
            rpNo?: string;
            santeiKbn?: number;
            setCd?: number;
            sikyuKbn?: number;
            sortNo?: number;
            syohoSbt?: number;
            tosekiKbn?: number;
            ordInfDetails?: Array<{
              __typename?: "UseCaseSuperSetDetailGetSuperSetDetailSetOrderInfDetailItem";
              isSelectiveComment?: boolean;
              buiKbn?: number;
              rousaiKbn?: number;
              centerName?: string;
              centerCd?: string;
              bunkatu?: string;
              bunkatuKoui?: number;
              centerItemCd1?: string;
              centerItemCd2?: string;
              cmtCol1?: number;
              cmtCol2?: number;
              cmtCol3?: number;
              cmtCol4?: number;
              cmtColKeta1?: number;
              cmtColKeta2?: number;
              cmtColKeta3?: number;
              cmtColKeta4?: number;
              cmtName?: string;
              cmtOpt?: string;
              cnvTermVal?: number;
              commentNewline?: number;
              displayItemName?: string;
              displayedQuantity?: string;
              drugKbn?: number;
              editingQuantity?: string;
              fontColor?: string;
              handanGrpKbn?: number;
              hpId?: number;
              inOutKbn?: number;
              ipnCd?: string;
              ipnName?: string;
              is820Cmt?: boolean;
              is830Cmt?: boolean;
              is831Cmt?: boolean;
              is840Cmt?: boolean;
              is842Cmt?: boolean;
              is850Cmt?: boolean;
              is851Cmt?: boolean;
              is852Cmt?: boolean;
              isComment?: boolean;
              isDrug?: boolean;
              isDrugUsage?: boolean;
              isGetPriceInYakka?: boolean;
              isInjection?: boolean;
              isInjectionUsage?: boolean;
              isKensa?: boolean;
              isKensaMstEmpty?: boolean;
              isNodspRece?: number;
              isNormalComment?: boolean;
              isShohoBiko?: boolean;
              isShohoComment?: boolean;
              isSpecialItem?: boolean;
              isStandardUsage?: boolean;
              isSuppUsage?: boolean;
              isUsage?: boolean;
              isYoho?: boolean;
              itemCd?: string;
              itemName?: string;
              kasan1?: number;
              kasan2?: number;
              kensaGaichu?: number;
              kohatuKbn?: number;
              kokuji1?: string;
              kokuji2?: string;
              masterSbt?: string;
              odrTermVal?: number;
              price?: number;
              rowNo?: number;
              rpEdaNo?: string;
              rpNo?: string;
              setCd?: number;
              sinKouiKbn?: number;
              suryo?: number;
              syohoKbn?: number;
              syohoLimitKbn?: number;
              ten?: number;
              termVal?: number;
              unitName?: string;
              unitSBT?: number;
              yakka?: number;
              yjCd?: string;
              yohoKbn?: number;
              yohoSets?: Array<{
                __typename?: "DomainModelsOrdInfDetailsYohoSetMstModel";
                createDate?: string;
                createId?: number;
                createMachine?: string;
                hpId?: number;
                isDeleted?: number;
                isModified?: boolean;
                itemCd?: string;
                itemname?: string;
                setId?: number;
                sortNo?: number;
                updateDate?: string;
                updateId?: number;
                updateMachine?: string;
                userId?: number;
                yohoKbn?: number;
              }>;
            }>;
          }>;
        }>;
        setInfo?: {
          __typename?: "DomainModelsSuperSetDetailSetInfoModel";
          parentSetCd?: number;
          setKana?: string;
          setName?: string;
        };
        setKarteInf?: {
          __typename?: "DomainModelsSuperSetDetailSetKarteInfModel";
          hpId?: number;
          isDeleted?: number;
          richText?: string;
          seqNo?: string;
          setCd?: number;
          text?: string;
        };
      };
    };
  };
};

export type GetApiSetDoHistoryToSuperSetQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiSetDoHistoryToSuperSetQuery = {
  __typename?: "query_root";
  getApiSetDOHistoryToSuperSet?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSetMstDoHistoryToSuperSetResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesSetMstDoHistoryToSuperSetResponse";
      data?: {
        __typename?: "UseCaseSuperSetDetailGetSuperSetDetailSuperSetDetailItem";
        setByomeiList?: Array<{
          __typename?: "UseCaseSuperSetDetailGetSuperSetDetailSetByomeiItem";
          byomeiCd?: string;
          byomeiCmt?: string;
          displayByomei?: string;
          fullByomei?: string;
          icd10?: string;
          icd1012013?: string;
          icd102013?: string;
          icd1022013?: string;
          id?: string;
          isDspKarte?: boolean;
          isDspRece?: boolean;
          isSuspected?: boolean;
          isSyobyoKbn?: boolean;
          nanByoCd?: number;
          sikkanKbn?: number;
          prefixSuffixList?: Array<{
            __typename?: "DomainModelsSuperSetDetailPrefixSuffixModel";
            code?: string;
            name?: string;
          }>;
        }>;
        setGroupOrderInfList?: Array<{
          __typename?: "UseCaseSuperSetDetailGetSuperSetDetailSetGroupOrderInfItem";
          groupKouiCode?: number;
          groupName?: string;
          guid?: string;
          inOutKbn?: number;
          inOutName?: string;
          isDrug?: boolean;
          isKensa?: boolean;
          isShowInOut?: boolean;
          isShowSantei?: boolean;
          isShowSikyu?: boolean;
          kouiCode?: number;
          santeiKbn?: number;
          santeiName?: string;
          sikyuKbn?: number;
          sikyuName?: string;
          syohoSbt?: number;
          tosekiKbn?: number;
          listSetOrdInfModels?: Array<{
            __typename?: "UseCaseSuperSetDetailGetSuperSetDetailSetOrderInfItem";
            createDate?: string;
            createId?: number;
            createName?: string;
            daysCnt?: number;
            groupKoui?: number;
            hpId?: number;
            id?: string;
            inoutKbn?: number;
            isDeleted?: number;
            isDrug?: boolean;
            isInjection?: boolean;
            isKensa?: boolean;
            isSelfInjection?: boolean;
            isShohoBiko?: boolean;
            isShohoComment?: boolean;
            isShohosenComment?: boolean;
            odrKouiKbn?: number;
            rpEdaNo?: string;
            rpName?: string;
            rpNo?: string;
            santeiKbn?: number;
            setCd?: number;
            sikyuKbn?: number;
            sortNo?: number;
            syohoSbt?: number;
            tosekiKbn?: number;
            ordInfDetails?: Array<{
              __typename?: "UseCaseSuperSetDetailGetSuperSetDetailSetOrderInfDetailItem";
              isSelectiveComment?: boolean;
              bunkatu?: string;
              bunkatuKoui?: number;
              centerItemCd1?: string;
              centerItemCd2?: string;
              cmtCol1?: number;
              cmtCol2?: number;
              cmtCol3?: number;
              cmtCol4?: number;
              cmtColKeta1?: number;
              cmtColKeta2?: number;
              cmtColKeta3?: number;
              cmtColKeta4?: number;
              cmtName?: string;
              cmtOpt?: string;
              cnvTermVal?: number;
              commentNewline?: number;
              displayItemName?: string;
              displayedQuantity?: string;
              drugKbn?: number;
              editingQuantity?: string;
              fontColor?: string;
              handanGrpKbn?: number;
              hpId?: number;
              inOutKbn?: number;
              ipnCd?: string;
              ipnName?: string;
              is820Cmt?: boolean;
              is830Cmt?: boolean;
              is831Cmt?: boolean;
              is840Cmt?: boolean;
              is842Cmt?: boolean;
              is850Cmt?: boolean;
              is851Cmt?: boolean;
              is852Cmt?: boolean;
              isComment?: boolean;
              isDrug?: boolean;
              isDrugUsage?: boolean;
              isGetPriceInYakka?: boolean;
              isInjection?: boolean;
              isInjectionUsage?: boolean;
              isKensa?: boolean;
              isKensaMstEmpty?: boolean;
              isNodspRece?: number;
              isNormalComment?: boolean;
              isShohoBiko?: boolean;
              isShohoComment?: boolean;
              isSpecialItem?: boolean;
              isStandardUsage?: boolean;
              isSuppUsage?: boolean;
              isUsage?: boolean;
              isYoho?: boolean;
              itemCd?: string;
              itemName?: string;
              kasan1?: number;
              kasan2?: number;
              kensaGaichu?: number;
              kohatuKbn?: number;
              kokuji1?: string;
              kokuji2?: string;
              masterSbt?: string;
              odrTermVal?: number;
              price?: number;
              rowNo?: number;
              rpEdaNo?: string;
              rpNo?: string;
              setCd?: number;
              sinKouiKbn?: number;
              suryo?: number;
              syohoKbn?: number;
              syohoLimitKbn?: number;
              ten?: number;
              termVal?: number;
              unitName?: string;
              unitSBT?: number;
              yakka?: number;
              yjCd?: string;
              yohoKbn?: number;
              yohoSets?: Array<{
                __typename?: "DomainModelsOrdInfDetailsYohoSetMstModel";
                createDate?: string;
                createId?: number;
                createMachine?: string;
                hpId?: number;
                isDeleted?: number;
                isModified?: boolean;
                itemCd?: string;
                itemname?: string;
                setId?: number;
                sortNo?: number;
                updateDate?: string;
                updateId?: number;
                updateMachine?: string;
                userId?: number;
                yohoKbn?: number;
              }>;
            }>;
          }>;
        }>;
        setInfo?: {
          __typename?: "DomainModelsSuperSetDetailSetInfoModel";
          parentSetCd?: number;
          setKana?: string;
          setName?: string;
        };
        setKarteInf?: {
          __typename?: "DomainModelsSuperSetDetailSetKarteInfModel";
          hpId?: number;
          richText?: string;
          setCd?: number;
          text?: string;
        };
      };
    };
  };
};

export const SetTreeFieldsFragmentDoc = gql`
  fragment SetTreeFields on UseCaseSetMstGetListGetSetMstListOutputItem {
    color
    generationId
    hpId
    isDeleted
    isGroup
    level1
    level2
    level3
    setCd
    setKana
    setKbn
    setKbnEdaNo
    setName
    userId
    weightKbn
  }
`;
export const FolderTreeFieldsFragmentDoc = gql`
  fragment FolderTreeFields on UseCaseSetMstGetListGetSetMstListOutputItem {
    color
    generationId
    hpId
    isDeleted
    isGroup
    level1
    level2
    level3
    setCd
    setKana
    setKbn
    setKbnEdaNo
    setName
    userId
    weightKbn
  }
`;
export const GetApiSetSendaiGenerationGetListDocument = gql`
  query getApiSetSendaiGenerationGetList {
    getApiSetSendaiGenerationGetList {
      data {
        listData {
          setSendaiGenerationPersons {
            createDate
            createDateStr
            endDate
            endDateStr
            generationId
            hpId
            indexRow
            setSbt
            startDate
            startDateStr
            userId
          }
          setSendaiGenerationShares {
            createDate
            createDateStr
            endDate
            endDateStr
            generationId
            hpId
            indexRow
            setSbt
            startDate
            startDateStr
            userId
          }
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiSetSendaiGenerationGetListQuery__
 *
 * To run a query within a React component, call `useGetApiSetSendaiGenerationGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiSetSendaiGenerationGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiSetSendaiGenerationGetListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiSetSendaiGenerationGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiSetSendaiGenerationGetListQuery,
    GetApiSetSendaiGenerationGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiSetSendaiGenerationGetListQuery,
    GetApiSetSendaiGenerationGetListQueryVariables
  >(GetApiSetSendaiGenerationGetListDocument, options);
}
export function useGetApiSetSendaiGenerationGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiSetSendaiGenerationGetListQuery,
    GetApiSetSendaiGenerationGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiSetSendaiGenerationGetListQuery,
    GetApiSetSendaiGenerationGetListQueryVariables
  >(GetApiSetSendaiGenerationGetListDocument, options);
}
export function useGetApiSetSendaiGenerationGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiSetSendaiGenerationGetListQuery,
    GetApiSetSendaiGenerationGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiSetSendaiGenerationGetListQuery,
    GetApiSetSendaiGenerationGetListQueryVariables
  >(GetApiSetSendaiGenerationGetListDocument, options);
}
export type GetApiSetSendaiGenerationGetListQueryHookResult = ReturnType<
  typeof useGetApiSetSendaiGenerationGetListQuery
>;
export type GetApiSetSendaiGenerationGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiSetSendaiGenerationGetListLazyQuery
>;
export type GetApiSetSendaiGenerationGetListSuspenseQueryHookResult =
  ReturnType<typeof useGetApiSetSendaiGenerationGetListSuspenseQuery>;
export type GetApiSetSendaiGenerationGetListQueryResult = Apollo.QueryResult<
  GetApiSetSendaiGenerationGetListQuery,
  GetApiSetSendaiGenerationGetListQueryVariables
>;
export const GetApiSetGetListDocument = gql`
  query getApiSetGetList(
    $generationId: Int
    $isPersonal: Boolean
    $textSearch: String
    $sinDate: Int
  ) {
    getApiSetGetList(
      generationId: $generationId
      isPersonal: $isPersonal
      textSearch: $textSearch
      sinDate: $sinDate
    ) {
      data {
        data {
          ...SetTreeFields
          childrens {
            ...SetTreeFields
            childrens {
              ...SetTreeFields
            }
          }
        }
      }
    }
  }
  ${SetTreeFieldsFragmentDoc}
`;

/**
 * __useGetApiSetGetListQuery__
 *
 * To run a query within a React component, call `useGetApiSetGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiSetGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiSetGetListQuery({
 *   variables: {
 *      generationId: // value for 'generationId'
 *      isPersonal: // value for 'isPersonal'
 *      textSearch: // value for 'textSearch'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiSetGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiSetGetListQuery,
    GetApiSetGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetApiSetGetListQuery, GetApiSetGetListQueryVariables>(
    GetApiSetGetListDocument,
    options,
  );
}
export function useGetApiSetGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiSetGetListQuery,
    GetApiSetGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiSetGetListQuery,
    GetApiSetGetListQueryVariables
  >(GetApiSetGetListDocument, options);
}
export function useGetApiSetGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiSetGetListQuery,
    GetApiSetGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiSetGetListQuery,
    GetApiSetGetListQueryVariables
  >(GetApiSetGetListDocument, options);
}
export type GetApiSetGetListQueryHookResult = ReturnType<
  typeof useGetApiSetGetListQuery
>;
export type GetApiSetGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiSetGetListLazyQuery
>;
export type GetApiSetGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiSetGetListSuspenseQuery
>;
export type GetApiSetGetListQueryResult = Apollo.QueryResult<
  GetApiSetGetListQuery,
  GetApiSetGetListQueryVariables
>;
export const PostApiSetSaveDocument = gql`
  mutation postApiSetSave(
    $emrCloudApiRequestsSetMstSaveSetMstRequestInput: EmrCloudApiRequestsSetMstSaveSetMstRequestInput!
  ) {
    postApiSetSave(
      emrCloudApiRequestsSetMstSaveSetMstRequestInput: $emrCloudApiRequestsSetMstSaveSetMstRequestInput
    ) {
      message
      status
      data {
        data {
          ...SetTreeFields
          childrens {
            ...SetTreeFields
            childrens {
              ...SetTreeFields
            }
          }
        }
      }
    }
  }
  ${SetTreeFieldsFragmentDoc}
`;
export type PostApiSetSaveMutationFn = Apollo.MutationFunction<
  PostApiSetSaveMutation,
  PostApiSetSaveMutationVariables
>;

/**
 * __usePostApiSetSaveMutation__
 *
 * To run a mutation, you first call `usePostApiSetSaveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiSetSaveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiSetSaveMutation, { data, loading, error }] = usePostApiSetSaveMutation({
 *   variables: {
 *      emrCloudApiRequestsSetMstSaveSetMstRequestInput: // value for 'emrCloudApiRequestsSetMstSaveSetMstRequestInput'
 *   },
 * });
 */
export function usePostApiSetSaveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiSetSaveMutation,
    PostApiSetSaveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiSetSaveMutation,
    PostApiSetSaveMutationVariables
  >(PostApiSetSaveDocument, options);
}
export type PostApiSetSaveMutationHookResult = ReturnType<
  typeof usePostApiSetSaveMutation
>;
export type PostApiSetSaveMutationResult =
  Apollo.MutationResult<PostApiSetSaveMutation>;
export type PostApiSetSaveMutationOptions = Apollo.BaseMutationOptions<
  PostApiSetSaveMutation,
  PostApiSetSaveMutationVariables
>;
export const GetApiSetGetToolTipDocument = gql`
  query getApiSetGetToolTip($setCd: Int) {
    getApiSetGetToolTip(setCd: $setCd) {
      message
      status
      data {
        data {
          dictionaryOrders
          listByomeis
          listKarteNames
        }
      }
    }
  }
`;

/**
 * __useGetApiSetGetToolTipQuery__
 *
 * To run a query within a React component, call `useGetApiSetGetToolTipQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiSetGetToolTipQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiSetGetToolTipQuery({
 *   variables: {
 *      setCd: // value for 'setCd'
 *   },
 * });
 */
export function useGetApiSetGetToolTipQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiSetGetToolTipQuery,
    GetApiSetGetToolTipQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiSetGetToolTipQuery,
    GetApiSetGetToolTipQueryVariables
  >(GetApiSetGetToolTipDocument, options);
}
export function useGetApiSetGetToolTipLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiSetGetToolTipQuery,
    GetApiSetGetToolTipQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiSetGetToolTipQuery,
    GetApiSetGetToolTipQueryVariables
  >(GetApiSetGetToolTipDocument, options);
}
export function useGetApiSetGetToolTipSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiSetGetToolTipQuery,
    GetApiSetGetToolTipQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiSetGetToolTipQuery,
    GetApiSetGetToolTipQueryVariables
  >(GetApiSetGetToolTipDocument, options);
}
export type GetApiSetGetToolTipQueryHookResult = ReturnType<
  typeof useGetApiSetGetToolTipQuery
>;
export type GetApiSetGetToolTipLazyQueryHookResult = ReturnType<
  typeof useGetApiSetGetToolTipLazyQuery
>;
export type GetApiSetGetToolTipSuspenseQueryHookResult = ReturnType<
  typeof useGetApiSetGetToolTipSuspenseQuery
>;
export type GetApiSetGetToolTipQueryResult = Apollo.QueryResult<
  GetApiSetGetToolTipQuery,
  GetApiSetGetToolTipQueryVariables
>;
export const PostApiSetSendaiGenerationInsertDocument = gql`
  mutation postApiSetSendaiGenerationInsert(
    $emrCloudApiRequestsSetSendaiGenerationAddSetSendaiGenerationRequestInput: EmrCloudApiRequestsSetSendaiGenerationAddSetSendaiGenerationRequestInput!
  ) {
    postApiSetSendaiGenerationInsert(
      emrCloudApiRequestsSetSendaiGenerationAddSetSendaiGenerationRequestInput: $emrCloudApiRequestsSetSendaiGenerationAddSetSendaiGenerationRequestInput
    )
  }
`;
export type PostApiSetSendaiGenerationInsertMutationFn =
  Apollo.MutationFunction<
    PostApiSetSendaiGenerationInsertMutation,
    PostApiSetSendaiGenerationInsertMutationVariables
  >;

/**
 * __usePostApiSetSendaiGenerationInsertMutation__
 *
 * To run a mutation, you first call `usePostApiSetSendaiGenerationInsertMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiSetSendaiGenerationInsertMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiSetSendaiGenerationInsertMutation, { data, loading, error }] = usePostApiSetSendaiGenerationInsertMutation({
 *   variables: {
 *      emrCloudApiRequestsSetSendaiGenerationAddSetSendaiGenerationRequestInput: // value for 'emrCloudApiRequestsSetSendaiGenerationAddSetSendaiGenerationRequestInput'
 *   },
 * });
 */
export function usePostApiSetSendaiGenerationInsertMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiSetSendaiGenerationInsertMutation,
    PostApiSetSendaiGenerationInsertMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiSetSendaiGenerationInsertMutation,
    PostApiSetSendaiGenerationInsertMutationVariables
  >(PostApiSetSendaiGenerationInsertDocument, options);
}
export type PostApiSetSendaiGenerationInsertMutationHookResult = ReturnType<
  typeof usePostApiSetSendaiGenerationInsertMutation
>;
export type PostApiSetSendaiGenerationInsertMutationResult =
  Apollo.MutationResult<PostApiSetSendaiGenerationInsertMutation>;
export type PostApiSetSendaiGenerationInsertMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiSetSendaiGenerationInsertMutation,
    PostApiSetSendaiGenerationInsertMutationVariables
  >;
export const PostApiSetSendaiGenerationDeleteDocument = gql`
  mutation postApiSetSendaiGenerationDelete(
    $emrCloudApiRequestsSetSendaiGenerationDeleteSetSendaiGenerationRequestInput: EmrCloudApiRequestsSetSendaiGenerationDeleteSetSendaiGenerationRequestInput!
  ) {
    postApiSetSendaiGenerationDelete(
      emrCloudApiRequestsSetSendaiGenerationDeleteSetSendaiGenerationRequestInput: $emrCloudApiRequestsSetSendaiGenerationDeleteSetSendaiGenerationRequestInput
    ) {
      message
      data {
        result
      }
      status
    }
  }
`;
export type PostApiSetSendaiGenerationDeleteMutationFn =
  Apollo.MutationFunction<
    PostApiSetSendaiGenerationDeleteMutation,
    PostApiSetSendaiGenerationDeleteMutationVariables
  >;

/**
 * __usePostApiSetSendaiGenerationDeleteMutation__
 *
 * To run a mutation, you first call `usePostApiSetSendaiGenerationDeleteMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiSetSendaiGenerationDeleteMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiSetSendaiGenerationDeleteMutation, { data, loading, error }] = usePostApiSetSendaiGenerationDeleteMutation({
 *   variables: {
 *      emrCloudApiRequestsSetSendaiGenerationDeleteSetSendaiGenerationRequestInput: // value for 'emrCloudApiRequestsSetSendaiGenerationDeleteSetSendaiGenerationRequestInput'
 *   },
 * });
 */
export function usePostApiSetSendaiGenerationDeleteMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiSetSendaiGenerationDeleteMutation,
    PostApiSetSendaiGenerationDeleteMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiSetSendaiGenerationDeleteMutation,
    PostApiSetSendaiGenerationDeleteMutationVariables
  >(PostApiSetSendaiGenerationDeleteDocument, options);
}
export type PostApiSetSendaiGenerationDeleteMutationHookResult = ReturnType<
  typeof usePostApiSetSendaiGenerationDeleteMutation
>;
export type PostApiSetSendaiGenerationDeleteMutationResult =
  Apollo.MutationResult<PostApiSetSendaiGenerationDeleteMutation>;
export type PostApiSetSendaiGenerationDeleteMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiSetSendaiGenerationDeleteMutation,
    PostApiSetSendaiGenerationDeleteMutationVariables
  >;
export const PostApiSetSaveSuperSetDetailDocument = gql`
  mutation postApiSetSaveSuperSetDetail(
    $input: EmrCloudApiRequestsSetMstSaveSuperSetDetailRequestInput
  ) {
    postApiSetSaveSuperSetDetail(
      emrCloudApiRequestsSetMstSaveSuperSetDetailRequestInput: $input
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiSetSaveSuperSetDetailMutationFn = Apollo.MutationFunction<
  PostApiSetSaveSuperSetDetailMutation,
  PostApiSetSaveSuperSetDetailMutationVariables
>;

/**
 * __usePostApiSetSaveSuperSetDetailMutation__
 *
 * To run a mutation, you first call `usePostApiSetSaveSuperSetDetailMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiSetSaveSuperSetDetailMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiSetSaveSuperSetDetailMutation, { data, loading, error }] = usePostApiSetSaveSuperSetDetailMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiSetSaveSuperSetDetailMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiSetSaveSuperSetDetailMutation,
    PostApiSetSaveSuperSetDetailMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiSetSaveSuperSetDetailMutation,
    PostApiSetSaveSuperSetDetailMutationVariables
  >(PostApiSetSaveSuperSetDetailDocument, options);
}
export type PostApiSetSaveSuperSetDetailMutationHookResult = ReturnType<
  typeof usePostApiSetSaveSuperSetDetailMutation
>;
export type PostApiSetSaveSuperSetDetailMutationResult =
  Apollo.MutationResult<PostApiSetSaveSuperSetDetailMutation>;
export type PostApiSetSaveSuperSetDetailMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiSetSaveSuperSetDetailMutation,
    PostApiSetSaveSuperSetDetailMutationVariables
  >;
export const GetApiSetGetSuperSetDetailDocument = gql`
  query getApiSetGetSuperSetDetail($setCd: Int, $sinDate: Int) {
    getApiSetGetSuperSetDetail(setCd: $setCd, sindate: $sinDate) {
      data {
        data {
          setByomeiList {
            byomeiCd
            byomeiCmt
            displayByomei
            fullByomei
            icd10
            icd1012013
            icd102013
            icd1022013
            id
            isDspKarte
            isDspRece
            isSuspected
            isSyobyoKbn
            nanByoCd
            prefixSuffixList {
              code
              name
            }
            sikkanKbn
          }
          setGroupOrderInfList {
            groupKouiCode
            groupName
            guid
            inOutKbn
            inOutName
            isDrug
            isKensa
            isShowInOut
            isShowSantei
            isShowSikyu
            kouiCode
            listSetOrdInfModels {
              createDate
              createId
              createName
              daysCnt
              groupKoui
              hpId
              id
              inoutKbn
              isDeleted
              isDrug
              isInjection
              isKensa
              isSelfInjection
              isShohoBiko
              isShohoComment
              isShohosenComment
              odrKouiKbn
              ordInfDetails {
                isSelectiveComment
                buiKbn
                rousaiKbn
                centerName
                centerCd
                bunkatu
                bunkatuKoui
                centerItemCd1
                centerItemCd2
                cmtCol1
                cmtCol2
                cmtCol3
                cmtCol4
                cmtColKeta1
                cmtColKeta2
                cmtColKeta3
                cmtColKeta4
                cmtName
                cmtOpt
                cnvTermVal
                commentNewline
                displayItemName
                displayedQuantity
                drugKbn
                editingQuantity
                fontColor
                handanGrpKbn
                hpId
                inOutKbn
                ipnCd
                ipnName
                is820Cmt
                is830Cmt
                is831Cmt
                is840Cmt
                is842Cmt
                is850Cmt
                is851Cmt
                is852Cmt
                isComment
                isDrug
                isDrugUsage
                isGetPriceInYakka
                isInjection
                isInjectionUsage
                isKensa
                isKensaMstEmpty
                isNodspRece
                isNormalComment
                isShohoBiko
                isShohoComment
                isSpecialItem
                isStandardUsage
                isSuppUsage
                isUsage
                isYoho
                itemCd
                itemName
                kasan1
                kasan2
                kensaGaichu
                kohatuKbn
                kokuji1
                kokuji2
                masterSbt
                odrTermVal
                price
                rowNo
                rpEdaNo
                rpNo
                setCd
                sinKouiKbn
                suryo
                syohoKbn
                syohoLimitKbn
                ten
                termVal
                unitName
                unitSBT
                yakka
                yjCd
                yohoKbn
                yohoSets {
                  createDate
                  createId
                  createMachine
                  hpId
                  isDeleted
                  isModified
                  itemCd
                  itemname
                  setId
                  sortNo
                  updateDate
                  updateId
                  updateMachine
                  userId
                  yohoKbn
                }
              }
              rpEdaNo
              rpName
              rpNo
              santeiKbn
              setCd
              sikyuKbn
              sortNo
              syohoSbt
              tosekiKbn
            }
            santeiKbn
            santeiName
            sikyuKbn
            sikyuName
            syohoSbt
            tosekiKbn
          }
          setInfo {
            parentSetCd
            setKana
            setName
          }
          setKarteInf {
            hpId
            isDeleted
            richText
            seqNo
            setCd
            text
          }
        }
      }
      status
      message
    }
  }
`;

/**
 * __useGetApiSetGetSuperSetDetailQuery__
 *
 * To run a query within a React component, call `useGetApiSetGetSuperSetDetailQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiSetGetSuperSetDetailQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiSetGetSuperSetDetailQuery({
 *   variables: {
 *      setCd: // value for 'setCd'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiSetGetSuperSetDetailQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiSetGetSuperSetDetailQuery,
    GetApiSetGetSuperSetDetailQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiSetGetSuperSetDetailQuery,
    GetApiSetGetSuperSetDetailQueryVariables
  >(GetApiSetGetSuperSetDetailDocument, options);
}
export function useGetApiSetGetSuperSetDetailLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiSetGetSuperSetDetailQuery,
    GetApiSetGetSuperSetDetailQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiSetGetSuperSetDetailQuery,
    GetApiSetGetSuperSetDetailQueryVariables
  >(GetApiSetGetSuperSetDetailDocument, options);
}
export function useGetApiSetGetSuperSetDetailSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiSetGetSuperSetDetailQuery,
    GetApiSetGetSuperSetDetailQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiSetGetSuperSetDetailQuery,
    GetApiSetGetSuperSetDetailQueryVariables
  >(GetApiSetGetSuperSetDetailDocument, options);
}
export type GetApiSetGetSuperSetDetailQueryHookResult = ReturnType<
  typeof useGetApiSetGetSuperSetDetailQuery
>;
export type GetApiSetGetSuperSetDetailLazyQueryHookResult = ReturnType<
  typeof useGetApiSetGetSuperSetDetailLazyQuery
>;
export type GetApiSetGetSuperSetDetailSuspenseQueryHookResult = ReturnType<
  typeof useGetApiSetGetSuperSetDetailSuspenseQuery
>;
export type GetApiSetGetSuperSetDetailQueryResult = Apollo.QueryResult<
  GetApiSetGetSuperSetDetailQuery,
  GetApiSetGetSuperSetDetailQueryVariables
>;
export const GetApiSetDoHistoryToSuperSetDocument = gql`
  query getApiSetDOHistoryToSuperSet(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiSetDOHistoryToSuperSet(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        data {
          setByomeiList {
            byomeiCd
            byomeiCmt
            displayByomei
            fullByomei
            icd10
            icd1012013
            icd102013
            icd1022013
            id
            isDspKarte
            isDspRece
            isSuspected
            isSyobyoKbn
            nanByoCd
            prefixSuffixList {
              code
              name
            }
            sikkanKbn
          }
          setGroupOrderInfList {
            groupKouiCode
            groupName
            guid
            inOutKbn
            inOutName
            isDrug
            isKensa
            isShowInOut
            isShowSantei
            isShowSikyu
            kouiCode
            listSetOrdInfModels {
              createDate
              createId
              createName
              daysCnt
              groupKoui
              hpId
              id
              inoutKbn
              isDeleted
              isDrug
              isInjection
              isKensa
              isSelfInjection
              isShohoBiko
              isShohoComment
              isShohosenComment
              odrKouiKbn
              ordInfDetails {
                isSelectiveComment
                bunkatu
                bunkatuKoui
                centerItemCd1
                centerItemCd2
                cmtCol1
                cmtCol2
                cmtCol3
                cmtCol4
                cmtColKeta1
                cmtColKeta2
                cmtColKeta3
                cmtColKeta4
                cmtName
                cmtOpt
                cnvTermVal
                commentNewline
                displayItemName
                displayedQuantity
                drugKbn
                editingQuantity
                fontColor
                handanGrpKbn
                hpId
                inOutKbn
                ipnCd
                ipnName
                is820Cmt
                is830Cmt
                is831Cmt
                is840Cmt
                is842Cmt
                is850Cmt
                is851Cmt
                is852Cmt
                isComment
                isDrug
                isDrugUsage
                isGetPriceInYakka
                isInjection
                isInjectionUsage
                isKensa
                isKensaMstEmpty
                isNodspRece
                isNormalComment
                isShohoBiko
                isShohoComment
                isSpecialItem
                isStandardUsage
                isSuppUsage
                isUsage
                isYoho
                itemCd
                itemName
                kasan1
                kasan2
                kensaGaichu
                kohatuKbn
                kokuji1
                kokuji2
                masterSbt
                odrTermVal
                price
                rowNo
                rpEdaNo
                rpNo
                setCd
                sinKouiKbn
                suryo
                syohoKbn
                syohoLimitKbn
                ten
                termVal
                unitName
                unitSBT
                yakka
                yjCd
                yohoKbn
                yohoSets {
                  createDate
                  createId
                  createMachine
                  hpId
                  isDeleted
                  isModified
                  itemCd
                  itemname
                  setId
                  sortNo
                  updateDate
                  updateId
                  updateMachine
                  userId
                  yohoKbn
                }
              }
              rpEdaNo
              rpName
              rpNo
              santeiKbn
              setCd
              sikyuKbn
              sortNo
              syohoSbt
              tosekiKbn
            }
            santeiKbn
            santeiName
            sikyuKbn
            sikyuName
            syohoSbt
            tosekiKbn
          }
          setInfo {
            parentSetCd
            setKana
            setName
          }
          setKarteInf {
            hpId
            richText
            setCd
            text
          }
        }
      }
      status
      message
    }
  }
`;

/**
 * __useGetApiSetDoHistoryToSuperSetQuery__
 *
 * To run a query within a React component, call `useGetApiSetDoHistoryToSuperSetQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiSetDoHistoryToSuperSetQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiSetDoHistoryToSuperSetQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiSetDoHistoryToSuperSetQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiSetDoHistoryToSuperSetQuery,
    GetApiSetDoHistoryToSuperSetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiSetDoHistoryToSuperSetQuery,
    GetApiSetDoHistoryToSuperSetQueryVariables
  >(GetApiSetDoHistoryToSuperSetDocument, options);
}
export function useGetApiSetDoHistoryToSuperSetLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiSetDoHistoryToSuperSetQuery,
    GetApiSetDoHistoryToSuperSetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiSetDoHistoryToSuperSetQuery,
    GetApiSetDoHistoryToSuperSetQueryVariables
  >(GetApiSetDoHistoryToSuperSetDocument, options);
}
export function useGetApiSetDoHistoryToSuperSetSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiSetDoHistoryToSuperSetQuery,
    GetApiSetDoHistoryToSuperSetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiSetDoHistoryToSuperSetQuery,
    GetApiSetDoHistoryToSuperSetQueryVariables
  >(GetApiSetDoHistoryToSuperSetDocument, options);
}
export type GetApiSetDoHistoryToSuperSetQueryHookResult = ReturnType<
  typeof useGetApiSetDoHistoryToSuperSetQuery
>;
export type GetApiSetDoHistoryToSuperSetLazyQueryHookResult = ReturnType<
  typeof useGetApiSetDoHistoryToSuperSetLazyQuery
>;
export type GetApiSetDoHistoryToSuperSetSuspenseQueryHookResult = ReturnType<
  typeof useGetApiSetDoHistoryToSuperSetSuspenseQuery
>;
export type GetApiSetDoHistoryToSuperSetQueryResult = Apollo.QueryResult<
  GetApiSetDoHistoryToSuperSetQuery,
  GetApiSetDoHistoryToSuperSetQueryVariables
>;
