import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type RemoveCacheMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsCacheRemoveCacheRequestInput;
}>;

export type RemoveCacheMutation = {
  __typename?: "mutation_root";
  postApiRemoveCacheRemoveCache?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesCacheRemoveCacheResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesCacheRemoveCacheResponse";
      status?: boolean;
    };
  };
};

export const RemoveCacheDocument = gql`
  mutation RemoveCache(
    $input: EmrCloudApiRequestsCacheRemoveCacheRequestInput!
  ) {
    postApiRemoveCacheRemoveCache(
      emrCloudApiRequestsCacheRemoveCacheRequestInput: $input
    ) {
      data {
        status
      }
      status
      message
    }
  }
`;
export type RemoveCacheMutationFn = Apollo.MutationFunction<
  RemoveCacheMutation,
  RemoveCacheMutationVariables
>;

/**
 * __useRemoveCacheMutation__
 *
 * To run a mutation, you first call `useRemoveCacheMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRemoveCacheMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [removeCacheMutation, { data, loading, error }] = useRemoveCacheMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useRemoveCacheMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RemoveCacheMutation,
    RemoveCacheMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<RemoveCacheMutation, RemoveCacheMutationVariables>(
    RemoveCacheDocument,
    options,
  );
}
export type RemoveCacheMutationHookResult = ReturnType<
  typeof useRemoveCacheMutation
>;
export type RemoveCacheMutationResult =
  Apollo.MutationResult<RemoveCacheMutation>;
export type RemoveCacheMutationOptions = Apollo.BaseMutationOptions<
  RemoveCacheMutation,
  RemoveCacheMutationVariables
>;
