import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetTemplateDocParamsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetTemplateDocParamsQuery = {
  __typename?: "query_root";
  getTemplateDocParams?: Array<{
    __typename?: "TemplateDocParam";
    itemName: string;
    parameterName: string;
    reflected_content: string;
  }>;
};

export type CreateTemplateDocMutationVariables = Types.Exact<{
  input: Types.TemplateDocInput;
}>;

export type CreateTemplateDocMutation = {
  __typename?: "mutation_root";
  createTemplateDoc?: {
    __typename?: "TemplateDocInfo";
    templateDocId: number;
    displayName: string;
    fileName: string;
    createdAt: string;
    updatedAt: string;
    s3Key: string;
  };
};

export type GetTemplateDocUploadUrlQueryVariables = Types.Exact<{
  input: Types.Scalars["String"]["input"];
}>;

export type GetTemplateDocUploadUrlQuery = {
  __typename?: "query_root";
  getTemplateDocUploadUrl: string;
};

export type GetTemplateDocListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetTemplateDocListQuery = {
  __typename?: "query_root";
  getTemplateDocList?: {
    __typename?: "TemplateDocListRes";
    templates?: Array<{
      __typename?: "TemplateDocInfo";
      templateDocId: number;
      displayName: string;
      fileName: string;
      createdAt: string;
      updatedAt: string;
      s3Key: string;
    }>;
  };
};

export type EditTemplateDocMutationVariables = Types.Exact<{
  input: Types.TemplateDocInput;
}>;

export type EditTemplateDocMutation = {
  __typename?: "mutation_root";
  editTemplateDoc?: {
    __typename?: "TemplateDocInfo";
    templateDocId: number;
    displayName: string;
    fileName: string;
    createdAt: string;
    updatedAt: string;
    s3Key: string;
  };
};

export type DeleteTemplateDocMutationVariables = Types.Exact<{
  templateDocId: Types.Scalars["Int"]["input"];
}>;

export type DeleteTemplateDocMutation = {
  __typename?: "mutation_root";
  deleteTemplateDoc?: { __typename?: "TemplateDocInfo"; isDeleted: boolean };
};

export type GetApiDocumentGetListDocumentCategoryQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiDocumentGetListDocumentCategoryQuery = {
  __typename?: "query_root";
  getApiDocumentGetListDocumentCategory?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDocumentGetListDocCategoryResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesDocumentGetListDocCategoryResponse";
      templateFiles?: Array<{
        __typename?: "DomainModelsDocumentTemplateDocumentModel";
        displayName?: string;
        fileLink?: string;
        fileName?: string;
        updatedAt?: string;
      }>;
    };
  };
};

export type PostApiDocumentDowloadDocumentTemplateMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsDocumentDownloadDocumentTemplateRequestInput>;
  }>;

export type PostApiDocumentDowloadDocumentTemplateMutation = {
  __typename?: "mutation_root";
  postApiDocumentDowloadDocumentTemplate?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesDocumentDocumentBase64Response";
    data?: {
      __typename?: "EmrCloudApiResponsesDocumentDocumentBase64Response";
      content?: string;
      fileName?: string;
      mimeType?: string;
    };
  };
};

export const GetTemplateDocParamsDocument = gql`
  query getTemplateDocParams {
    getTemplateDocParams {
      itemName
      parameterName
      reflected_content
    }
  }
`;

/**
 * __useGetTemplateDocParamsQuery__
 *
 * To run a query within a React component, call `useGetTemplateDocParamsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTemplateDocParamsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTemplateDocParamsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetTemplateDocParamsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetTemplateDocParamsQuery,
    GetTemplateDocParamsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetTemplateDocParamsQuery,
    GetTemplateDocParamsQueryVariables
  >(GetTemplateDocParamsDocument, options);
}
export function useGetTemplateDocParamsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetTemplateDocParamsQuery,
    GetTemplateDocParamsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetTemplateDocParamsQuery,
    GetTemplateDocParamsQueryVariables
  >(GetTemplateDocParamsDocument, options);
}
export function useGetTemplateDocParamsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetTemplateDocParamsQuery,
    GetTemplateDocParamsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetTemplateDocParamsQuery,
    GetTemplateDocParamsQueryVariables
  >(GetTemplateDocParamsDocument, options);
}
export type GetTemplateDocParamsQueryHookResult = ReturnType<
  typeof useGetTemplateDocParamsQuery
>;
export type GetTemplateDocParamsLazyQueryHookResult = ReturnType<
  typeof useGetTemplateDocParamsLazyQuery
>;
export type GetTemplateDocParamsSuspenseQueryHookResult = ReturnType<
  typeof useGetTemplateDocParamsSuspenseQuery
>;
export type GetTemplateDocParamsQueryResult = Apollo.QueryResult<
  GetTemplateDocParamsQuery,
  GetTemplateDocParamsQueryVariables
>;
export const CreateTemplateDocDocument = gql`
  mutation createTemplateDoc($input: TemplateDocInput!) {
    createTemplateDoc(input: $input) {
      templateDocId
      displayName
      fileName
      createdAt
      updatedAt
      s3Key
    }
  }
`;
export type CreateTemplateDocMutationFn = Apollo.MutationFunction<
  CreateTemplateDocMutation,
  CreateTemplateDocMutationVariables
>;

/**
 * __useCreateTemplateDocMutation__
 *
 * To run a mutation, you first call `useCreateTemplateDocMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateTemplateDocMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createTemplateDocMutation, { data, loading, error }] = useCreateTemplateDocMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateTemplateDocMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateTemplateDocMutation,
    CreateTemplateDocMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateTemplateDocMutation,
    CreateTemplateDocMutationVariables
  >(CreateTemplateDocDocument, options);
}
export type CreateTemplateDocMutationHookResult = ReturnType<
  typeof useCreateTemplateDocMutation
>;
export type CreateTemplateDocMutationResult =
  Apollo.MutationResult<CreateTemplateDocMutation>;
export type CreateTemplateDocMutationOptions = Apollo.BaseMutationOptions<
  CreateTemplateDocMutation,
  CreateTemplateDocMutationVariables
>;
export const GetTemplateDocUploadUrlDocument = gql`
  query getTemplateDocUploadUrl($input: String!) {
    getTemplateDocUploadUrl(s3Key: $input)
  }
`;

/**
 * __useGetTemplateDocUploadUrlQuery__
 *
 * To run a query within a React component, call `useGetTemplateDocUploadUrlQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTemplateDocUploadUrlQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTemplateDocUploadUrlQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetTemplateDocUploadUrlQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetTemplateDocUploadUrlQuery,
    GetTemplateDocUploadUrlQueryVariables
  > &
    (
      | { variables: GetTemplateDocUploadUrlQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetTemplateDocUploadUrlQuery,
    GetTemplateDocUploadUrlQueryVariables
  >(GetTemplateDocUploadUrlDocument, options);
}
export function useGetTemplateDocUploadUrlLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetTemplateDocUploadUrlQuery,
    GetTemplateDocUploadUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetTemplateDocUploadUrlQuery,
    GetTemplateDocUploadUrlQueryVariables
  >(GetTemplateDocUploadUrlDocument, options);
}
export function useGetTemplateDocUploadUrlSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetTemplateDocUploadUrlQuery,
    GetTemplateDocUploadUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetTemplateDocUploadUrlQuery,
    GetTemplateDocUploadUrlQueryVariables
  >(GetTemplateDocUploadUrlDocument, options);
}
export type GetTemplateDocUploadUrlQueryHookResult = ReturnType<
  typeof useGetTemplateDocUploadUrlQuery
>;
export type GetTemplateDocUploadUrlLazyQueryHookResult = ReturnType<
  typeof useGetTemplateDocUploadUrlLazyQuery
>;
export type GetTemplateDocUploadUrlSuspenseQueryHookResult = ReturnType<
  typeof useGetTemplateDocUploadUrlSuspenseQuery
>;
export type GetTemplateDocUploadUrlQueryResult = Apollo.QueryResult<
  GetTemplateDocUploadUrlQuery,
  GetTemplateDocUploadUrlQueryVariables
>;
export const GetTemplateDocListDocument = gql`
  query getTemplateDocList {
    getTemplateDocList {
      templates {
        templateDocId
        displayName
        fileName
        createdAt
        updatedAt
        s3Key
      }
    }
  }
`;

/**
 * __useGetTemplateDocListQuery__
 *
 * To run a query within a React component, call `useGetTemplateDocListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTemplateDocListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTemplateDocListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetTemplateDocListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetTemplateDocListQuery,
    GetTemplateDocListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetTemplateDocListQuery,
    GetTemplateDocListQueryVariables
  >(GetTemplateDocListDocument, options);
}
export function useGetTemplateDocListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetTemplateDocListQuery,
    GetTemplateDocListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetTemplateDocListQuery,
    GetTemplateDocListQueryVariables
  >(GetTemplateDocListDocument, options);
}
export function useGetTemplateDocListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetTemplateDocListQuery,
    GetTemplateDocListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetTemplateDocListQuery,
    GetTemplateDocListQueryVariables
  >(GetTemplateDocListDocument, options);
}
export type GetTemplateDocListQueryHookResult = ReturnType<
  typeof useGetTemplateDocListQuery
>;
export type GetTemplateDocListLazyQueryHookResult = ReturnType<
  typeof useGetTemplateDocListLazyQuery
>;
export type GetTemplateDocListSuspenseQueryHookResult = ReturnType<
  typeof useGetTemplateDocListSuspenseQuery
>;
export type GetTemplateDocListQueryResult = Apollo.QueryResult<
  GetTemplateDocListQuery,
  GetTemplateDocListQueryVariables
>;
export const EditTemplateDocDocument = gql`
  mutation editTemplateDoc($input: TemplateDocInput!) {
    editTemplateDoc(input: $input) {
      templateDocId
      displayName
      fileName
      createdAt
      updatedAt
      s3Key
    }
  }
`;
export type EditTemplateDocMutationFn = Apollo.MutationFunction<
  EditTemplateDocMutation,
  EditTemplateDocMutationVariables
>;

/**
 * __useEditTemplateDocMutation__
 *
 * To run a mutation, you first call `useEditTemplateDocMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditTemplateDocMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editTemplateDocMutation, { data, loading, error }] = useEditTemplateDocMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useEditTemplateDocMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditTemplateDocMutation,
    EditTemplateDocMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditTemplateDocMutation,
    EditTemplateDocMutationVariables
  >(EditTemplateDocDocument, options);
}
export type EditTemplateDocMutationHookResult = ReturnType<
  typeof useEditTemplateDocMutation
>;
export type EditTemplateDocMutationResult =
  Apollo.MutationResult<EditTemplateDocMutation>;
export type EditTemplateDocMutationOptions = Apollo.BaseMutationOptions<
  EditTemplateDocMutation,
  EditTemplateDocMutationVariables
>;
export const DeleteTemplateDocDocument = gql`
  mutation deleteTemplateDoc($templateDocId: Int!) {
    deleteTemplateDoc(templateDocId: $templateDocId) {
      isDeleted
    }
  }
`;
export type DeleteTemplateDocMutationFn = Apollo.MutationFunction<
  DeleteTemplateDocMutation,
  DeleteTemplateDocMutationVariables
>;

/**
 * __useDeleteTemplateDocMutation__
 *
 * To run a mutation, you first call `useDeleteTemplateDocMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteTemplateDocMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteTemplateDocMutation, { data, loading, error }] = useDeleteTemplateDocMutation({
 *   variables: {
 *      templateDocId: // value for 'templateDocId'
 *   },
 * });
 */
export function useDeleteTemplateDocMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteTemplateDocMutation,
    DeleteTemplateDocMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteTemplateDocMutation,
    DeleteTemplateDocMutationVariables
  >(DeleteTemplateDocDocument, options);
}
export type DeleteTemplateDocMutationHookResult = ReturnType<
  typeof useDeleteTemplateDocMutation
>;
export type DeleteTemplateDocMutationResult =
  Apollo.MutationResult<DeleteTemplateDocMutation>;
export type DeleteTemplateDocMutationOptions = Apollo.BaseMutationOptions<
  DeleteTemplateDocMutation,
  DeleteTemplateDocMutationVariables
>;
export const GetApiDocumentGetListDocumentCategoryDocument = gql`
  query getApiDocumentGetListDocumentCategory {
    getApiDocumentGetListDocumentCategory {
      data {
        templateFiles {
          displayName
          fileLink
          fileName
          updatedAt
        }
      }
    }
  }
`;

/**
 * __useGetApiDocumentGetListDocumentCategoryQuery__
 *
 * To run a query within a React component, call `useGetApiDocumentGetListDocumentCategoryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiDocumentGetListDocumentCategoryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiDocumentGetListDocumentCategoryQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiDocumentGetListDocumentCategoryQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiDocumentGetListDocumentCategoryQuery,
    GetApiDocumentGetListDocumentCategoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiDocumentGetListDocumentCategoryQuery,
    GetApiDocumentGetListDocumentCategoryQueryVariables
  >(GetApiDocumentGetListDocumentCategoryDocument, options);
}
export function useGetApiDocumentGetListDocumentCategoryLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiDocumentGetListDocumentCategoryQuery,
    GetApiDocumentGetListDocumentCategoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiDocumentGetListDocumentCategoryQuery,
    GetApiDocumentGetListDocumentCategoryQueryVariables
  >(GetApiDocumentGetListDocumentCategoryDocument, options);
}
export function useGetApiDocumentGetListDocumentCategorySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiDocumentGetListDocumentCategoryQuery,
    GetApiDocumentGetListDocumentCategoryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiDocumentGetListDocumentCategoryQuery,
    GetApiDocumentGetListDocumentCategoryQueryVariables
  >(GetApiDocumentGetListDocumentCategoryDocument, options);
}
export type GetApiDocumentGetListDocumentCategoryQueryHookResult = ReturnType<
  typeof useGetApiDocumentGetListDocumentCategoryQuery
>;
export type GetApiDocumentGetListDocumentCategoryLazyQueryHookResult =
  ReturnType<typeof useGetApiDocumentGetListDocumentCategoryLazyQuery>;
export type GetApiDocumentGetListDocumentCategorySuspenseQueryHookResult =
  ReturnType<typeof useGetApiDocumentGetListDocumentCategorySuspenseQuery>;
export type GetApiDocumentGetListDocumentCategoryQueryResult =
  Apollo.QueryResult<
    GetApiDocumentGetListDocumentCategoryQuery,
    GetApiDocumentGetListDocumentCategoryQueryVariables
  >;
export const PostApiDocumentDowloadDocumentTemplateDocument = gql`
  mutation postApiDocumentDowloadDocumentTemplate(
    $input: EmrCloudApiRequestsDocumentDownloadDocumentTemplateRequestInput
  ) {
    postApiDocumentDowloadDocumentTemplate(
      emrCloudApiRequestsDocumentDownloadDocumentTemplateRequestInput: $input
    ) {
      data {
        content
        fileName
        mimeType
      }
    }
  }
`;
export type PostApiDocumentDowloadDocumentTemplateMutationFn =
  Apollo.MutationFunction<
    PostApiDocumentDowloadDocumentTemplateMutation,
    PostApiDocumentDowloadDocumentTemplateMutationVariables
  >;

/**
 * __usePostApiDocumentDowloadDocumentTemplateMutation__
 *
 * To run a mutation, you first call `usePostApiDocumentDowloadDocumentTemplateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiDocumentDowloadDocumentTemplateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiDocumentDowloadDocumentTemplateMutation, { data, loading, error }] = usePostApiDocumentDowloadDocumentTemplateMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiDocumentDowloadDocumentTemplateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiDocumentDowloadDocumentTemplateMutation,
    PostApiDocumentDowloadDocumentTemplateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiDocumentDowloadDocumentTemplateMutation,
    PostApiDocumentDowloadDocumentTemplateMutationVariables
  >(PostApiDocumentDowloadDocumentTemplateDocument, options);
}
export type PostApiDocumentDowloadDocumentTemplateMutationHookResult =
  ReturnType<typeof usePostApiDocumentDowloadDocumentTemplateMutation>;
export type PostApiDocumentDowloadDocumentTemplateMutationResult =
  Apollo.MutationResult<PostApiDocumentDowloadDocumentTemplateMutation>;
export type PostApiDocumentDowloadDocumentTemplateMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiDocumentDowloadDocumentTemplateMutation,
    PostApiDocumentDowloadDocumentTemplateMutationVariables
  >;
