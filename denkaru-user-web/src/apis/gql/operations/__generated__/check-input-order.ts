import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiMedicalExaminationGetInfCheckedSpecialItemMutationVariables =
  Types.Exact<{
    input: Types.EmrCloudApiRequestsMedicalExaminationCheckedSpecialItemRequestInput;
  }>;

export type PostApiMedicalExaminationGetInfCheckedSpecialItemMutation = {
  __typename?: "mutation_root";
  postApiMedicalExaminationGetInfCheckedSpecialItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationCheckedSpecialItemResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationCheckedSpecialItemResponse";
      checkSpecialItemModels?: Array<{
        __typename?: "UseCaseOrdInfsCheckedSpecialItemCheckedSpecialItem";
        checkingContent?: string;
        checkingTypeDisplay?: string;
        checkingType?: number;
        itemCd?: string;
        label?: string;
      }>;
    };
  };
};

export const PostApiMedicalExaminationGetInfCheckedSpecialItemDocument = gql`
  mutation postApiMedicalExaminationGetInfCheckedSpecialItem(
    $input: EmrCloudApiRequestsMedicalExaminationCheckedSpecialItemRequestInput!
  ) {
    postApiMedicalExaminationGetInfCheckedSpecialItem(
      emrCloudApiRequestsMedicalExaminationCheckedSpecialItemRequestInput: $input
    ) {
      message
      status
      data {
        checkSpecialItemModels {
          checkingContent
          checkingTypeDisplay
          checkingType
          itemCd
          label
        }
      }
    }
  }
`;
export type PostApiMedicalExaminationGetInfCheckedSpecialItemMutationFn =
  Apollo.MutationFunction<
    PostApiMedicalExaminationGetInfCheckedSpecialItemMutation,
    PostApiMedicalExaminationGetInfCheckedSpecialItemMutationVariables
  >;

/**
 * __usePostApiMedicalExaminationGetInfCheckedSpecialItemMutation__
 *
 * To run a mutation, you first call `usePostApiMedicalExaminationGetInfCheckedSpecialItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMedicalExaminationGetInfCheckedSpecialItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMedicalExaminationGetInfCheckedSpecialItemMutation, { data, loading, error }] = usePostApiMedicalExaminationGetInfCheckedSpecialItemMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiMedicalExaminationGetInfCheckedSpecialItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMedicalExaminationGetInfCheckedSpecialItemMutation,
    PostApiMedicalExaminationGetInfCheckedSpecialItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMedicalExaminationGetInfCheckedSpecialItemMutation,
    PostApiMedicalExaminationGetInfCheckedSpecialItemMutationVariables
  >(PostApiMedicalExaminationGetInfCheckedSpecialItemDocument, options);
}
export type PostApiMedicalExaminationGetInfCheckedSpecialItemMutationHookResult =
  ReturnType<
    typeof usePostApiMedicalExaminationGetInfCheckedSpecialItemMutation
  >;
export type PostApiMedicalExaminationGetInfCheckedSpecialItemMutationResult =
  Apollo.MutationResult<PostApiMedicalExaminationGetInfCheckedSpecialItemMutation>;
export type PostApiMedicalExaminationGetInfCheckedSpecialItemMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMedicalExaminationGetInfCheckedSpecialItemMutation,
    PostApiMedicalExaminationGetInfCheckedSpecialItemMutationVariables
  >;
