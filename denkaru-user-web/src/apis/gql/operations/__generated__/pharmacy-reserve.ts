import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type FindPharmacyReservesQueryVariables = Types.Exact<{
  input: Types.FindPharmacyReservesInput;
}>;

export type FindPharmacyReservesQuery = {
  __typename?: "query_root";
  findPharmacyReserves: Array<{
    __typename?: "PharmacyReserve";
    pharmacyReserveId: number;
    desiredDateStatus: number;
    reserveUpdateDate: string;
    smsStatus: number;
    videocallStatus: number;
    postalServiceType: number;
    csvStatus: number;
    patient: {
      __typename?: "PharmacyPatient";
      ptId: number;
      ptNum: number;
      kanaName?: string;
      name?: string;
      sex: number;
      birthday: string;
      portalCustomerId?: number;
    };
    customer: {
      __typename?: "PortalCustomer";
      customerId: number;
      kanaName?: string;
      name?: string;
      gender?: number;
      birthday?: string;
    };
    desiredDate: Array<{
      __typename?: "DesiredDate";
      pharmacyDesiredDateId: number;
      desiredType: number;
      desiredDate: string;
    }>;
    reserve?: {
      __typename?: "ClinicReserve";
      reserveId: number;
      clinicName: string;
      reserveStartDate: string;
      reserveEndDate: string;
      meetingStatus: number;
    };
    meeting?: {
      __typename?: "PharmacyMeeting";
      meetingId: number;
      status: number;
    };
    pharmacyReserveDetails: Array<{
      __typename?: "PharmacyReserveDetail";
      pharmacyReserveDetailId: number;
      prescriptionType: number;
      status: number;
      guidanceStatus: number;
      paymentStatus: number;
      usesElectronicPrescription?: boolean;
      hasElectronicPrescription?: boolean;
      redemptionNumber?: string;
      patient: {
        __typename?: "PharmacyPatient";
        ptId: number;
        ptNum: number;
        kanaName?: string;
        name?: string;
        sex: number;
        birthday: string;
        portalCustomerId?: number;
      };
      customer: {
        __typename?: "PortalCustomer";
        customerId: number;
        kanaName?: string;
        name?: string;
        gender?: number;
        birthday?: string;
      };
    }>;
  }>;
};

export type HasPharmacyReserveInRangeQueryVariables = Types.Exact<{
  input: Types.HasPharmacyReserveInRangeInput;
}>;

export type HasPharmacyReserveInRangeQuery = {
  __typename?: "query_root";
  hasPharmacyReserveInRange: boolean;
};

export type UpdatePharmacyReserveDetailStatusMutationVariables = Types.Exact<{
  input: Types.UpdatePharmacyReserveDetailStatusInput;
}>;

export type UpdatePharmacyReserveDetailStatusMutation = {
  __typename?: "mutation_root";
  updatePharmacyReserveDetailStatus: boolean;
};

export type UpdatePharmacyReserveDetailGuidanceStatusMutationVariables =
  Types.Exact<{
    input: Types.UpdatePharmacyReserveDetailGuidanceStatusInput;
  }>;

export type UpdatePharmacyReserveDetailGuidanceStatusMutation = {
  __typename?: "mutation_root";
  updatePharmacyReserveDetailGuidanceStatus: boolean;
};

export type UpdatePharmacyReservePostalServiceTypeMutationVariables =
  Types.Exact<{
    input: Types.UpdatePharmacyReservePostalServiceTypeInput;
  }>;

export type UpdatePharmacyReservePostalServiceTypeMutation = {
  __typename?: "mutation_root";
  updatePharmacyReservePostalServiceType: boolean;
};

export type UpdatePharmacyReserveDesiredDateMutationVariables = Types.Exact<{
  input: Types.UpdatePharmacyReserveDesiredDateInput;
}>;

export type UpdatePharmacyReserveDesiredDateMutation = {
  __typename?: "mutation_root";
  updatePharmacyReserveDesiredDate: boolean;
};

export type UpdatePharmacyReserveDetailStatusByPharmacyReserveMutationVariables =
  Types.Exact<{
    input: Types.UpdatePharmacyReserveDetailStatusByPharmacyReserveInput;
  }>;

export type UpdatePharmacyReserveDetailStatusByPharmacyReserveMutation = {
  __typename?: "mutation_root";
  updatePharmacyReserveDetailStatusByPharmacyReserve: boolean;
};

export type UpdatePharmacyReserveCsvStatusMutationVariables = Types.Exact<{
  input: Types.UpdatePharmacyReserveCsvStatusInput;
}>;

export type UpdatePharmacyReserveCsvStatusMutation = {
  __typename?: "mutation_root";
  updatePharmacyReserveCSVStatus: boolean;
};

export type GetPharmacyReserveContactQueryVariables = Types.Exact<{
  input: Types.GetPharmacyReserveContactInput;
}>;

export type GetPharmacyReserveContactQuery = {
  __typename?: "query_root";
  getPharmacyReserveContact: {
    __typename?: "PharmacyReserveContact";
    customer: {
      __typename?: "PharmacyReserveContactCustomer";
      customerId: number;
      kanaName?: string;
      name?: string;
      gender?: number;
      birthday?: string;
      telephone?: string;
      email: string;
      post_code: string;
      address: string;
    };
    clinic: {
      __typename?: "PharmacyReserveContactClinic";
      hospital_id: number;
      name: string;
      telephone: string;
      fax: string;
      email: string;
      post_code: string;
      address: string;
    };
    deliveryAddress: {
      __typename?: "PharmacyReserveContactDeliveryAddress";
      name: string;
      post_code: string;
      address: string;
      phone_number: string;
    };
  };
};

export type GetPharmacyReserveInsuranceQueryVariables = Types.Exact<{
  input: Types.GetPharmacyReserveInsuranceInput;
}>;

export type GetPharmacyReserveInsuranceQuery = {
  __typename?: "query_root";
  getPharmacyReserveInsurance: {
    __typename?: "PharmacyReserveInsurance";
    insuranceImageUrl?: string;
    medicalCertificateImageUrls: Array<string>;
  };
};

export type GetPharmacyPatientFilesQueryVariables = Types.Exact<{
  input: Types.GetPharmacyPatientFilesInput;
}>;

export type GetPharmacyPatientFilesQuery = {
  __typename?: "query_root";
  getPharmacyPatientFiles: Array<{
    __typename?: "PharmacyPatientFile";
    pharmacyPatientFileID: number;
    pharmacyReserveDetailID: number;
    patientID: number;
    originalFileName: string;
    s3Key: string;
    createdAt: string;
    URL: string;
    type: number;
    mimeType?: string;
  }>;
};

export type UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutationVariables =
  Types.Exact<{
    input: Types.UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveInput;
  }>;

export type UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation =
  {
    __typename?: "mutation_root";
    updatePharmacyReserveDetailGuidanceStatusByPharmacyReserve: boolean;
  };

export type GetPharmacyReserveDetailSurveyInfoQueryVariables = Types.Exact<{
  input: Types.Scalars["Int"]["input"];
}>;

export type GetPharmacyReserveDetailSurveyInfoQuery = {
  __typename?: "query_root";
  getPharmacyReserveDetailSurveyInfo: {
    __typename?: "PharmacyReserveDetailSurveyInfo";
    basicAnswerJson?: string;
    pharmacyAnswerJson?: string;
    currentMedications: string;
    surveyAnswerDate?: string;
    hasMedicationFiles: boolean;
    medicationFormAnswerDate?: string;
    hasPrescriptionRecord: boolean;
    genericDrugDesire?: number;
  };
};

export const FindPharmacyReservesDocument = gql`
  query findPharmacyReserves($input: FindPharmacyReservesInput!) {
    findPharmacyReserves(input: $input) {
      pharmacyReserveId
      patient {
        ptId
        ptNum
        kanaName
        name
        sex
        birthday
        portalCustomerId
      }
      customer {
        customerId
        kanaName
        name
        gender
        birthday
      }
      desiredDateStatus
      desiredDate {
        pharmacyDesiredDateId
        desiredType
        desiredDate
      }
      reserveUpdateDate
      reserve {
        reserveId
        clinicName
        reserveStartDate
        reserveEndDate
        meetingStatus
      }
      smsStatus
      videocallStatus
      meeting {
        meetingId
        status
      }
      postalServiceType
      csvStatus
      pharmacyReserveDetails {
        pharmacyReserveDetailId
        patient {
          ptId
          ptNum
          kanaName
          name
          sex
          birthday
          portalCustomerId
        }
        customer {
          customerId
          kanaName
          name
          gender
          birthday
        }
        prescriptionType
        status
        guidanceStatus
        paymentStatus
        usesElectronicPrescription
        hasElectronicPrescription
        redemptionNumber
      }
    }
  }
`;

/**
 * __useFindPharmacyReservesQuery__
 *
 * To run a query within a React component, call `useFindPharmacyReservesQuery` and pass it any options that fit your needs.
 * When your component renders, `useFindPharmacyReservesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFindPharmacyReservesQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useFindPharmacyReservesQuery(
  baseOptions: Apollo.QueryHookOptions<
    FindPharmacyReservesQuery,
    FindPharmacyReservesQueryVariables
  > &
    (
      | { variables: FindPharmacyReservesQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    FindPharmacyReservesQuery,
    FindPharmacyReservesQueryVariables
  >(FindPharmacyReservesDocument, options);
}
export function useFindPharmacyReservesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FindPharmacyReservesQuery,
    FindPharmacyReservesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    FindPharmacyReservesQuery,
    FindPharmacyReservesQueryVariables
  >(FindPharmacyReservesDocument, options);
}
export function useFindPharmacyReservesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    FindPharmacyReservesQuery,
    FindPharmacyReservesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    FindPharmacyReservesQuery,
    FindPharmacyReservesQueryVariables
  >(FindPharmacyReservesDocument, options);
}
export type FindPharmacyReservesQueryHookResult = ReturnType<
  typeof useFindPharmacyReservesQuery
>;
export type FindPharmacyReservesLazyQueryHookResult = ReturnType<
  typeof useFindPharmacyReservesLazyQuery
>;
export type FindPharmacyReservesSuspenseQueryHookResult = ReturnType<
  typeof useFindPharmacyReservesSuspenseQuery
>;
export type FindPharmacyReservesQueryResult = Apollo.QueryResult<
  FindPharmacyReservesQuery,
  FindPharmacyReservesQueryVariables
>;
export const HasPharmacyReserveInRangeDocument = gql`
  query hasPharmacyReserveInRange($input: HasPharmacyReserveInRangeInput!) {
    hasPharmacyReserveInRange(input: $input)
  }
`;

/**
 * __useHasPharmacyReserveInRangeQuery__
 *
 * To run a query within a React component, call `useHasPharmacyReserveInRangeQuery` and pass it any options that fit your needs.
 * When your component renders, `useHasPharmacyReserveInRangeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useHasPharmacyReserveInRangeQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useHasPharmacyReserveInRangeQuery(
  baseOptions: Apollo.QueryHookOptions<
    HasPharmacyReserveInRangeQuery,
    HasPharmacyReserveInRangeQueryVariables
  > &
    (
      | { variables: HasPharmacyReserveInRangeQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    HasPharmacyReserveInRangeQuery,
    HasPharmacyReserveInRangeQueryVariables
  >(HasPharmacyReserveInRangeDocument, options);
}
export function useHasPharmacyReserveInRangeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    HasPharmacyReserveInRangeQuery,
    HasPharmacyReserveInRangeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    HasPharmacyReserveInRangeQuery,
    HasPharmacyReserveInRangeQueryVariables
  >(HasPharmacyReserveInRangeDocument, options);
}
export function useHasPharmacyReserveInRangeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    HasPharmacyReserveInRangeQuery,
    HasPharmacyReserveInRangeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    HasPharmacyReserveInRangeQuery,
    HasPharmacyReserveInRangeQueryVariables
  >(HasPharmacyReserveInRangeDocument, options);
}
export type HasPharmacyReserveInRangeQueryHookResult = ReturnType<
  typeof useHasPharmacyReserveInRangeQuery
>;
export type HasPharmacyReserveInRangeLazyQueryHookResult = ReturnType<
  typeof useHasPharmacyReserveInRangeLazyQuery
>;
export type HasPharmacyReserveInRangeSuspenseQueryHookResult = ReturnType<
  typeof useHasPharmacyReserveInRangeSuspenseQuery
>;
export type HasPharmacyReserveInRangeQueryResult = Apollo.QueryResult<
  HasPharmacyReserveInRangeQuery,
  HasPharmacyReserveInRangeQueryVariables
>;
export const UpdatePharmacyReserveDetailStatusDocument = gql`
  mutation updatePharmacyReserveDetailStatus(
    $input: UpdatePharmacyReserveDetailStatusInput!
  ) {
    updatePharmacyReserveDetailStatus(input: $input)
  }
`;
export type UpdatePharmacyReserveDetailStatusMutationFn =
  Apollo.MutationFunction<
    UpdatePharmacyReserveDetailStatusMutation,
    UpdatePharmacyReserveDetailStatusMutationVariables
  >;

/**
 * __useUpdatePharmacyReserveDetailStatusMutation__
 *
 * To run a mutation, you first call `useUpdatePharmacyReserveDetailStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePharmacyReserveDetailStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePharmacyReserveDetailStatusMutation, { data, loading, error }] = useUpdatePharmacyReserveDetailStatusMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePharmacyReserveDetailStatusMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePharmacyReserveDetailStatusMutation,
    UpdatePharmacyReserveDetailStatusMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePharmacyReserveDetailStatusMutation,
    UpdatePharmacyReserveDetailStatusMutationVariables
  >(UpdatePharmacyReserveDetailStatusDocument, options);
}
export type UpdatePharmacyReserveDetailStatusMutationHookResult = ReturnType<
  typeof useUpdatePharmacyReserveDetailStatusMutation
>;
export type UpdatePharmacyReserveDetailStatusMutationResult =
  Apollo.MutationResult<UpdatePharmacyReserveDetailStatusMutation>;
export type UpdatePharmacyReserveDetailStatusMutationOptions =
  Apollo.BaseMutationOptions<
    UpdatePharmacyReserveDetailStatusMutation,
    UpdatePharmacyReserveDetailStatusMutationVariables
  >;
export const UpdatePharmacyReserveDetailGuidanceStatusDocument = gql`
  mutation updatePharmacyReserveDetailGuidanceStatus(
    $input: UpdatePharmacyReserveDetailGuidanceStatusInput!
  ) {
    updatePharmacyReserveDetailGuidanceStatus(input: $input)
  }
`;
export type UpdatePharmacyReserveDetailGuidanceStatusMutationFn =
  Apollo.MutationFunction<
    UpdatePharmacyReserveDetailGuidanceStatusMutation,
    UpdatePharmacyReserveDetailGuidanceStatusMutationVariables
  >;

/**
 * __useUpdatePharmacyReserveDetailGuidanceStatusMutation__
 *
 * To run a mutation, you first call `useUpdatePharmacyReserveDetailGuidanceStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePharmacyReserveDetailGuidanceStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePharmacyReserveDetailGuidanceStatusMutation, { data, loading, error }] = useUpdatePharmacyReserveDetailGuidanceStatusMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePharmacyReserveDetailGuidanceStatusMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePharmacyReserveDetailGuidanceStatusMutation,
    UpdatePharmacyReserveDetailGuidanceStatusMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePharmacyReserveDetailGuidanceStatusMutation,
    UpdatePharmacyReserveDetailGuidanceStatusMutationVariables
  >(UpdatePharmacyReserveDetailGuidanceStatusDocument, options);
}
export type UpdatePharmacyReserveDetailGuidanceStatusMutationHookResult =
  ReturnType<typeof useUpdatePharmacyReserveDetailGuidanceStatusMutation>;
export type UpdatePharmacyReserveDetailGuidanceStatusMutationResult =
  Apollo.MutationResult<UpdatePharmacyReserveDetailGuidanceStatusMutation>;
export type UpdatePharmacyReserveDetailGuidanceStatusMutationOptions =
  Apollo.BaseMutationOptions<
    UpdatePharmacyReserveDetailGuidanceStatusMutation,
    UpdatePharmacyReserveDetailGuidanceStatusMutationVariables
  >;
export const UpdatePharmacyReservePostalServiceTypeDocument = gql`
  mutation updatePharmacyReservePostalServiceType(
    $input: UpdatePharmacyReservePostalServiceTypeInput!
  ) {
    updatePharmacyReservePostalServiceType(input: $input)
  }
`;
export type UpdatePharmacyReservePostalServiceTypeMutationFn =
  Apollo.MutationFunction<
    UpdatePharmacyReservePostalServiceTypeMutation,
    UpdatePharmacyReservePostalServiceTypeMutationVariables
  >;

/**
 * __useUpdatePharmacyReservePostalServiceTypeMutation__
 *
 * To run a mutation, you first call `useUpdatePharmacyReservePostalServiceTypeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePharmacyReservePostalServiceTypeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePharmacyReservePostalServiceTypeMutation, { data, loading, error }] = useUpdatePharmacyReservePostalServiceTypeMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePharmacyReservePostalServiceTypeMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePharmacyReservePostalServiceTypeMutation,
    UpdatePharmacyReservePostalServiceTypeMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePharmacyReservePostalServiceTypeMutation,
    UpdatePharmacyReservePostalServiceTypeMutationVariables
  >(UpdatePharmacyReservePostalServiceTypeDocument, options);
}
export type UpdatePharmacyReservePostalServiceTypeMutationHookResult =
  ReturnType<typeof useUpdatePharmacyReservePostalServiceTypeMutation>;
export type UpdatePharmacyReservePostalServiceTypeMutationResult =
  Apollo.MutationResult<UpdatePharmacyReservePostalServiceTypeMutation>;
export type UpdatePharmacyReservePostalServiceTypeMutationOptions =
  Apollo.BaseMutationOptions<
    UpdatePharmacyReservePostalServiceTypeMutation,
    UpdatePharmacyReservePostalServiceTypeMutationVariables
  >;
export const UpdatePharmacyReserveDesiredDateDocument = gql`
  mutation updatePharmacyReserveDesiredDate(
    $input: UpdatePharmacyReserveDesiredDateInput!
  ) {
    updatePharmacyReserveDesiredDate(input: $input)
  }
`;
export type UpdatePharmacyReserveDesiredDateMutationFn =
  Apollo.MutationFunction<
    UpdatePharmacyReserveDesiredDateMutation,
    UpdatePharmacyReserveDesiredDateMutationVariables
  >;

/**
 * __useUpdatePharmacyReserveDesiredDateMutation__
 *
 * To run a mutation, you first call `useUpdatePharmacyReserveDesiredDateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePharmacyReserveDesiredDateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePharmacyReserveDesiredDateMutation, { data, loading, error }] = useUpdatePharmacyReserveDesiredDateMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePharmacyReserveDesiredDateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePharmacyReserveDesiredDateMutation,
    UpdatePharmacyReserveDesiredDateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePharmacyReserveDesiredDateMutation,
    UpdatePharmacyReserveDesiredDateMutationVariables
  >(UpdatePharmacyReserveDesiredDateDocument, options);
}
export type UpdatePharmacyReserveDesiredDateMutationHookResult = ReturnType<
  typeof useUpdatePharmacyReserveDesiredDateMutation
>;
export type UpdatePharmacyReserveDesiredDateMutationResult =
  Apollo.MutationResult<UpdatePharmacyReserveDesiredDateMutation>;
export type UpdatePharmacyReserveDesiredDateMutationOptions =
  Apollo.BaseMutationOptions<
    UpdatePharmacyReserveDesiredDateMutation,
    UpdatePharmacyReserveDesiredDateMutationVariables
  >;
export const UpdatePharmacyReserveDetailStatusByPharmacyReserveDocument = gql`
  mutation updatePharmacyReserveDetailStatusByPharmacyReserve(
    $input: UpdatePharmacyReserveDetailStatusByPharmacyReserveInput!
  ) {
    updatePharmacyReserveDetailStatusByPharmacyReserve(input: $input)
  }
`;
export type UpdatePharmacyReserveDetailStatusByPharmacyReserveMutationFn =
  Apollo.MutationFunction<
    UpdatePharmacyReserveDetailStatusByPharmacyReserveMutation,
    UpdatePharmacyReserveDetailStatusByPharmacyReserveMutationVariables
  >;

/**
 * __useUpdatePharmacyReserveDetailStatusByPharmacyReserveMutation__
 *
 * To run a mutation, you first call `useUpdatePharmacyReserveDetailStatusByPharmacyReserveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePharmacyReserveDetailStatusByPharmacyReserveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePharmacyReserveDetailStatusByPharmacyReserveMutation, { data, loading, error }] = useUpdatePharmacyReserveDetailStatusByPharmacyReserveMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePharmacyReserveDetailStatusByPharmacyReserveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePharmacyReserveDetailStatusByPharmacyReserveMutation,
    UpdatePharmacyReserveDetailStatusByPharmacyReserveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePharmacyReserveDetailStatusByPharmacyReserveMutation,
    UpdatePharmacyReserveDetailStatusByPharmacyReserveMutationVariables
  >(UpdatePharmacyReserveDetailStatusByPharmacyReserveDocument, options);
}
export type UpdatePharmacyReserveDetailStatusByPharmacyReserveMutationHookResult =
  ReturnType<
    typeof useUpdatePharmacyReserveDetailStatusByPharmacyReserveMutation
  >;
export type UpdatePharmacyReserveDetailStatusByPharmacyReserveMutationResult =
  Apollo.MutationResult<UpdatePharmacyReserveDetailStatusByPharmacyReserveMutation>;
export type UpdatePharmacyReserveDetailStatusByPharmacyReserveMutationOptions =
  Apollo.BaseMutationOptions<
    UpdatePharmacyReserveDetailStatusByPharmacyReserveMutation,
    UpdatePharmacyReserveDetailStatusByPharmacyReserveMutationVariables
  >;
export const UpdatePharmacyReserveCsvStatusDocument = gql`
  mutation updatePharmacyReserveCSVStatus(
    $input: UpdatePharmacyReserveCSVStatusInput!
  ) {
    updatePharmacyReserveCSVStatus(input: $input)
  }
`;
export type UpdatePharmacyReserveCsvStatusMutationFn = Apollo.MutationFunction<
  UpdatePharmacyReserveCsvStatusMutation,
  UpdatePharmacyReserveCsvStatusMutationVariables
>;

/**
 * __useUpdatePharmacyReserveCsvStatusMutation__
 *
 * To run a mutation, you first call `useUpdatePharmacyReserveCsvStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePharmacyReserveCsvStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePharmacyReserveCsvStatusMutation, { data, loading, error }] = useUpdatePharmacyReserveCsvStatusMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePharmacyReserveCsvStatusMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePharmacyReserveCsvStatusMutation,
    UpdatePharmacyReserveCsvStatusMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePharmacyReserveCsvStatusMutation,
    UpdatePharmacyReserveCsvStatusMutationVariables
  >(UpdatePharmacyReserveCsvStatusDocument, options);
}
export type UpdatePharmacyReserveCsvStatusMutationHookResult = ReturnType<
  typeof useUpdatePharmacyReserveCsvStatusMutation
>;
export type UpdatePharmacyReserveCsvStatusMutationResult =
  Apollo.MutationResult<UpdatePharmacyReserveCsvStatusMutation>;
export type UpdatePharmacyReserveCsvStatusMutationOptions =
  Apollo.BaseMutationOptions<
    UpdatePharmacyReserveCsvStatusMutation,
    UpdatePharmacyReserveCsvStatusMutationVariables
  >;
export const GetPharmacyReserveContactDocument = gql`
  query getPharmacyReserveContact($input: GetPharmacyReserveContactInput!) {
    getPharmacyReserveContact(input: $input) {
      customer {
        customerId
        kanaName
        name
        gender
        birthday
        telephone
        email
        post_code
        address
      }
      clinic {
        hospital_id
        name
        telephone
        fax
        email
        post_code
        address
      }
      deliveryAddress {
        name
        post_code
        address
        phone_number
      }
    }
  }
`;

/**
 * __useGetPharmacyReserveContactQuery__
 *
 * To run a query within a React component, call `useGetPharmacyReserveContactQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPharmacyReserveContactQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPharmacyReserveContactQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPharmacyReserveContactQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPharmacyReserveContactQuery,
    GetPharmacyReserveContactQueryVariables
  > &
    (
      | { variables: GetPharmacyReserveContactQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPharmacyReserveContactQuery,
    GetPharmacyReserveContactQueryVariables
  >(GetPharmacyReserveContactDocument, options);
}
export function useGetPharmacyReserveContactLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPharmacyReserveContactQuery,
    GetPharmacyReserveContactQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPharmacyReserveContactQuery,
    GetPharmacyReserveContactQueryVariables
  >(GetPharmacyReserveContactDocument, options);
}
export function useGetPharmacyReserveContactSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPharmacyReserveContactQuery,
    GetPharmacyReserveContactQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPharmacyReserveContactQuery,
    GetPharmacyReserveContactQueryVariables
  >(GetPharmacyReserveContactDocument, options);
}
export type GetPharmacyReserveContactQueryHookResult = ReturnType<
  typeof useGetPharmacyReserveContactQuery
>;
export type GetPharmacyReserveContactLazyQueryHookResult = ReturnType<
  typeof useGetPharmacyReserveContactLazyQuery
>;
export type GetPharmacyReserveContactSuspenseQueryHookResult = ReturnType<
  typeof useGetPharmacyReserveContactSuspenseQuery
>;
export type GetPharmacyReserveContactQueryResult = Apollo.QueryResult<
  GetPharmacyReserveContactQuery,
  GetPharmacyReserveContactQueryVariables
>;
export const GetPharmacyReserveInsuranceDocument = gql`
  query getPharmacyReserveInsurance($input: GetPharmacyReserveInsuranceInput!) {
    getPharmacyReserveInsurance(input: $input) {
      insuranceImageUrl
      medicalCertificateImageUrls
    }
  }
`;

/**
 * __useGetPharmacyReserveInsuranceQuery__
 *
 * To run a query within a React component, call `useGetPharmacyReserveInsuranceQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPharmacyReserveInsuranceQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPharmacyReserveInsuranceQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPharmacyReserveInsuranceQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPharmacyReserveInsuranceQuery,
    GetPharmacyReserveInsuranceQueryVariables
  > &
    (
      | { variables: GetPharmacyReserveInsuranceQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPharmacyReserveInsuranceQuery,
    GetPharmacyReserveInsuranceQueryVariables
  >(GetPharmacyReserveInsuranceDocument, options);
}
export function useGetPharmacyReserveInsuranceLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPharmacyReserveInsuranceQuery,
    GetPharmacyReserveInsuranceQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPharmacyReserveInsuranceQuery,
    GetPharmacyReserveInsuranceQueryVariables
  >(GetPharmacyReserveInsuranceDocument, options);
}
export function useGetPharmacyReserveInsuranceSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPharmacyReserveInsuranceQuery,
    GetPharmacyReserveInsuranceQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPharmacyReserveInsuranceQuery,
    GetPharmacyReserveInsuranceQueryVariables
  >(GetPharmacyReserveInsuranceDocument, options);
}
export type GetPharmacyReserveInsuranceQueryHookResult = ReturnType<
  typeof useGetPharmacyReserveInsuranceQuery
>;
export type GetPharmacyReserveInsuranceLazyQueryHookResult = ReturnType<
  typeof useGetPharmacyReserveInsuranceLazyQuery
>;
export type GetPharmacyReserveInsuranceSuspenseQueryHookResult = ReturnType<
  typeof useGetPharmacyReserveInsuranceSuspenseQuery
>;
export type GetPharmacyReserveInsuranceQueryResult = Apollo.QueryResult<
  GetPharmacyReserveInsuranceQuery,
  GetPharmacyReserveInsuranceQueryVariables
>;
export const GetPharmacyPatientFilesDocument = gql`
  query getPharmacyPatientFiles($input: getPharmacyPatientFilesInput!) {
    getPharmacyPatientFiles(input: $input) {
      pharmacyPatientFileID
      pharmacyReserveDetailID
      patientID
      originalFileName
      s3Key
      createdAt
      URL
      type
      mimeType
    }
  }
`;

/**
 * __useGetPharmacyPatientFilesQuery__
 *
 * To run a query within a React component, call `useGetPharmacyPatientFilesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPharmacyPatientFilesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPharmacyPatientFilesQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPharmacyPatientFilesQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPharmacyPatientFilesQuery,
    GetPharmacyPatientFilesQueryVariables
  > &
    (
      | { variables: GetPharmacyPatientFilesQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPharmacyPatientFilesQuery,
    GetPharmacyPatientFilesQueryVariables
  >(GetPharmacyPatientFilesDocument, options);
}
export function useGetPharmacyPatientFilesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPharmacyPatientFilesQuery,
    GetPharmacyPatientFilesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPharmacyPatientFilesQuery,
    GetPharmacyPatientFilesQueryVariables
  >(GetPharmacyPatientFilesDocument, options);
}
export function useGetPharmacyPatientFilesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPharmacyPatientFilesQuery,
    GetPharmacyPatientFilesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPharmacyPatientFilesQuery,
    GetPharmacyPatientFilesQueryVariables
  >(GetPharmacyPatientFilesDocument, options);
}
export type GetPharmacyPatientFilesQueryHookResult = ReturnType<
  typeof useGetPharmacyPatientFilesQuery
>;
export type GetPharmacyPatientFilesLazyQueryHookResult = ReturnType<
  typeof useGetPharmacyPatientFilesLazyQuery
>;
export type GetPharmacyPatientFilesSuspenseQueryHookResult = ReturnType<
  typeof useGetPharmacyPatientFilesSuspenseQuery
>;
export type GetPharmacyPatientFilesQueryResult = Apollo.QueryResult<
  GetPharmacyPatientFilesQuery,
  GetPharmacyPatientFilesQueryVariables
>;
export const UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveDocument = gql`
  mutation updatePharmacyReserveDetailGuidanceStatusByPharmacyReserve(
    $input: UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveInput!
  ) {
    updatePharmacyReserveDetailGuidanceStatusByPharmacyReserve(input: $input)
  }
`;
export type UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutationFn =
  Apollo.MutationFunction<
    UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation,
    UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutationVariables
  >;

/**
 * __useUpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation__
 *
 * To run a mutation, you first call `useUpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation, { data, loading, error }] = useUpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation,
    UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation,
    UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutationVariables
  >(
    UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveDocument,
    options,
  );
}
export type UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutationHookResult =
  ReturnType<
    typeof useUpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation
  >;
export type UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutationResult =
  Apollo.MutationResult<UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation>;
export type UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutationOptions =
  Apollo.BaseMutationOptions<
    UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutation,
    UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveMutationVariables
  >;
export const GetPharmacyReserveDetailSurveyInfoDocument = gql`
  query getPharmacyReserveDetailSurveyInfo($input: Int!) {
    getPharmacyReserveDetailSurveyInfo(pharmacyReserveDetailId: $input) {
      basicAnswerJson
      pharmacyAnswerJson
      currentMedications
      surveyAnswerDate
      hasMedicationFiles
      medicationFormAnswerDate
      hasPrescriptionRecord
      genericDrugDesire
    }
  }
`;

/**
 * __useGetPharmacyReserveDetailSurveyInfoQuery__
 *
 * To run a query within a React component, call `useGetPharmacyReserveDetailSurveyInfoQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPharmacyReserveDetailSurveyInfoQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPharmacyReserveDetailSurveyInfoQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPharmacyReserveDetailSurveyInfoQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPharmacyReserveDetailSurveyInfoQuery,
    GetPharmacyReserveDetailSurveyInfoQueryVariables
  > &
    (
      | {
          variables: GetPharmacyReserveDetailSurveyInfoQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPharmacyReserveDetailSurveyInfoQuery,
    GetPharmacyReserveDetailSurveyInfoQueryVariables
  >(GetPharmacyReserveDetailSurveyInfoDocument, options);
}
export function useGetPharmacyReserveDetailSurveyInfoLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPharmacyReserveDetailSurveyInfoQuery,
    GetPharmacyReserveDetailSurveyInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPharmacyReserveDetailSurveyInfoQuery,
    GetPharmacyReserveDetailSurveyInfoQueryVariables
  >(GetPharmacyReserveDetailSurveyInfoDocument, options);
}
export function useGetPharmacyReserveDetailSurveyInfoSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPharmacyReserveDetailSurveyInfoQuery,
    GetPharmacyReserveDetailSurveyInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPharmacyReserveDetailSurveyInfoQuery,
    GetPharmacyReserveDetailSurveyInfoQueryVariables
  >(GetPharmacyReserveDetailSurveyInfoDocument, options);
}
export type GetPharmacyReserveDetailSurveyInfoQueryHookResult = ReturnType<
  typeof useGetPharmacyReserveDetailSurveyInfoQuery
>;
export type GetPharmacyReserveDetailSurveyInfoLazyQueryHookResult = ReturnType<
  typeof useGetPharmacyReserveDetailSurveyInfoLazyQuery
>;
export type GetPharmacyReserveDetailSurveyInfoSuspenseQueryHookResult =
  ReturnType<typeof useGetPharmacyReserveDetailSurveyInfoSuspenseQuery>;
export type GetPharmacyReserveDetailSurveyInfoQueryResult = Apollo.QueryResult<
  GetPharmacyReserveDetailSurveyInfoQuery,
  GetPharmacyReserveDetailSurveyInfoQueryVariables
>;
