import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetExaminationsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetExaminationsQuery = {
  __typename?: "query_root";
  getExaminations: Array<{
    __typename?: "Examination";
    examinationId: number;
    name: string;
    type: number;
  }>;
};

export const GetExaminationsDocument = gql`
  query getExaminations {
    getExaminations {
      examinationId
      name
      type
    }
  }
`;

/**
 * __useGetExaminationsQuery__
 *
 * To run a query within a React component, call `useGetExaminationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetExaminationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetExaminationsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetExaminationsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetExaminationsQuery,
    GetExaminationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetExaminationsQuery, GetExaminationsQueryVariables>(
    GetExaminationsDocument,
    options,
  );
}
export function useGetExaminationsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetExaminationsQuery,
    GetExaminationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetExaminationsQuery,
    GetExaminationsQueryVariables
  >(GetExaminationsDocument, options);
}
export function useGetExaminationsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetExaminationsQuery,
    GetExaminationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetExaminationsQuery,
    GetExaminationsQueryVariables
  >(GetExaminationsDocument, options);
}
export type GetExaminationsQueryHookResult = ReturnType<
  typeof useGetExaminationsQuery
>;
export type GetExaminationsLazyQueryHookResult = ReturnType<
  typeof useGetExaminationsLazyQuery
>;
export type GetExaminationsSuspenseQueryHookResult = ReturnType<
  typeof useGetExaminationsSuspenseQuery
>;
export type GetExaminationsQueryResult = Apollo.QueryResult<
  GetExaminationsQuery,
  GetExaminationsQueryVariables
>;
