import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiExamResultGetExamResultsQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  endDate?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  startDate?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  keyWord?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  kensaItemCds?: Types.InputMaybe<
    Array<Types.Scalars["String"]["input"]> | Types.Scalars["String"]["input"]
  >;
  timeSequence: Types.Scalars["Boolean"]["input"];
  centerCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiExamResultGetExamResultsQuery = {
  __typename?: "query_root";
  getApiExamResultGetExamResults?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesExamResultsGetExamResultsListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesExamResultsGetExamResultsListResponse";
      examResultsDto?: {
        __typename?: "DomainModelsExamResultsExamResultsDto";
        averageDay?: any;
        averageMonths?: any;
        examResultsModels?: Array<{
          __typename?: "DomainModelsExamResultsExamResultsModel";
          dspCenterName?: string;
          inoutKbn?: number;
          iraiDate?: number;
          kensaTime?: string;
          sikyuKbn?: number;
          tosekiKbn?: number;
          centerCd?: string;
          kensaInfDetailModels?: Array<{
            __typename?: "DomainModelsSpecialNotePatientInfoKensaInfDetailModel";
            abnormalKbn?: string;
            cmtCd1?: string;
            cmtCd2?: string;
            femaleStd?: string;
            formula?: string;
            hpId?: number;
            iraiCd?: string;
            iraiDate?: number;
            isDeleted?: number;
            kensaItemCd?: string;
            kensaName?: string;
            maleStd?: string;
            kensaKana?: string;
            ptId?: string;
            raiinNo?: string;
            resultType?: string;
            resultVal?: string;
            seqNo?: string;
            sortNo?: string;
            unit?: string;
            updateDate?: string;
          }>;
        }>;
      };
    };
  };
};

export const GetApiExamResultGetExamResultsDocument = gql`
  query getApiExamResultGetExamResults(
    $ptId: BigInt!
    $endDate: BigInt
    $startDate: BigInt
    $keyWord: String
    $kensaItemCds: [String!]
    $timeSequence: Boolean!
    $centerCd: String
  ) {
    getApiExamResultGetExamResults(
      ptId: $ptId
      endDate: $endDate
      startDate: $startDate
      keyWord: $keyWord
      kensaItemCds: $kensaItemCds
      timeSequence: $timeSequence
      centerCd: $centerCd
    ) {
      data {
        examResultsDto {
          examResultsModels {
            dspCenterName
            inoutKbn
            iraiDate
            kensaTime
            sikyuKbn
            tosekiKbn
            centerCd
            kensaInfDetailModels {
              abnormalKbn
              cmtCd1
              cmtCd2
              femaleStd
              formula
              hpId
              iraiCd
              iraiDate
              isDeleted
              kensaItemCd
              kensaName
              maleStd
              kensaKana
              ptId
              raiinNo
              resultType
              resultVal
              seqNo
              sortNo
              unit
              updateDate
            }
          }
          averageDay
          averageMonths
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiExamResultGetExamResultsQuery__
 *
 * To run a query within a React component, call `useGetApiExamResultGetExamResultsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiExamResultGetExamResultsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiExamResultGetExamResultsQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      endDate: // value for 'endDate'
 *      startDate: // value for 'startDate'
 *      keyWord: // value for 'keyWord'
 *      kensaItemCds: // value for 'kensaItemCds'
 *      timeSequence: // value for 'timeSequence'
 *      centerCd: // value for 'centerCd'
 *   },
 * });
 */
export function useGetApiExamResultGetExamResultsQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiExamResultGetExamResultsQuery,
    GetApiExamResultGetExamResultsQueryVariables
  > &
    (
      | {
          variables: GetApiExamResultGetExamResultsQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiExamResultGetExamResultsQuery,
    GetApiExamResultGetExamResultsQueryVariables
  >(GetApiExamResultGetExamResultsDocument, options);
}
export function useGetApiExamResultGetExamResultsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiExamResultGetExamResultsQuery,
    GetApiExamResultGetExamResultsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiExamResultGetExamResultsQuery,
    GetApiExamResultGetExamResultsQueryVariables
  >(GetApiExamResultGetExamResultsDocument, options);
}
export function useGetApiExamResultGetExamResultsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiExamResultGetExamResultsQuery,
    GetApiExamResultGetExamResultsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiExamResultGetExamResultsQuery,
    GetApiExamResultGetExamResultsQueryVariables
  >(GetApiExamResultGetExamResultsDocument, options);
}
export type GetApiExamResultGetExamResultsQueryHookResult = ReturnType<
  typeof useGetApiExamResultGetExamResultsQuery
>;
export type GetApiExamResultGetExamResultsLazyQueryHookResult = ReturnType<
  typeof useGetApiExamResultGetExamResultsLazyQuery
>;
export type GetApiExamResultGetExamResultsSuspenseQueryHookResult = ReturnType<
  typeof useGetApiExamResultGetExamResultsSuspenseQuery
>;
export type GetApiExamResultGetExamResultsQueryResult = Apollo.QueryResult<
  GetApiExamResultGetExamResultsQuery,
  GetApiExamResultGetExamResultsQueryVariables
>;
