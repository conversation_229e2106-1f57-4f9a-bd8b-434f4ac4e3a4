import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiTodayOrdUpsertMutationVariables = Types.Exact<{
  emrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput>;
}>;

export type PostApiTodayOrdUpsertMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdUpsert?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationUpsertTodayOdrResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationUpsertTodayOdrResponse";
      status?: number;
      validationKarte?: {
        __typename?: "EmrCloudApiResponsesKarteInfValidationKarteInfResponse";
        status?: number;
        validationMessage?: string;
      };
      validationOdrInfs?: Array<{
        __typename?: "EmrCloudApiResponsesMedicalExaminationValidationTodayOrdItemResponse";
        orderInfDetailPosition?: string;
        orderInfPosition?: string;
        status?: number;
        validationField?: string;
        validationMessage?: string;
        hokenName?: {
          __typename?: "DomainModelsTodayOdrHokenInfo";
          hoKenId?: number;
          hokenName?: string;
        };
        kohiNames?: Array<{
          __typename?: "DomainModelsTodayOdrHokenInfo";
          hoKenId?: number;
          hokenName?: string;
        }>;
      }>;
      validationRaiinInf?: {
        __typename?: "EmrCloudApiResponsesMedicalExaminationRaiinInfItemResponse";
        status?: number;
        validationMessage?: string;
        kohiNames?: Array<{
          __typename?: "DomainModelsTodayOdrHokenInfo";
          hoKenId?: number;
          hokenName?: string;
        }>;
        hokenName?: {
          __typename?: "DomainModelsTodayOdrHokenInfo";
          hoKenId?: number;
          hokenName?: string;
        };
      };
    };
  };
};

export type GetApiOrdInfGetListQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiOrdInfGetListQuery = {
  __typename?: "query_root";
  getApiOrdInfGetList?: {
    __typename: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOrdInfsGetOrdInfListTreeResponse";
    status?: number;
    message?: string;
    data?: {
      __typename: "EmrCloudApiResponsesOrdInfsGetOrdInfListTreeResponse";
      karteEdition?: {
        __typename: "UseCaseOrdInfsGetListTreesKarteEdition";
        createId?: number;
        edition?: number;
        hpId?: number;
        isDeleted?: number;
        karteStatus?: number;
        approvalDate?: string;
        approvalId?: number;
        ptId?: string;
        raiinNo?: string;
        updateDate?: string;
        updateId?: number;
        groupHokenItems?: Array<{
          __typename: "UseCaseOrdInfsGetListTreesGroupHokenItem";
          hokenPid?: number;
          hokenTitle?: string;
          groupOdrItems?: Array<{
            __typename: "UseCaseOrdInfsGetListTreesGroupOdrItem";
            groupKouiCode?: number;
            groupName?: string;
            hokenPid?: number;
            inOutKbn?: number;
            inOutName?: string;
            kouiCode?: number;
            isKensa?: boolean;
            isDrug?: boolean;
            santeiKbn?: number;
            santeiName?: string;
            sikyuKbn?: number;
            sikyuName?: string;
            sinkyuName?: string;
            syohoSbt?: number;
            tosekiKbn?: number;
            odrInfs?: Array<{
              __typename: "UseCaseOrdInfsGetListTreesOdrInfItem";
              syohoSbt?: number;
              sikyuKbn?: number;
              santeiKbn?: number;
              createDate?: string;
              createId?: number;
              tosekiKbn?: number;
              createName?: string;
              daysCnt?: number;
              groupOdrKouiKbn?: number;
              hokenPid?: number;
              hpId?: number;
              id?: string;
              inoutKbn?: number;
              isDeleted?: number;
              odrKouiKbn?: number;
              ptId?: string;
              raiinNo?: string;
              rpEdaNo?: string;
              rpName?: string;
              rpNo?: string;
              sinDate?: number;
              sortNo?: number;
              updateDate?: string;
              updateMachine?: string;
              updateName?: string;
              odrDetails?: Array<{
                __typename: "UseCaseOrdInfsGetListTreesOdrInfDetailItem";
                isSelectiveComment?: boolean;
                rousaiKbn?: number;
                bikoComment?: number;
                bunkatu?: string;
                alternationIndex?: number;
                centerName?: string;
                centerItemCd1?: string;
                bunkatuKoui?: number;
                centerItemCd2?: string;
                cmtCol1?: number;
                cmtName?: string;
                cnvUnitName?: string;
                drugKbn?: number;
                hasCmtName?: boolean;
                hpId?: number;
                ipnCd?: string;
                itemCd?: string;
                itemName?: string;
                ptId?: string;
                raiinNo?: string;
                sinKouiKbn?: number;
                rowNo?: number;
                fontColor?: string;
                unitSbt?: number;
                cmtCol2?: number;
                cmtCol3?: number;
                cmtCol4?: number;
                cmtColKeta1?: number;
                cmtColKeta2?: number;
                cmtColKeta3?: number;
                cmtColKeta4?: number;
                cmtOpt?: string;
                cnvTermVal?: number;
                displayItemName?: string;
                commentNewline?: number;
                handanGrpKbn?: number;
                ipnName?: string;
                isGetPriceInYakka?: boolean;
                isKensaMstEmpty?: boolean;
                isNodspRece?: number;
                jissiDate?: string;
                jissiId?: number;
                jissiKbn?: number;
                jissiMachine?: string;
                kasan1?: number;
                kasan2?: number;
                kensaGaichu?: number;
                kikakiUnit?: string;
                kohatuKbn?: number;
                kokuji1?: string;
                masterSbt?: string;
                kokuji2?: string;
                memoItem?: string;
                odrTermVal?: number;
                odrUnitName?: string;
                reqCd?: string;
                rikikaRate?: number;
                rikikaUnit?: string;
                rpEdaNo?: string;
                rpNo?: string;
                sinDate?: number;
                suryo?: number;
                syohoLimitKbn?: number;
                syohoKbn?: number;
                ten?: number;
                termVal?: number;
                unitName?: string;
                yakka?: number;
                yjCd?: string;
                yakkaiUnit?: string;
                yohoKbn?: number;
                youkaiekiCd?: string;
                buiKbn?: number;
                centerCd?: string;
                isAdopted?: number;
                senteiRyoyoKbn?: number;
                yohoSets?: Array<{
                  __typename: "DomainModelsOrdInfDetailsYohoSetMstModel";
                  createDate?: string;
                  createId?: number;
                  createMachine?: string;
                  hpId?: number;
                  isDeleted?: number;
                  isModified?: boolean;
                  itemCd?: string;
                  itemname?: string;
                  setId?: number;
                  sortNo?: number;
                  updateDate?: string;
                  updateId?: number;
                  updateMachine?: string;
                  userId?: number;
                  yohoKbn?: number;
                }>;
              }>;
            }>;
          }>;
        }>;
      };
    };
  };
};

export type GetApiKarteInfGetListQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
}>;

export type GetApiKarteInfGetListQuery = {
  __typename?: "query_root";
  getApiKarteInfGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteInfGetListKarteInfResponse";
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteInfGetListKarteInfResponse";
      karteInfs?: Array<{
        __typename?: "EmrCloudApiResponsesKarteInfKarteInfDto";
        richText?: string;
        text?: string;
      }>;
    };
  };
};

export const PostApiTodayOrdUpsertDocument = gql`
  mutation postApiTodayOrdUpsert(
    $emrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput: EmrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput
  ) {
    postApiTodayOrdUpsert(
      emrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput: $emrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput
    ) {
      data {
        status
        validationKarte {
          status
          validationMessage
        }
        validationOdrInfs {
          orderInfDetailPosition
          orderInfPosition
          status
          validationField
          validationMessage
          hokenName {
            hoKenId
            hokenName
          }
          kohiNames {
            hoKenId
            hokenName
          }
        }
        validationRaiinInf {
          status
          validationMessage
          kohiNames {
            hoKenId
            hokenName
          }
          hokenName {
            hoKenId
            hokenName
          }
        }
      }
      message
      status
    }
  }
`;
export type PostApiTodayOrdUpsertMutationFn = Apollo.MutationFunction<
  PostApiTodayOrdUpsertMutation,
  PostApiTodayOrdUpsertMutationVariables
>;

/**
 * __usePostApiTodayOrdUpsertMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdUpsertMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdUpsertMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdUpsertMutation, { data, loading, error }] = usePostApiTodayOrdUpsertMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdUpsertMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdUpsertMutation,
    PostApiTodayOrdUpsertMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdUpsertMutation,
    PostApiTodayOrdUpsertMutationVariables
  >(PostApiTodayOrdUpsertDocument, options);
}
export type PostApiTodayOrdUpsertMutationHookResult = ReturnType<
  typeof usePostApiTodayOrdUpsertMutation
>;
export type PostApiTodayOrdUpsertMutationResult =
  Apollo.MutationResult<PostApiTodayOrdUpsertMutation>;
export type PostApiTodayOrdUpsertMutationOptions = Apollo.BaseMutationOptions<
  PostApiTodayOrdUpsertMutation,
  PostApiTodayOrdUpsertMutationVariables
>;
export const GetApiOrdInfGetListDocument = gql`
  query getApiOrdInfGetList(
    $ptId: BigInt = ""
    $raiinNo: BigInt = ""
    $sinDate: Int = 20250221
  ) {
    getApiOrdInfGetList(sinDate: $sinDate, ptId: $ptId, raiinNo: $raiinNo) {
      status
      message
      data {
        karteEdition {
          createId
          edition
          hpId
          isDeleted
          karteStatus
          approvalDate
          approvalId
          groupHokenItems {
            groupOdrItems {
              groupKouiCode
              groupName
              hokenPid
              inOutKbn
              inOutName
              kouiCode
              isKensa
              isDrug
              santeiKbn
              santeiName
              sikyuKbn
              sikyuName
              sinkyuName
              syohoSbt
              tosekiKbn
              odrInfs {
                syohoSbt
                sikyuKbn
                santeiKbn
                createDate
                createId
                tosekiKbn
                createName
                daysCnt
                groupOdrKouiKbn
                hokenPid
                hpId
                id
                inoutKbn
                isDeleted
                odrKouiKbn
                ptId
                raiinNo
                rpEdaNo
                rpName
                rpNo
                sinDate
                sortNo
                tosekiKbn
                updateDate
                updateMachine
                updateName
                odrDetails {
                  isSelectiveComment
                  rousaiKbn
                  bikoComment
                  bunkatu
                  alternationIndex
                  centerName
                  centerItemCd1
                  bunkatuKoui
                  centerItemCd2
                  cmtCol1
                  cmtName
                  cnvUnitName
                  drugKbn
                  hasCmtName
                  hpId
                  ipnCd
                  itemCd
                  itemName
                  ptId
                  raiinNo
                  sinKouiKbn
                  rowNo
                  fontColor
                  unitSbt
                  cmtCol2
                  cmtCol3
                  cmtCol4
                  cmtColKeta1
                  cmtColKeta2
                  cmtColKeta3
                  cmtColKeta4
                  cmtOpt
                  cnvTermVal
                  displayItemName
                  commentNewline
                  handanGrpKbn
                  ipnName
                  isGetPriceInYakka
                  isKensaMstEmpty
                  isNodspRece
                  jissiDate
                  jissiId
                  jissiKbn
                  jissiMachine
                  kasan1
                  kasan2
                  kensaGaichu
                  kikakiUnit
                  kohatuKbn
                  kokuji1
                  masterSbt
                  kokuji2
                  memoItem
                  odrTermVal
                  odrUnitName
                  reqCd
                  rikikaRate
                  rikikaUnit
                  rpEdaNo
                  rpNo
                  sinDate
                  suryo
                  syohoLimitKbn
                  syohoKbn
                  ten
                  termVal
                  unitName
                  yakka
                  yjCd
                  yakkaiUnit
                  yohoKbn
                  youkaiekiCd
                  yohoSets {
                    createDate
                    createId
                    createMachine
                    hpId
                    isDeleted
                    isModified
                    itemCd
                    itemname
                    setId
                    sortNo
                    updateDate
                    updateId
                    updateMachine
                    userId
                    yohoKbn
                    __typename
                  }
                  __typename
                  bikoComment
                  buiKbn
                  centerCd
                  centerName
                  isAdopted
                  rousaiKbn
                  senteiRyoyoKbn
                }
                __typename
              }
              __typename
            }
            hokenPid
            hokenTitle
            __typename
          }
          __typename
          ptId
          raiinNo
          updateDate
          updateId
        }
        __typename
      }
      __typename
    }
  }
`;

/**
 * __useGetApiOrdInfGetListQuery__
 *
 * To run a query within a React component, call `useGetApiOrdInfGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiOrdInfGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiOrdInfGetListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiOrdInfGetListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiOrdInfGetListQuery,
    GetApiOrdInfGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiOrdInfGetListQuery,
    GetApiOrdInfGetListQueryVariables
  >(GetApiOrdInfGetListDocument, options);
}
export function useGetApiOrdInfGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiOrdInfGetListQuery,
    GetApiOrdInfGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiOrdInfGetListQuery,
    GetApiOrdInfGetListQueryVariables
  >(GetApiOrdInfGetListDocument, options);
}
export function useGetApiOrdInfGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiOrdInfGetListQuery,
    GetApiOrdInfGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiOrdInfGetListQuery,
    GetApiOrdInfGetListQueryVariables
  >(GetApiOrdInfGetListDocument, options);
}
export type GetApiOrdInfGetListQueryHookResult = ReturnType<
  typeof useGetApiOrdInfGetListQuery
>;
export type GetApiOrdInfGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiOrdInfGetListLazyQuery
>;
export type GetApiOrdInfGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiOrdInfGetListSuspenseQuery
>;
export type GetApiOrdInfGetListQueryResult = Apollo.QueryResult<
  GetApiOrdInfGetListQuery,
  GetApiOrdInfGetListQueryVariables
>;
export const GetApiKarteInfGetListDocument = gql`
  query getApiKarteInfGetList(
    $ptId: BigInt!
    $raiinNo: BigInt!
    $sinDate: Int!
  ) {
    getApiKarteInfGetList(ptId: $ptId, raiinNo: $raiinNo, sinDate: $sinDate) {
      data {
        karteInfs {
          richText
          text
        }
      }
      status
    }
  }
`;

/**
 * __useGetApiKarteInfGetListQuery__
 *
 * To run a query within a React component, call `useGetApiKarteInfGetListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKarteInfGetListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKarteInfGetListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiKarteInfGetListQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiKarteInfGetListQuery,
    GetApiKarteInfGetListQueryVariables
  > &
    (
      | { variables: GetApiKarteInfGetListQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiKarteInfGetListQuery,
    GetApiKarteInfGetListQueryVariables
  >(GetApiKarteInfGetListDocument, options);
}
export function useGetApiKarteInfGetListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiKarteInfGetListQuery,
    GetApiKarteInfGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiKarteInfGetListQuery,
    GetApiKarteInfGetListQueryVariables
  >(GetApiKarteInfGetListDocument, options);
}
export function useGetApiKarteInfGetListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiKarteInfGetListQuery,
    GetApiKarteInfGetListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiKarteInfGetListQuery,
    GetApiKarteInfGetListQueryVariables
  >(GetApiKarteInfGetListDocument, options);
}
export type GetApiKarteInfGetListQueryHookResult = ReturnType<
  typeof useGetApiKarteInfGetListQuery
>;
export type GetApiKarteInfGetListLazyQueryHookResult = ReturnType<
  typeof useGetApiKarteInfGetListLazyQuery
>;
export type GetApiKarteInfGetListSuspenseQueryHookResult = ReturnType<
  typeof useGetApiKarteInfGetListSuspenseQuery
>;
export type GetApiKarteInfGetListQueryResult = Apollo.QueryResult<
  GetApiKarteInfGetListQuery,
  GetApiKarteInfGetListQueryVariables
>;
