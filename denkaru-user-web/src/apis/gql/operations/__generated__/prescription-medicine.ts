import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiEpsGetDispensingInfFromCsvDataMutationVariables =
  Types.Exact<{
    dispensingCsvBase64Data: Types.Scalars["String"]["input"];
  }>;

export type PostApiEpsGetDispensingInfFromCsvDataMutation = {
  __typename?: "mutation_root";
  postApiEpsGetDispensingInfFromCsvData?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpSGetDispensingFromCsvResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpSGetDispensingFromCsvResponse";
      dispensingGroupDetailModels?: Array<{
        __typename?: "DomainModelsEpsDispensingGroupDetailModel";
        data?: string;
        groupTitle?: string;
        item?: string;
      }>;
      groupOrderModels?: Array<{
        __typename?: "DomainModelsEpsGroupOrderModel";
        rp?: string;
        rpDisplay?: string;
        drugOrders?: Array<{
          __typename?: "DomainModelsEpsDrugOrderModel";
          drugName?: string;
          medicineSupplement?: string;
          quantity?: string;
          usageNote?: string;
        }>;
        usageOrders?: Array<{
          __typename?: "DomainModelsEpsUsageOrderModel";
          dispensing?: string;
          dosageForm?: string;
          prescriptionCaution?: string;
          usage?: string;
          usageSupplementary?: string;
        }>;
      }>;
      longInformationModels?: Array<{
        __typename?: "DomainModelsEpsLongInformationModel";
        header?: string;
        recordedContents?: Array<string>;
      }>;
    };
  };
};

export type PostApiEpsUpsertDispensingInfMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<
    | Array<Types.EmrCloudApiRequestsEpsUpdateEpsDispensingItemsInput>
    | Types.EmrCloudApiRequestsEpsUpdateEpsDispensingItemsInput
  >;
}>;

export type PostApiEpsUpsertDispensingInfMutation = {
  __typename?: "mutation_root";
  postApiEpsUpsertDispensingInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsUpdateEpsDispensingsResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsUpdateEpsDispensingsResponse";
      isSuccess?: boolean;
    };
  };
};

export const PostApiEpsGetDispensingInfFromCsvDataDocument = gql`
  mutation postApiEpsGetDispensingInfFromCsvData(
    $dispensingCsvBase64Data: String!
  ) {
    postApiEpsGetDispensingInfFromCsvData(
      emrCloudApiRequestsEpsGetDispensingInfFromCsvRequestInput: {
        dispensingCsvBase64Data: $dispensingCsvBase64Data
      }
    ) {
      data {
        dispensingGroupDetailModels {
          data
          groupTitle
          item
        }
        groupOrderModels {
          drugOrders {
            drugName
            medicineSupplement
            quantity
            usageNote
          }
          rp
          rpDisplay
          usageOrders {
            dispensing
            dosageForm
            prescriptionCaution
            usage
            usageSupplementary
          }
        }
        longInformationModels {
          header
          recordedContents
        }
      }
      message
      status
    }
  }
`;
export type PostApiEpsGetDispensingInfFromCsvDataMutationFn =
  Apollo.MutationFunction<
    PostApiEpsGetDispensingInfFromCsvDataMutation,
    PostApiEpsGetDispensingInfFromCsvDataMutationVariables
  >;

/**
 * __usePostApiEpsGetDispensingInfFromCsvDataMutation__
 *
 * To run a mutation, you first call `usePostApiEpsGetDispensingInfFromCsvDataMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsGetDispensingInfFromCsvDataMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsGetDispensingInfFromCsvDataMutation, { data, loading, error }] = usePostApiEpsGetDispensingInfFromCsvDataMutation({
 *   variables: {
 *      dispensingCsvBase64Data: // value for 'dispensingCsvBase64Data'
 *   },
 * });
 */
export function usePostApiEpsGetDispensingInfFromCsvDataMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsGetDispensingInfFromCsvDataMutation,
    PostApiEpsGetDispensingInfFromCsvDataMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsGetDispensingInfFromCsvDataMutation,
    PostApiEpsGetDispensingInfFromCsvDataMutationVariables
  >(PostApiEpsGetDispensingInfFromCsvDataDocument, options);
}
export type PostApiEpsGetDispensingInfFromCsvDataMutationHookResult =
  ReturnType<typeof usePostApiEpsGetDispensingInfFromCsvDataMutation>;
export type PostApiEpsGetDispensingInfFromCsvDataMutationResult =
  Apollo.MutationResult<PostApiEpsGetDispensingInfFromCsvDataMutation>;
export type PostApiEpsGetDispensingInfFromCsvDataMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsGetDispensingInfFromCsvDataMutation,
    PostApiEpsGetDispensingInfFromCsvDataMutationVariables
  >;
export const PostApiEpsUpsertDispensingInfDocument = gql`
  mutation postApiEpsUpsertDispensingInf(
    $input: [EmrCloudApiRequestsEpsUpdateEpsDispensingItemsInput!]
  ) {
    postApiEpsUpsertDispensingInf(
      emrCloudApiRequestsEpsUpsertDispensingInfRequestInput: {
        updateEpsDispensingItems: $input
      }
    ) {
      data {
        isSuccess
      }
      message
      status
    }
  }
`;
export type PostApiEpsUpsertDispensingInfMutationFn = Apollo.MutationFunction<
  PostApiEpsUpsertDispensingInfMutation,
  PostApiEpsUpsertDispensingInfMutationVariables
>;

/**
 * __usePostApiEpsUpsertDispensingInfMutation__
 *
 * To run a mutation, you first call `usePostApiEpsUpsertDispensingInfMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsUpsertDispensingInfMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsUpsertDispensingInfMutation, { data, loading, error }] = usePostApiEpsUpsertDispensingInfMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiEpsUpsertDispensingInfMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsUpsertDispensingInfMutation,
    PostApiEpsUpsertDispensingInfMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsUpsertDispensingInfMutation,
    PostApiEpsUpsertDispensingInfMutationVariables
  >(PostApiEpsUpsertDispensingInfDocument, options);
}
export type PostApiEpsUpsertDispensingInfMutationHookResult = ReturnType<
  typeof usePostApiEpsUpsertDispensingInfMutation
>;
export type PostApiEpsUpsertDispensingInfMutationResult =
  Apollo.MutationResult<PostApiEpsUpsertDispensingInfMutation>;
export type PostApiEpsUpsertDispensingInfMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsUpsertDispensingInfMutation,
    PostApiEpsUpsertDispensingInfMutationVariables
  >;
