import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type SaveKarteSummaryMutationVariables = Types.Exact<{
  id: Types.Scalars["BigInt"]["input"];
  ptId: Types.Scalars["BigInt"]["input"];
  rtext: Types.Scalars["String"]["input"];
  seqNo: Types.Scalars["BigInt"]["input"];
  text: Types.Scalars["String"]["input"];
}>;

export type SaveKarteSummaryMutation = {
  __typename?: "mutation_root";
  postApiSummarySave?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSummarySaveSummaryResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesSummarySaveSummaryResponse";
      success?: boolean;
    };
  };
};

export type GetKarteSummaryQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
}>;

export type GetKarteSummaryQuery = {
  __typename?: "query_root";
  getApiSummaryGetSummaryInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesSummaryGetSummaryResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesSummaryGetSummaryResponse";
      summaryInfModel?: {
        __typename?: "DomainModelsSpecialNoteSummaryInfSummaryInfModel";
        hpId?: number;
        id?: string;
        rtext?: string;
        seqNo?: string;
        text?: string;
      };
    };
  };
};

export type GetJsonSettingListQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetJsonSettingListQuery = {
  __typename?: "query_root";
  getApiJsonSettingGetList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesJsonSettingGetAllJsonSettingResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesJsonSettingGetAllJsonSettingResponse";
      settings?: Array<{
        __typename?: "UseCaseJsonSettingJsonSettingDto";
        key?: string;
        value?: any;
      }>;
    };
  };
};

export const SaveKarteSummaryDocument = gql`
  mutation saveKarteSummary(
    $id: BigInt!
    $ptId: BigInt!
    $rtext: String!
    $seqNo: BigInt!
    $text: String!
  ) {
    postApiSummarySave(
      emrCloudApiRequestsSummarySaveSummaryRequestInput: {
        id: $id
        ptId: $ptId
        rtext: $rtext
        seqNo: $seqNo
        text: $text
      }
    ) {
      data {
        success
      }
      message
      status
    }
  }
`;
export type SaveKarteSummaryMutationFn = Apollo.MutationFunction<
  SaveKarteSummaryMutation,
  SaveKarteSummaryMutationVariables
>;

/**
 * __useSaveKarteSummaryMutation__
 *
 * To run a mutation, you first call `useSaveKarteSummaryMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveKarteSummaryMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveKarteSummaryMutation, { data, loading, error }] = useSaveKarteSummaryMutation({
 *   variables: {
 *      id: // value for 'id'
 *      ptId: // value for 'ptId'
 *      rtext: // value for 'rtext'
 *      seqNo: // value for 'seqNo'
 *      text: // value for 'text'
 *   },
 * });
 */
export function useSaveKarteSummaryMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SaveKarteSummaryMutation,
    SaveKarteSummaryMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SaveKarteSummaryMutation,
    SaveKarteSummaryMutationVariables
  >(SaveKarteSummaryDocument, options);
}
export type SaveKarteSummaryMutationHookResult = ReturnType<
  typeof useSaveKarteSummaryMutation
>;
export type SaveKarteSummaryMutationResult =
  Apollo.MutationResult<SaveKarteSummaryMutation>;
export type SaveKarteSummaryMutationOptions = Apollo.BaseMutationOptions<
  SaveKarteSummaryMutation,
  SaveKarteSummaryMutationVariables
>;
export const GetKarteSummaryDocument = gql`
  query getKarteSummary($ptId: BigInt!) {
    getApiSummaryGetSummaryInf(ptId: $ptId) {
      data {
        summaryInfModel {
          hpId
          id
          rtext
          seqNo
          text
        }
      }
    }
  }
`;

/**
 * __useGetKarteSummaryQuery__
 *
 * To run a query within a React component, call `useGetKarteSummaryQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetKarteSummaryQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetKarteSummaryQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetKarteSummaryQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetKarteSummaryQuery,
    GetKarteSummaryQueryVariables
  > &
    (
      | { variables: GetKarteSummaryQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetKarteSummaryQuery, GetKarteSummaryQueryVariables>(
    GetKarteSummaryDocument,
    options,
  );
}
export function useGetKarteSummaryLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetKarteSummaryQuery,
    GetKarteSummaryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetKarteSummaryQuery,
    GetKarteSummaryQueryVariables
  >(GetKarteSummaryDocument, options);
}
export function useGetKarteSummarySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetKarteSummaryQuery,
    GetKarteSummaryQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetKarteSummaryQuery,
    GetKarteSummaryQueryVariables
  >(GetKarteSummaryDocument, options);
}
export type GetKarteSummaryQueryHookResult = ReturnType<
  typeof useGetKarteSummaryQuery
>;
export type GetKarteSummaryLazyQueryHookResult = ReturnType<
  typeof useGetKarteSummaryLazyQuery
>;
export type GetKarteSummarySuspenseQueryHookResult = ReturnType<
  typeof useGetKarteSummarySuspenseQuery
>;
export type GetKarteSummaryQueryResult = Apollo.QueryResult<
  GetKarteSummaryQuery,
  GetKarteSummaryQueryVariables
>;
export const GetJsonSettingListDocument = gql`
  query getJsonSettingList {
    getApiJsonSettingGetList {
      data {
        settings {
          key
          value
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetJsonSettingListQuery__
 *
 * To run a query within a React component, call `useGetJsonSettingListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetJsonSettingListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetJsonSettingListQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetJsonSettingListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetJsonSettingListQuery,
    GetJsonSettingListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetJsonSettingListQuery,
    GetJsonSettingListQueryVariables
  >(GetJsonSettingListDocument, options);
}
export function useGetJsonSettingListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetJsonSettingListQuery,
    GetJsonSettingListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetJsonSettingListQuery,
    GetJsonSettingListQueryVariables
  >(GetJsonSettingListDocument, options);
}
export function useGetJsonSettingListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetJsonSettingListQuery,
    GetJsonSettingListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetJsonSettingListQuery,
    GetJsonSettingListQueryVariables
  >(GetJsonSettingListDocument, options);
}
export type GetJsonSettingListQueryHookResult = ReturnType<
  typeof useGetJsonSettingListQuery
>;
export type GetJsonSettingListLazyQueryHookResult = ReturnType<
  typeof useGetJsonSettingListLazyQuery
>;
export type GetJsonSettingListSuspenseQueryHookResult = ReturnType<
  typeof useGetJsonSettingListSuspenseQuery
>;
export type GetJsonSettingListQueryResult = Apollo.QueryResult<
  GetJsonSettingListQuery,
  GetJsonSettingListQueryVariables
>;
