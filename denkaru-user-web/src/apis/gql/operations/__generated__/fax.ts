import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetFaxFileUploadUrlQueryVariables = Types.Exact<{
  input: Types.GetFaxFileUploadReq;
}>;

export type GetFaxFileUploadUrlQuery = {
  __typename?: "query_root";
  getFaxFileUploadUrl: {
    __typename?: "GetFaxFileUploadRes";
    s3Key: string;
    url: string;
  };
};

export type GetFaxListQueryVariables = Types.Exact<{
  input: Types.GetFaxListReq;
}>;

export type GetFaxListQuery = {
  __typename?: "query_root";
  getFaxList: Array<{
    __typename?: "FaxInfo";
    faxId: number;
    fileName: string;
    fileUrl: string;
    s3Key: string;
    direction: number;
    from: string;
    to: string;
    status: number;
    createdAt: string;
  }>;
};

export type SendFaxMutationVariables = Types.Exact<{
  input: Types.SendFaxReq;
}>;

export type SendFaxMutation = {
  __typename?: "mutation_root";
  sendFax: { __typename?: "SendFaxRes"; faxId: number; status: number };
};

export const GetFaxFileUploadUrlDocument = gql`
  query getFaxFileUploadUrl($input: GetFaxFileUploadReq!) {
    getFaxFileUploadUrl(input: $input) {
      s3Key
      url
    }
  }
`;

/**
 * __useGetFaxFileUploadUrlQuery__
 *
 * To run a query within a React component, call `useGetFaxFileUploadUrlQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetFaxFileUploadUrlQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetFaxFileUploadUrlQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetFaxFileUploadUrlQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetFaxFileUploadUrlQuery,
    GetFaxFileUploadUrlQueryVariables
  > &
    (
      | { variables: GetFaxFileUploadUrlQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetFaxFileUploadUrlQuery,
    GetFaxFileUploadUrlQueryVariables
  >(GetFaxFileUploadUrlDocument, options);
}
export function useGetFaxFileUploadUrlLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetFaxFileUploadUrlQuery,
    GetFaxFileUploadUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetFaxFileUploadUrlQuery,
    GetFaxFileUploadUrlQueryVariables
  >(GetFaxFileUploadUrlDocument, options);
}
export function useGetFaxFileUploadUrlSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetFaxFileUploadUrlQuery,
    GetFaxFileUploadUrlQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetFaxFileUploadUrlQuery,
    GetFaxFileUploadUrlQueryVariables
  >(GetFaxFileUploadUrlDocument, options);
}
export type GetFaxFileUploadUrlQueryHookResult = ReturnType<
  typeof useGetFaxFileUploadUrlQuery
>;
export type GetFaxFileUploadUrlLazyQueryHookResult = ReturnType<
  typeof useGetFaxFileUploadUrlLazyQuery
>;
export type GetFaxFileUploadUrlSuspenseQueryHookResult = ReturnType<
  typeof useGetFaxFileUploadUrlSuspenseQuery
>;
export type GetFaxFileUploadUrlQueryResult = Apollo.QueryResult<
  GetFaxFileUploadUrlQuery,
  GetFaxFileUploadUrlQueryVariables
>;
export const GetFaxListDocument = gql`
  query getFaxList($input: GetFaxListReq!) {
    getFaxList(input: $input) {
      faxId
      fileName
      fileUrl
      s3Key
      direction
      from
      to
      status
      createdAt
    }
  }
`;

/**
 * __useGetFaxListQuery__
 *
 * To run a query within a React component, call `useGetFaxListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetFaxListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetFaxListQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetFaxListQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetFaxListQuery,
    GetFaxListQueryVariables
  > &
    (
      | { variables: GetFaxListQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetFaxListQuery, GetFaxListQueryVariables>(
    GetFaxListDocument,
    options,
  );
}
export function useGetFaxListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetFaxListQuery,
    GetFaxListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetFaxListQuery, GetFaxListQueryVariables>(
    GetFaxListDocument,
    options,
  );
}
export function useGetFaxListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetFaxListQuery,
    GetFaxListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<GetFaxListQuery, GetFaxListQueryVariables>(
    GetFaxListDocument,
    options,
  );
}
export type GetFaxListQueryHookResult = ReturnType<typeof useGetFaxListQuery>;
export type GetFaxListLazyQueryHookResult = ReturnType<
  typeof useGetFaxListLazyQuery
>;
export type GetFaxListSuspenseQueryHookResult = ReturnType<
  typeof useGetFaxListSuspenseQuery
>;
export type GetFaxListQueryResult = Apollo.QueryResult<
  GetFaxListQuery,
  GetFaxListQueryVariables
>;
export const SendFaxDocument = gql`
  mutation sendFax($input: SendFaxReq!) {
    sendFax(input: $input) {
      faxId
      status
    }
  }
`;
export type SendFaxMutationFn = Apollo.MutationFunction<
  SendFaxMutation,
  SendFaxMutationVariables
>;

/**
 * __useSendFaxMutation__
 *
 * To run a mutation, you first call `useSendFaxMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSendFaxMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sendFaxMutation, { data, loading, error }] = useSendFaxMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSendFaxMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SendFaxMutation,
    SendFaxMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<SendFaxMutation, SendFaxMutationVariables>(
    SendFaxDocument,
    options,
  );
}
export type SendFaxMutationHookResult = ReturnType<typeof useSendFaxMutation>;
export type SendFaxMutationResult = Apollo.MutationResult<SendFaxMutation>;
export type SendFaxMutationOptions = Apollo.BaseMutationOptions<
  SendFaxMutation,
  SendFaxMutationVariables
>;
