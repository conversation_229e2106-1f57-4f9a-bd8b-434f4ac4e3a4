import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiRequestExamGetRequestExamQueryVariables = Types.Exact<{
  endDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  startDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kensaCenterMstCenterCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  kensaCenterMstPrimaryKbn?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  isRequestConfirmation?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiRequestExamGetRequestExamQuery = {
  __typename?: "query_root";
  getApiRequestExamGetRequestExam?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesRequestExamGetRequestExamResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesRequestExamGetRequestExamResponse";
      kensaInfList?: Array<{
        __typename?: "EmrCloudApiResponsesRequestExamDtoRequestExamDto";
        birthday?: number;
        dspCenterName?: string;
        id?: string;
        iraiCd?: string;
        iraiDate?: number;
        isDeleted?: number;
        kaId?: number;
        kaName?: string;
        kanaName?: string;
        kensaNames?: Array<string>;
        kensaTime?: string;
        name?: string;
        ptId?: string;
        ptNum?: string;
        raiinNo?: string;
        sex?: number;
        sikyuKbn?: number;
        sinDate?: number;
        status?: number;
        tosekiKbn?: number;
        updateDate?: string;
        userName?: string;
        kensaIraiDetails?: Array<{
          __typename?: "EmrCloudApiResponsesMainMenuDtoKensaIraiDetailDto";
          centerCd?: string;
          centerItemCd?: string;
          containerCd?: string;
          itemCd?: string;
          itemName?: string;
          kanaName1?: string;
          kensaItemCd?: string;
          kensaKana?: string;
          kensaName?: string;
          rowNo?: number;
          rpEdaNo?: string;
          rpNo?: string;
          seqNo?: number;
          tenKensaItemCd?: string;
        }>;
      }>;
    };
  };
};

export type PostApiRequestExamSaveMutationVariables = Types.Exact<{
  orderInfIds:
    | Array<Types.Scalars["BigInt"]["input"]>
    | Types.Scalars["BigInt"]["input"];
  centerCd: Types.Scalars["String"]["input"];
}>;

export type PostApiRequestExamSaveMutation = {
  __typename?: "mutation_root";
  postApiRequestExamSave?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesRequestExamSaveRequestExamsResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesRequestExamSaveRequestExamsResponse";
      iraiCds?: any;
    };
  };
};

export type PostApiMainMenuKensaIraiReportMutationVariables = Types.Exact<{
  centerCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  fromDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kensaInfList?: Types.InputMaybe<
    | Array<
        Types.InputMaybe<Types.EmrCloudApiRequestsMainMenuRequestItemKensaIraiReportRequestItemInput>
      >
    | Types.InputMaybe<Types.EmrCloudApiRequestsMainMenuRequestItemKensaIraiReportRequestItemInput>
  >;
  systemDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  toDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type PostApiMainMenuKensaIraiReportMutation = {
  __typename?: "mutation_root";
  postApiMainMenuKensaIraiReport?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMainMenuKensaIraiReportResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMainMenuKensaIraiReportResponse";
      datFile?: string;
      fileType?: string;
      pdfFile?: string;
    };
  };
};

export type PostApiRequestExamDeleteMutationVariables = Types.Exact<{
  iraiCds?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars["BigInt"]["input"]>>
    | Types.InputMaybe<Types.Scalars["BigInt"]["input"]>
  >;
}>;

export type PostApiRequestExamDeleteMutation = {
  __typename?: "mutation_root";
  postApiRequestExamDelete?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesRequestExamDeleteRequestExamsResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesRequestExamDeleteRequestExamsResponse";
      isSuccess?: boolean;
    };
  };
};

export type PostApiMainMenuImportKensaIraiMutationVariables = Types.Exact<{
  datFileBase64: Types.Scalars["String"]["input"];
}>;

export type PostApiMainMenuImportKensaIraiMutation = {
  __typename?: "mutation_root";
  postApiMainMenuImportKensaIrai?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMainMenuImportKensaIraiResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMainMenuImportKensaIraiResponse";
      importKensaInfMessageModel?: {
        __typename?: "DomainModelsKensaIraiImportKensaInfMessageModel";
        errorMessage?: string;
        successCount?: number;
        successed?: boolean;
      };
    };
  };
};

export type GetApiMainMenuGetKensaIraiLogQueryVariables = Types.Exact<{
  endDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  startDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiMainMenuGetKensaIraiLogQuery = {
  __typename?: "query_root";
  getApiMainMenuGetKensaIraiLog?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMainMenuGetKensaIraiLogResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMainMenuGetKensaIraiLogResponse";
      kensaIraiLogList?: Array<{
        __typename?: "EmrCloudApiResponsesMainMenuDtoKensaIraiLogDto";
        centerCd?: string;
        centerName?: string;
        createDate?: string;
        fromDate?: number;
        iraiDate?: number;
        iraiFile?: string;
        iraiList?: string;
        toDate?: number;
      }>;
    };
  };
};

export type PostApiMainMenuReCreateDataKensaIraiRenkeiMutationVariables =
  Types.Exact<{
    kensaIraiList?: Types.InputMaybe<
      | Array<
          Types.InputMaybe<Types.EmrCloudApiRequestsMainMenuRequestItemKensaIraiRequestItemInput>
        >
      | Types.InputMaybe<Types.EmrCloudApiRequestsMainMenuRequestItemKensaIraiRequestItemInput>
    >;
    systemDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type PostApiMainMenuReCreateDataKensaIraiRenkeiMutation = {
  __typename?: "mutation_root";
  postApiMainMenuReCreateDataKensaIraiRenkei?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMainMenuCreateDataKensaIraiRenkeiResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMainMenuCreateDataKensaIraiRenkeiResponse";
      successed?: boolean;
      kensaIraiList?: Array<{
        __typename?: "EmrCloudApiResponsesMainMenuDtoKensaIraiDto";
        age?: number;
        birthday?: number;
        iraiCd?: string;
        kaId?: number;
        kanaName?: string;
        name?: string;
        ptId?: string;
        ptNum?: string;
        raiinNo?: string;
        sex?: number;
        sikyuKbn?: number;
        sikyuStr?: string;
        sinDate?: number;
        tosekiKbn?: number;
        tosekiStr?: string;
        updateDate?: string;
        kensaIraiDetails?: Array<{
          __typename?: "EmrCloudApiResponsesMainMenuDtoKensaIraiDetailDto";
          centerCd?: string;
          centerItemCd?: string;
          containerCd?: string;
          itemCd?: string;
          itemName?: string;
          kanaName1?: string;
          kensaItemCd?: string;
          kensaKana?: string;
          kensaName?: string;
          rowNo?: number;
          rpEdaNo?: string;
          rpNo?: string;
          seqNo?: number;
          tenKensaItemCd?: string;
        }>;
      }>;
    };
  };
};

export type GetApiMainMenuDownloadKensaIraiLogQueryVariables = Types.Exact<{
  centerCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  createDate?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiMainMenuDownloadKensaIraiLogQuery = {
  __typename?: "query_root";
  getApiMainMenuDownloadKensaIraiLog?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMainMenuDownloadKensaIraiLogResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMainMenuDownloadKensaIraiLogResponse";
      downloadKensaIraiLog?: {
        __typename?: "EmrCloudApiResponsesMainMenuDtoDownloadKensaIraiLogDto";
        centerCd?: string;
        createDate?: string;
        iraiFile?: string;
        iraiList?: string;
      };
    };
  };
};

export const GetApiRequestExamGetRequestExamDocument = gql`
  query getApiRequestExamGetRequestExam(
    $endDate: Int
    $startDate: Int
    $kensaCenterMstCenterCd: String
    $kensaCenterMstPrimaryKbn: Int
    $ptId: BigInt
    $isRequestConfirmation: Boolean
  ) {
    getApiRequestExamGetRequestExam(
      endDate: $endDate
      startDate: $startDate
      kensaCenterMstCenterCd: $kensaCenterMstCenterCd
      kensaCenterMstPrimaryKbn: $kensaCenterMstPrimaryKbn
      ptId: $ptId
      isRequestConfirmation: $isRequestConfirmation
    ) {
      message
      status
      data {
        kensaInfList {
          birthday
          dspCenterName
          id
          iraiCd
          iraiDate
          isDeleted
          kaId
          kaName
          kanaName
          kensaNames
          kensaIraiDetails {
            centerCd
            centerItemCd
            containerCd
            itemCd
            itemName
            kanaName1
            kensaItemCd
            kensaKana
            kensaName
            rowNo
            rpEdaNo
            rpNo
            seqNo
            tenKensaItemCd
          }
          kensaTime
          name
          ptId
          ptNum
          raiinNo
          sex
          sikyuKbn
          sinDate
          status
          tosekiKbn
          updateDate
          userName
        }
      }
    }
  }
`;

/**
 * __useGetApiRequestExamGetRequestExamQuery__
 *
 * To run a query within a React component, call `useGetApiRequestExamGetRequestExamQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiRequestExamGetRequestExamQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiRequestExamGetRequestExamQuery({
 *   variables: {
 *      endDate: // value for 'endDate'
 *      startDate: // value for 'startDate'
 *      kensaCenterMstCenterCd: // value for 'kensaCenterMstCenterCd'
 *      kensaCenterMstPrimaryKbn: // value for 'kensaCenterMstPrimaryKbn'
 *      ptId: // value for 'ptId'
 *      isRequestConfirmation: // value for 'isRequestConfirmation'
 *   },
 * });
 */
export function useGetApiRequestExamGetRequestExamQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiRequestExamGetRequestExamQuery,
    GetApiRequestExamGetRequestExamQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiRequestExamGetRequestExamQuery,
    GetApiRequestExamGetRequestExamQueryVariables
  >(GetApiRequestExamGetRequestExamDocument, options);
}
export function useGetApiRequestExamGetRequestExamLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiRequestExamGetRequestExamQuery,
    GetApiRequestExamGetRequestExamQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiRequestExamGetRequestExamQuery,
    GetApiRequestExamGetRequestExamQueryVariables
  >(GetApiRequestExamGetRequestExamDocument, options);
}
export function useGetApiRequestExamGetRequestExamSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiRequestExamGetRequestExamQuery,
    GetApiRequestExamGetRequestExamQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiRequestExamGetRequestExamQuery,
    GetApiRequestExamGetRequestExamQueryVariables
  >(GetApiRequestExamGetRequestExamDocument, options);
}
export type GetApiRequestExamGetRequestExamQueryHookResult = ReturnType<
  typeof useGetApiRequestExamGetRequestExamQuery
>;
export type GetApiRequestExamGetRequestExamLazyQueryHookResult = ReturnType<
  typeof useGetApiRequestExamGetRequestExamLazyQuery
>;
export type GetApiRequestExamGetRequestExamSuspenseQueryHookResult = ReturnType<
  typeof useGetApiRequestExamGetRequestExamSuspenseQuery
>;
export type GetApiRequestExamGetRequestExamQueryResult = Apollo.QueryResult<
  GetApiRequestExamGetRequestExamQuery,
  GetApiRequestExamGetRequestExamQueryVariables
>;
export const PostApiRequestExamSaveDocument = gql`
  mutation postApiRequestExamSave(
    $orderInfIds: [BigInt!]!
    $centerCd: String!
  ) {
    postApiRequestExamSave(
      emrCloudApiRequestsRequestExamSaveRequestExamsRequestInput: {
        orderInfIds: $orderInfIds
        centerCd: $centerCd
      }
    ) {
      data {
        iraiCds
      }
      message
      status
    }
  }
`;
export type PostApiRequestExamSaveMutationFn = Apollo.MutationFunction<
  PostApiRequestExamSaveMutation,
  PostApiRequestExamSaveMutationVariables
>;

/**
 * __usePostApiRequestExamSaveMutation__
 *
 * To run a mutation, you first call `usePostApiRequestExamSaveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiRequestExamSaveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiRequestExamSaveMutation, { data, loading, error }] = usePostApiRequestExamSaveMutation({
 *   variables: {
 *      orderInfIds: // value for 'orderInfIds'
 *      centerCd: // value for 'centerCd'
 *   },
 * });
 */
export function usePostApiRequestExamSaveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiRequestExamSaveMutation,
    PostApiRequestExamSaveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiRequestExamSaveMutation,
    PostApiRequestExamSaveMutationVariables
  >(PostApiRequestExamSaveDocument, options);
}
export type PostApiRequestExamSaveMutationHookResult = ReturnType<
  typeof usePostApiRequestExamSaveMutation
>;
export type PostApiRequestExamSaveMutationResult =
  Apollo.MutationResult<PostApiRequestExamSaveMutation>;
export type PostApiRequestExamSaveMutationOptions = Apollo.BaseMutationOptions<
  PostApiRequestExamSaveMutation,
  PostApiRequestExamSaveMutationVariables
>;
export const PostApiMainMenuKensaIraiReportDocument = gql`
  mutation postApiMainMenuKensaIraiReport(
    $centerCd: String
    $fromDate: Int
    $kensaInfList: [EmrCloudApiRequestsMainMenuRequestItemKensaIraiReportRequestItemInput]
    $systemDate: Int
    $toDate: Int
  ) {
    postApiMainMenuKensaIraiReport(
      emrCloudApiRequestsMainMenuKensaIraiReportRequestInput: {
        centerCd: $centerCd
        fromDate: $fromDate
        kensaInfList: $kensaInfList
        toDate: $toDate
        systemDate: $systemDate
      }
    ) {
      data {
        datFile
        fileType
        pdfFile
      }
      message
      status
    }
  }
`;
export type PostApiMainMenuKensaIraiReportMutationFn = Apollo.MutationFunction<
  PostApiMainMenuKensaIraiReportMutation,
  PostApiMainMenuKensaIraiReportMutationVariables
>;

/**
 * __usePostApiMainMenuKensaIraiReportMutation__
 *
 * To run a mutation, you first call `usePostApiMainMenuKensaIraiReportMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMainMenuKensaIraiReportMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMainMenuKensaIraiReportMutation, { data, loading, error }] = usePostApiMainMenuKensaIraiReportMutation({
 *   variables: {
 *      centerCd: // value for 'centerCd'
 *      fromDate: // value for 'fromDate'
 *      kensaInfList: // value for 'kensaInfList'
 *      systemDate: // value for 'systemDate'
 *      toDate: // value for 'toDate'
 *   },
 * });
 */
export function usePostApiMainMenuKensaIraiReportMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMainMenuKensaIraiReportMutation,
    PostApiMainMenuKensaIraiReportMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMainMenuKensaIraiReportMutation,
    PostApiMainMenuKensaIraiReportMutationVariables
  >(PostApiMainMenuKensaIraiReportDocument, options);
}
export type PostApiMainMenuKensaIraiReportMutationHookResult = ReturnType<
  typeof usePostApiMainMenuKensaIraiReportMutation
>;
export type PostApiMainMenuKensaIraiReportMutationResult =
  Apollo.MutationResult<PostApiMainMenuKensaIraiReportMutation>;
export type PostApiMainMenuKensaIraiReportMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMainMenuKensaIraiReportMutation,
    PostApiMainMenuKensaIraiReportMutationVariables
  >;
export const PostApiRequestExamDeleteDocument = gql`
  mutation postApiRequestExamDelete($iraiCds: [BigInt]) {
    postApiRequestExamDelete(
      emrCloudApiRequestsRequestExamDeleteRequestExamsRequestInput: {
        iraiCds: $iraiCds
      }
    ) {
      data {
        isSuccess
      }
      message
      status
    }
  }
`;
export type PostApiRequestExamDeleteMutationFn = Apollo.MutationFunction<
  PostApiRequestExamDeleteMutation,
  PostApiRequestExamDeleteMutationVariables
>;

/**
 * __usePostApiRequestExamDeleteMutation__
 *
 * To run a mutation, you first call `usePostApiRequestExamDeleteMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiRequestExamDeleteMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiRequestExamDeleteMutation, { data, loading, error }] = usePostApiRequestExamDeleteMutation({
 *   variables: {
 *      iraiCds: // value for 'iraiCds'
 *   },
 * });
 */
export function usePostApiRequestExamDeleteMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiRequestExamDeleteMutation,
    PostApiRequestExamDeleteMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiRequestExamDeleteMutation,
    PostApiRequestExamDeleteMutationVariables
  >(PostApiRequestExamDeleteDocument, options);
}
export type PostApiRequestExamDeleteMutationHookResult = ReturnType<
  typeof usePostApiRequestExamDeleteMutation
>;
export type PostApiRequestExamDeleteMutationResult =
  Apollo.MutationResult<PostApiRequestExamDeleteMutation>;
export type PostApiRequestExamDeleteMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiRequestExamDeleteMutation,
    PostApiRequestExamDeleteMutationVariables
  >;
export const PostApiMainMenuImportKensaIraiDocument = gql`
  mutation postApiMainMenuImportKensaIrai($datFileBase64: String!) {
    postApiMainMenuImportKensaIrai(
      apiMainMenuImportKensaIraiInput: { datFileBase64: $datFileBase64 }
    ) {
      data {
        importKensaInfMessageModel {
          errorMessage
          successCount
          successed
        }
      }
      message
      status
    }
  }
`;
export type PostApiMainMenuImportKensaIraiMutationFn = Apollo.MutationFunction<
  PostApiMainMenuImportKensaIraiMutation,
  PostApiMainMenuImportKensaIraiMutationVariables
>;

/**
 * __usePostApiMainMenuImportKensaIraiMutation__
 *
 * To run a mutation, you first call `usePostApiMainMenuImportKensaIraiMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMainMenuImportKensaIraiMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMainMenuImportKensaIraiMutation, { data, loading, error }] = usePostApiMainMenuImportKensaIraiMutation({
 *   variables: {
 *      datFileBase64: // value for 'datFileBase64'
 *   },
 * });
 */
export function usePostApiMainMenuImportKensaIraiMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMainMenuImportKensaIraiMutation,
    PostApiMainMenuImportKensaIraiMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMainMenuImportKensaIraiMutation,
    PostApiMainMenuImportKensaIraiMutationVariables
  >(PostApiMainMenuImportKensaIraiDocument, options);
}
export type PostApiMainMenuImportKensaIraiMutationHookResult = ReturnType<
  typeof usePostApiMainMenuImportKensaIraiMutation
>;
export type PostApiMainMenuImportKensaIraiMutationResult =
  Apollo.MutationResult<PostApiMainMenuImportKensaIraiMutation>;
export type PostApiMainMenuImportKensaIraiMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMainMenuImportKensaIraiMutation,
    PostApiMainMenuImportKensaIraiMutationVariables
  >;
export const GetApiMainMenuGetKensaIraiLogDocument = gql`
  query getApiMainMenuGetKensaIraiLog($endDate: Int, $startDate: Int) {
    getApiMainMenuGetKensaIraiLog(endDate: $endDate, startDate: $startDate) {
      data {
        kensaIraiLogList {
          centerCd
          centerName
          createDate
          fromDate
          iraiDate
          iraiFile
          iraiList
          toDate
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiMainMenuGetKensaIraiLogQuery__
 *
 * To run a query within a React component, call `useGetApiMainMenuGetKensaIraiLogQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMainMenuGetKensaIraiLogQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMainMenuGetKensaIraiLogQuery({
 *   variables: {
 *      endDate: // value for 'endDate'
 *      startDate: // value for 'startDate'
 *   },
 * });
 */
export function useGetApiMainMenuGetKensaIraiLogQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMainMenuGetKensaIraiLogQuery,
    GetApiMainMenuGetKensaIraiLogQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMainMenuGetKensaIraiLogQuery,
    GetApiMainMenuGetKensaIraiLogQueryVariables
  >(GetApiMainMenuGetKensaIraiLogDocument, options);
}
export function useGetApiMainMenuGetKensaIraiLogLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMainMenuGetKensaIraiLogQuery,
    GetApiMainMenuGetKensaIraiLogQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMainMenuGetKensaIraiLogQuery,
    GetApiMainMenuGetKensaIraiLogQueryVariables
  >(GetApiMainMenuGetKensaIraiLogDocument, options);
}
export function useGetApiMainMenuGetKensaIraiLogSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMainMenuGetKensaIraiLogQuery,
    GetApiMainMenuGetKensaIraiLogQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMainMenuGetKensaIraiLogQuery,
    GetApiMainMenuGetKensaIraiLogQueryVariables
  >(GetApiMainMenuGetKensaIraiLogDocument, options);
}
export type GetApiMainMenuGetKensaIraiLogQueryHookResult = ReturnType<
  typeof useGetApiMainMenuGetKensaIraiLogQuery
>;
export type GetApiMainMenuGetKensaIraiLogLazyQueryHookResult = ReturnType<
  typeof useGetApiMainMenuGetKensaIraiLogLazyQuery
>;
export type GetApiMainMenuGetKensaIraiLogSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMainMenuGetKensaIraiLogSuspenseQuery
>;
export type GetApiMainMenuGetKensaIraiLogQueryResult = Apollo.QueryResult<
  GetApiMainMenuGetKensaIraiLogQuery,
  GetApiMainMenuGetKensaIraiLogQueryVariables
>;
export const PostApiMainMenuReCreateDataKensaIraiRenkeiDocument = gql`
  mutation postApiMainMenuReCreateDataKensaIraiRenkei(
    $kensaIraiList: [EmrCloudApiRequestsMainMenuRequestItemKensaIraiRequestItemInput]
    $systemDate: Int
  ) {
    postApiMainMenuReCreateDataKensaIraiRenkei(
      emrCloudApiRequestsMainMenuReCreateDataKensaIraiRenkeiRequestInput: {
        kensaIraiList: $kensaIraiList
        systemDate: $systemDate
      }
    ) {
      data {
        kensaIraiList {
          age
          birthday
          iraiCd
          kaId
          kanaName
          kensaIraiDetails {
            centerCd
            centerItemCd
            containerCd
            itemCd
            itemName
            kanaName1
            kensaItemCd
            kensaKana
            kensaName
            rowNo
            rpEdaNo
            rpNo
            seqNo
            tenKensaItemCd
          }
          name
          ptId
          ptNum
          raiinNo
          sex
          sikyuKbn
          sikyuStr
          sinDate
          tosekiKbn
          tosekiStr
          updateDate
        }
        successed
      }
      message
      status
    }
  }
`;
export type PostApiMainMenuReCreateDataKensaIraiRenkeiMutationFn =
  Apollo.MutationFunction<
    PostApiMainMenuReCreateDataKensaIraiRenkeiMutation,
    PostApiMainMenuReCreateDataKensaIraiRenkeiMutationVariables
  >;

/**
 * __usePostApiMainMenuReCreateDataKensaIraiRenkeiMutation__
 *
 * To run a mutation, you first call `usePostApiMainMenuReCreateDataKensaIraiRenkeiMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMainMenuReCreateDataKensaIraiRenkeiMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMainMenuReCreateDataKensaIraiRenkeiMutation, { data, loading, error }] = usePostApiMainMenuReCreateDataKensaIraiRenkeiMutation({
 *   variables: {
 *      kensaIraiList: // value for 'kensaIraiList'
 *      systemDate: // value for 'systemDate'
 *   },
 * });
 */
export function usePostApiMainMenuReCreateDataKensaIraiRenkeiMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMainMenuReCreateDataKensaIraiRenkeiMutation,
    PostApiMainMenuReCreateDataKensaIraiRenkeiMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMainMenuReCreateDataKensaIraiRenkeiMutation,
    PostApiMainMenuReCreateDataKensaIraiRenkeiMutationVariables
  >(PostApiMainMenuReCreateDataKensaIraiRenkeiDocument, options);
}
export type PostApiMainMenuReCreateDataKensaIraiRenkeiMutationHookResult =
  ReturnType<typeof usePostApiMainMenuReCreateDataKensaIraiRenkeiMutation>;
export type PostApiMainMenuReCreateDataKensaIraiRenkeiMutationResult =
  Apollo.MutationResult<PostApiMainMenuReCreateDataKensaIraiRenkeiMutation>;
export type PostApiMainMenuReCreateDataKensaIraiRenkeiMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMainMenuReCreateDataKensaIraiRenkeiMutation,
    PostApiMainMenuReCreateDataKensaIraiRenkeiMutationVariables
  >;
export const GetApiMainMenuDownloadKensaIraiLogDocument = gql`
  query getApiMainMenuDownloadKensaIraiLog(
    $centerCd: String
    $createDate: String
  ) {
    getApiMainMenuDownloadKensaIraiLog(
      centerCd: $centerCd
      createDate: $createDate
    ) {
      data {
        downloadKensaIraiLog {
          centerCd
          createDate
          iraiFile
          iraiList
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiMainMenuDownloadKensaIraiLogQuery__
 *
 * To run a query within a React component, call `useGetApiMainMenuDownloadKensaIraiLogQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMainMenuDownloadKensaIraiLogQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMainMenuDownloadKensaIraiLogQuery({
 *   variables: {
 *      centerCd: // value for 'centerCd'
 *      createDate: // value for 'createDate'
 *   },
 * });
 */
export function useGetApiMainMenuDownloadKensaIraiLogQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMainMenuDownloadKensaIraiLogQuery,
    GetApiMainMenuDownloadKensaIraiLogQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMainMenuDownloadKensaIraiLogQuery,
    GetApiMainMenuDownloadKensaIraiLogQueryVariables
  >(GetApiMainMenuDownloadKensaIraiLogDocument, options);
}
export function useGetApiMainMenuDownloadKensaIraiLogLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMainMenuDownloadKensaIraiLogQuery,
    GetApiMainMenuDownloadKensaIraiLogQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMainMenuDownloadKensaIraiLogQuery,
    GetApiMainMenuDownloadKensaIraiLogQueryVariables
  >(GetApiMainMenuDownloadKensaIraiLogDocument, options);
}
export function useGetApiMainMenuDownloadKensaIraiLogSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMainMenuDownloadKensaIraiLogQuery,
    GetApiMainMenuDownloadKensaIraiLogQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMainMenuDownloadKensaIraiLogQuery,
    GetApiMainMenuDownloadKensaIraiLogQueryVariables
  >(GetApiMainMenuDownloadKensaIraiLogDocument, options);
}
export type GetApiMainMenuDownloadKensaIraiLogQueryHookResult = ReturnType<
  typeof useGetApiMainMenuDownloadKensaIraiLogQuery
>;
export type GetApiMainMenuDownloadKensaIraiLogLazyQueryHookResult = ReturnType<
  typeof useGetApiMainMenuDownloadKensaIraiLogLazyQuery
>;
export type GetApiMainMenuDownloadKensaIraiLogSuspenseQueryHookResult =
  ReturnType<typeof useGetApiMainMenuDownloadKensaIraiLogSuspenseQuery>;
export type GetApiMainMenuDownloadKensaIraiLogQueryResult = Apollo.QueryResult<
  GetApiMainMenuDownloadKensaIraiLogQuery,
  GetApiMainMenuDownloadKensaIraiLogQueryVariables
>;
