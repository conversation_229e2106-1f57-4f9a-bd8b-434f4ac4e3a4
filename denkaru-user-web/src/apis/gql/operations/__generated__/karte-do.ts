import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiTodayOrdConvertFromHistoryToTodayOrderMutationVariables =
  Types.Exact<{
    emrCloudApiRequestsMedicalExaminationConvertFromHistoryToTodayOrderRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsMedicalExaminationConvertFromHistoryToTodayOrderRequestInput>;
  }>;

export type PostApiTodayOrdConvertFromHistoryToTodayOrderMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdConvertFromHistoryToTodayOrder?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationConvertFromHistoryToTodayOrderResponse";
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationConvertFromHistoryToTodayOrderResponse";
      odrInfItems?: Array<{
        __typename?: "UseCaseOrdInfsGetListTreesOdrInfItem";
        createDate?: string;
        createId?: number;
        createMachine?: string;
        createName?: string;
        daysCnt?: number;
        groupOdrKouiKbn?: number;
        hokenPid?: number;
        hpId?: number;
        id?: string;
        inoutKbn?: number;
        isDeleted?: number;
        odrKouiKbn?: number;
        ptId?: string;
        rpEdaNo?: string;
        raiinNo?: string;
        rpName?: string;
        rpNo?: string;
        santeiKbn?: number;
        sikyuKbn?: number;
        sinDate?: number;
        sortNo?: number;
        syohoSbt?: number;
        tosekiKbn?: number;
        updateDate?: string;
        updateMachine?: string;
        updateName?: string;
        odrDetails?: Array<{
          __typename?: "UseCaseOrdInfsGetListTreesOdrInfDetailItem";
          isSelectiveComment?: boolean;
          alternationIndex?: number;
          bikoComment?: number;
          buiKbn?: number;
          bunkatu?: string;
          bunkatuKoui?: number;
          centerCd?: string;
          centerItemCd1?: string;
          centerItemCd2?: string;
          centerName?: string;
          cmtCol1?: number;
          cmtCol2?: number;
          cmtCol3?: number;
          cmtCol4?: number;
          cmtColKeta1?: number;
          cmtColKeta2?: number;
          cmtColKeta3?: number;
          cmtColKeta4?: number;
          cmtName?: string;
          cmtOpt?: string;
          cnvTermVal?: number;
          cnvUnitName?: string;
          commentNewline?: number;
          displayItemName?: string;
          drugKbn?: number;
          fontColor?: string;
          handanGrpKbn?: number;
          hasCmtName?: boolean;
          hpId?: number;
          ipnCd?: string;
          ipnName?: string;
          isAdopted?: number;
          isKensaMstEmpty?: boolean;
          isGetPriceInYakka?: boolean;
          isNodspRece?: number;
          itemCd?: string;
          itemName?: string;
          jissiDate?: string;
          jissiId?: number;
          jissiKbn?: number;
          jissiMachine?: string;
          kasan1?: number;
          kasan2?: number;
          kensaGaichu?: number;
          kikakiUnit?: string;
          kohatuKbn?: number;
          kokuji1?: string;
          kokuji2?: string;
          masterSbt?: string;
          odrTermVal?: number;
          memoItem?: string;
          odrUnitName?: string;
          ptId?: string;
          raiinNo?: string;
          reqCd?: string;
          rikikaRate?: number;
          rikikaUnit?: string;
          rousaiKbn?: number;
          rowNo?: number;
          rpEdaNo?: string;
          rpNo?: string;
          senteiRyoyoKbn?: number;
          sinDate?: number;
          sinKouiKbn?: number;
          suryo?: number;
          syohoKbn?: number;
          syohoLimitKbn?: number;
          ten?: number;
          termVal?: number;
          unitName?: string;
          unitSbt?: number;
          yakka?: number;
          yakkaiUnit?: string;
          yjCd?: string;
          yohoKbn?: number;
          youkaiekiCd?: string;
        }>;
      }>;
    };
  };
};

export const PostApiTodayOrdConvertFromHistoryToTodayOrderDocument = gql`
  mutation postApiTodayOrdConvertFromHistoryToTodayOrder(
    $emrCloudApiRequestsMedicalExaminationConvertFromHistoryToTodayOrderRequestInput: EmrCloudApiRequestsMedicalExaminationConvertFromHistoryToTodayOrderRequestInput = {

    }
  ) {
    postApiTodayOrdConvertFromHistoryToTodayOrder(
      emrCloudApiRequestsMedicalExaminationConvertFromHistoryToTodayOrderRequestInput: $emrCloudApiRequestsMedicalExaminationConvertFromHistoryToTodayOrderRequestInput
    ) {
      status
      data {
        odrInfItems {
          createDate
          createId
          createMachine
          createName
          daysCnt
          groupOdrKouiKbn
          hokenPid
          hpId
          id
          inoutKbn
          isDeleted
          odrKouiKbn
          ptId
          rpEdaNo
          raiinNo
          rpName
          rpNo
          santeiKbn
          sikyuKbn
          sinDate
          sortNo
          syohoSbt
          tosekiKbn
          updateDate
          updateMachine
          updateName
          odrDetails {
            isSelectiveComment
            alternationIndex
            bikoComment
            buiKbn
            bunkatu
            bunkatuKoui
            centerCd
            centerItemCd1
            centerItemCd2
            centerName
            cmtCol1
            cmtCol2
            cmtCol3
            cmtCol4
            cmtColKeta1
            cmtColKeta2
            cmtColKeta3
            cmtColKeta4
            cmtName
            cmtOpt
            cnvTermVal
            cnvUnitName
            commentNewline
            displayItemName
            drugKbn
            fontColor
            handanGrpKbn
            hasCmtName
            hpId
            ipnCd
            ipnName
            isAdopted
            isKensaMstEmpty
            isGetPriceInYakka
            isNodspRece
            itemCd
            itemName
            jissiDate
            jissiId
            jissiKbn
            jissiMachine
            kasan1
            kasan2
            kensaGaichu
            kikakiUnit
            kohatuKbn
            kokuji1
            kokuji2
            masterSbt
            odrTermVal
            memoItem
            odrUnitName
            ptId
            raiinNo
            reqCd
            rikikaRate
            rikikaUnit
            rousaiKbn
            rowNo
            rpEdaNo
            rpNo
            senteiRyoyoKbn
            sinDate
            sinKouiKbn
            suryo
            syohoKbn
            syohoLimitKbn
            ten
            termVal
            unitName
            unitSbt
            yakka
            yakkaiUnit
            yjCd
            yohoKbn
            youkaiekiCd
          }
        }
      }
    }
  }
`;
export type PostApiTodayOrdConvertFromHistoryToTodayOrderMutationFn =
  Apollo.MutationFunction<
    PostApiTodayOrdConvertFromHistoryToTodayOrderMutation,
    PostApiTodayOrdConvertFromHistoryToTodayOrderMutationVariables
  >;

/**
 * __usePostApiTodayOrdConvertFromHistoryToTodayOrderMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdConvertFromHistoryToTodayOrderMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdConvertFromHistoryToTodayOrderMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdConvertFromHistoryToTodayOrderMutation, { data, loading, error }] = usePostApiTodayOrdConvertFromHistoryToTodayOrderMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationConvertFromHistoryToTodayOrderRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationConvertFromHistoryToTodayOrderRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdConvertFromHistoryToTodayOrderMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdConvertFromHistoryToTodayOrderMutation,
    PostApiTodayOrdConvertFromHistoryToTodayOrderMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdConvertFromHistoryToTodayOrderMutation,
    PostApiTodayOrdConvertFromHistoryToTodayOrderMutationVariables
  >(PostApiTodayOrdConvertFromHistoryToTodayOrderDocument, options);
}
export type PostApiTodayOrdConvertFromHistoryToTodayOrderMutationHookResult =
  ReturnType<typeof usePostApiTodayOrdConvertFromHistoryToTodayOrderMutation>;
export type PostApiTodayOrdConvertFromHistoryToTodayOrderMutationResult =
  Apollo.MutationResult<PostApiTodayOrdConvertFromHistoryToTodayOrderMutation>;
export type PostApiTodayOrdConvertFromHistoryToTodayOrderMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTodayOrdConvertFromHistoryToTodayOrderMutation,
    PostApiTodayOrdConvertFromHistoryToTodayOrderMutationVariables
  >;
