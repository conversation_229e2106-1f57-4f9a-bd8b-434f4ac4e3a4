import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiAccountingGetAccountingFormMstResponseQueryVariables =
  Types.Exact<{ [key: string]: never }>;

export type GetApiAccountingGetAccountingFormMstResponseQuery = {
  __typename?: "query_root";
  getApiAccountingGetAccountingFormMstResponse?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingGetAccountingFormMstResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingGetAccountingFormMstResponse";
      accountingFormMstModels?: Array<{
        __typename?: "DomainModelsAccountingAccountingFormMstModel";
        hpId?: number;
        formNo?: number;
        formName?: string;
        formType?: number;
        printSort?: number;
        miseisanKbn?: number;
        saiKbn?: number;
        misyuKbn?: number;
        seikyuKbn?: number;
        hokenKbn?: number;
        form?: string;
        base?: number;
        sortNo?: number;
        isDeleted?: number;
        createId?: number;
        createDate?: string;
        updateId?: number;
        updateDate?: string;
        modelModified?: boolean;
      }>;
    };
  };
};

export type PostApiAccountingUpdateAccountingFormMstMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsAccountingUpdateAccountingFormMstRequestInput>;
  }>;

export type PostApiAccountingUpdateAccountingFormMstMutation = {
  __typename?: "mutation_root";
  postApiAccountingUpdateAccountingFormMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesAccountingUpdateAccountingFormMstResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesAccountingUpdateAccountingFormMstResponse";
      success?: boolean;
    };
  };
};

export const GetApiAccountingGetAccountingFormMstResponseDocument = gql`
  query getApiAccountingGetAccountingFormMstResponse {
    getApiAccountingGetAccountingFormMstResponse {
      data {
        accountingFormMstModels {
          hpId
          formNo
          formName
          formType
          printSort
          miseisanKbn
          saiKbn
          misyuKbn
          seikyuKbn
          hokenKbn
          form
          base
          sortNo
          isDeleted
          createId
          createDate
          updateId
          updateDate
          modelModified
        }
      }
    }
  }
`;

/**
 * __useGetApiAccountingGetAccountingFormMstResponseQuery__
 *
 * To run a query within a React component, call `useGetApiAccountingGetAccountingFormMstResponseQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiAccountingGetAccountingFormMstResponseQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiAccountingGetAccountingFormMstResponseQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiAccountingGetAccountingFormMstResponseQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiAccountingGetAccountingFormMstResponseQuery,
    GetApiAccountingGetAccountingFormMstResponseQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiAccountingGetAccountingFormMstResponseQuery,
    GetApiAccountingGetAccountingFormMstResponseQueryVariables
  >(GetApiAccountingGetAccountingFormMstResponseDocument, options);
}
export function useGetApiAccountingGetAccountingFormMstResponseLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiAccountingGetAccountingFormMstResponseQuery,
    GetApiAccountingGetAccountingFormMstResponseQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiAccountingGetAccountingFormMstResponseQuery,
    GetApiAccountingGetAccountingFormMstResponseQueryVariables
  >(GetApiAccountingGetAccountingFormMstResponseDocument, options);
}
export function useGetApiAccountingGetAccountingFormMstResponseSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiAccountingGetAccountingFormMstResponseQuery,
    GetApiAccountingGetAccountingFormMstResponseQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiAccountingGetAccountingFormMstResponseQuery,
    GetApiAccountingGetAccountingFormMstResponseQueryVariables
  >(GetApiAccountingGetAccountingFormMstResponseDocument, options);
}
export type GetApiAccountingGetAccountingFormMstResponseQueryHookResult =
  ReturnType<typeof useGetApiAccountingGetAccountingFormMstResponseQuery>;
export type GetApiAccountingGetAccountingFormMstResponseLazyQueryHookResult =
  ReturnType<typeof useGetApiAccountingGetAccountingFormMstResponseLazyQuery>;
export type GetApiAccountingGetAccountingFormMstResponseSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiAccountingGetAccountingFormMstResponseSuspenseQuery
  >;
export type GetApiAccountingGetAccountingFormMstResponseQueryResult =
  Apollo.QueryResult<
    GetApiAccountingGetAccountingFormMstResponseQuery,
    GetApiAccountingGetAccountingFormMstResponseQueryVariables
  >;
export const PostApiAccountingUpdateAccountingFormMstDocument = gql`
  mutation postApiAccountingUpdateAccountingFormMst(
    $input: EmrCloudApiRequestsAccountingUpdateAccountingFormMstRequestInput
  ) {
    postApiAccountingUpdateAccountingFormMst(
      emrCloudApiRequestsAccountingUpdateAccountingFormMstRequestInput: $input
    ) {
      data {
        success
      }
      message
      status
    }
  }
`;
export type PostApiAccountingUpdateAccountingFormMstMutationFn =
  Apollo.MutationFunction<
    PostApiAccountingUpdateAccountingFormMstMutation,
    PostApiAccountingUpdateAccountingFormMstMutationVariables
  >;

/**
 * __usePostApiAccountingUpdateAccountingFormMstMutation__
 *
 * To run a mutation, you first call `usePostApiAccountingUpdateAccountingFormMstMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiAccountingUpdateAccountingFormMstMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiAccountingUpdateAccountingFormMstMutation, { data, loading, error }] = usePostApiAccountingUpdateAccountingFormMstMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiAccountingUpdateAccountingFormMstMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiAccountingUpdateAccountingFormMstMutation,
    PostApiAccountingUpdateAccountingFormMstMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiAccountingUpdateAccountingFormMstMutation,
    PostApiAccountingUpdateAccountingFormMstMutationVariables
  >(PostApiAccountingUpdateAccountingFormMstDocument, options);
}
export type PostApiAccountingUpdateAccountingFormMstMutationHookResult =
  ReturnType<typeof usePostApiAccountingUpdateAccountingFormMstMutation>;
export type PostApiAccountingUpdateAccountingFormMstMutationResult =
  Apollo.MutationResult<PostApiAccountingUpdateAccountingFormMstMutation>;
export type PostApiAccountingUpdateAccountingFormMstMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiAccountingUpdateAccountingFormMstMutation,
    PostApiAccountingUpdateAccountingFormMstMutationVariables
  >;
