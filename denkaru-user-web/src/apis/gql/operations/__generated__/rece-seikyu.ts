import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiReceSeikyuGetListReceSeikyuQueryVariables = Types.Exact<{
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isIncludingUnConfirmed?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  ptNumSearch?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  noFilter?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isFilterMonthlyDelay?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isFilterReturn?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isFilterOnlineReturn?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isGetDataPending?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiReceSeikyuGetListReceSeikyuQuery = {
  __typename?: "query_root";
  getApiReceSeikyuGetListReceSeikyu?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceSeikyuGetListReceSeikyuResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceSeikyuGetListReceSeikyuResponse";
      datas?: Array<{
        __typename?: "DomainModelsReceSeikyuReceSeikyuModel";
        sinDay?: number;
        hpId?: number;
        ptId?: string;
        ptName?: string;
        receListSinYm?: number;
        sinYm?: number;
        hokenId?: number;
        hokensyaNo?: string;
        seqNo?: number;
        seikyuYm?: number;
        seikyuKbn?: number;
        preHokenId?: number;
        cmt?: string;
        isChecked?: boolean;
        isEnableCheckBox?: boolean;
        isCompletedSeikyu?: boolean;
        isNotCompletedSeikyu?: boolean;
        seikyuYmDisplay?: string;
        ptNum?: string;
        henHokenName?: string;
        henreiJiyuu?: string;
        hokenKbn?: number;
        houbetu?: string;
        hokenSentaku?: string;
        hokenStartDate?: number;
        hokenEndDate?: number;
        isModified?: boolean;
        originSeikyuYm?: number;
        originSinYm?: number;
        isAddNew?: boolean;
        isDeleted?: number;
        isDefaultValue?: boolean;
        seikyuKbnDisplay?: string;
        listRecedenHenJiyuuModel?: Array<{
          __typename?: "DomainModelsReceSeikyuRecedenHenJiyuuModel";
          hpId?: number;
          ptId?: string;
          hokenId?: number;
          sinYm?: number;
          seqNo?: number;
          henreiJiyuuCd?: string;
          henreiJiyuu?: string;
          hosoku?: string;
          isDeleted?: number;
          hokenKbn?: number;
          houbetu?: string;
          hokenSentaku?: string;
          hokenStartDate?: number;
          hokenEndDate?: number;
          hokensyaNo?: string;
        }>;
      }>;
    };
  };
};

export type PostApiReceSeikyuSaveReceSeikyuMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsReceSeikyuSaveReceSeiKyuRequestInput;
}>;

export type PostApiReceSeikyuSaveReceSeikyuMutation = {
  __typename?: "mutation_root";
  postApiReceSeikyuSaveReceSeikyu: string;
};

export type PostApiReceSeikyuCancelSeikyuMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsReceSeikyuCancelSeikyuRequestInput;
}>;

export type PostApiReceSeikyuCancelSeikyuMutation = {
  __typename?: "mutation_root";
  postApiReceSeikyuCancelSeikyu?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceSeikyuCancelSeikyuResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceSeikyuCancelSeikyuResponse";
      success?: boolean;
    };
  };
};

export type GetApiReceSeikyuGetReceSeikyModelByPtNumQueryVariables =
  Types.Exact<{
    ptNum?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    sinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiReceSeikyuGetReceSeikyModelByPtNumQuery = {
  __typename?: "query_root";
  getApiReceSeikyuGetReceSeikyModelByPtNum?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceSeikyuGetReceSeikyModelByPtNumResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceSeikyuGetReceSeikyModelByPtNumResponse";
      receSeikyuModel?: {
        __typename?: "DomainModelsReceSeikyuReceSeikyuModel";
        sinDay?: number;
        ptId?: string;
        ptName?: string;
        receListSinYm?: number;
        sinYm?: number;
        hokenId?: number;
        hokensyaNo?: string;
        seqNo?: number;
        seikyuYm?: number;
        seikyuKbn?: number;
        preHokenId?: number;
        cmt?: string;
        isChecked?: boolean;
        isEnableCheckBox?: boolean;
        isCompletedSeikyu?: boolean;
        isNotCompletedSeikyu?: boolean;
        seikyuYmDisplay?: string;
        ptNum?: string;
        henHokenName?: string;
        henreiJiyuu?: string;
        hokenKbn?: number;
        houbetu?: string;
        hokenSentaku?: string;
        hokenStartDate?: number;
        hokenEndDate?: number;
        isModified?: boolean;
        originSeikyuYm?: number;
        originSinYm?: number;
        isAddNew?: boolean;
        isDeleted?: number;
        isDefaultValue?: boolean;
        seikyuKbnDisplay?: string;
        listRecedenHenJiyuuModel?: Array<{
          __typename?: "DomainModelsReceSeikyuRecedenHenJiyuuModel";
          ptId?: string;
          hokenId?: number;
          sinYm?: number;
          seqNo?: number;
          henreiJiyuuCd?: string;
          henreiJiyuu?: string;
          hosoku?: string;
          isDeleted?: number;
          hokenKbn?: number;
          houbetu?: string;
          hokenSentaku?: string;
          hokenStartDate?: number;
          hokenEndDate?: number;
          hokensyaNo?: string;
        }>;
      };
    };
  };
};

export const GetApiReceSeikyuGetListReceSeikyuDocument = gql`
  query getApiReceSeikyuGetListReceSeikyu(
    $sinDate: Int
    $sinYm: Int
    $isIncludingUnConfirmed: Boolean
    $ptNumSearch: BigInt
    $noFilter: Boolean
    $isFilterMonthlyDelay: Boolean
    $isFilterReturn: Boolean
    $isFilterOnlineReturn: Boolean
    $isGetDataPending: Boolean
  ) {
    getApiReceSeikyuGetListReceSeikyu(
      sinDate: $sinDate
      sinYm: $sinYm
      isIncludingUnConfirmed: $isIncludingUnConfirmed
      ptNumSearch: $ptNumSearch
      noFilter: $noFilter
      isFilterMonthlyDelay: $isFilterMonthlyDelay
      isFilterReturn: $isFilterReturn
      isFilterOnlineReturn: $isFilterOnlineReturn
      isGetDataPending: $isGetDataPending
    ) {
      data {
        datas {
          sinDay
          hpId
          ptId
          ptName
          receListSinYm
          sinYm
          hokenId
          hokensyaNo
          seqNo
          seikyuYm
          seikyuKbn
          preHokenId
          cmt
          isChecked
          isEnableCheckBox
          isCompletedSeikyu
          isNotCompletedSeikyu
          seikyuYmDisplay
          ptNum
          henHokenName
          henreiJiyuu
          hokenKbn
          houbetu
          hokenSentaku
          hokenStartDate
          hokenEndDate
          isModified
          originSeikyuYm
          originSinYm
          isAddNew
          isDeleted
          listRecedenHenJiyuuModel {
            hpId
            ptId
            hokenId
            sinYm
            seqNo
            henreiJiyuuCd
            henreiJiyuu
            hosoku
            isDeleted
            hokenKbn
            houbetu
            hokenSentaku
            hokenStartDate
            hokenEndDate
            hokensyaNo
          }
          isDefaultValue
          seikyuKbnDisplay
        }
      }
    }
  }
`;

/**
 * __useGetApiReceSeikyuGetListReceSeikyuQuery__
 *
 * To run a query within a React component, call `useGetApiReceSeikyuGetListReceSeikyuQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceSeikyuGetListReceSeikyuQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceSeikyuGetListReceSeikyuQuery({
 *   variables: {
 *      sinDate: // value for 'sinDate'
 *      sinYm: // value for 'sinYm'
 *      isIncludingUnConfirmed: // value for 'isIncludingUnConfirmed'
 *      ptNumSearch: // value for 'ptNumSearch'
 *      noFilter: // value for 'noFilter'
 *      isFilterMonthlyDelay: // value for 'isFilterMonthlyDelay'
 *      isFilterReturn: // value for 'isFilterReturn'
 *      isFilterOnlineReturn: // value for 'isFilterOnlineReturn'
 *      isGetDataPending: // value for 'isGetDataPending'
 *   },
 * });
 */
export function useGetApiReceSeikyuGetListReceSeikyuQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceSeikyuGetListReceSeikyuQuery,
    GetApiReceSeikyuGetListReceSeikyuQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceSeikyuGetListReceSeikyuQuery,
    GetApiReceSeikyuGetListReceSeikyuQueryVariables
  >(GetApiReceSeikyuGetListReceSeikyuDocument, options);
}
export function useGetApiReceSeikyuGetListReceSeikyuLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceSeikyuGetListReceSeikyuQuery,
    GetApiReceSeikyuGetListReceSeikyuQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceSeikyuGetListReceSeikyuQuery,
    GetApiReceSeikyuGetListReceSeikyuQueryVariables
  >(GetApiReceSeikyuGetListReceSeikyuDocument, options);
}
export function useGetApiReceSeikyuGetListReceSeikyuSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceSeikyuGetListReceSeikyuQuery,
    GetApiReceSeikyuGetListReceSeikyuQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceSeikyuGetListReceSeikyuQuery,
    GetApiReceSeikyuGetListReceSeikyuQueryVariables
  >(GetApiReceSeikyuGetListReceSeikyuDocument, options);
}
export type GetApiReceSeikyuGetListReceSeikyuQueryHookResult = ReturnType<
  typeof useGetApiReceSeikyuGetListReceSeikyuQuery
>;
export type GetApiReceSeikyuGetListReceSeikyuLazyQueryHookResult = ReturnType<
  typeof useGetApiReceSeikyuGetListReceSeikyuLazyQuery
>;
export type GetApiReceSeikyuGetListReceSeikyuSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceSeikyuGetListReceSeikyuSuspenseQuery>;
export type GetApiReceSeikyuGetListReceSeikyuQueryResult = Apollo.QueryResult<
  GetApiReceSeikyuGetListReceSeikyuQuery,
  GetApiReceSeikyuGetListReceSeikyuQueryVariables
>;
export const PostApiReceSeikyuSaveReceSeikyuDocument = gql`
  mutation postApiReceSeikyuSaveReceSeikyu(
    $input: EmrCloudApiRequestsReceSeikyuSaveReceSeiKyuRequestInput!
  ) {
    postApiReceSeikyuSaveReceSeikyu(
      emrCloudApiRequestsReceSeikyuSaveReceSeiKyuRequestInput: $input
    )
  }
`;
export type PostApiReceSeikyuSaveReceSeikyuMutationFn = Apollo.MutationFunction<
  PostApiReceSeikyuSaveReceSeikyuMutation,
  PostApiReceSeikyuSaveReceSeikyuMutationVariables
>;

/**
 * __usePostApiReceSeikyuSaveReceSeikyuMutation__
 *
 * To run a mutation, you first call `usePostApiReceSeikyuSaveReceSeikyuMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceSeikyuSaveReceSeikyuMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceSeikyuSaveReceSeikyuMutation, { data, loading, error }] = usePostApiReceSeikyuSaveReceSeikyuMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceSeikyuSaveReceSeikyuMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceSeikyuSaveReceSeikyuMutation,
    PostApiReceSeikyuSaveReceSeikyuMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceSeikyuSaveReceSeikyuMutation,
    PostApiReceSeikyuSaveReceSeikyuMutationVariables
  >(PostApiReceSeikyuSaveReceSeikyuDocument, options);
}
export type PostApiReceSeikyuSaveReceSeikyuMutationHookResult = ReturnType<
  typeof usePostApiReceSeikyuSaveReceSeikyuMutation
>;
export type PostApiReceSeikyuSaveReceSeikyuMutationResult =
  Apollo.MutationResult<PostApiReceSeikyuSaveReceSeikyuMutation>;
export type PostApiReceSeikyuSaveReceSeikyuMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceSeikyuSaveReceSeikyuMutation,
    PostApiReceSeikyuSaveReceSeikyuMutationVariables
  >;
export const PostApiReceSeikyuCancelSeikyuDocument = gql`
  mutation postApiReceSeikyuCancelSeikyu(
    $input: EmrCloudApiRequestsReceSeikyuCancelSeikyuRequestInput!
  ) {
    postApiReceSeikyuCancelSeikyu(
      emrCloudApiRequestsReceSeikyuCancelSeikyuRequestInput: $input
    ) {
      data {
        success
      }
    }
  }
`;
export type PostApiReceSeikyuCancelSeikyuMutationFn = Apollo.MutationFunction<
  PostApiReceSeikyuCancelSeikyuMutation,
  PostApiReceSeikyuCancelSeikyuMutationVariables
>;

/**
 * __usePostApiReceSeikyuCancelSeikyuMutation__
 *
 * To run a mutation, you first call `usePostApiReceSeikyuCancelSeikyuMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiReceSeikyuCancelSeikyuMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiReceSeikyuCancelSeikyuMutation, { data, loading, error }] = usePostApiReceSeikyuCancelSeikyuMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiReceSeikyuCancelSeikyuMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiReceSeikyuCancelSeikyuMutation,
    PostApiReceSeikyuCancelSeikyuMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiReceSeikyuCancelSeikyuMutation,
    PostApiReceSeikyuCancelSeikyuMutationVariables
  >(PostApiReceSeikyuCancelSeikyuDocument, options);
}
export type PostApiReceSeikyuCancelSeikyuMutationHookResult = ReturnType<
  typeof usePostApiReceSeikyuCancelSeikyuMutation
>;
export type PostApiReceSeikyuCancelSeikyuMutationResult =
  Apollo.MutationResult<PostApiReceSeikyuCancelSeikyuMutation>;
export type PostApiReceSeikyuCancelSeikyuMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiReceSeikyuCancelSeikyuMutation,
    PostApiReceSeikyuCancelSeikyuMutationVariables
  >;
export const GetApiReceSeikyuGetReceSeikyModelByPtNumDocument = gql`
  query getApiReceSeikyuGetReceSeikyModelByPtNum(
    $ptNum: BigInt
    $sinDate: Int
    $sinYm: Int
  ) {
    getApiReceSeikyuGetReceSeikyModelByPtNum(
      ptNum: $ptNum
      sinDate: $sinDate
      sinYm: $sinYm
    ) {
      data {
        receSeikyuModel {
          sinDay
          ptId
          ptName
          receListSinYm
          sinYm
          hokenId
          hokensyaNo
          seqNo
          seikyuYm
          seikyuKbn
          preHokenId
          cmt
          isChecked
          isEnableCheckBox
          isCompletedSeikyu
          isNotCompletedSeikyu
          seikyuYmDisplay
          ptNum
          henHokenName
          henreiJiyuu
          hokenKbn
          houbetu
          hokenSentaku
          hokenStartDate
          hokenEndDate
          isModified
          originSeikyuYm
          originSinYm
          isAddNew
          isDeleted
          listRecedenHenJiyuuModel {
            ptId
            hokenId
            sinYm
            seqNo
            henreiJiyuuCd
            henreiJiyuu
            hosoku
            isDeleted
            hokenKbn
            houbetu
            hokenSentaku
            hokenStartDate
            hokenEndDate
            hokensyaNo
          }
          isDefaultValue
          seikyuKbnDisplay
        }
      }
    }
  }
`;

/**
 * __useGetApiReceSeikyuGetReceSeikyModelByPtNumQuery__
 *
 * To run a query within a React component, call `useGetApiReceSeikyuGetReceSeikyModelByPtNumQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceSeikyuGetReceSeikyModelByPtNumQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceSeikyuGetReceSeikyModelByPtNumQuery({
 *   variables: {
 *      ptNum: // value for 'ptNum'
 *      sinDate: // value for 'sinDate'
 *      sinYm: // value for 'sinYm'
 *   },
 * });
 */
export function useGetApiReceSeikyuGetReceSeikyModelByPtNumQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceSeikyuGetReceSeikyModelByPtNumQuery,
    GetApiReceSeikyuGetReceSeikyModelByPtNumQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceSeikyuGetReceSeikyModelByPtNumQuery,
    GetApiReceSeikyuGetReceSeikyModelByPtNumQueryVariables
  >(GetApiReceSeikyuGetReceSeikyModelByPtNumDocument, options);
}
export function useGetApiReceSeikyuGetReceSeikyModelByPtNumLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceSeikyuGetReceSeikyModelByPtNumQuery,
    GetApiReceSeikyuGetReceSeikyModelByPtNumQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceSeikyuGetReceSeikyModelByPtNumQuery,
    GetApiReceSeikyuGetReceSeikyModelByPtNumQueryVariables
  >(GetApiReceSeikyuGetReceSeikyModelByPtNumDocument, options);
}
export function useGetApiReceSeikyuGetReceSeikyModelByPtNumSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceSeikyuGetReceSeikyModelByPtNumQuery,
    GetApiReceSeikyuGetReceSeikyModelByPtNumQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceSeikyuGetReceSeikyModelByPtNumQuery,
    GetApiReceSeikyuGetReceSeikyModelByPtNumQueryVariables
  >(GetApiReceSeikyuGetReceSeikyModelByPtNumDocument, options);
}
export type GetApiReceSeikyuGetReceSeikyModelByPtNumQueryHookResult =
  ReturnType<typeof useGetApiReceSeikyuGetReceSeikyModelByPtNumQuery>;
export type GetApiReceSeikyuGetReceSeikyModelByPtNumLazyQueryHookResult =
  ReturnType<typeof useGetApiReceSeikyuGetReceSeikyModelByPtNumLazyQuery>;
export type GetApiReceSeikyuGetReceSeikyModelByPtNumSuspenseQueryHookResult =
  ReturnType<typeof useGetApiReceSeikyuGetReceSeikyModelByPtNumSuspenseQuery>;
export type GetApiReceSeikyuGetReceSeikyModelByPtNumQueryResult =
  Apollo.QueryResult<
    GetApiReceSeikyuGetReceSeikyModelByPtNumQuery,
    GetApiReceSeikyuGetReceSeikyModelByPtNumQueryVariables
  >;
