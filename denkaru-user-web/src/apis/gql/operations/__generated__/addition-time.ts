import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiTimeZoneConfGetTimeZoneConfGroupQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetApiTimeZoneConfGetTimeZoneConfGroupQuery = {
  __typename?: "query_root";
  getApiTimeZoneConfGetTimeZoneConfGroup?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesTimeZoneConfGetTimeZoneConfGroupResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesTimeZoneConfGetTimeZoneConfGroupResponse";
      isHavePermission?: boolean;
      datas?: Array<{
        __typename?: "DomainModelsTimeZoneTimeZoneConfGroupModel";
        youbiKbn?: number;
        details?: Array<{
          __typename?: "DomainModelsTimeZoneTimeZoneConfModel";
          endTimeBinding?: string;
          startTimeBinding?: string;
          timeKbn?: number;
          youbiKbn?: number;
          modelModified?: boolean;
          seqNo?: string;
          sortNo?: number;
        }>;
      }>;
    };
  };
};

export type PostApiTimeZoneConfSaveTimeZoneConfMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsTimeZoneConfSaveTimeZoneConfRequestInput;
}>;

export type PostApiTimeZoneConfSaveTimeZoneConfMutation = {
  __typename?: "mutation_root";
  postApiTimeZoneConfSaveTimeZoneConf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesTimeZoneConfSaveTimeZoneConfResponse";
    message?: string;
    status?: number;
  };
};

export const GetApiTimeZoneConfGetTimeZoneConfGroupDocument = gql`
  query getApiTimeZoneConfGetTimeZoneConfGroup {
    getApiTimeZoneConfGetTimeZoneConfGroup {
      data {
        datas {
          youbiKbn
          details {
            endTimeBinding
            startTimeBinding
            timeKbn
            youbiKbn
            modelModified
            seqNo
            sortNo
          }
        }
        isHavePermission
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiTimeZoneConfGetTimeZoneConfGroupQuery__
 *
 * To run a query within a React component, call `useGetApiTimeZoneConfGetTimeZoneConfGroupQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiTimeZoneConfGetTimeZoneConfGroupQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiTimeZoneConfGetTimeZoneConfGroupQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetApiTimeZoneConfGetTimeZoneConfGroupQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiTimeZoneConfGetTimeZoneConfGroupQuery,
    GetApiTimeZoneConfGetTimeZoneConfGroupQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiTimeZoneConfGetTimeZoneConfGroupQuery,
    GetApiTimeZoneConfGetTimeZoneConfGroupQueryVariables
  >(GetApiTimeZoneConfGetTimeZoneConfGroupDocument, options);
}
export function useGetApiTimeZoneConfGetTimeZoneConfGroupLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiTimeZoneConfGetTimeZoneConfGroupQuery,
    GetApiTimeZoneConfGetTimeZoneConfGroupQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiTimeZoneConfGetTimeZoneConfGroupQuery,
    GetApiTimeZoneConfGetTimeZoneConfGroupQueryVariables
  >(GetApiTimeZoneConfGetTimeZoneConfGroupDocument, options);
}
export function useGetApiTimeZoneConfGetTimeZoneConfGroupSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiTimeZoneConfGetTimeZoneConfGroupQuery,
    GetApiTimeZoneConfGetTimeZoneConfGroupQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiTimeZoneConfGetTimeZoneConfGroupQuery,
    GetApiTimeZoneConfGetTimeZoneConfGroupQueryVariables
  >(GetApiTimeZoneConfGetTimeZoneConfGroupDocument, options);
}
export type GetApiTimeZoneConfGetTimeZoneConfGroupQueryHookResult = ReturnType<
  typeof useGetApiTimeZoneConfGetTimeZoneConfGroupQuery
>;
export type GetApiTimeZoneConfGetTimeZoneConfGroupLazyQueryHookResult =
  ReturnType<typeof useGetApiTimeZoneConfGetTimeZoneConfGroupLazyQuery>;
export type GetApiTimeZoneConfGetTimeZoneConfGroupSuspenseQueryHookResult =
  ReturnType<typeof useGetApiTimeZoneConfGetTimeZoneConfGroupSuspenseQuery>;
export type GetApiTimeZoneConfGetTimeZoneConfGroupQueryResult =
  Apollo.QueryResult<
    GetApiTimeZoneConfGetTimeZoneConfGroupQuery,
    GetApiTimeZoneConfGetTimeZoneConfGroupQueryVariables
  >;
export const PostApiTimeZoneConfSaveTimeZoneConfDocument = gql`
  mutation postApiTimeZoneConfSaveTimeZoneConf(
    $input: EmrCloudApiRequestsTimeZoneConfSaveTimeZoneConfRequestInput!
  ) {
    postApiTimeZoneConfSaveTimeZoneConf(
      emrCloudApiRequestsTimeZoneConfSaveTimeZoneConfRequestInput: $input
    ) {
      message
      status
    }
  }
`;
export type PostApiTimeZoneConfSaveTimeZoneConfMutationFn =
  Apollo.MutationFunction<
    PostApiTimeZoneConfSaveTimeZoneConfMutation,
    PostApiTimeZoneConfSaveTimeZoneConfMutationVariables
  >;

/**
 * __usePostApiTimeZoneConfSaveTimeZoneConfMutation__
 *
 * To run a mutation, you first call `usePostApiTimeZoneConfSaveTimeZoneConfMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTimeZoneConfSaveTimeZoneConfMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTimeZoneConfSaveTimeZoneConfMutation, { data, loading, error }] = usePostApiTimeZoneConfSaveTimeZoneConfMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiTimeZoneConfSaveTimeZoneConfMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTimeZoneConfSaveTimeZoneConfMutation,
    PostApiTimeZoneConfSaveTimeZoneConfMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTimeZoneConfSaveTimeZoneConfMutation,
    PostApiTimeZoneConfSaveTimeZoneConfMutationVariables
  >(PostApiTimeZoneConfSaveTimeZoneConfDocument, options);
}
export type PostApiTimeZoneConfSaveTimeZoneConfMutationHookResult = ReturnType<
  typeof usePostApiTimeZoneConfSaveTimeZoneConfMutation
>;
export type PostApiTimeZoneConfSaveTimeZoneConfMutationResult =
  Apollo.MutationResult<PostApiTimeZoneConfSaveTimeZoneConfMutation>;
export type PostApiTimeZoneConfSaveTimeZoneConfMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTimeZoneConfSaveTimeZoneConfMutation,
    PostApiTimeZoneConfSaveTimeZoneConfMutationVariables
  >;
