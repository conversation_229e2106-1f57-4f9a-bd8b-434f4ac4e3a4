import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type SaveKarteAllergyOtherMutationVariables = Types.Exact<{
  cmt?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  alrgyName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  endDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isDeleted?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  seqNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sortNo?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  startDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type SaveKarteAllergyOtherMutation = {
  __typename?: "mutation_root";
  postApiKarteAllergySaveKarteAllergyOthers?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteAllergySaveKarteAllergyOthersResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteAllergySaveKarteAllergyOthersResponse";
      status?: boolean;
    };
  };
};

export const SaveKarteAllergyOtherDocument = gql`
  mutation saveKarteAllergyOther(
    $cmt: String
    $alrgyName: String
    $endDate: Int
    $isDeleted: Int
    $ptId: BigInt
    $seqNo: Int
    $sortNo: Int
    $startDate: Int
  ) {
    postApiKarteAllergySaveKarteAllergyOthers(
      emrCloudApiRequestsKarteAllergySaveKarteAllergyOtherRequestInput: {
        alrgyElseItems: {
          cmt: $cmt
          alrgyName: $alrgyName
          endDate: $endDate
          isDeleted: $isDeleted
          ptId: $ptId
          startDate: $startDate
          sortNo: $sortNo
          seqNo: $seqNo
        }
      }
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type SaveKarteAllergyOtherMutationFn = Apollo.MutationFunction<
  SaveKarteAllergyOtherMutation,
  SaveKarteAllergyOtherMutationVariables
>;

/**
 * __useSaveKarteAllergyOtherMutation__
 *
 * To run a mutation, you first call `useSaveKarteAllergyOtherMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveKarteAllergyOtherMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveKarteAllergyOtherMutation, { data, loading, error }] = useSaveKarteAllergyOtherMutation({
 *   variables: {
 *      cmt: // value for 'cmt'
 *      alrgyName: // value for 'alrgyName'
 *      endDate: // value for 'endDate'
 *      isDeleted: // value for 'isDeleted'
 *      ptId: // value for 'ptId'
 *      seqNo: // value for 'seqNo'
 *      sortNo: // value for 'sortNo'
 *      startDate: // value for 'startDate'
 *   },
 * });
 */
export function useSaveKarteAllergyOtherMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SaveKarteAllergyOtherMutation,
    SaveKarteAllergyOtherMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SaveKarteAllergyOtherMutation,
    SaveKarteAllergyOtherMutationVariables
  >(SaveKarteAllergyOtherDocument, options);
}
export type SaveKarteAllergyOtherMutationHookResult = ReturnType<
  typeof useSaveKarteAllergyOtherMutation
>;
export type SaveKarteAllergyOtherMutationResult =
  Apollo.MutationResult<SaveKarteAllergyOtherMutation>;
export type SaveKarteAllergyOtherMutationOptions = Apollo.BaseMutationOptions<
  SaveKarteAllergyOtherMutation,
  SaveKarteAllergyOtherMutationVariables
>;
