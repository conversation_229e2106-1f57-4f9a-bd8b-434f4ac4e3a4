import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiOnlineGetBatchOnlineCheckQueryVariables = Types.Exact<{
  batchConfirmationType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiOnlineGetBatchOnlineCheckQuery = {
  __typename?: "query_root";
  getApiOnlineGetBatchOnlineCheck?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineGetBatchOnlineCheckResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineGetBatchOnlineCheckResponse";
      batchOnlineCheckModels?: Array<{
        __typename?: "DomainModelsOnlineBatchOnlineCheckModel";
        batchConfirmationType?: number;
        confirmationType?: number;
        consentFrom?: number;
        consentTo?: number;
        errorMessage?: string;
        examinationFrom?: number;
        examinationTo?: number;
        processTime?: string;
        receptionDateTime?: string;
        receptionNo?: string;
        segmentOfResult?: string;
        sinYm?: number;
        yoyakuDate?: number;
      }>;
    };
  };
};

export type GetApiOnlineGetPatientHomeVisitQueryVariables = Types.Exact<{
  ptNum?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiOnlineGetPatientHomeVisitQuery = {
  __typename?: "query_root";
  getApiOnlineGetPatientHomeVisit?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineGetPatientHomeVisitResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineGetPatientHomeVisitResponse";
      patientInforModels?: Array<{
        __typename?: "DomainModelsPatientInforPatientInforModel";
        age?: string;
        birthday?: number;
        birthdayDisplay?: string;
        comment?: string;
        deathDate?: number;
        email?: string;
        firstVisitDate?: number;
        homeAddress1?: string;
        homeAddress2?: string;
        homePost?: string;
        houmonAgreed?: string;
        hpId?: number;
        isDead?: number;
        isRyosyoDetail?: number;
        isShowKyuSeiName?: boolean;
        isTester?: number;
        job?: string;
        kanaName?: string;
        lastVisitDate?: number;
        limitConsFlg?: number;
        mail?: string;
        mainHokenPid?: number;
        memo?: string;
        name?: string;
        officeAddress1?: string;
        officeAddress2?: string;
        officeMemo?: string;
        officeName?: string;
        officePost?: string;
        officeTel?: string;
        primaryDoctor?: number;
        ptId?: string;
        ptNum?: string;
        rainCount?: string;
        rainCountInt?: number;
        referenceNo?: string;
        renrakuAddress1?: string;
        renrakuAddress2?: string;
        renrakuMemo?: string;
        renrakuName?: string;
        renrakuPost?: string;
        renrakuTel?: string;
        seqNo?: string;
        setanusi?: string;
        sex?: number;
        sinDate?: number;
        tel1?: string;
        tel2?: string;
        zokugara?: string;
      }>;
    };
  };
};

export type PostApiOnlineCancelPatientHomeVisitMutationVariables = Types.Exact<{
  payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineCancelPatientHomeVisitRequestInput>;
}>;

export type PostApiOnlineCancelPatientHomeVisitMutation = {
  __typename?: "mutation_root";
  postApiOnlineCancelPatientHomeVisit?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineCancelPatientHomeVisitResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineCancelPatientHomeVisitResponse";
      status?: number;
    };
  };
};

export type PostApiOnlineProcessXmloqSmuquc01resMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineProcessXmLProcessXmloqSmuquc01RequestInput>;
  }>;

export type PostApiOnlineProcessXmloqSmuquc01resMutation = {
  __typename?: "mutation_root";
  postApiOnlineProcessXMLOQSmuquc01res?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
      status?: number;
    };
  };
};

export type PostApiOnlineProcessXmloqSmuhvq01resMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineProcessXmLProcessXmloqSmuhvq01RequestInput>;
  }>;

export type PostApiOnlineProcessXmloqSmuhvq01resMutation = {
  __typename?: "mutation_root";
  postApiOnlineProcessXMLOQSmuhvq01res?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
      status?: number;
    };
  };
};

export type PostApiOnlineProcessXmloqSmuonq01resMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineProcessXmLProcessXmloqSmuonq01RequestInput>;
  }>;

export type PostApiOnlineProcessXmloqSmuonq01resMutation = {
  __typename?: "mutation_root";
  postApiOnlineProcessXMLOQSmuonq01res?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
      status?: number;
    };
  };
};

export type PostApiOnlineProcessXmloqSmutic01resMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineProcessXmLProcessXmloqSmutic01RequestInput>;
  }>;

export type PostApiOnlineProcessXmloqSmutic01resMutation = {
  __typename?: "mutation_root";
  postApiOnlineProcessXMLOQSmutic01res?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
      status?: number;
    };
  };
};

export type PostApiOnlineProcessXmlOqSmuhvq02resMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineProcessXmLProcessOqSmuhvq02resRequestInput>;
  }>;

export type PostApiOnlineProcessXmlOqSmuhvq02resMutation = {
  __typename?: "mutation_root";
  postApiOnlineProcessXmlOQSmuhvq02res?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
      status?: number;
    };
  };
};

export type PostApiOnlineProcessXmlOqSmuonq02resMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineProcessXmLProcessXmlOqSmuonq02resRequestInput>;
  }>;

export type PostApiOnlineProcessXmlOqSmuonq02resMutation = {
  __typename?: "mutation_root";
  postApiOnlineProcessXmlOQSmuonq02res?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
      status?: number;
    };
  };
};

export type PostApiOnlineProcessXmlOqSmuquc02resMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineProcessXmLProcessXmlOqSmuquc02resRequestInput>;
  }>;

export type PostApiOnlineProcessXmlOqSmuquc02resMutation = {
  __typename?: "mutation_root";
  postApiOnlineProcessXmlOQSmuquc02res?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
      status?: number;
    };
  };
};

export type PostApiOnlineProcessXmlOqSmutic02resMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineProcessXmLProcessXmlOqSmutic02resRequestInput>;
  }>;

export type PostApiOnlineProcessXmlOqSmutic02resMutation = {
  __typename?: "mutation_root";
  postApiOnlineProcessXmlOQSmutic02res?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineProcessXmlResponseProcessXmlResponse";
      status?: number;
    };
  };
};

export type PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput>;
  }>;

export type PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation = {
  __typename?: "mutation_root";
  postApiOnlineConvertXmlToOQSmutic02XmlMsg?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSmutic02Response";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSmutic02Response";
      oqSmutic02Response?: {
        __typename?: "DomainModelsOnlineOqSmutic02resOqSmutic02Response";
        messageHeader?: {
          __typename?: "DomainModelsOnlineOqSmutic02resMessageHeader";
          segmentOfResult?: string;
          errorMessage?: string;
          errorCode?: string;
          receptionNumber?: string;
        };
      };
    };
  };
};

export type PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput>;
  }>;

export type PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation = {
  __typename?: "mutation_root";
  postApiOnlineConvertXmlToOQSmuquc02XmlMsg?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSmuquc02Response";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSmuquc02Response";
      oqSmuquc02Response?: {
        __typename?: "DomainModelsOnlineOqSmuquc02resOqSmuquc02Response";
        messageHeader?: {
          __typename?: "DomainModelsOnlineOqSmuquc02resMessageHeader";
          segmentOfResult?: string;
          errorMessage?: string;
          errorCode?: string;
          receptionNumber?: string;
        };
      };
    };
  };
};

export type PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput>;
  }>;

export type PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation = {
  __typename?: "mutation_root";
  postApiOnlineConvertXmlToOQSmuonq02XmlMsg?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSmuonq02Response";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSmuonq02Response";
      oqSmuonq02Response?: {
        __typename?: "DomainModelsOnlineOqSmuonq02resOqSmuonq02Response";
        messageHeader?: {
          __typename?: "DomainModelsOnlineOqSmuonq02resMessageHeader";
          segmentOfResult?: string;
          errorMessage?: string;
          errorCode?: string;
          receptionNumber?: string;
        };
      };
    };
  };
};

export type PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput>;
  }>;

export type PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation = {
  __typename?: "mutation_root";
  postApiOnlineConvertXmlToOQSmuhvq02XmlMsg?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSmuhvq02Response";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSmuhvq02Response";
      oqSmuhvq02Response?: {
        __typename?: "DomainModelsOnlineOqSmuhvq02resOqSmuhvq02Response";
        messageHeader?: {
          __typename?: "DomainModelsOnlineOqSmuhvq02resMessageHeader";
          segmentOfResult?: string;
          errorMessage?: string;
          errorCode?: string;
          receptionNumber?: string;
        };
      };
    };
  };
};

export type PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput>;
  }>;

export type PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation = {
  __typename?: "mutation_root";
  postApiOnlineConvertXmlToOQSmutic01XmlMsg?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSmutic01Response";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSmutic01Response";
      oqSmutic01Response?: {
        __typename?: "DomainModelsOnlineOqSmutic01resOqSmutic01Response";
        messageHeader?: {
          __typename?: "DomainModelsOnlineOqSmutic01resMessageHeader";
          arbitraryFileIdentifier?: string;
          characterCodeIdentifier?: string;
          medicalInstitutionCode?: string;
          processExecutionTime?: string;
        };
        messageBody?: {
          __typename?: "DomainModelsOnlineOqSmutic01resMessageBody";
          receptionDateTime?: string;
          receptionNumber?: string;
        };
      };
    };
  };
};

export type PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutationVariables =
  Types.Exact<{
    payload?: Types.InputMaybe<Types.EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput>;
  }>;

export type PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation = {
  __typename?: "mutation_root";
  postApiOnlineConvertXmlToOQSsihvd01XmlMsg?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSsihvd01resResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesOnlineConvertXmlToOqsResponseConvertXmlToOqSsihvd01resResponse";
      oqSsihvdred01Response?: {
        __typename?: "DomainModelsOnlineBatchOnlineConfirmationOqSsihvd01Response";
        messageBody?: {
          __typename?: "DomainModelsOnlineBatchOnlineConfirmationMessageBody";
          processingResultCode?: string;
          processingResultMessage?: string;
          processingResultStatus?: string;
          consentCancelInfo?: {
            __typename?: "DomainModelsOnlineBatchOnlineConfirmationConsentCancelInfo";
            birthdate?: string;
            insuredBranchNumber?: string;
            insuredCardSymbol?: string;
            insuredIdentificationNumber?: string;
            insurerNumber?: string;
            medicalTreatmentFlag?: string;
          };
        };
        messageHeader?: {
          __typename?: "DomainModelsOnlineBatchOnlineConfirmationMessageHeader";
          arbitraryFileIdentifier?: string;
          characterCodeIdentifier?: string;
          errorCode?: string;
          errorMessage?: string;
          medicalInstitutionCode?: string;
          processExecutionTime?: string;
          segmentOfResult?: string;
        };
      };
    };
  };
};

export type GetApiMainMenuGetRsvInfToConfirmValidityQueryVariables =
  Types.Exact<{
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiMainMenuGetRsvInfToConfirmValidityQuery = {
  __typename?: "query_root";
  getApiMainMenuGetRsvInfToConfirmValidity?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMainMenuGetRsvInfToConfirmValidityResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMainMenuGetRsvInfToConfirmValidityResponse";
      rsvInfToConfirms?: Array<{
        __typename?: "DomainModelsRsvInfRsvInfToConfirmModel";
        ptId?: string;
        ptNum?: string;
        ptName?: string;
        age?: number;
        birthday?: number;
        prescriptionIssueType?: number;
        listPtHokenInfModel?: Array<{
          __typename?: "DomainModelsInsuranceHokenInfModel";
          hokenId?: number;
          hokenKbn?: number;
          kigo?: string;
          bango?: string;
          kogakuKbn?: number;
          edaNo?: string;
          hokensyaNo?: string;
        }>;
        listPtKohiInfModel?: Array<{
          __typename?: "DomainModelsInsuranceKohiInfModel";
          hokenId?: number;
          hokenNo?: number;
          futansyaNo?: string;
          jyukyusyaNo?: string;
        }>;
      }>;
    };
  };
};

export const GetApiOnlineGetBatchOnlineCheckDocument = gql`
  query getApiOnlineGetBatchOnlineCheck($batchConfirmationType: Int) {
    getApiOnlineGetBatchOnlineCheck(
      batchConfirmationType: $batchConfirmationType
    ) {
      data {
        batchOnlineCheckModels {
          batchConfirmationType
          confirmationType
          consentFrom
          consentTo
          errorMessage
          examinationFrom
          examinationTo
          processTime
          receptionDateTime
          receptionNo
          segmentOfResult
          sinYm
          yoyakuDate
        }
      }
    }
  }
`;

/**
 * __useGetApiOnlineGetBatchOnlineCheckQuery__
 *
 * To run a query within a React component, call `useGetApiOnlineGetBatchOnlineCheckQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiOnlineGetBatchOnlineCheckQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiOnlineGetBatchOnlineCheckQuery({
 *   variables: {
 *      batchConfirmationType: // value for 'batchConfirmationType'
 *   },
 * });
 */
export function useGetApiOnlineGetBatchOnlineCheckQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiOnlineGetBatchOnlineCheckQuery,
    GetApiOnlineGetBatchOnlineCheckQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiOnlineGetBatchOnlineCheckQuery,
    GetApiOnlineGetBatchOnlineCheckQueryVariables
  >(GetApiOnlineGetBatchOnlineCheckDocument, options);
}
export function useGetApiOnlineGetBatchOnlineCheckLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiOnlineGetBatchOnlineCheckQuery,
    GetApiOnlineGetBatchOnlineCheckQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiOnlineGetBatchOnlineCheckQuery,
    GetApiOnlineGetBatchOnlineCheckQueryVariables
  >(GetApiOnlineGetBatchOnlineCheckDocument, options);
}
export function useGetApiOnlineGetBatchOnlineCheckSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiOnlineGetBatchOnlineCheckQuery,
    GetApiOnlineGetBatchOnlineCheckQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiOnlineGetBatchOnlineCheckQuery,
    GetApiOnlineGetBatchOnlineCheckQueryVariables
  >(GetApiOnlineGetBatchOnlineCheckDocument, options);
}
export type GetApiOnlineGetBatchOnlineCheckQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetBatchOnlineCheckQuery
>;
export type GetApiOnlineGetBatchOnlineCheckLazyQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetBatchOnlineCheckLazyQuery
>;
export type GetApiOnlineGetBatchOnlineCheckSuspenseQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetBatchOnlineCheckSuspenseQuery
>;
export type GetApiOnlineGetBatchOnlineCheckQueryResult = Apollo.QueryResult<
  GetApiOnlineGetBatchOnlineCheckQuery,
  GetApiOnlineGetBatchOnlineCheckQueryVariables
>;
export const GetApiOnlineGetPatientHomeVisitDocument = gql`
  query getApiOnlineGetPatientHomeVisit($ptNum: Int) {
    getApiOnlineGetPatientHomeVisit(ptNum: $ptNum) {
      data {
        patientInforModels {
          age
          birthday
          birthdayDisplay
          comment
          deathDate
          email
          firstVisitDate
          homeAddress1
          homeAddress2
          homePost
          houmonAgreed
          hpId
          isDead
          isRyosyoDetail
          isShowKyuSeiName
          isTester
          job
          kanaName
          lastVisitDate
          limitConsFlg
          mail
          mainHokenPid
          memo
          name
          officeAddress1
          officeAddress2
          officeMemo
          officeName
          officePost
          officeTel
          primaryDoctor
          ptId
          ptNum
          rainCount
          rainCountInt
          referenceNo
          renrakuAddress1
          renrakuAddress2
          renrakuMemo
          renrakuName
          renrakuPost
          renrakuTel
          seqNo
          setanusi
          sex
          sinDate
          tel1
          tel2
          zokugara
        }
      }
    }
  }
`;

/**
 * __useGetApiOnlineGetPatientHomeVisitQuery__
 *
 * To run a query within a React component, call `useGetApiOnlineGetPatientHomeVisitQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiOnlineGetPatientHomeVisitQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiOnlineGetPatientHomeVisitQuery({
 *   variables: {
 *      ptNum: // value for 'ptNum'
 *   },
 * });
 */
export function useGetApiOnlineGetPatientHomeVisitQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiOnlineGetPatientHomeVisitQuery,
    GetApiOnlineGetPatientHomeVisitQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiOnlineGetPatientHomeVisitQuery,
    GetApiOnlineGetPatientHomeVisitQueryVariables
  >(GetApiOnlineGetPatientHomeVisitDocument, options);
}
export function useGetApiOnlineGetPatientHomeVisitLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiOnlineGetPatientHomeVisitQuery,
    GetApiOnlineGetPatientHomeVisitQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiOnlineGetPatientHomeVisitQuery,
    GetApiOnlineGetPatientHomeVisitQueryVariables
  >(GetApiOnlineGetPatientHomeVisitDocument, options);
}
export function useGetApiOnlineGetPatientHomeVisitSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiOnlineGetPatientHomeVisitQuery,
    GetApiOnlineGetPatientHomeVisitQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiOnlineGetPatientHomeVisitQuery,
    GetApiOnlineGetPatientHomeVisitQueryVariables
  >(GetApiOnlineGetPatientHomeVisitDocument, options);
}
export type GetApiOnlineGetPatientHomeVisitQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetPatientHomeVisitQuery
>;
export type GetApiOnlineGetPatientHomeVisitLazyQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetPatientHomeVisitLazyQuery
>;
export type GetApiOnlineGetPatientHomeVisitSuspenseQueryHookResult = ReturnType<
  typeof useGetApiOnlineGetPatientHomeVisitSuspenseQuery
>;
export type GetApiOnlineGetPatientHomeVisitQueryResult = Apollo.QueryResult<
  GetApiOnlineGetPatientHomeVisitQuery,
  GetApiOnlineGetPatientHomeVisitQueryVariables
>;
export const PostApiOnlineCancelPatientHomeVisitDocument = gql`
  mutation postApiOnlineCancelPatientHomeVisit(
    $payload: EmrCloudApiRequestsOnlineCancelPatientHomeVisitRequestInput
  ) {
    postApiOnlineCancelPatientHomeVisit(
      emrCloudApiRequestsOnlineCancelPatientHomeVisitRequestInput: $payload
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiOnlineCancelPatientHomeVisitMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineCancelPatientHomeVisitMutation,
    PostApiOnlineCancelPatientHomeVisitMutationVariables
  >;

/**
 * __usePostApiOnlineCancelPatientHomeVisitMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineCancelPatientHomeVisitMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineCancelPatientHomeVisitMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineCancelPatientHomeVisitMutation, { data, loading, error }] = usePostApiOnlineCancelPatientHomeVisitMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineCancelPatientHomeVisitMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineCancelPatientHomeVisitMutation,
    PostApiOnlineCancelPatientHomeVisitMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineCancelPatientHomeVisitMutation,
    PostApiOnlineCancelPatientHomeVisitMutationVariables
  >(PostApiOnlineCancelPatientHomeVisitDocument, options);
}
export type PostApiOnlineCancelPatientHomeVisitMutationHookResult = ReturnType<
  typeof usePostApiOnlineCancelPatientHomeVisitMutation
>;
export type PostApiOnlineCancelPatientHomeVisitMutationResult =
  Apollo.MutationResult<PostApiOnlineCancelPatientHomeVisitMutation>;
export type PostApiOnlineCancelPatientHomeVisitMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineCancelPatientHomeVisitMutation,
    PostApiOnlineCancelPatientHomeVisitMutationVariables
  >;
export const PostApiOnlineProcessXmloqSmuquc01resDocument = gql`
  mutation postApiOnlineProcessXMLOQSmuquc01res(
    $payload: EmrCloudApiRequestsOnlineProcessXmLProcessXmloqSmuquc01RequestInput
  ) {
    postApiOnlineProcessXMLOQSmuquc01res(
      emrCloudApiRequestsOnlineProcessXmLProcessXmloqSmuquc01RequestInput: $payload
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiOnlineProcessXmloqSmuquc01resMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineProcessXmloqSmuquc01resMutation,
    PostApiOnlineProcessXmloqSmuquc01resMutationVariables
  >;

/**
 * __usePostApiOnlineProcessXmloqSmuquc01resMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineProcessXmloqSmuquc01resMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineProcessXmloqSmuquc01resMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineProcessXmloqSmuquc01resMutation, { data, loading, error }] = usePostApiOnlineProcessXmloqSmuquc01resMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineProcessXmloqSmuquc01resMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineProcessXmloqSmuquc01resMutation,
    PostApiOnlineProcessXmloqSmuquc01resMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineProcessXmloqSmuquc01resMutation,
    PostApiOnlineProcessXmloqSmuquc01resMutationVariables
  >(PostApiOnlineProcessXmloqSmuquc01resDocument, options);
}
export type PostApiOnlineProcessXmloqSmuquc01resMutationHookResult = ReturnType<
  typeof usePostApiOnlineProcessXmloqSmuquc01resMutation
>;
export type PostApiOnlineProcessXmloqSmuquc01resMutationResult =
  Apollo.MutationResult<PostApiOnlineProcessXmloqSmuquc01resMutation>;
export type PostApiOnlineProcessXmloqSmuquc01resMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineProcessXmloqSmuquc01resMutation,
    PostApiOnlineProcessXmloqSmuquc01resMutationVariables
  >;
export const PostApiOnlineProcessXmloqSmuhvq01resDocument = gql`
  mutation postApiOnlineProcessXMLOQSmuhvq01res(
    $payload: EmrCloudApiRequestsOnlineProcessXmLProcessXmloqSmuhvq01RequestInput
  ) {
    postApiOnlineProcessXMLOQSmuhvq01res(
      emrCloudApiRequestsOnlineProcessXmLProcessXmloqSmuhvq01RequestInput: $payload
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiOnlineProcessXmloqSmuhvq01resMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineProcessXmloqSmuhvq01resMutation,
    PostApiOnlineProcessXmloqSmuhvq01resMutationVariables
  >;

/**
 * __usePostApiOnlineProcessXmloqSmuhvq01resMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineProcessXmloqSmuhvq01resMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineProcessXmloqSmuhvq01resMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineProcessXmloqSmuhvq01resMutation, { data, loading, error }] = usePostApiOnlineProcessXmloqSmuhvq01resMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineProcessXmloqSmuhvq01resMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineProcessXmloqSmuhvq01resMutation,
    PostApiOnlineProcessXmloqSmuhvq01resMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineProcessXmloqSmuhvq01resMutation,
    PostApiOnlineProcessXmloqSmuhvq01resMutationVariables
  >(PostApiOnlineProcessXmloqSmuhvq01resDocument, options);
}
export type PostApiOnlineProcessXmloqSmuhvq01resMutationHookResult = ReturnType<
  typeof usePostApiOnlineProcessXmloqSmuhvq01resMutation
>;
export type PostApiOnlineProcessXmloqSmuhvq01resMutationResult =
  Apollo.MutationResult<PostApiOnlineProcessXmloqSmuhvq01resMutation>;
export type PostApiOnlineProcessXmloqSmuhvq01resMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineProcessXmloqSmuhvq01resMutation,
    PostApiOnlineProcessXmloqSmuhvq01resMutationVariables
  >;
export const PostApiOnlineProcessXmloqSmuonq01resDocument = gql`
  mutation postApiOnlineProcessXMLOQSmuonq01res(
    $payload: EmrCloudApiRequestsOnlineProcessXmLProcessXmloqSmuonq01RequestInput
  ) {
    postApiOnlineProcessXMLOQSmuonq01res(
      emrCloudApiRequestsOnlineProcessXmLProcessXmloqSmuonq01RequestInput: $payload
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiOnlineProcessXmloqSmuonq01resMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineProcessXmloqSmuonq01resMutation,
    PostApiOnlineProcessXmloqSmuonq01resMutationVariables
  >;

/**
 * __usePostApiOnlineProcessXmloqSmuonq01resMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineProcessXmloqSmuonq01resMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineProcessXmloqSmuonq01resMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineProcessXmloqSmuonq01resMutation, { data, loading, error }] = usePostApiOnlineProcessXmloqSmuonq01resMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineProcessXmloqSmuonq01resMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineProcessXmloqSmuonq01resMutation,
    PostApiOnlineProcessXmloqSmuonq01resMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineProcessXmloqSmuonq01resMutation,
    PostApiOnlineProcessXmloqSmuonq01resMutationVariables
  >(PostApiOnlineProcessXmloqSmuonq01resDocument, options);
}
export type PostApiOnlineProcessXmloqSmuonq01resMutationHookResult = ReturnType<
  typeof usePostApiOnlineProcessXmloqSmuonq01resMutation
>;
export type PostApiOnlineProcessXmloqSmuonq01resMutationResult =
  Apollo.MutationResult<PostApiOnlineProcessXmloqSmuonq01resMutation>;
export type PostApiOnlineProcessXmloqSmuonq01resMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineProcessXmloqSmuonq01resMutation,
    PostApiOnlineProcessXmloqSmuonq01resMutationVariables
  >;
export const PostApiOnlineProcessXmloqSmutic01resDocument = gql`
  mutation postApiOnlineProcessXMLOQSmutic01res(
    $payload: EmrCloudApiRequestsOnlineProcessXmLProcessXmloqSmutic01RequestInput
  ) {
    postApiOnlineProcessXMLOQSmutic01res(
      emrCloudApiRequestsOnlineProcessXmLProcessXmloqSmutic01RequestInput: $payload
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiOnlineProcessXmloqSmutic01resMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineProcessXmloqSmutic01resMutation,
    PostApiOnlineProcessXmloqSmutic01resMutationVariables
  >;

/**
 * __usePostApiOnlineProcessXmloqSmutic01resMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineProcessXmloqSmutic01resMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineProcessXmloqSmutic01resMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineProcessXmloqSmutic01resMutation, { data, loading, error }] = usePostApiOnlineProcessXmloqSmutic01resMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineProcessXmloqSmutic01resMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineProcessXmloqSmutic01resMutation,
    PostApiOnlineProcessXmloqSmutic01resMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineProcessXmloqSmutic01resMutation,
    PostApiOnlineProcessXmloqSmutic01resMutationVariables
  >(PostApiOnlineProcessXmloqSmutic01resDocument, options);
}
export type PostApiOnlineProcessXmloqSmutic01resMutationHookResult = ReturnType<
  typeof usePostApiOnlineProcessXmloqSmutic01resMutation
>;
export type PostApiOnlineProcessXmloqSmutic01resMutationResult =
  Apollo.MutationResult<PostApiOnlineProcessXmloqSmutic01resMutation>;
export type PostApiOnlineProcessXmloqSmutic01resMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineProcessXmloqSmutic01resMutation,
    PostApiOnlineProcessXmloqSmutic01resMutationVariables
  >;
export const PostApiOnlineProcessXmlOqSmuhvq02resDocument = gql`
  mutation postApiOnlineProcessXmlOQSmuhvq02res(
    $payload: EmrCloudApiRequestsOnlineProcessXmLProcessOqSmuhvq02resRequestInput
  ) {
    postApiOnlineProcessXmlOQSmuhvq02res(
      emrCloudApiRequestsOnlineProcessXmLProcessOqSmuhvq02resRequestInput: $payload
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiOnlineProcessXmlOqSmuhvq02resMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineProcessXmlOqSmuhvq02resMutation,
    PostApiOnlineProcessXmlOqSmuhvq02resMutationVariables
  >;

/**
 * __usePostApiOnlineProcessXmlOqSmuhvq02resMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineProcessXmlOqSmuhvq02resMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineProcessXmlOqSmuhvq02resMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineProcessXmlOqSmuhvq02resMutation, { data, loading, error }] = usePostApiOnlineProcessXmlOqSmuhvq02resMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineProcessXmlOqSmuhvq02resMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineProcessXmlOqSmuhvq02resMutation,
    PostApiOnlineProcessXmlOqSmuhvq02resMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineProcessXmlOqSmuhvq02resMutation,
    PostApiOnlineProcessXmlOqSmuhvq02resMutationVariables
  >(PostApiOnlineProcessXmlOqSmuhvq02resDocument, options);
}
export type PostApiOnlineProcessXmlOqSmuhvq02resMutationHookResult = ReturnType<
  typeof usePostApiOnlineProcessXmlOqSmuhvq02resMutation
>;
export type PostApiOnlineProcessXmlOqSmuhvq02resMutationResult =
  Apollo.MutationResult<PostApiOnlineProcessXmlOqSmuhvq02resMutation>;
export type PostApiOnlineProcessXmlOqSmuhvq02resMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineProcessXmlOqSmuhvq02resMutation,
    PostApiOnlineProcessXmlOqSmuhvq02resMutationVariables
  >;
export const PostApiOnlineProcessXmlOqSmuonq02resDocument = gql`
  mutation postApiOnlineProcessXmlOQSmuonq02res(
    $payload: EmrCloudApiRequestsOnlineProcessXmLProcessXmlOqSmuonq02resRequestInput
  ) {
    postApiOnlineProcessXmlOQSmuonq02res(
      emrCloudApiRequestsOnlineProcessXmLProcessXmlOqSmuonq02resRequestInput: $payload
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiOnlineProcessXmlOqSmuonq02resMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineProcessXmlOqSmuonq02resMutation,
    PostApiOnlineProcessXmlOqSmuonq02resMutationVariables
  >;

/**
 * __usePostApiOnlineProcessXmlOqSmuonq02resMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineProcessXmlOqSmuonq02resMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineProcessXmlOqSmuonq02resMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineProcessXmlOqSmuonq02resMutation, { data, loading, error }] = usePostApiOnlineProcessXmlOqSmuonq02resMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineProcessXmlOqSmuonq02resMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineProcessXmlOqSmuonq02resMutation,
    PostApiOnlineProcessXmlOqSmuonq02resMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineProcessXmlOqSmuonq02resMutation,
    PostApiOnlineProcessXmlOqSmuonq02resMutationVariables
  >(PostApiOnlineProcessXmlOqSmuonq02resDocument, options);
}
export type PostApiOnlineProcessXmlOqSmuonq02resMutationHookResult = ReturnType<
  typeof usePostApiOnlineProcessXmlOqSmuonq02resMutation
>;
export type PostApiOnlineProcessXmlOqSmuonq02resMutationResult =
  Apollo.MutationResult<PostApiOnlineProcessXmlOqSmuonq02resMutation>;
export type PostApiOnlineProcessXmlOqSmuonq02resMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineProcessXmlOqSmuonq02resMutation,
    PostApiOnlineProcessXmlOqSmuonq02resMutationVariables
  >;
export const PostApiOnlineProcessXmlOqSmuquc02resDocument = gql`
  mutation postApiOnlineProcessXmlOQSmuquc02res(
    $payload: EmrCloudApiRequestsOnlineProcessXmLProcessXmlOqSmuquc02resRequestInput
  ) {
    postApiOnlineProcessXmlOQSmuquc02res(
      emrCloudApiRequestsOnlineProcessXmLProcessXmlOqSmuquc02resRequestInput: $payload
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiOnlineProcessXmlOqSmuquc02resMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineProcessXmlOqSmuquc02resMutation,
    PostApiOnlineProcessXmlOqSmuquc02resMutationVariables
  >;

/**
 * __usePostApiOnlineProcessXmlOqSmuquc02resMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineProcessXmlOqSmuquc02resMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineProcessXmlOqSmuquc02resMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineProcessXmlOqSmuquc02resMutation, { data, loading, error }] = usePostApiOnlineProcessXmlOqSmuquc02resMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineProcessXmlOqSmuquc02resMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineProcessXmlOqSmuquc02resMutation,
    PostApiOnlineProcessXmlOqSmuquc02resMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineProcessXmlOqSmuquc02resMutation,
    PostApiOnlineProcessXmlOqSmuquc02resMutationVariables
  >(PostApiOnlineProcessXmlOqSmuquc02resDocument, options);
}
export type PostApiOnlineProcessXmlOqSmuquc02resMutationHookResult = ReturnType<
  typeof usePostApiOnlineProcessXmlOqSmuquc02resMutation
>;
export type PostApiOnlineProcessXmlOqSmuquc02resMutationResult =
  Apollo.MutationResult<PostApiOnlineProcessXmlOqSmuquc02resMutation>;
export type PostApiOnlineProcessXmlOqSmuquc02resMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineProcessXmlOqSmuquc02resMutation,
    PostApiOnlineProcessXmlOqSmuquc02resMutationVariables
  >;
export const PostApiOnlineProcessXmlOqSmutic02resDocument = gql`
  mutation postApiOnlineProcessXmlOQSmutic02res(
    $payload: EmrCloudApiRequestsOnlineProcessXmLProcessXmlOqSmutic02resRequestInput
  ) {
    postApiOnlineProcessXmlOQSmutic02res(
      emrCloudApiRequestsOnlineProcessXmLProcessXmlOqSmutic02resRequestInput: $payload
    ) {
      data {
        status
      }
      message
      status
    }
  }
`;
export type PostApiOnlineProcessXmlOqSmutic02resMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineProcessXmlOqSmutic02resMutation,
    PostApiOnlineProcessXmlOqSmutic02resMutationVariables
  >;

/**
 * __usePostApiOnlineProcessXmlOqSmutic02resMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineProcessXmlOqSmutic02resMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineProcessXmlOqSmutic02resMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineProcessXmlOqSmutic02resMutation, { data, loading, error }] = usePostApiOnlineProcessXmlOqSmutic02resMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineProcessXmlOqSmutic02resMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineProcessXmlOqSmutic02resMutation,
    PostApiOnlineProcessXmlOqSmutic02resMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineProcessXmlOqSmutic02resMutation,
    PostApiOnlineProcessXmlOqSmutic02resMutationVariables
  >(PostApiOnlineProcessXmlOqSmutic02resDocument, options);
}
export type PostApiOnlineProcessXmlOqSmutic02resMutationHookResult = ReturnType<
  typeof usePostApiOnlineProcessXmlOqSmutic02resMutation
>;
export type PostApiOnlineProcessXmlOqSmutic02resMutationResult =
  Apollo.MutationResult<PostApiOnlineProcessXmlOqSmutic02resMutation>;
export type PostApiOnlineProcessXmlOqSmutic02resMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineProcessXmlOqSmutic02resMutation,
    PostApiOnlineProcessXmlOqSmutic02resMutationVariables
  >;
export const PostApiOnlineConvertXmlToOqSmutic02XmlMsgDocument = gql`
  mutation postApiOnlineConvertXmlToOQSmutic02XmlMsg(
    $payload: EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput
  ) {
    postApiOnlineConvertXmlToOQSmutic02XmlMsg(
      emrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput: $payload
    ) {
      data {
        oqSmutic02Response {
          messageHeader {
            segmentOfResult
            errorMessage
            errorCode
            receptionNumber
          }
        }
      }
    }
  }
`;
export type PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutationVariables
  >;

/**
 * __usePostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineConvertXmlToOqSmutic02XmlMsgMutation, { data, loading, error }] = usePostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutationVariables
  >(PostApiOnlineConvertXmlToOqSmutic02XmlMsgDocument, options);
}
export type PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutationHookResult =
  ReturnType<typeof usePostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutationResult =
  Apollo.MutationResult<PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmutic02XmlMsgMutationVariables
  >;
export const PostApiOnlineConvertXmlToOqSmuquc02XmlMsgDocument = gql`
  mutation postApiOnlineConvertXmlToOQSmuquc02XmlMsg(
    $payload: EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput
  ) {
    postApiOnlineConvertXmlToOQSmuquc02XmlMsg(
      emrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput: $payload
    ) {
      data {
        oqSmuquc02Response {
          messageHeader {
            segmentOfResult
            errorMessage
            errorCode
            receptionNumber
          }
        }
      }
    }
  }
`;
export type PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutationVariables
  >;

/**
 * __usePostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation, { data, loading, error }] = usePostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutationVariables
  >(PostApiOnlineConvertXmlToOqSmuquc02XmlMsgDocument, options);
}
export type PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutationHookResult =
  ReturnType<typeof usePostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutationResult =
  Apollo.MutationResult<PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutationVariables
  >;
export const PostApiOnlineConvertXmlToOqSmuonq02XmlMsgDocument = gql`
  mutation postApiOnlineConvertXmlToOQSmuonq02XmlMsg(
    $payload: EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput
  ) {
    postApiOnlineConvertXmlToOQSmuonq02XmlMsg(
      emrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput: $payload
    ) {
      data {
        oqSmuonq02Response {
          messageHeader {
            segmentOfResult
            errorMessage
            errorCode
            receptionNumber
          }
        }
      }
    }
  }
`;
export type PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutationVariables
  >;

/**
 * __usePostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation, { data, loading, error }] = usePostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutationVariables
  >(PostApiOnlineConvertXmlToOqSmuonq02XmlMsgDocument, options);
}
export type PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutationHookResult =
  ReturnType<typeof usePostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutationResult =
  Apollo.MutationResult<PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutationVariables
  >;
export const PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgDocument = gql`
  mutation postApiOnlineConvertXmlToOQSmuhvq02XmlMsg(
    $payload: EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput
  ) {
    postApiOnlineConvertXmlToOQSmuhvq02XmlMsg(
      emrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput: $payload
    ) {
      data {
        oqSmuhvq02Response {
          messageHeader {
            segmentOfResult
            errorMessage
            errorCode
            receptionNumber
          }
        }
      }
    }
  }
`;
export type PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutationVariables
  >;

/**
 * __usePostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation, { data, loading, error }] = usePostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutationVariables
  >(PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgDocument, options);
}
export type PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutationHookResult =
  ReturnType<typeof usePostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutationResult =
  Apollo.MutationResult<PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutationVariables
  >;
export const PostApiOnlineConvertXmlToOqSmutic01XmlMsgDocument = gql`
  mutation postApiOnlineConvertXmlToOQSmutic01XmlMsg(
    $payload: EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput
  ) {
    postApiOnlineConvertXmlToOQSmutic01XmlMsg(
      emrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput: $payload
    ) {
      data {
        oqSmutic01Response {
          messageHeader {
            arbitraryFileIdentifier
            characterCodeIdentifier
            medicalInstitutionCode
            processExecutionTime
          }
          messageBody {
            receptionDateTime
            receptionNumber
          }
        }
      }
    }
  }
`;
export type PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutationVariables
  >;

/**
 * __usePostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineConvertXmlToOqSmutic01XmlMsgMutation, { data, loading, error }] = usePostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutationVariables
  >(PostApiOnlineConvertXmlToOqSmutic01XmlMsgDocument, options);
}
export type PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutationHookResult =
  ReturnType<typeof usePostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutationResult =
  Apollo.MutationResult<PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSmutic01XmlMsgMutationVariables
  >;
export const PostApiOnlineConvertXmlToOqSsihvd01XmlMsgDocument = gql`
  mutation postApiOnlineConvertXmlToOQSsihvd01XmlMsg(
    $payload: EmrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput
  ) {
    postApiOnlineConvertXmlToOQSsihvd01XmlMsg(
      emrCloudApiRequestsOnlineConvertXmlToOqsXmlMsgRequestInput: $payload
    ) {
      data {
        oqSsihvdred01Response {
          messageBody {
            processingResultCode
            processingResultMessage
            processingResultStatus
            consentCancelInfo {
              birthdate
              insuredBranchNumber
              insuredCardSymbol
              insuredIdentificationNumber
              insurerNumber
              medicalTreatmentFlag
            }
          }
          messageHeader {
            arbitraryFileIdentifier
            characterCodeIdentifier
            errorCode
            errorMessage
            medicalInstitutionCode
            processExecutionTime
            segmentOfResult
          }
        }
      }
    }
  }
`;
export type PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutationFn =
  Apollo.MutationFunction<
    PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutationVariables
  >;

/**
 * __usePostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation__
 *
 * To run a mutation, you first call `usePostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation, { data, loading, error }] = usePostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation({
 *   variables: {
 *      payload: // value for 'payload'
 *   },
 * });
 */
export function usePostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutationVariables
  >(PostApiOnlineConvertXmlToOqSsihvd01XmlMsgDocument, options);
}
export type PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutationHookResult =
  ReturnType<typeof usePostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutationResult =
  Apollo.MutationResult<PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation>;
export type PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation,
    PostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutationVariables
  >;
export const GetApiMainMenuGetRsvInfToConfirmValidityDocument = gql`
  query getApiMainMenuGetRsvInfToConfirmValidity($sinDate: Int) {
    getApiMainMenuGetRsvInfToConfirmValidity(sinDate: $sinDate) {
      data {
        rsvInfToConfirms {
          ptId
          ptNum
          ptName
          age
          birthday
          prescriptionIssueType
          listPtHokenInfModel {
            hokenId
            hokenKbn
            kigo
            bango
            kogakuKbn
            edaNo
            hokensyaNo
          }
          listPtKohiInfModel {
            hokenId
            hokenNo
            futansyaNo
            jyukyusyaNo
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiMainMenuGetRsvInfToConfirmValidityQuery__
 *
 * To run a query within a React component, call `useGetApiMainMenuGetRsvInfToConfirmValidityQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMainMenuGetRsvInfToConfirmValidityQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMainMenuGetRsvInfToConfirmValidityQuery({
 *   variables: {
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiMainMenuGetRsvInfToConfirmValidityQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMainMenuGetRsvInfToConfirmValidityQuery,
    GetApiMainMenuGetRsvInfToConfirmValidityQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMainMenuGetRsvInfToConfirmValidityQuery,
    GetApiMainMenuGetRsvInfToConfirmValidityQueryVariables
  >(GetApiMainMenuGetRsvInfToConfirmValidityDocument, options);
}
export function useGetApiMainMenuGetRsvInfToConfirmValidityLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMainMenuGetRsvInfToConfirmValidityQuery,
    GetApiMainMenuGetRsvInfToConfirmValidityQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMainMenuGetRsvInfToConfirmValidityQuery,
    GetApiMainMenuGetRsvInfToConfirmValidityQueryVariables
  >(GetApiMainMenuGetRsvInfToConfirmValidityDocument, options);
}
export function useGetApiMainMenuGetRsvInfToConfirmValiditySuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMainMenuGetRsvInfToConfirmValidityQuery,
    GetApiMainMenuGetRsvInfToConfirmValidityQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMainMenuGetRsvInfToConfirmValidityQuery,
    GetApiMainMenuGetRsvInfToConfirmValidityQueryVariables
  >(GetApiMainMenuGetRsvInfToConfirmValidityDocument, options);
}
export type GetApiMainMenuGetRsvInfToConfirmValidityQueryHookResult =
  ReturnType<typeof useGetApiMainMenuGetRsvInfToConfirmValidityQuery>;
export type GetApiMainMenuGetRsvInfToConfirmValidityLazyQueryHookResult =
  ReturnType<typeof useGetApiMainMenuGetRsvInfToConfirmValidityLazyQuery>;
export type GetApiMainMenuGetRsvInfToConfirmValiditySuspenseQueryHookResult =
  ReturnType<typeof useGetApiMainMenuGetRsvInfToConfirmValiditySuspenseQuery>;
export type GetApiMainMenuGetRsvInfToConfirmValidityQueryResult =
  Apollo.QueryResult<
    GetApiMainMenuGetRsvInfToConfirmValidityQuery,
    GetApiMainMenuGetRsvInfToConfirmValidityQueryVariables
  >;
