import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiRecalculationMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsReceiptRecalculationRequestInput;
}>;

export type PostApiRecalculationMutation = {
  __typename?: "mutation_root";
  postApiRecalculation: string;
};

export type PostApiRecalculationReceiptCheckMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsReceiptCheckReceiptCheckRecalculationRequestInput>;
}>;

export type PostApiRecalculationReceiptCheckMutation = {
  __typename?: "mutation_root";
  postApiRecalculationReceiptCheck: string;
};

export const PostApiRecalculationDocument = gql`
  mutation postApiRecalculation(
    $input: EmrCloudApiRequestsReceiptRecalculationRequestInput!
  ) {
    postApiRecalculation(
      emrCloudApiRequestsReceiptRecalculationRequestInput: $input
    )
  }
`;
export type PostApiRecalculationMutationFn = Apollo.MutationFunction<
  PostApiRecalculationMutation,
  PostApiRecalculationMutationVariables
>;

/**
 * __usePostApiRecalculationMutation__
 *
 * To run a mutation, you first call `usePostApiRecalculationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiRecalculationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiRecalculationMutation, { data, loading, error }] = usePostApiRecalculationMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiRecalculationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiRecalculationMutation,
    PostApiRecalculationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiRecalculationMutation,
    PostApiRecalculationMutationVariables
  >(PostApiRecalculationDocument, options);
}
export type PostApiRecalculationMutationHookResult = ReturnType<
  typeof usePostApiRecalculationMutation
>;
export type PostApiRecalculationMutationResult =
  Apollo.MutationResult<PostApiRecalculationMutation>;
export type PostApiRecalculationMutationOptions = Apollo.BaseMutationOptions<
  PostApiRecalculationMutation,
  PostApiRecalculationMutationVariables
>;
export const PostApiRecalculationReceiptCheckDocument = gql`
  mutation postApiRecalculationReceiptCheck(
    $input: EmrCloudApiRequestsReceiptCheckReceiptCheckRecalculationRequestInput
  ) {
    postApiRecalculationReceiptCheck(
      emrCloudApiRequestsReceiptCheckReceiptCheckRecalculationRequestInput: $input
    )
  }
`;
export type PostApiRecalculationReceiptCheckMutationFn =
  Apollo.MutationFunction<
    PostApiRecalculationReceiptCheckMutation,
    PostApiRecalculationReceiptCheckMutationVariables
  >;

/**
 * __usePostApiRecalculationReceiptCheckMutation__
 *
 * To run a mutation, you first call `usePostApiRecalculationReceiptCheckMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiRecalculationReceiptCheckMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiRecalculationReceiptCheckMutation, { data, loading, error }] = usePostApiRecalculationReceiptCheckMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiRecalculationReceiptCheckMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiRecalculationReceiptCheckMutation,
    PostApiRecalculationReceiptCheckMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiRecalculationReceiptCheckMutation,
    PostApiRecalculationReceiptCheckMutationVariables
  >(PostApiRecalculationReceiptCheckDocument, options);
}
export type PostApiRecalculationReceiptCheckMutationHookResult = ReturnType<
  typeof usePostApiRecalculationReceiptCheckMutation
>;
export type PostApiRecalculationReceiptCheckMutationResult =
  Apollo.MutationResult<PostApiRecalculationReceiptCheckMutation>;
export type PostApiRecalculationReceiptCheckMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiRecalculationReceiptCheckMutation,
    PostApiRecalculationReceiptCheckMutationVariables
  >;
