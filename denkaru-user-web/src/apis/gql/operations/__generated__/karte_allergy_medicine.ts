import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type SaveKarteAllergyMedicineMutationVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  drugName: Types.Scalars["String"]["input"];
  cmt?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  isContraindicated: Types.Scalars["Boolean"]["input"];
  isDeleted: Types.Scalars["Int"]["input"];
  startDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  endDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  seqNo: Types.Scalars["Int"]["input"];
  itemCd?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type SaveKarteAllergyMedicineMutation = {
  __typename?: "mutation_root";
  postApiKarteAllergySaveKarteAllergyMedicine?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesKarteAllergySaveKarteAllergyMedicineResponse";
    status?: number;
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesKarteAllergySaveKarteAllergyMedicineResponse";
      success?: boolean;
    };
  };
};

export const SaveKarteAllergyMedicineDocument = gql`
  mutation saveKarteAllergyMedicine(
    $ptId: BigInt!
    $drugName: String!
    $cmt: String
    $isContraindicated: Boolean!
    $isDeleted: Int!
    $startDate: Int
    $endDate: Int
    $seqNo: Int!
    $itemCd: String
  ) {
    postApiKarteAllergySaveKarteAllergyMedicine(
      emrCloudApiRequestsKarteAllergySaveKarteAllergyMedicineRequestInput: {
        ptAllergyDrug: {
          cmt: $cmt
          drugName: $drugName
          isContraindicated: $isContraindicated
          isDeleted: $isDeleted
          ptId: $ptId
          seqNo: $seqNo
          startDate: $startDate
          endDate: $endDate
          itemCd: $itemCd
        }
      }
    ) {
      data {
        success
      }
      status
      message
    }
  }
`;
export type SaveKarteAllergyMedicineMutationFn = Apollo.MutationFunction<
  SaveKarteAllergyMedicineMutation,
  SaveKarteAllergyMedicineMutationVariables
>;

/**
 * __useSaveKarteAllergyMedicineMutation__
 *
 * To run a mutation, you first call `useSaveKarteAllergyMedicineMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSaveKarteAllergyMedicineMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [saveKarteAllergyMedicineMutation, { data, loading, error }] = useSaveKarteAllergyMedicineMutation({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      drugName: // value for 'drugName'
 *      cmt: // value for 'cmt'
 *      isContraindicated: // value for 'isContraindicated'
 *      isDeleted: // value for 'isDeleted'
 *      startDate: // value for 'startDate'
 *      endDate: // value for 'endDate'
 *      seqNo: // value for 'seqNo'
 *      itemCd: // value for 'itemCd'
 *   },
 * });
 */
export function useSaveKarteAllergyMedicineMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SaveKarteAllergyMedicineMutation,
    SaveKarteAllergyMedicineMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SaveKarteAllergyMedicineMutation,
    SaveKarteAllergyMedicineMutationVariables
  >(SaveKarteAllergyMedicineDocument, options);
}
export type SaveKarteAllergyMedicineMutationHookResult = ReturnType<
  typeof useSaveKarteAllergyMedicineMutation
>;
export type SaveKarteAllergyMedicineMutationResult =
  Apollo.MutationResult<SaveKarteAllergyMedicineMutation>;
export type SaveKarteAllergyMedicineMutationOptions =
  Apollo.BaseMutationOptions<
    SaveKarteAllergyMedicineMutation,
    SaveKarteAllergyMedicineMutationVariables
  >;
