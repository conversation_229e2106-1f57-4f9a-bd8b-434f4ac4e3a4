import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetClientCertificateQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetClientCertificateQuery = {
  __typename?: "query_root";
  getClientCertificate?: Array<{
    __typename?: "GetClientCertificateRes";
    ClientCertificateID: number;
    Label: string;
    CommonName: string;
    IssueDate: string;
    ExpirationDate: string;
    DownloadURL: string;
    TokenExpireTime: string;
    InstallPassword?: string;
  }>;
};

export type CreateClientCertificateMutationVariables = Types.Exact<{
  input: Types.CreateClientCertificateReq;
}>;

export type CreateClientCertificateMutation = {
  __typename?: "mutation_root";
  createClientCertificate: {
    __typename?: "CreateClientCertificateRes";
    ClientCertificateID: number;
    InstallPassword: string;
    DownloadURL: string;
    Label: string;
    CommonName: string;
    TokenExpireTime: string;
  };
};

export type DeleteClientCertificateMutationVariables = Types.Exact<{
  input: Types.DeleteClientCertificateReq;
}>;

export type DeleteClientCertificateMutation = {
  __typename?: "mutation_root";
  deleteClientCertificate?: {
    __typename?: "DeleteClientCertificateRes";
    message: string;
  };
};

export const GetClientCertificateDocument = gql`
  query getClientCertificate {
    getClientCertificate {
      ClientCertificateID
      Label
      CommonName
      IssueDate
      ExpirationDate
      DownloadURL
      TokenExpireTime
      InstallPassword
    }
  }
`;

/**
 * __useGetClientCertificateQuery__
 *
 * To run a query within a React component, call `useGetClientCertificateQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetClientCertificateQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetClientCertificateQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetClientCertificateQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetClientCertificateQuery,
    GetClientCertificateQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetClientCertificateQuery,
    GetClientCertificateQueryVariables
  >(GetClientCertificateDocument, options);
}
export function useGetClientCertificateLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetClientCertificateQuery,
    GetClientCertificateQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetClientCertificateQuery,
    GetClientCertificateQueryVariables
  >(GetClientCertificateDocument, options);
}
export function useGetClientCertificateSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetClientCertificateQuery,
    GetClientCertificateQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetClientCertificateQuery,
    GetClientCertificateQueryVariables
  >(GetClientCertificateDocument, options);
}
export type GetClientCertificateQueryHookResult = ReturnType<
  typeof useGetClientCertificateQuery
>;
export type GetClientCertificateLazyQueryHookResult = ReturnType<
  typeof useGetClientCertificateLazyQuery
>;
export type GetClientCertificateSuspenseQueryHookResult = ReturnType<
  typeof useGetClientCertificateSuspenseQuery
>;
export type GetClientCertificateQueryResult = Apollo.QueryResult<
  GetClientCertificateQuery,
  GetClientCertificateQueryVariables
>;
export const CreateClientCertificateDocument = gql`
  mutation createClientCertificate($input: CreateClientCertificateReq!) {
    createClientCertificate(input: $input) {
      ClientCertificateID
      InstallPassword
      DownloadURL
      Label
      CommonName
      TokenExpireTime
    }
  }
`;
export type CreateClientCertificateMutationFn = Apollo.MutationFunction<
  CreateClientCertificateMutation,
  CreateClientCertificateMutationVariables
>;

/**
 * __useCreateClientCertificateMutation__
 *
 * To run a mutation, you first call `useCreateClientCertificateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateClientCertificateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createClientCertificateMutation, { data, loading, error }] = useCreateClientCertificateMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateClientCertificateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateClientCertificateMutation,
    CreateClientCertificateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateClientCertificateMutation,
    CreateClientCertificateMutationVariables
  >(CreateClientCertificateDocument, options);
}
export type CreateClientCertificateMutationHookResult = ReturnType<
  typeof useCreateClientCertificateMutation
>;
export type CreateClientCertificateMutationResult =
  Apollo.MutationResult<CreateClientCertificateMutation>;
export type CreateClientCertificateMutationOptions = Apollo.BaseMutationOptions<
  CreateClientCertificateMutation,
  CreateClientCertificateMutationVariables
>;
export const DeleteClientCertificateDocument = gql`
  mutation deleteClientCertificate($input: DeleteClientCertificateReq!) {
    deleteClientCertificate(input: $input) {
      message
    }
  }
`;
export type DeleteClientCertificateMutationFn = Apollo.MutationFunction<
  DeleteClientCertificateMutation,
  DeleteClientCertificateMutationVariables
>;

/**
 * __useDeleteClientCertificateMutation__
 *
 * To run a mutation, you first call `useDeleteClientCertificateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteClientCertificateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteClientCertificateMutation, { data, loading, error }] = useDeleteClientCertificateMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useDeleteClientCertificateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteClientCertificateMutation,
    DeleteClientCertificateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteClientCertificateMutation,
    DeleteClientCertificateMutationVariables
  >(DeleteClientCertificateDocument, options);
}
export type DeleteClientCertificateMutationHookResult = ReturnType<
  typeof useDeleteClientCertificateMutation
>;
export type DeleteClientCertificateMutationResult =
  Apollo.MutationResult<DeleteClientCertificateMutation>;
export type DeleteClientCertificateMutationOptions = Apollo.BaseMutationOptions<
  DeleteClientCertificateMutation,
  DeleteClientCertificateMutationVariables
>;
