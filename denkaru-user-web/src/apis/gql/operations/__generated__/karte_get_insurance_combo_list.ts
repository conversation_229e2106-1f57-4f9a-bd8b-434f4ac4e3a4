import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiTodayOrdGetInsuranceComboListQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiTodayOrdGetInsuranceComboListQuery = {
  __typename?: "query_root";
  getApiTodayOrdGetInsuranceComboList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceListGetInsuranceComboListResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceListGetInsuranceComboListResponse";
      data?: Array<{
        __typename?: "UseCaseInsuranceGetComboListGetInsuranceComboItemOuputData";
        hokenPid?: number;
        hokenName?: string;
        hokenId?: number;
        kohiId1?: number;
        kohiId2?: number;
        kohiId3?: number;
        kohiId4?: number;
        isExpired?: boolean;
        hokenSName?: string;
        displayRateOnly?: string;
        hokenKbn?: number;
        hokenSentaku?: string;
        isJibaiOrRosai?: boolean;
        isJihi?: boolean;
        isKokuho?: boolean;
        isNoHoken?: boolean;
        isShaho?: boolean;
        futansyaNo?: string;
        hokenSbtCd?: number;
        houbetu?: string;
      }>;
    };
  };
};

export const GetApiTodayOrdGetInsuranceComboListDocument = gql`
  query getApiTodayOrdGetInsuranceComboList($ptId: BigInt, $sinDate: Int) {
    getApiTodayOrdGetInsuranceComboList(ptId: $ptId, sinDate: $sinDate) {
      data {
        data {
          hokenPid
          hokenName
          hokenId
          kohiId1
          kohiId2
          kohiId3
          kohiId4
          isExpired
          hokenSName
          displayRateOnly
          hokenKbn
          hokenSentaku
          isJibaiOrRosai
          isJihi
          isKokuho
          isNoHoken
          isShaho
          futansyaNo
          hokenSbtCd
          houbetu
        }
      }
    }
  }
`;

/**
 * __useGetApiTodayOrdGetInsuranceComboListQuery__
 *
 * To run a query within a React component, call `useGetApiTodayOrdGetInsuranceComboListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiTodayOrdGetInsuranceComboListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiTodayOrdGetInsuranceComboListQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiTodayOrdGetInsuranceComboListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiTodayOrdGetInsuranceComboListQuery,
    GetApiTodayOrdGetInsuranceComboListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiTodayOrdGetInsuranceComboListQuery,
    GetApiTodayOrdGetInsuranceComboListQueryVariables
  >(GetApiTodayOrdGetInsuranceComboListDocument, options);
}
export function useGetApiTodayOrdGetInsuranceComboListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiTodayOrdGetInsuranceComboListQuery,
    GetApiTodayOrdGetInsuranceComboListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiTodayOrdGetInsuranceComboListQuery,
    GetApiTodayOrdGetInsuranceComboListQueryVariables
  >(GetApiTodayOrdGetInsuranceComboListDocument, options);
}
export function useGetApiTodayOrdGetInsuranceComboListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiTodayOrdGetInsuranceComboListQuery,
    GetApiTodayOrdGetInsuranceComboListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiTodayOrdGetInsuranceComboListQuery,
    GetApiTodayOrdGetInsuranceComboListQueryVariables
  >(GetApiTodayOrdGetInsuranceComboListDocument, options);
}
export type GetApiTodayOrdGetInsuranceComboListQueryHookResult = ReturnType<
  typeof useGetApiTodayOrdGetInsuranceComboListQuery
>;
export type GetApiTodayOrdGetInsuranceComboListLazyQueryHookResult = ReturnType<
  typeof useGetApiTodayOrdGetInsuranceComboListLazyQuery
>;
export type GetApiTodayOrdGetInsuranceComboListSuspenseQueryHookResult =
  ReturnType<typeof useGetApiTodayOrdGetInsuranceComboListSuspenseQuery>;
export type GetApiTodayOrdGetInsuranceComboListQueryResult = Apollo.QueryResult<
  GetApiTodayOrdGetInsuranceComboListQuery,
  GetApiTodayOrdGetInsuranceComboListQueryVariables
>;
