import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiPdfCreatorExportKarte1QueryVariables = Types.Exact<{
  hokenPid?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  syuByomei?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  tenkiByomei?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiPdfCreatorExportKarte1Query = {
  __typename?: "query_root";
  getApiPdfCreatorExportKarte1?: {
    __typename?: "ExportKarte1Respone";
    fileUrl?: string;
    message?: string;
    status?: number;
  };
};

export type GetApiPatientInforGetHokenPatternByPtIdKarteQueryVariables =
  Types.Exact<{
    isOdrInf?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiPatientInforGetHokenPatternByPtIdKarteQuery = {
  __typename?: "query_root";
  getApiPatientInforGetHokenPatternByPtId?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceAiChatGetHokenPatternByPtIdResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceAiChatGetHokenPatternByPtIdResponse";
      data?: Array<{
        __typename?: "DomainModelsInsuranceAiChatHokenPatternDto";
        hokenId?: number;
        hokenKbn?: number;
        hokenPid?: number;
        patternName?: string;
        ptId?: string;
        seqNo?: string;
        hokenInf?: {
          __typename?: "DomainModelsInsuranceAiChatHokenInfDto";
          hokenId?: number;
          hokenKbn?: number;
          hokenSbtKbn?: number;
          houbetu?: string;
          hokenMstHokenNameCd?: string;
          hokenNoMaster?: number;
          hokenSentaku?: string;
        };
        kohi1?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          hokenId?: number;
          kohiName?: string;
          isExpirated?: boolean;
        };
        kohi2?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          hokenId?: number;
          kohiName?: string;
          isExpirated?: boolean;
        };
        kohi3?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          hokenId?: number;
          kohiName?: string;
          isExpirated?: boolean;
        };
        kohi4?: {
          __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
          hokenId?: number;
          kohiName?: string;
          isExpirated?: boolean;
        };
      }>;
    };
  };
};

export type GetApiPatientInforGetKohiInfByPtIdKarteQueryVariables =
  Types.Exact<{
    isOdrInf?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type GetApiPatientInforGetKohiInfByPtIdKarteQuery = {
  __typename?: "query_root";
  getApiPatientInforGetKohiInfByPtId?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceAiChatGetKohiInfByPtIdResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceAiChatGetKohiInfByPtIdResponse";
      data?: Array<{
        __typename?: "DomainModelsInsuranceAiChatKohiInfDto";
        checkDate?: number;
        endDate?: number;
        hokenId?: number;
        hokenNameCd?: string;
        hokenNo?: number;
        hokenSbtKbn?: number;
        houbetu?: string;
        isDefault?: boolean;
        isDeleted?: number;
        isEmptyModel?: boolean;
        isExpirated?: boolean;
        jyukyusyaNo?: string;
        kofuDate?: number;
        kohiName?: string;
      }>;
    };
  };
};

export type GetApiPdfCreatorExportKarte1ByIdQueryVariables = Types.Exact<{
  hokenId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi1Id?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi2Id?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi3Id?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kohi4Id?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  syuByomei?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  tenkiByomei?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiPdfCreatorExportKarte1ByIdQuery = {
  __typename?: "query_root";
  getApiPdfCreatorExportKarte1ById?: {
    __typename?: "ExportKarte1ByIdRespone";
    fileUrl?: string;
    message?: string;
    status?: number;
  };
};

export type GetApiPdfCreatorExportKarte2QueryVariables = Types.Exact<{
  deletedOdrVisibilitySetting?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  emptyMode?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  endDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  hpId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  isCheckedApproved?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedDoctor?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedEndTime?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedHideOrder?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedHoken?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedHokenJibai?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedHokenJihi?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedHokenRousai?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedInputDate?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedJihi?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedJihiRece?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedSetName?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedStartTime?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedSyosai?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isCheckedVisitingTime?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isIncludeTempSave?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isIppanNameChecked?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isUketsukeNameChecked?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  startDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  includeDraft?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isGetVersionData?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type GetApiPdfCreatorExportKarte2Query = {
  __typename?: "query_root";
  getApiPdfCreatorExportKarte2?: {
    __typename?: "getApiPdfCreatorExportKarte2Response";
    fileUrl?: string;
    message?: string;
    status?: number;
  };
};

export type GetApiPdfCreatorExportKarte3QueryVariables = Types.Exact<{
  endSinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  includeHoken?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  includeJihi?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  startSinYm?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiPdfCreatorExportKarte3Query = {
  __typename?: "query_root";
  getApiPdfCreatorExportKarte3: string;
};

export const GetApiPdfCreatorExportKarte1Document = gql`
  query getApiPdfCreatorExportKarte1(
    $hokenPid: Int
    $ptId: BigInt
    $sinDate: Int
    $syuByomei: Boolean
    $tenkiByomei: Boolean
  ) {
    getApiPdfCreatorExportKarte1(
      hokenPid: $hokenPid
      ptId: $ptId
      sinDate: $sinDate
      syuByomei: $syuByomei
      tenkiByomei: $tenkiByomei
    ) {
      fileUrl
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorExportKarte1Query__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorExportKarte1Query` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorExportKarte1Query` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorExportKarte1Query({
 *   variables: {
 *      hokenPid: // value for 'hokenPid'
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      syuByomei: // value for 'syuByomei'
 *      tenkiByomei: // value for 'tenkiByomei'
 *   },
 * });
 */
export function useGetApiPdfCreatorExportKarte1Query(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorExportKarte1Query,
    GetApiPdfCreatorExportKarte1QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorExportKarte1Query,
    GetApiPdfCreatorExportKarte1QueryVariables
  >(GetApiPdfCreatorExportKarte1Document, options);
}
export function useGetApiPdfCreatorExportKarte1LazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorExportKarte1Query,
    GetApiPdfCreatorExportKarte1QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorExportKarte1Query,
    GetApiPdfCreatorExportKarte1QueryVariables
  >(GetApiPdfCreatorExportKarte1Document, options);
}
export function useGetApiPdfCreatorExportKarte1SuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorExportKarte1Query,
    GetApiPdfCreatorExportKarte1QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorExportKarte1Query,
    GetApiPdfCreatorExportKarte1QueryVariables
  >(GetApiPdfCreatorExportKarte1Document, options);
}
export type GetApiPdfCreatorExportKarte1QueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte1Query
>;
export type GetApiPdfCreatorExportKarte1LazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte1LazyQuery
>;
export type GetApiPdfCreatorExportKarte1SuspenseQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte1SuspenseQuery
>;
export type GetApiPdfCreatorExportKarte1QueryResult = Apollo.QueryResult<
  GetApiPdfCreatorExportKarte1Query,
  GetApiPdfCreatorExportKarte1QueryVariables
>;
export const GetApiPatientInforGetHokenPatternByPtIdKarteDocument = gql`
  query getApiPatientInforGetHokenPatternByPtIdKarte(
    $isOdrInf: Boolean
    $ptId: BigInt
    $sinDate: Int
  ) {
    getApiPatientInforGetHokenPatternByPtId(
      isOdrInf: $isOdrInf
      ptId: $ptId
      sinDate: $sinDate
    ) {
      data {
        data {
          hokenId
          hokenKbn
          hokenPid
          patternName
          ptId
          seqNo
          hokenInf {
            hokenId
            hokenKbn
            hokenSbtKbn
            houbetu
            hokenMstHokenNameCd
            hokenNoMaster
            hokenSentaku
          }
          kohi1 {
            hokenId
            kohiName
            isExpirated
          }
          kohi2 {
            hokenId
            kohiName
            isExpirated
          }
          kohi3 {
            hokenId
            kohiName
            isExpirated
          }
          kohi4 {
            hokenId
            kohiName
            isExpirated
          }
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetHokenPatternByPtIdKarteQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetHokenPatternByPtIdKarteQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetHokenPatternByPtIdKarteQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetHokenPatternByPtIdKarteQuery({
 *   variables: {
 *      isOdrInf: // value for 'isOdrInf'
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiPatientInforGetHokenPatternByPtIdKarteQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetHokenPatternByPtIdKarteQuery,
    GetApiPatientInforGetHokenPatternByPtIdKarteQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetHokenPatternByPtIdKarteQuery,
    GetApiPatientInforGetHokenPatternByPtIdKarteQueryVariables
  >(GetApiPatientInforGetHokenPatternByPtIdKarteDocument, options);
}
export function useGetApiPatientInforGetHokenPatternByPtIdKarteLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetHokenPatternByPtIdKarteQuery,
    GetApiPatientInforGetHokenPatternByPtIdKarteQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetHokenPatternByPtIdKarteQuery,
    GetApiPatientInforGetHokenPatternByPtIdKarteQueryVariables
  >(GetApiPatientInforGetHokenPatternByPtIdKarteDocument, options);
}
export function useGetApiPatientInforGetHokenPatternByPtIdKarteSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetHokenPatternByPtIdKarteQuery,
    GetApiPatientInforGetHokenPatternByPtIdKarteQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetHokenPatternByPtIdKarteQuery,
    GetApiPatientInforGetHokenPatternByPtIdKarteQueryVariables
  >(GetApiPatientInforGetHokenPatternByPtIdKarteDocument, options);
}
export type GetApiPatientInforGetHokenPatternByPtIdKarteQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetHokenPatternByPtIdKarteQuery>;
export type GetApiPatientInforGetHokenPatternByPtIdKarteLazyQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetHokenPatternByPtIdKarteLazyQuery>;
export type GetApiPatientInforGetHokenPatternByPtIdKarteSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiPatientInforGetHokenPatternByPtIdKarteSuspenseQuery
  >;
export type GetApiPatientInforGetHokenPatternByPtIdKarteQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforGetHokenPatternByPtIdKarteQuery,
    GetApiPatientInforGetHokenPatternByPtIdKarteQueryVariables
  >;
export const GetApiPatientInforGetKohiInfByPtIdKarteDocument = gql`
  query getApiPatientInforGetKohiInfByPtIdKarte(
    $isOdrInf: Boolean
    $ptId: BigInt
    $sinDate: Int
  ) {
    getApiPatientInforGetKohiInfByPtId(
      isOdrInf: $isOdrInf
      ptId: $ptId
      sinDate: $sinDate
    ) {
      data {
        data {
          checkDate
          endDate
          hokenId
          hokenNameCd
          hokenNo
          hokenSbtKbn
          houbetu
          isDefault
          isDeleted
          isEmptyModel
          isExpirated
          jyukyusyaNo
          kofuDate
          kohiName
        }
      }
    }
  }
`;

/**
 * __useGetApiPatientInforGetKohiInfByPtIdKarteQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetKohiInfByPtIdKarteQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetKohiInfByPtIdKarteQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetKohiInfByPtIdKarteQuery({
 *   variables: {
 *      isOdrInf: // value for 'isOdrInf'
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiPatientInforGetKohiInfByPtIdKarteQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetKohiInfByPtIdKarteQuery,
    GetApiPatientInforGetKohiInfByPtIdKarteQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetKohiInfByPtIdKarteQuery,
    GetApiPatientInforGetKohiInfByPtIdKarteQueryVariables
  >(GetApiPatientInforGetKohiInfByPtIdKarteDocument, options);
}
export function useGetApiPatientInforGetKohiInfByPtIdKarteLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetKohiInfByPtIdKarteQuery,
    GetApiPatientInforGetKohiInfByPtIdKarteQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetKohiInfByPtIdKarteQuery,
    GetApiPatientInforGetKohiInfByPtIdKarteQueryVariables
  >(GetApiPatientInforGetKohiInfByPtIdKarteDocument, options);
}
export function useGetApiPatientInforGetKohiInfByPtIdKarteSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetKohiInfByPtIdKarteQuery,
    GetApiPatientInforGetKohiInfByPtIdKarteQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetKohiInfByPtIdKarteQuery,
    GetApiPatientInforGetKohiInfByPtIdKarteQueryVariables
  >(GetApiPatientInforGetKohiInfByPtIdKarteDocument, options);
}
export type GetApiPatientInforGetKohiInfByPtIdKarteQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetKohiInfByPtIdKarteQuery
>;
export type GetApiPatientInforGetKohiInfByPtIdKarteLazyQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetKohiInfByPtIdKarteLazyQuery>;
export type GetApiPatientInforGetKohiInfByPtIdKarteSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetKohiInfByPtIdKarteSuspenseQuery>;
export type GetApiPatientInforGetKohiInfByPtIdKarteQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforGetKohiInfByPtIdKarteQuery,
    GetApiPatientInforGetKohiInfByPtIdKarteQueryVariables
  >;
export const GetApiPdfCreatorExportKarte1ByIdDocument = gql`
  query getApiPdfCreatorExportKarte1ById(
    $hokenId: Int
    $kohi1Id: Int
    $kohi2Id: Int
    $kohi3Id: Int
    $kohi4Id: Int
    $ptId: BigInt
    $sinDate: Int
    $syuByomei: Boolean
    $tenkiByomei: Boolean
  ) {
    getApiPdfCreatorExportKarte1ById(
      hokenId: $hokenId
      kohi1Id: $kohi1Id
      kohi2Id: $kohi2Id
      kohi3Id: $kohi3Id
      kohi4Id: $kohi4Id
      ptId: $ptId
      sinDate: $sinDate
      syuByomei: $syuByomei
      tenkiByomei: $tenkiByomei
    ) {
      fileUrl
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorExportKarte1ByIdQuery__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorExportKarte1ByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorExportKarte1ByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorExportKarte1ByIdQuery({
 *   variables: {
 *      hokenId: // value for 'hokenId'
 *      kohi1Id: // value for 'kohi1Id'
 *      kohi2Id: // value for 'kohi2Id'
 *      kohi3Id: // value for 'kohi3Id'
 *      kohi4Id: // value for 'kohi4Id'
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      syuByomei: // value for 'syuByomei'
 *      tenkiByomei: // value for 'tenkiByomei'
 *   },
 * });
 */
export function useGetApiPdfCreatorExportKarte1ByIdQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorExportKarte1ByIdQuery,
    GetApiPdfCreatorExportKarte1ByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorExportKarte1ByIdQuery,
    GetApiPdfCreatorExportKarte1ByIdQueryVariables
  >(GetApiPdfCreatorExportKarte1ByIdDocument, options);
}
export function useGetApiPdfCreatorExportKarte1ByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorExportKarte1ByIdQuery,
    GetApiPdfCreatorExportKarte1ByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorExportKarte1ByIdQuery,
    GetApiPdfCreatorExportKarte1ByIdQueryVariables
  >(GetApiPdfCreatorExportKarte1ByIdDocument, options);
}
export function useGetApiPdfCreatorExportKarte1ByIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorExportKarte1ByIdQuery,
    GetApiPdfCreatorExportKarte1ByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorExportKarte1ByIdQuery,
    GetApiPdfCreatorExportKarte1ByIdQueryVariables
  >(GetApiPdfCreatorExportKarte1ByIdDocument, options);
}
export type GetApiPdfCreatorExportKarte1ByIdQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte1ByIdQuery
>;
export type GetApiPdfCreatorExportKarte1ByIdLazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte1ByIdLazyQuery
>;
export type GetApiPdfCreatorExportKarte1ByIdSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPdfCreatorExportKarte1ByIdSuspenseQuery>;
export type GetApiPdfCreatorExportKarte1ByIdQueryResult = Apollo.QueryResult<
  GetApiPdfCreatorExportKarte1ByIdQuery,
  GetApiPdfCreatorExportKarte1ByIdQueryVariables
>;
export const GetApiPdfCreatorExportKarte2Document = gql`
  query getApiPdfCreatorExportKarte2(
    $deletedOdrVisibilitySetting: Int
    $emptyMode: Boolean
    $endDate: Int
    $hpId: Int
    $isCheckedApproved: Boolean
    $isCheckedDoctor: Boolean
    $isCheckedEndTime: Boolean
    $isCheckedHideOrder: Boolean
    $isCheckedHoken: Boolean
    $isCheckedHokenJibai: Boolean
    $isCheckedHokenJihi: Boolean
    $isCheckedHokenRousai: Boolean
    $isCheckedInputDate: Boolean
    $isCheckedJihi: Boolean
    $isCheckedJihiRece: Boolean
    $isCheckedSetName: Boolean
    $isCheckedStartTime: Boolean
    $isCheckedSyosai: Boolean
    $isCheckedVisitingTime: Boolean
    $isIncludeTempSave: Boolean
    $isIppanNameChecked: Boolean
    $isUketsukeNameChecked: Boolean
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
    $startDate: Int
    $includeDraft: Boolean
    $isGetVersionData: Boolean
  ) {
    getApiPdfCreatorExportKarte2(
      deletedOdrVisibilitySetting: $deletedOdrVisibilitySetting
      emptyMode: $emptyMode
      endDate: $endDate
      hpId: $hpId
      isCheckedApproved: $isCheckedApproved
      isCheckedDoctor: $isCheckedDoctor
      isCheckedEndTime: $isCheckedEndTime
      isCheckedHideOrder: $isCheckedHideOrder
      isCheckedHoken: $isCheckedHoken
      isCheckedHokenJibai: $isCheckedHokenJibai
      isCheckedHokenJihi: $isCheckedHokenJihi
      isCheckedHokenRousai: $isCheckedHokenRousai
      isCheckedInputDate: $isCheckedInputDate
      isCheckedJihi: $isCheckedJihi
      isCheckedJihiRece: $isCheckedJihiRece
      isCheckedSetName: $isCheckedSetName
      isCheckedStartTime: $isCheckedStartTime
      isCheckedSyosai: $isCheckedSyosai
      isCheckedVisitingTime: $isCheckedVisitingTime
      isIncludeTempSave: $isIncludeTempSave
      isIppanNameChecked: $isIppanNameChecked
      isUketsukeNameChecked: $isUketsukeNameChecked
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
      startDate: $startDate
      includeDraft: $includeDraft
      isGetVersionData: $isGetVersionData
    ) {
      fileUrl
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorExportKarte2Query__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorExportKarte2Query` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorExportKarte2Query` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorExportKarte2Query({
 *   variables: {
 *      deletedOdrVisibilitySetting: // value for 'deletedOdrVisibilitySetting'
 *      emptyMode: // value for 'emptyMode'
 *      endDate: // value for 'endDate'
 *      hpId: // value for 'hpId'
 *      isCheckedApproved: // value for 'isCheckedApproved'
 *      isCheckedDoctor: // value for 'isCheckedDoctor'
 *      isCheckedEndTime: // value for 'isCheckedEndTime'
 *      isCheckedHideOrder: // value for 'isCheckedHideOrder'
 *      isCheckedHoken: // value for 'isCheckedHoken'
 *      isCheckedHokenJibai: // value for 'isCheckedHokenJibai'
 *      isCheckedHokenJihi: // value for 'isCheckedHokenJihi'
 *      isCheckedHokenRousai: // value for 'isCheckedHokenRousai'
 *      isCheckedInputDate: // value for 'isCheckedInputDate'
 *      isCheckedJihi: // value for 'isCheckedJihi'
 *      isCheckedJihiRece: // value for 'isCheckedJihiRece'
 *      isCheckedSetName: // value for 'isCheckedSetName'
 *      isCheckedStartTime: // value for 'isCheckedStartTime'
 *      isCheckedSyosai: // value for 'isCheckedSyosai'
 *      isCheckedVisitingTime: // value for 'isCheckedVisitingTime'
 *      isIncludeTempSave: // value for 'isIncludeTempSave'
 *      isIppanNameChecked: // value for 'isIppanNameChecked'
 *      isUketsukeNameChecked: // value for 'isUketsukeNameChecked'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *      startDate: // value for 'startDate'
 *      includeDraft: // value for 'includeDraft'
 *      isGetVersionData: // value for 'isGetVersionData'
 *   },
 * });
 */
export function useGetApiPdfCreatorExportKarte2Query(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorExportKarte2Query,
    GetApiPdfCreatorExportKarte2QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorExportKarte2Query,
    GetApiPdfCreatorExportKarte2QueryVariables
  >(GetApiPdfCreatorExportKarte2Document, options);
}
export function useGetApiPdfCreatorExportKarte2LazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorExportKarte2Query,
    GetApiPdfCreatorExportKarte2QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorExportKarte2Query,
    GetApiPdfCreatorExportKarte2QueryVariables
  >(GetApiPdfCreatorExportKarte2Document, options);
}
export function useGetApiPdfCreatorExportKarte2SuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorExportKarte2Query,
    GetApiPdfCreatorExportKarte2QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorExportKarte2Query,
    GetApiPdfCreatorExportKarte2QueryVariables
  >(GetApiPdfCreatorExportKarte2Document, options);
}
export type GetApiPdfCreatorExportKarte2QueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte2Query
>;
export type GetApiPdfCreatorExportKarte2LazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte2LazyQuery
>;
export type GetApiPdfCreatorExportKarte2SuspenseQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte2SuspenseQuery
>;
export type GetApiPdfCreatorExportKarte2QueryResult = Apollo.QueryResult<
  GetApiPdfCreatorExportKarte2Query,
  GetApiPdfCreatorExportKarte2QueryVariables
>;
export const GetApiPdfCreatorExportKarte3Document = gql`
  query getApiPdfCreatorExportKarte3(
    $endSinYm: Int
    $includeHoken: Boolean
    $includeJihi: Boolean
    $ptId: BigInt
    $startSinYm: Int
  ) {
    getApiPdfCreatorExportKarte3(
      endSinYm: $endSinYm
      includeHoken: $includeHoken
      includeJihi: $includeJihi
      ptId: $ptId
      startSinYm: $startSinYm
    )
  }
`;

/**
 * __useGetApiPdfCreatorExportKarte3Query__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorExportKarte3Query` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorExportKarte3Query` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorExportKarte3Query({
 *   variables: {
 *      endSinYm: // value for 'endSinYm'
 *      includeHoken: // value for 'includeHoken'
 *      includeJihi: // value for 'includeJihi'
 *      ptId: // value for 'ptId'
 *      startSinYm: // value for 'startSinYm'
 *   },
 * });
 */
export function useGetApiPdfCreatorExportKarte3Query(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorExportKarte3Query,
    GetApiPdfCreatorExportKarte3QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorExportKarte3Query,
    GetApiPdfCreatorExportKarte3QueryVariables
  >(GetApiPdfCreatorExportKarte3Document, options);
}
export function useGetApiPdfCreatorExportKarte3LazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorExportKarte3Query,
    GetApiPdfCreatorExportKarte3QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorExportKarte3Query,
    GetApiPdfCreatorExportKarte3QueryVariables
  >(GetApiPdfCreatorExportKarte3Document, options);
}
export function useGetApiPdfCreatorExportKarte3SuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorExportKarte3Query,
    GetApiPdfCreatorExportKarte3QueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorExportKarte3Query,
    GetApiPdfCreatorExportKarte3QueryVariables
  >(GetApiPdfCreatorExportKarte3Document, options);
}
export type GetApiPdfCreatorExportKarte3QueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte3Query
>;
export type GetApiPdfCreatorExportKarte3LazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte3LazyQuery
>;
export type GetApiPdfCreatorExportKarte3SuspenseQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportKarte3SuspenseQuery
>;
export type GetApiPdfCreatorExportKarte3QueryResult = Apollo.QueryResult<
  GetApiPdfCreatorExportKarte3Query,
  GetApiPdfCreatorExportKarte3QueryVariables
>;
