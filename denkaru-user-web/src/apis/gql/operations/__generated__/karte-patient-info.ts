import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiPatientInforGetBasicPatientInfoQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiPatientInforGetBasicPatientInfoQuery = {
  __typename?: "query_root";
  getApiPatientInforGetBasicPatientInfo?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforGetBasicPatientInfoResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforGetBasicPatientInfoResponse";
      patientInfos?: {
        __typename?: "DomainModelsPatientInforBasicPatientInfoModel";
        patientInforModel?: {
          __typename?: "DomainModelsPatientInforPatientInforModel";
          age?: string;
          birthday?: number;
          birthdayDisplay?: string;
          comment?: string;
          deathDate?: number;
          email?: string;
          firstVisitDate?: number;
          homeAddress1?: string;
          homeAddress2?: string;
          homePost?: string;
          houmonAgreed?: string;
          hpId?: number;
          isDead?: number;
          isRyosyoDetail?: number;
          isShowKyuSeiName?: boolean;
          isTester?: number;
          job?: string;
          kanaName?: string;
          lastAppointmentDepartment?: string;
          lastVisitDate?: number;
          limitConsFlg?: number;
          mail?: string;
          mainHokenPid?: number;
          memo?: string;
          name?: string;
          nextAppointmentDepartment?: string;
          officeAddress1?: string;
          tel2?: string;
          tel1?: string;
          zokugara?: string;
          sinDate?: number;
          officeAddress2?: string;
          officeMemo?: string;
          officePost?: string;
          officeName?: string;
          officeTel?: string;
          primaryDoctor?: number;
          ptId?: string;
          ptNum?: string;
          rainCount?: string;
          rainCountInt?: number;
          referenceNo?: string;
          renrakuAddress1?: string;
          renrakuAddress2?: string;
          renrakuMemo?: string;
          renrakuName?: string;
          renrakuName2?: string;
          renrakuPost?: string;
          renrakuTel?: string;
          renrakuTel2?: string;
          seqNo?: string;
          setanusi?: string;
          sex?: number;
        };
        portalCustomerModel?: {
          __typename?: "DomainModelsPortalCustomerPortalCustomerModel";
          aid?: string;
          bid?: string;
          birthday?: string;
          cid?: string;
          customerId?: number;
          endDate?: string;
          insureCode?: string;
          insuredBranchCode?: string;
          insuredCustomerCode?: string;
          insuredOrgCode?: string;
          isDeleted?: number;
          isReceiveNotifications?: boolean;
          isSameParentAddress?: number;
          kanaName?: string;
          medicalCertPayerCode?: string;
          medicalCertReceiptCode?: string;
          name?: string;
          parentId?: number;
          startDate?: string;
          status?: number;
          telephone?: string;
          gender?: number;
        };
        portalCustomerDeliveryAddressModel?: {
          __typename?: "DomainModelsNewFolderPortalCustomerDeliveryAddressModel";
          postCode?: string;
          deliveryAddressId?: number;
          customerId?: number;
          address2?: string;
          address1?: string;
        };
        portalCustomerLoginModel?: {
          __typename?: "DomainModelsPortalCustomerLoginPortalCustomerLoginModel";
          customerId?: number;
          email?: string;
          loginId?: number;
        };
      };
    };
  };
};

export type PostApiPatientInforEditBasicInfoOverwriteMutationVariables =
  Types.Exact<{
    address1?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    address2?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    birthday?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    homePost?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    isAddressUpdated?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isBirthdayUpdated?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isHomePostUpdated?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isKanaNameUpdated?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isMailUpdated?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isNameUpdated?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isSexUpdated?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isTelUpdated?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    kanaName?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    mail?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    name?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sex?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    tel?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  }>;

export type PostApiPatientInforEditBasicInfoOverwriteMutation = {
  __typename?: "mutation_root";
  postApiPatientInforEditBasicInfoOverwrite?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPatientInforEditBasicInfoOverwriteResponse";
    message?: string;
    data?: {
      __typename?: "EmrCloudApiResponsesPatientInforEditBasicInfoOverwriteResponse";
      status?: number;
    };
  };
};

export const GetApiPatientInforGetBasicPatientInfoDocument = gql`
  query getApiPatientInforGetBasicPatientInfo($ptId: BigInt) {
    getApiPatientInforGetBasicPatientInfo(ptId: $ptId) {
      data {
        patientInfos {
          patientInforModel {
            age
            birthday
            birthdayDisplay
            comment
            deathDate
            email
            firstVisitDate
            homeAddress1
            homeAddress2
            homePost
            houmonAgreed
            hpId
            isDead
            isRyosyoDetail
            isShowKyuSeiName
            isTester
            job
            kanaName
            lastAppointmentDepartment
            lastVisitDate
            limitConsFlg
            mail
            mainHokenPid
            memo
            name
            nextAppointmentDepartment
            officeAddress1
            tel2
            tel1
            zokugara
            sinDate
            officeAddress2
            officeMemo
            officePost
            officeName
            officeTel
            primaryDoctor
            ptId
            ptNum
            rainCount
            rainCountInt
            referenceNo
            renrakuAddress1
            renrakuAddress2
            renrakuMemo
            renrakuName
            renrakuName2
            renrakuPost
            renrakuTel
            renrakuTel2
            seqNo
            setanusi
            sex
          }
          portalCustomerModel {
            aid
            bid
            birthday
            cid
            customerId
            endDate
            insureCode
            insuredBranchCode
            insuredCustomerCode
            insuredOrgCode
            isDeleted
            isReceiveNotifications
            isSameParentAddress
            kanaName
            medicalCertPayerCode
            medicalCertReceiptCode
            name
            parentId
            startDate
            status
            telephone
            gender
          }
          portalCustomerDeliveryAddressModel {
            postCode
            deliveryAddressId
            customerId
            address2
            address1
          }
          portalCustomerLoginModel {
            customerId
            email
            loginId
          }
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiPatientInforGetBasicPatientInfoQuery__
 *
 * To run a query within a React component, call `useGetApiPatientInforGetBasicPatientInfoQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPatientInforGetBasicPatientInfoQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPatientInforGetBasicPatientInfoQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *   },
 * });
 */
export function useGetApiPatientInforGetBasicPatientInfoQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPatientInforGetBasicPatientInfoQuery,
    GetApiPatientInforGetBasicPatientInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPatientInforGetBasicPatientInfoQuery,
    GetApiPatientInforGetBasicPatientInfoQueryVariables
  >(GetApiPatientInforGetBasicPatientInfoDocument, options);
}
export function useGetApiPatientInforGetBasicPatientInfoLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPatientInforGetBasicPatientInfoQuery,
    GetApiPatientInforGetBasicPatientInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPatientInforGetBasicPatientInfoQuery,
    GetApiPatientInforGetBasicPatientInfoQueryVariables
  >(GetApiPatientInforGetBasicPatientInfoDocument, options);
}
export function useGetApiPatientInforGetBasicPatientInfoSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPatientInforGetBasicPatientInfoQuery,
    GetApiPatientInforGetBasicPatientInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPatientInforGetBasicPatientInfoQuery,
    GetApiPatientInforGetBasicPatientInfoQueryVariables
  >(GetApiPatientInforGetBasicPatientInfoDocument, options);
}
export type GetApiPatientInforGetBasicPatientInfoQueryHookResult = ReturnType<
  typeof useGetApiPatientInforGetBasicPatientInfoQuery
>;
export type GetApiPatientInforGetBasicPatientInfoLazyQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetBasicPatientInfoLazyQuery>;
export type GetApiPatientInforGetBasicPatientInfoSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPatientInforGetBasicPatientInfoSuspenseQuery>;
export type GetApiPatientInforGetBasicPatientInfoQueryResult =
  Apollo.QueryResult<
    GetApiPatientInforGetBasicPatientInfoQuery,
    GetApiPatientInforGetBasicPatientInfoQueryVariables
  >;
export const PostApiPatientInforEditBasicInfoOverwriteDocument = gql`
  mutation postApiPatientInforEditBasicInfoOverwrite(
    $address1: String
    $address2: String
    $birthday: Int
    $homePost: String
    $isAddressUpdated: Boolean
    $isBirthdayUpdated: Boolean
    $isHomePostUpdated: Boolean
    $isKanaNameUpdated: Boolean
    $isMailUpdated: Boolean
    $isNameUpdated: Boolean
    $isSexUpdated: Boolean
    $isTelUpdated: Boolean
    $kanaName: String
    $mail: String
    $name: String
    $ptId: BigInt
    $sex: Int
    $sinDate: Int
    $tel: String
  ) {
    postApiPatientInforEditBasicInfoOverwrite(
      emrCloudApiRequestsPatientInforBasicPatientInfoEditBasicInfoOverwriteRequestInput: {
        address1: $address1
        address2: $address2
        homePost: $homePost
        isAddressUpdated: $isAddressUpdated
        isBirthdayUpdated: $isBirthdayUpdated
        isHomePostUpdated: $isHomePostUpdated
        isKanaNameUpdated: $isKanaNameUpdated
        isMailUpdated: $isMailUpdated
        isNameUpdated: $isNameUpdated
        isSexUpdated: $isSexUpdated
        kanaName: $kanaName
        mail: $mail
        name: $name
        ptId: $ptId
        sex: $sex
        sinDate: $sinDate
        tel: $tel
        birthday: $birthday
        isTelUpdated: $isTelUpdated
      }
    ) {
      data {
        status
      }
      message
    }
  }
`;
export type PostApiPatientInforEditBasicInfoOverwriteMutationFn =
  Apollo.MutationFunction<
    PostApiPatientInforEditBasicInfoOverwriteMutation,
    PostApiPatientInforEditBasicInfoOverwriteMutationVariables
  >;

/**
 * __usePostApiPatientInforEditBasicInfoOverwriteMutation__
 *
 * To run a mutation, you first call `usePostApiPatientInforEditBasicInfoOverwriteMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPatientInforEditBasicInfoOverwriteMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPatientInforEditBasicInfoOverwriteMutation, { data, loading, error }] = usePostApiPatientInforEditBasicInfoOverwriteMutation({
 *   variables: {
 *      address1: // value for 'address1'
 *      address2: // value for 'address2'
 *      birthday: // value for 'birthday'
 *      homePost: // value for 'homePost'
 *      isAddressUpdated: // value for 'isAddressUpdated'
 *      isBirthdayUpdated: // value for 'isBirthdayUpdated'
 *      isHomePostUpdated: // value for 'isHomePostUpdated'
 *      isKanaNameUpdated: // value for 'isKanaNameUpdated'
 *      isMailUpdated: // value for 'isMailUpdated'
 *      isNameUpdated: // value for 'isNameUpdated'
 *      isSexUpdated: // value for 'isSexUpdated'
 *      isTelUpdated: // value for 'isTelUpdated'
 *      kanaName: // value for 'kanaName'
 *      mail: // value for 'mail'
 *      name: // value for 'name'
 *      ptId: // value for 'ptId'
 *      sex: // value for 'sex'
 *      sinDate: // value for 'sinDate'
 *      tel: // value for 'tel'
 *   },
 * });
 */
export function usePostApiPatientInforEditBasicInfoOverwriteMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPatientInforEditBasicInfoOverwriteMutation,
    PostApiPatientInforEditBasicInfoOverwriteMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPatientInforEditBasicInfoOverwriteMutation,
    PostApiPatientInforEditBasicInfoOverwriteMutationVariables
  >(PostApiPatientInforEditBasicInfoOverwriteDocument, options);
}
export type PostApiPatientInforEditBasicInfoOverwriteMutationHookResult =
  ReturnType<typeof usePostApiPatientInforEditBasicInfoOverwriteMutation>;
export type PostApiPatientInforEditBasicInfoOverwriteMutationResult =
  Apollo.MutationResult<PostApiPatientInforEditBasicInfoOverwriteMutation>;
export type PostApiPatientInforEditBasicInfoOverwriteMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPatientInforEditBasicInfoOverwriteMutation,
    PostApiPatientInforEditBasicInfoOverwriteMutationVariables
  >;
