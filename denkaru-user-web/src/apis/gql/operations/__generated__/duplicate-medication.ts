import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiEpsGetPreRegistrationDataMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsEpsGetPreRegistrationDataRequestInput>;
}>;

export type PostApiEpsGetPreRegistrationDataMutation = {
  __typename?: "mutation_root";
  postApiEpsGetPreRegistrationData?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsGetPreRegistrationResponseData";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsGetPreRegistrationResponseData";
      preRegistrationCheckingModel?: {
        __typename?: "DomainModelsEpsPreRegistrationCheckingModel";
        wasConfirmedOnline?: boolean;
        epsPrescriptionModel?: Array<{
          __typename?: "DomainModelsEpsPrescriptionEpsPrescriptionModel";
          accessCode?: string;
          bango?: string;
          createDate?: string;
          createId?: number;
          deleteReasonDisplay?: string;
          deletedDate?: string;
          deletedReason?: number;
          dispensingDate?: number;
          dispensingDateDisplay?: string;
          edaNo?: string;
          epsUpdateDateTime?: string;
          hokenDisplay?: string;
          hokensyaNo?: string;
          hpId?: number;
          id?: number;
          issueType?: number;
          issueTypeDisplay?: string;
          kaId?: number;
          kaSName?: string;
          kigo?: string;
          kohiFutansyaNo?: string;
          kohiJyukyusyaNo?: string;
          messageFlag?: string;
          pharmacyName?: string;
          prescriptionDocument?: string;
          prescriptionId?: string;
          ptId?: string;
          ptName?: string;
          ptNum?: string;
          ptNumDisplay?: string;
          raiinNo?: string;
          refileCount?: number;
          refill?: string;
          resultType?: number;
          resultTypeDisplay?: string;
          seqNo?: string;
          sinDate?: number;
          sinDateDisplay?: string;
          status?: number;
          statusDisplay?: string;
          tantoId?: number;
          tantoName?: string;
          updateDate?: string;
          updateId?: number;
          epsDispensing?: {
            __typename?: "DomainModelsEpsDispensingEpsDispensingModel";
            bango?: string;
            cancelReason?: string;
            createDate?: string;
            createId?: number;
            createMachine?: string;
            dispensingDate?: number;
            dispensingDocument?: string;
            dispensingResultId?: string;
            dispensingTimes?: number;
            edaNo?: string;
            epsUpdateDateTime?: string;
            hokensyaNo?: string;
            hpId?: number;
            id?: string;
            isDeleted?: number;
            kigo?: string;
            kohiFutansyaNo?: string;
            kohiJyukyusyaNo?: string;
            messageFlg?: number;
            prescriptionId?: string;
            ptId?: string;
            receptionPharmacyName?: string;
            resultType?: number;
            updateDate?: string;
            updateId?: number;
            updateMachine?: string;
          };
          epsDispensingModel?: Array<{
            __typename?: "DomainModelsEpsDispensingEpsDispensingModel";
            bango?: string;
            cancelReason?: string;
            createDate?: string;
            createId?: number;
            createMachine?: string;
            dispensingDate?: number;
            dispensingDocument?: string;
            dispensingResultId?: string;
            dispensingTimes?: number;
            edaNo?: string;
            epsUpdateDateTime?: string;
            hokensyaNo?: string;
            hpId?: number;
            id?: string;
            isDeleted?: number;
            kigo?: string;
            kohiFutansyaNo?: string;
            kohiJyukyusyaNo?: string;
            messageFlg?: number;
            prescriptionId?: string;
            ptId?: string;
            receptionPharmacyName?: string;
            resultType?: number;
            updateDate?: string;
            updateId?: number;
            updateMachine?: string;
          }>;
          kaInf?: {
            __typename?: "DomainModelsKaKaMstModel";
            id?: string;
            kaId?: number;
            kaName?: string;
            kaSname?: string;
            receKaCd?: string;
            sortNo?: number;
            yousikiKaCd?: string;
          };
          ptInf?: {
            __typename?: "DomainModelsPatientInforPatientInforModel";
            age?: string;
            birthday?: number;
            birthdayDisplay?: string;
            comment?: string;
            deathDate?: number;
            email?: string;
            firstVisitDate?: number;
            homeAddress1?: string;
            homeAddress2?: string;
            homePost?: string;
            houmonAgreed?: string;
            hpId?: number;
            isDead?: number;
            isRyosyoDetail?: number;
            isShowKyuSeiName?: boolean;
            isTester?: number;
            job?: string;
            kanaName?: string;
            lastAppointmentDepartment?: string;
            lastVisitDate?: number;
            limitConsFlg?: number;
            mail?: string;
            mainHokenPid?: number;
            memo?: string;
            name?: string;
            nextAppointmentDepartment?: string;
            officeAddress1?: string;
            officeAddress2?: string;
            officeMemo?: string;
            officeName?: string;
            officePost?: string;
            officeTel?: string;
            primaryDoctor?: number;
            ptId?: string;
            ptNum?: string;
            rainCount?: string;
            rainCountInt?: number;
            referenceNo?: string;
            renrakuAddress1?: string;
            renrakuAddress2?: string;
            renrakuMemo?: string;
            renrakuName?: string;
            renrakuName2?: string;
            renrakuPost?: string;
            renrakuTel?: string;
            renrakuTel2?: string;
            seqNo?: string;
            setanusi?: string;
            sex?: number;
            sinDate?: number;
            tel1?: string;
            tel2?: string;
            zokugara?: string;
          };
          tantoInf?: {
            __typename?: "DomainModelsUserUserMstModel";
            drName?: string;
            email?: string;
            emailUpdateDate?: string;
            endDate?: number;
            hpId?: number;
            id?: number;
            isDeleted?: number;
            isInitLoginId?: number;
            isInitPassword?: number;
            jobCd?: number;
            kaId?: number;
            kaSName?: string;
            kanaName?: string;
            loginId?: string;
            loginPass?: string;
            managerKbn?: number;
            mayakuLicenseNo?: string;
            missLoginCount?: number;
            name?: string;
            renkeiCd1?: string;
            sNameBinding?: string;
            sname?: string;
            sortNo?: number;
            startDate?: number;
            status?: number;
            userId?: number;
            functionMstModels?: Array<{
              __typename?: "DomainModelsUserFunctionMstModel";
              functionCd?: string;
              functionName?: string;
              jobCd?: number;
              permissions?: Array<{
                __typename?: "DomainModelsUserPermissionMstModel";
                functionCd?: string;
                permission?: number;
                permissionKey?: string;
              }>;
              userMstModel?: {
                __typename?: "DomainModelsUserUserPermissionModel";
                functionCd?: string;
                hpId?: number;
                isDefault?: boolean;
                permission?: number;
                userId?: number;
              };
            }>;
            permissions?: Array<{
              __typename?: "DomainModelsUserUserPermissionModel";
              functionCd?: string;
              hpId?: number;
              isDefault?: boolean;
              permission?: number;
              userId?: number;
            }>;
          };
        }>;
        odrInfs?: Array<{
          __typename?: "DomainModelsEpsEpsOrderInfModel";
          daysCnt?: number;
          hokenPid?: number;
          hpId?: number;
          id?: string;
          inoutKbn?: number;
          isDeleted?: number;
          odrKouiKbn?: number;
          orderContainsCommonNameNotInCommonNameMedicines?: boolean;
          ptId?: string;
          raiinNo?: string;
          rpName?: string;
          rpEdaNo?: string;
          rpNo?: string;
          santeiKbn?: number;
          sinDate?: number;
          sikyuKbn?: number;
          sortNo?: number;
          syohoSbt?: number;
          tosekiKbn?: number;
          hokenInfModel?: {
            __typename?: "DomainModelsInsuranceHokenInfModel";
            bango?: string;
            confirmDate?: number;
            edaNo?: string;
            endDate?: number;
            endDateSort?: number;
            futansyaNo?: string;
            genmenGaku?: number;
            genmenKbn?: number;
            genmenRate?: number;
            hasDateConfirmed?: boolean;
            hokenEdaNo?: number;
            hokenId?: number;
            hokenKbn?: number;
            hokenMstDisplayTextMaster?: string;
            hokenMstEndDate?: number;
            hokenMstFutanKbn?: number;
            hokenMstFutanRate?: number;
            hokenMstHoubetu?: string;
            hokenMstSbtKbn?: number;
            hokenMstStartDate?: number;
            hokenNo?: number;
            hokenSbtCd?: number;
            hokenSentaku?: string;
            hokensyaAddress?: string;
            hokensyaName?: string;
            hokensyaNo?: string;
            hokensyaTel?: string;
            honkeKbn?: number;
            houbetu?: string;
            hpId?: number;
            insuredName?: string;
            isAddHokenCheck?: boolean;
            isAddNew?: boolean;
            isDeleted?: number;
            isEmptyModel?: boolean;
            isExpirated?: boolean;
            isHaveHokenMst?: boolean;
            isHoken?: boolean;
            isJibai?: boolean;
            isJibaiOrRosai?: boolean;
            isJihi?: boolean;
            isKokuho?: boolean;
            isNoHoken?: boolean;
            isNotKenkoKanri?: boolean;
            isNotNenkin?: boolean;
            isNotRodo?: boolean;
            isReceKisaiOrNoHoken?: boolean;
            isRousai?: boolean;
            isShaho?: boolean;
            isShahoOrKokuho?: boolean;
            jibaiHokenName?: string;
            jibaiHokenTanto?: string;
            jibaiHokenTel?: string;
            jibaiJyusyouDate?: number;
            keizokuKbn?: number;
            kenkoKanriBango?: string;
            kigo?: string;
            kofuDate?: number;
            kogakuKbn?: number;
            lastDateConfirmed?: number;
            nenkinBango?: string;
            ptId?: string;
            rodoBango?: string;
            rousaiCityName?: string;
            rousaiJigyosyoName?: string;
            rousaiKantokuCd?: string;
            rousaiKofuNo?: string;
            rousaiPrefName?: string;
            rousaiReceCount?: number;
            rousaiRoudouCd?: string;
            rousaiSaigaiKbn?: number;
            rousaiSyobyoCd?: string;
            rousaiSyobyoDate?: number;
            ryoyoEndDate?: number;
            ryoyoStartDate?: number;
            seqNo?: string;
            sikakuDate?: number;
            sinDate?: number;
            startDate?: number;
            syokumuKbn?: number;
            tasukaiYm?: number;
            tokki1?: string;
            tokki2?: string;
            tokki3?: string;
            tokki4?: string;
            tokki5?: string;
            tokureiYm1?: number;
            tokureiYm2?: number;
            confirmDateList?: Array<{
              __typename?: "DomainModelsInsuranceConfirmDateModel";
              checkComment?: string;
              checkId?: number;
              checkMachine?: string;
              checkName?: string;
              confirmDate?: number;
              hokenGrp?: number;
              hokenId?: number;
              isDeleted?: number;
              onlineConfirmationId?: number;
              ptId?: string;
              seqNo?: string;
            }>;
            filingInfModels?: Array<{
              __typename?: "DomainModelsInsuranceFilingInfModel";
              fileId?: number;
              fileLink?: string;
            }>;
            hokenMst?: {
              __typename?: "DomainModelsInsuranceMstHokenMstModel";
              ageEnd?: number;
              ageStart?: number;
              calcSpKbn?: number;
              checkDigit?: number;
              countKbn?: number;
              dayLimitCount?: number;
              dayLimitFutan?: number;
              displayHokenNo?: string;
              displayTextMaster?: string;
              displayTextMasterWithoutHokenNo?: string;
              enTen?: number;
              endDate?: number;
              futanKbn?: number;
              futanRate?: number;
              futanYusen?: number;
              hokenEdaNo?: number;
              hokenKohiKbn?: number;
              hokenName?: string;
              hokenNameCd?: string;
              hokenNo?: number;
              hokenSName?: string;
              hokenSbtKbn?: number;
              houbetu?: string;
              houbetuDisplayText?: string;
              isAdded?: boolean;
              isFutansyaNoCheck?: number;
              isJyukyusyaNoCheck?: number;
              isLimitList?: number;
              isLimitListSum?: number;
              isOtherPrefValid?: number;
              isTokusyuNoCheck?: number;
              jyuKyuCheckDigit?: number;
              kaiFutangaku?: number;
              kaiLimitFutan?: number;
              kogakuHairyoKbn?: number;
              kogakuTekiyo?: number;
              kogakuTotalAll?: number;
              kogakuTotalExcFutan?: number;
              kogakuTotalKbn?: number;
              limitKbn?: number;
              moneyLimitListFlag?: number;
              monthLimitCount?: number;
              monthLimitFutan?: number;
              monthSpLimit?: number;
              prefNo?: number;
              prefactureName?: string;
              receFutanHide?: number;
              receFutanKbn?: number;
              receFutanRound?: number;
              receKisai?: number;
              receKisai2?: number;
              receKisaiKokho?: number;
              receSeikyuKbn?: number;
              receSpKbn?: number;
              receTenKisai?: number;
              receZeroKisai?: number;
              seikyuYm?: number;
              selectedValueMaster?: string;
              sortNo?: number;
              startDate?: number;
              excepHokenSyas?: Array<{
                __typename?: "DomainModelsInsuranceMstExceptHokensyaModel";
                hokenEdaNo?: number;
                hokenNo?: number;
                hokensyaNo?: string;
                hpId?: number;
                id?: string;
                prefNo?: number;
                startDate?: number;
              }>;
            };
            hokensyaMst?: {
              __typename?: "DomainModelsInsuranceMstHokensyaMstModel";
              address1?: string;
              address2?: string;
              bango?: string;
              hokenKbn?: number;
              hokensyaNo?: string;
              houbetu?: string;
              houbetuKbn?: string;
              hpId?: number;
              isKigoNa?: number;
              isReadOnlyHokenSyaNo?: boolean;
              kanaName?: string;
              kigo?: string;
              name?: string;
              postCdDisplay?: string;
              postCode?: string;
              prefNo?: number;
              rateHonnin?: number;
              rateKazoku?: number;
              tel1?: string;
            };
            listRousaiTenki?: Array<{
              __typename?: "DomainModelsInsuranceRousaiTenkiModel";
              rousaiTenkiEndDate?: number;
              rousaiTenkiIsDeleted?: number;
              rousaiTenkiSinkei?: number;
              rousaiTenkiTenki?: number;
              seqNo?: string;
            }>;
          };
          kohiInfModel?: Array<{
            __typename?: "DomainModelsInsuranceKohiInfModel";
            birthday?: number;
            calcSpKbn?: number;
            confirmDate?: number;
            endDate?: number;
            futansyaNo?: string;
            gendoGaku?: number;
            hasDateConfirmed?: boolean;
            hokenEdaNo?: number;
            hokenId?: number;
            hokenName?: string;
            hokenNo?: number;
            hokenSbtKbn?: number;
            houbetu?: string;
            isDeleted?: number;
            isEmptyModel?: boolean;
            isAddNew?: boolean;
            isExpirated?: boolean;
            isExpired?: boolean;
            isHaveKohiMst?: boolean;
            isLimitList?: number;
            jyukyusyaNo?: string;
            kofuDate?: number;
            lastDateConfirmed?: number;
            prefNo?: number;
            prefNoMst?: string;
            rate?: number;
            seqNo?: string;
            sikakuDate?: number;
            sinDate?: number;
            startDate?: number;
            tokusyuNo?: string;
            confirmDateList?: Array<{
              __typename?: "DomainModelsInsuranceConfirmDateModel";
              checkComment?: string;
              checkId?: number;
              checkMachine?: string;
              checkName?: string;
              confirmDate?: number;
              hokenGrp?: number;
              hokenId?: number;
              isDeleted?: number;
              onlineConfirmationId?: number;
              ptId?: string;
              seqNo?: string;
            }>;
            filingInfModels?: Array<{
              __typename?: "DomainModelsInsuranceFilingInfModel";
              fileId?: number;
              fileLink?: string;
            }>;
            hokenMstModel?: {
              __typename?: "DomainModelsInsuranceMstHokenMstModel";
              ageEnd?: number;
              ageStart?: number;
              calcSpKbn?: number;
              checkDigit?: number;
              countKbn?: number;
              dayLimitCount?: number;
              dayLimitFutan?: number;
              displayHokenNo?: string;
              displayTextMaster?: string;
              displayTextMasterWithoutHokenNo?: string;
              enTen?: number;
              endDate?: number;
              futanKbn?: number;
              futanRate?: number;
              futanYusen?: number;
              hokenEdaNo?: number;
              hokenKohiKbn?: number;
              hokenName?: string;
              hokenNameCd?: string;
              hokenNo?: number;
              hokenSName?: string;
              hokenSbtKbn?: number;
              houbetu?: string;
              houbetuDisplayText?: string;
              isAdded?: boolean;
              isFutansyaNoCheck?: number;
              isJyukyusyaNoCheck?: number;
              isLimitList?: number;
              isLimitListSum?: number;
              isOtherPrefValid?: number;
              isTokusyuNoCheck?: number;
              jyuKyuCheckDigit?: number;
              kaiFutangaku?: number;
              kaiLimitFutan?: number;
              kogakuHairyoKbn?: number;
              kogakuTekiyo?: number;
              kogakuTotalAll?: number;
              kogakuTotalExcFutan?: number;
              kogakuTotalKbn?: number;
              limitKbn?: number;
              moneyLimitListFlag?: number;
              monthLimitCount?: number;
              monthLimitFutan?: number;
              monthSpLimit?: number;
              prefNo?: number;
              prefactureName?: string;
              receFutanHide?: number;
              receFutanKbn?: number;
              receFutanRound?: number;
              receKisai?: number;
              receKisai2?: number;
              receKisaiKokho?: number;
              receSeikyuKbn?: number;
              receSpKbn?: number;
              receTenKisai?: number;
              receZeroKisai?: number;
              seikyuYm?: number;
              selectedValueMaster?: string;
              sortNo?: number;
              startDate?: number;
              excepHokenSyas?: Array<{
                __typename?: "DomainModelsInsuranceMstExceptHokensyaModel";
                hokenEdaNo?: number;
                hokenNo?: number;
                hokensyaNo?: string;
                hpId?: number;
                id?: string;
                prefNo?: number;
                startDate?: number;
              }>;
            };
            limitListModel?: Array<{
              __typename?: "DomainModelsMaxMoneyLimitListModel";
              biko?: string;
              code?: string;
              futanGaku?: number;
              hokenPid?: number;
              id?: string;
              isDeleted?: number;
              kohiId?: number;
              raiinNo?: string;
              seqNo?: number;
              sinDate?: number;
              sinDateD?: number;
              sinDateM?: number;
              sinDateY?: number;
              sort?: number;
              sortKey?: string;
              totalGaku?: number;
              totalMoney?: number;
            }>;
          }>;
          orderDetailsNotContainMedicine?: Array<{
            __typename?: "DomainModelsEpsEpsOrdInfDetailModel";
            alternationIndex?: number;
            bikoComment?: number;
            buiKbn?: number;
            bunkatu?: string;
            bunkatuKoui?: number;
            cdEdaNo?: number;
            cdKbn?: string;
            cdKbnNo?: number;
            cdKouNo?: number;
            centerCd?: string;
            centerItemCd1?: string;
            centerItemCd2?: string;
            centerName?: string;
            cmtCol1?: number;
            cmtCol2?: number;
            cmtCol3?: number;
            cmtCol4?: number;
            cmtColKeta1?: number;
            cmtColKeta2?: number;
            cmtColKeta3?: number;
            cmtColKeta4?: number;
            cmtName?: string;
            cmtOpt?: string;
            cnvTermVal?: number;
            cnvUnitName?: string;
            commentNewline?: number;
            drugKbn?: number;
            fontColor?: string;
            handanGrpKbn?: number;
            hpId?: number;
            inOutKbn?: number;
            ipnCd?: string;
            ipnName?: string;
            isAdopted?: number;
            isDrug?: boolean;
            isDrugUsage?: boolean;
            isDummy?: boolean;
            isGetPriceInYakka?: boolean;
            isGetYakka?: boolean;
            isInjection?: boolean;
            isInjectionUsage?: boolean;
            isKensaMstEmpty?: boolean;
            isNormalComment?: boolean;
            isNodspRece?: number;
            isShohoBiko?: boolean;
            isShohoComment?: boolean;
            isSuppUsage?: boolean;
            isStandardUsage?: boolean;
            itemCd?: string;
            jissiDate?: string;
            itemName?: string;
            jissiId?: number;
            jissiKbn?: number;
            jissiMachine?: string;
            kasan1?: number;
            kasan2?: number;
            kensaGaichu?: number;
            kikakiUnit?: string;
            kohatuKbn?: number;
            kokuji1?: string;
            kokuji2?: string;
            masterSbt?: string;
            memoItem?: string;
            odrKouiKbn?: number;
            odrTermVal?: number;
            odrUnitName?: string;
            ptId?: string;
            raiinNo?: string;
            refillSetting?: number;
            relationItem?: string;
            reqCd?: string;
            rikikaUnit?: string;
            rikikaRate?: number;
            rowNo?: number;
            rousaiKbn?: number;
            rpEdaNo?: string;
            senteiRyoyoKbn?: number;
            rpNo?: string;
            sinDate?: number;
            sinKouiKbn?: number;
            sinYm?: number;
            suryo?: number;
            syohoLimitKbn?: number;
            syohoKbn?: number;
            ten?: number;
            termVal?: number;
            unitName?: string;
            unitSbt?: number;
            yakka?: number;
            yakkaiUnit?: string;
            yjCd?: string;
            yohoKbn?: number;
            youkaiekiCd?: string;
          }>;
          ptHokenPatternModel?: {
            __typename?: "DomainModelsReceiptRecalculationPtHokenPatternModel";
            endDate?: number;
            hokenId?: number;
            hokenKbn?: number;
            hokenMemo?: string;
            hokenPid?: number;
            hokenSbtCd?: number;
            kohi1Id?: number;
            kohi2Id?: number;
            kohi3Id?: number;
            kohi4Id?: number;
            ptId?: string;
            seqNo?: string;
            startDate?: number;
          };
        }>;
      };
    };
  };
};

export type PostApiEpsGetOutDrugCsvDataMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsEpsGetOutDrugCsvDataRequestInput;
}>;

export type PostApiEpsGetOutDrugCsvDataMutation = {
  __typename?: "mutation_root";
  postApiEpsGetOutDrugCsvData?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsGetOutDrugCsvDataResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsGetOutDrugCsvDataResponse";
      outDrugCsvData?: string;
      prescriptionDocument?: string;
    };
  };
};

export type GetApiEpsGetDuplicateMedicationCheckQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiEpsGetDuplicateMedicationCheckQuery = {
  __typename?: "query_root";
  getApiEpsGetDuplicateMedicationCheck?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsGetDuplicateMedicationCheckResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsGetDuplicateMedicationCheckResponse";
      epsChkList?: Array<{
        __typename?: "DomainModelsEpsChkEpsChkModel";
        checkResult?: number;
        createDate?: string;
        createId?: number;
        createMachine?: string;
        drugInfo?: string;
        hpId?: number;
        isDeleted?: number;
        onlineConsent?: number;
        oralBrowsingConsent?: number;
        ptId?: string;
        raiinNo?: string;
        sameMedicalInstitutionAlertFlg?: number;
        seqNo?: string;
        sinDate?: number;
        updateDate?: string;
        updateId?: number;
        updateMachine?: string;
        epsChkDetailModels?: Array<{
          __typename?: "DomainModelsEpsChkDetailEpsChkDetailModel";
          comment?: string;
          hpId?: number;
          message?: string;
          messageCategory?: string;
          messageId?: string;
          pastDate?: string;
          pastDispensingQuantity?: string;
          pastDosageForm?: string;
          pastInsurancePharmacyName?: string;
          pastMedicalInstitutionName?: string;
          pastPharmaceuticalCode?: string;
          pastPharmaceuticalCodeType?: string;
          pastPharmaceuticalName?: string;
          pastUsage?: string;
          pharmaceuticalsIngredientName?: string;
          ptId?: string;
          raiinNo?: string;
          seqNo?: string;
          targetDispensingQuantity?: string;
          targetDosageForm?: string;
          targetPharmaceuticalCode?: string;
          targetPharmaceuticalCodeType?: string;
          targetPharmaceuticalName?: string;
          targetUsage?: string;
        }>;
      }>;
    };
  };
};

export type PostApiEpsSaveDuplicateMedicationCheckMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsEpsSaveDuplicateMedicationCheckRequestInput>;
  }>;

export type PostApiEpsSaveDuplicateMedicationCheckMutation = {
  __typename?: "mutation_root";
  postApiEpsSaveDuplicateMedicationCheck?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsSaveDuplicateMedicationCheckResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsSaveDuplicateMedicationCheckResponse";
      isSuccess?: boolean;
    };
  };
};

export type PostApiEpsCheckErrorForPreRegistrationMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsEpsGetPreRegistrationDataRequestInput>;
  }>;

export type PostApiEpsCheckErrorForPreRegistrationMutation = {
  __typename?: "mutation_root";
  postApiEpsCheckErrorForPreRegistration?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpSCheckErrorPreRegistrationResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpSCheckErrorPreRegistrationResponse";
      errorMessage?: string;
    };
  };
};

export type PostApiEpsSavePrescriptionInfoMutationVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.EmrCloudApiRequestsEpsSavePrescriptionInfoRequestInput>;
}>;

export type PostApiEpsSavePrescriptionInfoMutation = {
  __typename?: "mutation_root";
  postApiEpsSavePrescriptionInfo?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsSavePrescriptionInfoResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsSavePrescriptionInfoResponse";
      status?: number;
      epsPrescriptions?: Array<{
        __typename?: "DomainModelsEpsSaveEpsPrescriptionInfoModel";
        accessCode?: string;
        bango?: string;
        deletedReason?: number;
        edaNo?: string;
        hokensyaNo?: string;
        hpId?: number;
        issueType?: number;
        kigo?: string;
        kohiFutansyaNo?: string;
        kohiJyukyusyaNo?: string;
        prescriptionDocument?: string;
        prescriptionId?: string;
        ptId?: string;
        raiinNo?: string;
        refileCount?: number;
        seqNo?: string;
        sinDate?: number;
        status?: number;
      }>;
    };
  };
};

export type GetApiEpsGetEpsInsuranceInfoQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiEpsGetEpsInsuranceInfoQuery = {
  __typename?: "query_root";
  getApiEpsGetEpsInsuranceInfo?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsGetEpsInsuranceInfResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsGetEpsInsuranceInfResponse";
      wasConfirmedOnline?: boolean;
      ptHokenPatterns?: Array<{
        __typename?: "DomainModelsEpsInsurancePtHokenPatternModel";
        endDate?: number;
        hokenId?: number;
        hokenKbn?: number;
        hokenPid?: number;
        hokenSbtCd?: number;
        kohi1Id?: number;
        kohi2Id?: number;
        kohi3Id?: number;
        kohi4Id?: number;
        ptId?: string;
        seqNo?: string;
        ptHokenInf?: {
          __typename?: "DomainModelsEpsInsurancePtHokenInfModel";
          bango?: string;
          edaNo?: string;
          hokenEdaNo?: number;
          hokenId?: number;
          hokenKbn?: number;
          hokenNo?: number;
          hokensyaNo?: string;
          isDeleted?: number;
          kigo?: string;
          ptId?: string;
          startDate?: number;
          syokumuKbn?: number;
          tasukaiYm?: number;
        };
        ptKohi1?: {
          __typename?: "DomainModelsEpsInsurancePtKohiModel";
          futansyaNo?: string;
          hokenEdaNo?: number;
          hokenId?: number;
          hokenNo?: number;
          hokenSbtKbn?: number;
          hpId?: number;
          isDeleted?: number;
          jyukyusyaNo?: string;
          ptId?: string;
          seqNo?: string;
        };
        ptKohi2?: {
          __typename?: "DomainModelsEpsInsurancePtKohiModel";
          futansyaNo?: string;
          hokenEdaNo?: number;
          hokenId?: number;
          hokenNo?: number;
          hokenSbtKbn?: number;
          hpId?: number;
          isDeleted?: number;
          jyukyusyaNo?: string;
          ptId?: string;
          seqNo?: string;
        };
        ptKohi3?: {
          __typename?: "DomainModelsEpsInsurancePtKohiModel";
          futansyaNo?: string;
          hokenEdaNo?: number;
          hokenId?: number;
          hokenNo?: number;
          hokenSbtKbn?: number;
          hpId?: number;
          isDeleted?: number;
          jyukyusyaNo?: string;
          ptId?: string;
          seqNo?: string;
        };
        ptKohi4?: {
          __typename?: "DomainModelsEpsInsurancePtKohiModel";
          futansyaNo?: string;
          hokenEdaNo?: number;
          hokenId?: number;
          hokenNo?: number;
          hokenSbtKbn?: number;
          hpId?: number;
          isDeleted?: number;
          jyukyusyaNo?: string;
          ptId?: string;
          seqNo?: string;
        };
      }>;
      raiinInf?: {
        __typename?: "DomainModelsEpsRaiinInfModel";
        hokenPid?: number;
        hpId?: number;
        prescriptionIssueSelect?: number;
        printEpsReference?: number;
        ptId?: string;
        raiinNo?: string;
        sinDate?: number;
      };
    };
  };
};

export const PostApiEpsGetPreRegistrationDataDocument = gql`
  mutation postApiEpsGetPreRegistrationData(
    $input: EmrCloudApiRequestsEpsGetPreRegistrationDataRequestInput
  ) {
    postApiEpsGetPreRegistrationData(
      emrCloudApiRequestsEpsGetPreRegistrationDataRequestInput: $input
    ) {
      data {
        preRegistrationCheckingModel {
          epsPrescriptionModel {
            accessCode
            bango
            createDate
            createId
            deleteReasonDisplay
            deletedDate
            deletedReason
            dispensingDate
            dispensingDateDisplay
            edaNo
            epsDispensing {
              bango
              cancelReason
              createDate
              createId
              createMachine
              dispensingDate
              dispensingDocument
              dispensingResultId
              dispensingTimes
              edaNo
              epsUpdateDateTime
              hokensyaNo
              hpId
              id
              isDeleted
              kigo
              kohiFutansyaNo
              kohiJyukyusyaNo
              messageFlg
              prescriptionId
              ptId
              receptionPharmacyName
              resultType
              updateDate
              updateId
              updateMachine
            }
            epsDispensingModel {
              bango
              cancelReason
              createDate
              createId
              createMachine
              dispensingDate
              dispensingDocument
              dispensingResultId
              dispensingTimes
              edaNo
              epsUpdateDateTime
              hokensyaNo
              hpId
              id
              isDeleted
              kigo
              kohiFutansyaNo
              kohiJyukyusyaNo
              messageFlg
              prescriptionId
              ptId
              receptionPharmacyName
              resultType
              updateDate
              updateId
              updateMachine
            }
            epsUpdateDateTime
            hokenDisplay
            hokensyaNo
            hpId
            id
            issueType
            issueTypeDisplay
            kaId
            kaInf {
              id
              kaId
              kaName
              kaSname
              receKaCd
              sortNo
              yousikiKaCd
            }
            kaSName
            kigo
            kohiFutansyaNo
            kohiJyukyusyaNo
            messageFlag
            pharmacyName
            prescriptionDocument
            prescriptionId
            ptId
            ptInf {
              age
              birthday
              birthdayDisplay
              comment
              deathDate
              email
              firstVisitDate
              homeAddress1
              homeAddress2
              homePost
              houmonAgreed
              hpId
              isDead
              isRyosyoDetail
              isShowKyuSeiName
              isTester
              job
              kanaName
              lastAppointmentDepartment
              lastVisitDate
              limitConsFlg
              mail
              mainHokenPid
              memo
              name
              nextAppointmentDepartment
              officeAddress1
              officeAddress2
              officeMemo
              officeName
              officePost
              officeTel
              primaryDoctor
              ptId
              ptNum
              rainCount
              rainCountInt
              referenceNo
              renrakuAddress1
              renrakuAddress2
              renrakuMemo
              renrakuName
              renrakuName2
              renrakuPost
              renrakuTel
              renrakuTel2
              seqNo
              setanusi
              sex
              sinDate
              tel1
              tel2
              zokugara
            }
            ptName
            ptNum
            ptNumDisplay
            raiinNo
            refileCount
            refill
            resultType
            resultTypeDisplay
            seqNo
            sinDate
            sinDateDisplay
            status
            statusDisplay
            tantoId
            tantoInf {
              drName
              email
              emailUpdateDate
              endDate
              functionMstModels {
                functionCd
                functionName
                jobCd
                permissions {
                  functionCd
                  permission
                  permissionKey
                }
                userMstModel {
                  functionCd
                  hpId
                  isDefault
                  permission
                  userId
                }
              }
              hpId
              id
              isDeleted
              isInitLoginId
              isInitPassword
              jobCd
              kaId
              kaSName
              kanaName
              loginId
              loginPass
              managerKbn
              mayakuLicenseNo
              missLoginCount
              name
              permissions {
                functionCd
                hpId
                isDefault
                permission
                userId
              }
              renkeiCd1
              sNameBinding
              sname
              sortNo
              startDate
              status
              userId
            }
            tantoName
            updateDate
            updateId
          }
          odrInfs {
            daysCnt
            hokenInfModel {
              bango
              confirmDate
              confirmDateList {
                checkComment
                checkId
                checkMachine
                checkName
                confirmDate
                hokenGrp
                hokenId
                isDeleted
                onlineConfirmationId
                ptId
                seqNo
              }
              edaNo
              endDate
              endDateSort
              filingInfModels {
                fileId
                fileLink
              }
              futansyaNo
              genmenGaku
              genmenKbn
              genmenRate
              hasDateConfirmed
              hokenEdaNo
              hokenId
              hokenKbn
              hokenMst {
                ageEnd
                ageStart
                calcSpKbn
                checkDigit
                countKbn
                dayLimitCount
                dayLimitFutan
                displayHokenNo
                displayTextMaster
                displayTextMasterWithoutHokenNo
                enTen
                endDate
                excepHokenSyas {
                  hokenEdaNo
                  hokenNo
                  hokensyaNo
                  hpId
                  id
                  prefNo
                  startDate
                }
                futanKbn
                futanRate
                futanYusen
                hokenEdaNo
                hokenKohiKbn
                hokenName
                hokenNameCd
                hokenNo
                hokenSName
                hokenSbtKbn
                houbetu
                houbetuDisplayText
                isAdded
                isFutansyaNoCheck
                isJyukyusyaNoCheck
                isLimitList
                isLimitListSum
                isOtherPrefValid
                isTokusyuNoCheck
                jyuKyuCheckDigit
                kaiFutangaku
                kaiLimitFutan
                kogakuHairyoKbn
                kogakuTekiyo
                kogakuTotalAll
                kogakuTotalExcFutan
                kogakuTotalKbn
                limitKbn
                moneyLimitListFlag
                monthLimitCount
                monthLimitFutan
                monthSpLimit
                prefNo
                prefactureName
                receFutanHide
                receFutanKbn
                receFutanRound
                receKisai
                receKisai2
                receKisaiKokho
                receSeikyuKbn
                receSpKbn
                receTenKisai
                receZeroKisai
                seikyuYm
                selectedValueMaster
                sortNo
                startDate
              }
              hokenMstDisplayTextMaster
              hokenMstEndDate
              hokenMstFutanKbn
              hokenMstFutanRate
              hokenMstHoubetu
              hokenMstSbtKbn
              hokenMstStartDate
              hokenNo
              hokenSbtCd
              hokenSentaku
              hokensyaAddress
              hokensyaMst {
                address1
                address2
                bango
                hokenKbn
                hokensyaNo
                houbetu
                houbetuKbn
                hpId
                isKigoNa
                isReadOnlyHokenSyaNo
                kanaName
                kigo
                name
                postCdDisplay
                postCode
                prefNo
                rateHonnin
                rateKazoku
                tel1
              }
              hokensyaName
              hokensyaNo
              hokensyaTel
              honkeKbn
              houbetu
              hpId
              insuredName
              isAddHokenCheck
              isAddNew
              isDeleted
              isEmptyModel
              isExpirated
              isHaveHokenMst
              isHoken
              isJibai
              isJibaiOrRosai
              isJihi
              isKokuho
              isNoHoken
              isNotKenkoKanri
              isNotNenkin
              isNotRodo
              isReceKisaiOrNoHoken
              isRousai
              isShaho
              isShahoOrKokuho
              jibaiHokenName
              jibaiHokenTanto
              jibaiHokenTel
              jibaiJyusyouDate
              keizokuKbn
              kenkoKanriBango
              kigo
              kofuDate
              kogakuKbn
              lastDateConfirmed
              listRousaiTenki {
                rousaiTenkiEndDate
                rousaiTenkiIsDeleted
                rousaiTenkiSinkei
                rousaiTenkiTenki
                seqNo
              }
              nenkinBango
              ptId
              rodoBango
              rousaiCityName
              rousaiJigyosyoName
              rousaiKantokuCd
              rousaiKofuNo
              rousaiPrefName
              rousaiReceCount
              rousaiRoudouCd
              rousaiSaigaiKbn
              rousaiSyobyoCd
              rousaiSyobyoDate
              ryoyoEndDate
              ryoyoStartDate
              seqNo
              sikakuDate
              sinDate
              startDate
              syokumuKbn
              tasukaiYm
              tokki1
              tokki2
              tokki3
              tokki4
              tokki5
              tokureiYm1
              tokureiYm2
            }
            hokenPid
            hpId
            id
            inoutKbn
            isDeleted
            kohiInfModel {
              birthday
              calcSpKbn
              confirmDate
              confirmDateList {
                checkComment
                checkId
                checkMachine
                checkName
                confirmDate
                hokenGrp
                hokenId
                isDeleted
                onlineConfirmationId
                ptId
                seqNo
              }
              endDate
              filingInfModels {
                fileId
                fileLink
              }
              futansyaNo
              gendoGaku
              hasDateConfirmed
              hokenEdaNo
              hokenId
              hokenMstModel {
                ageEnd
                ageStart
                calcSpKbn
                checkDigit
                countKbn
                dayLimitCount
                dayLimitFutan
                displayHokenNo
                displayTextMaster
                displayTextMasterWithoutHokenNo
                enTen
                endDate
                excepHokenSyas {
                  hokenEdaNo
                  hokenNo
                  hokensyaNo
                  hpId
                  id
                  prefNo
                  startDate
                }
                futanKbn
                futanRate
                futanYusen
                hokenEdaNo
                hokenKohiKbn
                hokenName
                hokenNameCd
                hokenNo
                hokenSName
                hokenSbtKbn
                houbetu
                houbetuDisplayText
                isAdded
                isFutansyaNoCheck
                isJyukyusyaNoCheck
                isLimitList
                isLimitListSum
                isOtherPrefValid
                isTokusyuNoCheck
                jyuKyuCheckDigit
                kaiFutangaku
                kaiLimitFutan
                kogakuHairyoKbn
                kogakuTekiyo
                kogakuTotalAll
                kogakuTotalExcFutan
                kogakuTotalKbn
                limitKbn
                moneyLimitListFlag
                monthLimitCount
                monthLimitFutan
                monthSpLimit
                prefNo
                prefactureName
                receFutanHide
                receFutanKbn
                receFutanRound
                receKisai
                receKisai2
                receKisaiKokho
                receSeikyuKbn
                receSpKbn
                receTenKisai
                receZeroKisai
                seikyuYm
                selectedValueMaster
                sortNo
                startDate
              }
              hokenName
              hokenNo
              hokenSbtKbn
              houbetu
              isDeleted
              isEmptyModel
              isAddNew
              isExpirated
              isExpired
              isHaveKohiMst
              isLimitList
              jyukyusyaNo
              kofuDate
              lastDateConfirmed
              limitListModel {
                biko
                code
                futanGaku
                hokenPid
                id
                isDeleted
                kohiId
                raiinNo
                seqNo
                sinDate
                sinDateD
                sinDateM
                sinDateY
                sort
                sortKey
                totalGaku
                totalMoney
              }
              prefNo
              prefNoMst
              rate
              seqNo
              sikakuDate
              sinDate
              startDate
              tokusyuNo
            }
            odrKouiKbn
            orderContainsCommonNameNotInCommonNameMedicines
            orderDetailsNotContainMedicine {
              alternationIndex
              bikoComment
              buiKbn
              bunkatu
              bunkatuKoui
              cdEdaNo
              cdKbn
              cdKbnNo
              cdKouNo
              centerCd
              centerItemCd1
              centerItemCd2
              centerName
              cmtCol1
              cmtCol2
              cmtCol3
              cmtCol4
              cmtColKeta1
              cmtColKeta2
              cmtColKeta3
              cmtColKeta4
              cmtName
              cmtOpt
              cnvTermVal
              cnvUnitName
              commentNewline
              drugKbn
              fontColor
              handanGrpKbn
              hpId
              inOutKbn
              ipnCd
              ipnName
              isAdopted
              isDrug
              isDrugUsage
              isDummy
              isGetPriceInYakka
              isGetYakka
              isInjection
              isInjectionUsage
              isKensaMstEmpty
              isNormalComment
              isNodspRece
              isShohoBiko
              isShohoComment
              isSuppUsage
              isStandardUsage
              itemCd
              jissiDate
              itemName
              jissiId
              jissiKbn
              jissiMachine
              kasan1
              kasan2
              kensaGaichu
              kikakiUnit
              kohatuKbn
              kokuji1
              kokuji2
              masterSbt
              memoItem
              odrKouiKbn
              odrTermVal
              odrUnitName
              ptId
              raiinNo
              refillSetting
              relationItem
              reqCd
              rikikaUnit
              rikikaRate
              rowNo
              rousaiKbn
              rpEdaNo
              senteiRyoyoKbn
              rpNo
              sinDate
              sinKouiKbn
              sinYm
              suryo
              syohoLimitKbn
              syohoKbn
              ten
              termVal
              unitName
              unitSbt
              yakka
              yakkaiUnit
              yjCd
              yohoKbn
              youkaiekiCd
            }
            ptHokenPatternModel {
              endDate
              hokenId
              hokenKbn
              hokenMemo
              hokenPid
              hokenSbtCd
              kohi1Id
              kohi2Id
              kohi3Id
              kohi4Id
              ptId
              seqNo
              startDate
            }
            ptId
            raiinNo
            rpName
            rpEdaNo
            rpNo
            santeiKbn
            sinDate
            sikyuKbn
            sortNo
            syohoSbt
            tosekiKbn
          }
          wasConfirmedOnline
        }
      }
      message
      status
    }
  }
`;
export type PostApiEpsGetPreRegistrationDataMutationFn =
  Apollo.MutationFunction<
    PostApiEpsGetPreRegistrationDataMutation,
    PostApiEpsGetPreRegistrationDataMutationVariables
  >;

/**
 * __usePostApiEpsGetPreRegistrationDataMutation__
 *
 * To run a mutation, you first call `usePostApiEpsGetPreRegistrationDataMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsGetPreRegistrationDataMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsGetPreRegistrationDataMutation, { data, loading, error }] = usePostApiEpsGetPreRegistrationDataMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiEpsGetPreRegistrationDataMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsGetPreRegistrationDataMutation,
    PostApiEpsGetPreRegistrationDataMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsGetPreRegistrationDataMutation,
    PostApiEpsGetPreRegistrationDataMutationVariables
  >(PostApiEpsGetPreRegistrationDataDocument, options);
}
export type PostApiEpsGetPreRegistrationDataMutationHookResult = ReturnType<
  typeof usePostApiEpsGetPreRegistrationDataMutation
>;
export type PostApiEpsGetPreRegistrationDataMutationResult =
  Apollo.MutationResult<PostApiEpsGetPreRegistrationDataMutation>;
export type PostApiEpsGetPreRegistrationDataMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsGetPreRegistrationDataMutation,
    PostApiEpsGetPreRegistrationDataMutationVariables
  >;
export const PostApiEpsGetOutDrugCsvDataDocument = gql`
  mutation postApiEpsGetOutDrugCsvData(
    $input: EmrCloudApiRequestsEpsGetOutDrugCsvDataRequestInput!
  ) {
    postApiEpsGetOutDrugCsvData(
      emrCloudApiRequestsEpsGetOutDrugCsvDataRequestInput: $input
    ) {
      data {
        outDrugCsvData
        prescriptionDocument
      }
      message
      status
    }
  }
`;
export type PostApiEpsGetOutDrugCsvDataMutationFn = Apollo.MutationFunction<
  PostApiEpsGetOutDrugCsvDataMutation,
  PostApiEpsGetOutDrugCsvDataMutationVariables
>;

/**
 * __usePostApiEpsGetOutDrugCsvDataMutation__
 *
 * To run a mutation, you first call `usePostApiEpsGetOutDrugCsvDataMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsGetOutDrugCsvDataMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsGetOutDrugCsvDataMutation, { data, loading, error }] = usePostApiEpsGetOutDrugCsvDataMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiEpsGetOutDrugCsvDataMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsGetOutDrugCsvDataMutation,
    PostApiEpsGetOutDrugCsvDataMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsGetOutDrugCsvDataMutation,
    PostApiEpsGetOutDrugCsvDataMutationVariables
  >(PostApiEpsGetOutDrugCsvDataDocument, options);
}
export type PostApiEpsGetOutDrugCsvDataMutationHookResult = ReturnType<
  typeof usePostApiEpsGetOutDrugCsvDataMutation
>;
export type PostApiEpsGetOutDrugCsvDataMutationResult =
  Apollo.MutationResult<PostApiEpsGetOutDrugCsvDataMutation>;
export type PostApiEpsGetOutDrugCsvDataMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsGetOutDrugCsvDataMutation,
    PostApiEpsGetOutDrugCsvDataMutationVariables
  >;
export const GetApiEpsGetDuplicateMedicationCheckDocument = gql`
  query getApiEpsGetDuplicateMedicationCheck(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiEpsGetDuplicateMedicationCheck(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      data {
        epsChkList {
          checkResult
          createDate
          createId
          createMachine
          drugInfo
          epsChkDetailModels {
            comment
            hpId
            message
            messageCategory
            messageId
            pastDate
            pastDispensingQuantity
            pastDosageForm
            pastInsurancePharmacyName
            pastMedicalInstitutionName
            pastPharmaceuticalCode
            pastPharmaceuticalCodeType
            pastPharmaceuticalName
            pastUsage
            pharmaceuticalsIngredientName
            ptId
            raiinNo
            seqNo
            targetDispensingQuantity
            targetDosageForm
            targetPharmaceuticalCode
            targetPharmaceuticalCodeType
            targetPharmaceuticalName
            targetUsage
          }
          hpId
          isDeleted
          onlineConsent
          oralBrowsingConsent
          ptId
          raiinNo
          sameMedicalInstitutionAlertFlg
          seqNo
          sinDate
          updateDate
          updateId
          updateMachine
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiEpsGetDuplicateMedicationCheckQuery__
 *
 * To run a query within a React component, call `useGetApiEpsGetDuplicateMedicationCheckQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiEpsGetDuplicateMedicationCheckQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiEpsGetDuplicateMedicationCheckQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiEpsGetDuplicateMedicationCheckQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiEpsGetDuplicateMedicationCheckQuery,
    GetApiEpsGetDuplicateMedicationCheckQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiEpsGetDuplicateMedicationCheckQuery,
    GetApiEpsGetDuplicateMedicationCheckQueryVariables
  >(GetApiEpsGetDuplicateMedicationCheckDocument, options);
}
export function useGetApiEpsGetDuplicateMedicationCheckLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiEpsGetDuplicateMedicationCheckQuery,
    GetApiEpsGetDuplicateMedicationCheckQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiEpsGetDuplicateMedicationCheckQuery,
    GetApiEpsGetDuplicateMedicationCheckQueryVariables
  >(GetApiEpsGetDuplicateMedicationCheckDocument, options);
}
export function useGetApiEpsGetDuplicateMedicationCheckSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiEpsGetDuplicateMedicationCheckQuery,
    GetApiEpsGetDuplicateMedicationCheckQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiEpsGetDuplicateMedicationCheckQuery,
    GetApiEpsGetDuplicateMedicationCheckQueryVariables
  >(GetApiEpsGetDuplicateMedicationCheckDocument, options);
}
export type GetApiEpsGetDuplicateMedicationCheckQueryHookResult = ReturnType<
  typeof useGetApiEpsGetDuplicateMedicationCheckQuery
>;
export type GetApiEpsGetDuplicateMedicationCheckLazyQueryHookResult =
  ReturnType<typeof useGetApiEpsGetDuplicateMedicationCheckLazyQuery>;
export type GetApiEpsGetDuplicateMedicationCheckSuspenseQueryHookResult =
  ReturnType<typeof useGetApiEpsGetDuplicateMedicationCheckSuspenseQuery>;
export type GetApiEpsGetDuplicateMedicationCheckQueryResult =
  Apollo.QueryResult<
    GetApiEpsGetDuplicateMedicationCheckQuery,
    GetApiEpsGetDuplicateMedicationCheckQueryVariables
  >;
export const PostApiEpsSaveDuplicateMedicationCheckDocument = gql`
  mutation postApiEpsSaveDuplicateMedicationCheck(
    $input: EmrCloudApiRequestsEpsSaveDuplicateMedicationCheckRequestInput
  ) {
    postApiEpsSaveDuplicateMedicationCheck(
      emrCloudApiRequestsEpsSaveDuplicateMedicationCheckRequestInput: $input
    ) {
      data {
        isSuccess
      }
      message
      status
    }
  }
`;
export type PostApiEpsSaveDuplicateMedicationCheckMutationFn =
  Apollo.MutationFunction<
    PostApiEpsSaveDuplicateMedicationCheckMutation,
    PostApiEpsSaveDuplicateMedicationCheckMutationVariables
  >;

/**
 * __usePostApiEpsSaveDuplicateMedicationCheckMutation__
 *
 * To run a mutation, you first call `usePostApiEpsSaveDuplicateMedicationCheckMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsSaveDuplicateMedicationCheckMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsSaveDuplicateMedicationCheckMutation, { data, loading, error }] = usePostApiEpsSaveDuplicateMedicationCheckMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiEpsSaveDuplicateMedicationCheckMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsSaveDuplicateMedicationCheckMutation,
    PostApiEpsSaveDuplicateMedicationCheckMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsSaveDuplicateMedicationCheckMutation,
    PostApiEpsSaveDuplicateMedicationCheckMutationVariables
  >(PostApiEpsSaveDuplicateMedicationCheckDocument, options);
}
export type PostApiEpsSaveDuplicateMedicationCheckMutationHookResult =
  ReturnType<typeof usePostApiEpsSaveDuplicateMedicationCheckMutation>;
export type PostApiEpsSaveDuplicateMedicationCheckMutationResult =
  Apollo.MutationResult<PostApiEpsSaveDuplicateMedicationCheckMutation>;
export type PostApiEpsSaveDuplicateMedicationCheckMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsSaveDuplicateMedicationCheckMutation,
    PostApiEpsSaveDuplicateMedicationCheckMutationVariables
  >;
export const PostApiEpsCheckErrorForPreRegistrationDocument = gql`
  mutation postApiEpsCheckErrorForPreRegistration(
    $input: EmrCloudApiRequestsEpsGetPreRegistrationDataRequestInput
  ) {
    postApiEpsCheckErrorForPreRegistration(
      emrCloudApiRequestsEpsGetPreRegistrationDataRequestInput: $input
    ) {
      data {
        errorMessage
      }
      message
      status
    }
  }
`;
export type PostApiEpsCheckErrorForPreRegistrationMutationFn =
  Apollo.MutationFunction<
    PostApiEpsCheckErrorForPreRegistrationMutation,
    PostApiEpsCheckErrorForPreRegistrationMutationVariables
  >;

/**
 * __usePostApiEpsCheckErrorForPreRegistrationMutation__
 *
 * To run a mutation, you first call `usePostApiEpsCheckErrorForPreRegistrationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsCheckErrorForPreRegistrationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsCheckErrorForPreRegistrationMutation, { data, loading, error }] = usePostApiEpsCheckErrorForPreRegistrationMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiEpsCheckErrorForPreRegistrationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsCheckErrorForPreRegistrationMutation,
    PostApiEpsCheckErrorForPreRegistrationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsCheckErrorForPreRegistrationMutation,
    PostApiEpsCheckErrorForPreRegistrationMutationVariables
  >(PostApiEpsCheckErrorForPreRegistrationDocument, options);
}
export type PostApiEpsCheckErrorForPreRegistrationMutationHookResult =
  ReturnType<typeof usePostApiEpsCheckErrorForPreRegistrationMutation>;
export type PostApiEpsCheckErrorForPreRegistrationMutationResult =
  Apollo.MutationResult<PostApiEpsCheckErrorForPreRegistrationMutation>;
export type PostApiEpsCheckErrorForPreRegistrationMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsCheckErrorForPreRegistrationMutation,
    PostApiEpsCheckErrorForPreRegistrationMutationVariables
  >;
export const PostApiEpsSavePrescriptionInfoDocument = gql`
  mutation postApiEpsSavePrescriptionInfo(
    $input: EmrCloudApiRequestsEpsSavePrescriptionInfoRequestInput
  ) {
    postApiEpsSavePrescriptionInfo(
      emrCloudApiRequestsEpsSavePrescriptionInfoRequestInput: $input
    ) {
      data {
        epsPrescriptions {
          accessCode
          bango
          deletedReason
          edaNo
          hokensyaNo
          hpId
          issueType
          kigo
          kohiFutansyaNo
          kohiJyukyusyaNo
          prescriptionDocument
          prescriptionId
          ptId
          raiinNo
          refileCount
          seqNo
          sinDate
          status
        }
        status
      }
      message
      status
    }
  }
`;
export type PostApiEpsSavePrescriptionInfoMutationFn = Apollo.MutationFunction<
  PostApiEpsSavePrescriptionInfoMutation,
  PostApiEpsSavePrescriptionInfoMutationVariables
>;

/**
 * __usePostApiEpsSavePrescriptionInfoMutation__
 *
 * To run a mutation, you first call `usePostApiEpsSavePrescriptionInfoMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsSavePrescriptionInfoMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsSavePrescriptionInfoMutation, { data, loading, error }] = usePostApiEpsSavePrescriptionInfoMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiEpsSavePrescriptionInfoMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsSavePrescriptionInfoMutation,
    PostApiEpsSavePrescriptionInfoMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsSavePrescriptionInfoMutation,
    PostApiEpsSavePrescriptionInfoMutationVariables
  >(PostApiEpsSavePrescriptionInfoDocument, options);
}
export type PostApiEpsSavePrescriptionInfoMutationHookResult = ReturnType<
  typeof usePostApiEpsSavePrescriptionInfoMutation
>;
export type PostApiEpsSavePrescriptionInfoMutationResult =
  Apollo.MutationResult<PostApiEpsSavePrescriptionInfoMutation>;
export type PostApiEpsSavePrescriptionInfoMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsSavePrescriptionInfoMutation,
    PostApiEpsSavePrescriptionInfoMutationVariables
  >;
export const GetApiEpsGetEpsInsuranceInfoDocument = gql`
  query getApiEpsGetEpsInsuranceInfo(
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
  ) {
    getApiEpsGetEpsInsuranceInfo(
      ptId: $ptId
      raiinNo: $raiinNo
      sinDate: $sinDate
    ) {
      message
      status
      data {
        ptHokenPatterns {
          endDate
          hokenId
          hokenKbn
          hokenPid
          hokenSbtCd
          kohi1Id
          kohi2Id
          kohi3Id
          kohi4Id
          ptId
          seqNo
          ptHokenInf {
            bango
            edaNo
            hokenEdaNo
            hokenId
            hokenKbn
            hokenNo
            hokensyaNo
            isDeleted
            kigo
            ptId
            startDate
            syokumuKbn
            tasukaiYm
          }
          ptKohi1 {
            futansyaNo
            hokenEdaNo
            hokenId
            hokenNo
            hokenSbtKbn
            hpId
            isDeleted
            jyukyusyaNo
            ptId
            seqNo
          }
          ptKohi2 {
            futansyaNo
            hokenEdaNo
            hokenId
            hokenNo
            hokenSbtKbn
            hpId
            isDeleted
            jyukyusyaNo
            ptId
            seqNo
          }
          ptKohi3 {
            futansyaNo
            hokenEdaNo
            hokenId
            hokenNo
            hokenSbtKbn
            hpId
            isDeleted
            jyukyusyaNo
            ptId
            seqNo
          }
          ptKohi4 {
            futansyaNo
            hokenEdaNo
            hokenId
            hokenNo
            hokenSbtKbn
            hpId
            isDeleted
            jyukyusyaNo
            ptId
            seqNo
          }
        }
        raiinInf {
          hokenPid
          hpId
          prescriptionIssueSelect
          printEpsReference
          ptId
          raiinNo
          sinDate
        }
        wasConfirmedOnline
      }
    }
  }
`;

/**
 * __useGetApiEpsGetEpsInsuranceInfoQuery__
 *
 * To run a query within a React component, call `useGetApiEpsGetEpsInsuranceInfoQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiEpsGetEpsInsuranceInfoQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiEpsGetEpsInsuranceInfoQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiEpsGetEpsInsuranceInfoQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiEpsGetEpsInsuranceInfoQuery,
    GetApiEpsGetEpsInsuranceInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiEpsGetEpsInsuranceInfoQuery,
    GetApiEpsGetEpsInsuranceInfoQueryVariables
  >(GetApiEpsGetEpsInsuranceInfoDocument, options);
}
export function useGetApiEpsGetEpsInsuranceInfoLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiEpsGetEpsInsuranceInfoQuery,
    GetApiEpsGetEpsInsuranceInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiEpsGetEpsInsuranceInfoQuery,
    GetApiEpsGetEpsInsuranceInfoQueryVariables
  >(GetApiEpsGetEpsInsuranceInfoDocument, options);
}
export function useGetApiEpsGetEpsInsuranceInfoSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiEpsGetEpsInsuranceInfoQuery,
    GetApiEpsGetEpsInsuranceInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiEpsGetEpsInsuranceInfoQuery,
    GetApiEpsGetEpsInsuranceInfoQueryVariables
  >(GetApiEpsGetEpsInsuranceInfoDocument, options);
}
export type GetApiEpsGetEpsInsuranceInfoQueryHookResult = ReturnType<
  typeof useGetApiEpsGetEpsInsuranceInfoQuery
>;
export type GetApiEpsGetEpsInsuranceInfoLazyQueryHookResult = ReturnType<
  typeof useGetApiEpsGetEpsInsuranceInfoLazyQuery
>;
export type GetApiEpsGetEpsInsuranceInfoSuspenseQueryHookResult = ReturnType<
  typeof useGetApiEpsGetEpsInsuranceInfoSuspenseQuery
>;
export type GetApiEpsGetEpsInsuranceInfoQueryResult = Apollo.QueryResult<
  GetApiEpsGetEpsInsuranceInfoQuery,
  GetApiEpsGetEpsInsuranceInfoQueryVariables
>;
