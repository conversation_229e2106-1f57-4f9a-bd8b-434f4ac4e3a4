import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type FindPharmacyHolidaysQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type FindPharmacyHolidaysQuery = {
  __typename?: "query_root";
  findPharmacyHolidays: Array<{
    __typename?: "PharmacyHoliday";
    pharmacyHolidayId: number;
    hospitalId: number;
    holidayStartDate: string;
    holidayEndDate: string;
  }>;
};

export type GetPharmacyHolidayByIdQueryVariables = Types.Exact<{
  input: Types.GetPharmacyHolidayByIdInput;
}>;

export type GetPharmacyHolidayByIdQuery = {
  __typename?: "query_root";
  getPharmacyHolidayById: {
    __typename?: "PharmacyHoliday";
    pharmacyHolidayId: number;
    hospitalId: number;
    holidayStartDate: string;
    holidayEndDate: string;
  };
};

export type IsPharmacyHolidaysQueryVariables = Types.Exact<{
  input:
    | Array<Types.Scalars["DateTime"]["input"]>
    | Types.Scalars["DateTime"]["input"];
}>;

export type IsPharmacyHolidaysQuery = {
  __typename?: "query_root";
  isPharmacyHolidays: Array<{
    __typename?: "IsPharmacyHoliday";
    date: string;
    isHoliday: boolean;
  }>;
};

export type GetPharmacyBusinessDaysQueryVariables = Types.Exact<{
  input: Types.GetPharmacyBusinessDaysInput;
}>;

export type GetPharmacyBusinessDaysQuery = {
  __typename?: "query_root";
  getPharmacyBusinessDays: Array<{
    __typename?: "IsPharmacyHoliday";
    date: string;
    isHoliday: boolean;
  }>;
};

export type CreatePharmacyHolidayMutationVariables = Types.Exact<{
  input: Types.CreatePharmacyHolidayInput;
}>;

export type CreatePharmacyHolidayMutation = {
  __typename?: "mutation_root";
  createPharmacyHoliday: {
    __typename?: "PharmacyHoliday";
    pharmacyHolidayId: number;
    hospitalId: number;
    holidayStartDate: string;
    holidayEndDate: string;
  };
};

export type UpdatePharmacyHolidayMutationVariables = Types.Exact<{
  input: Types.UpdatePharmacyHolidayInput;
}>;

export type UpdatePharmacyHolidayMutation = {
  __typename?: "mutation_root";
  updatePharmacyHoliday: {
    __typename?: "PharmacyHoliday";
    pharmacyHolidayId: number;
    hospitalId: number;
    holidayStartDate: string;
    holidayEndDate: string;
  };
};

export type DeletePharmacyHolidayMutationVariables = Types.Exact<{
  input: Types.DeletePharmacyHolidayInput;
}>;

export type DeletePharmacyHolidayMutation = {
  __typename?: "mutation_root";
  deletePharmacyHoliday: boolean;
};

export const FindPharmacyHolidaysDocument = gql`
  query findPharmacyHolidays {
    findPharmacyHolidays {
      pharmacyHolidayId
      hospitalId
      holidayStartDate
      holidayEndDate
    }
  }
`;

/**
 * __useFindPharmacyHolidaysQuery__
 *
 * To run a query within a React component, call `useFindPharmacyHolidaysQuery` and pass it any options that fit your needs.
 * When your component renders, `useFindPharmacyHolidaysQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFindPharmacyHolidaysQuery({
 *   variables: {
 *   },
 * });
 */
export function useFindPharmacyHolidaysQuery(
  baseOptions?: Apollo.QueryHookOptions<
    FindPharmacyHolidaysQuery,
    FindPharmacyHolidaysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    FindPharmacyHolidaysQuery,
    FindPharmacyHolidaysQueryVariables
  >(FindPharmacyHolidaysDocument, options);
}
export function useFindPharmacyHolidaysLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FindPharmacyHolidaysQuery,
    FindPharmacyHolidaysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    FindPharmacyHolidaysQuery,
    FindPharmacyHolidaysQueryVariables
  >(FindPharmacyHolidaysDocument, options);
}
export function useFindPharmacyHolidaysSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    FindPharmacyHolidaysQuery,
    FindPharmacyHolidaysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    FindPharmacyHolidaysQuery,
    FindPharmacyHolidaysQueryVariables
  >(FindPharmacyHolidaysDocument, options);
}
export type FindPharmacyHolidaysQueryHookResult = ReturnType<
  typeof useFindPharmacyHolidaysQuery
>;
export type FindPharmacyHolidaysLazyQueryHookResult = ReturnType<
  typeof useFindPharmacyHolidaysLazyQuery
>;
export type FindPharmacyHolidaysSuspenseQueryHookResult = ReturnType<
  typeof useFindPharmacyHolidaysSuspenseQuery
>;
export type FindPharmacyHolidaysQueryResult = Apollo.QueryResult<
  FindPharmacyHolidaysQuery,
  FindPharmacyHolidaysQueryVariables
>;
export const GetPharmacyHolidayByIdDocument = gql`
  query getPharmacyHolidayById($input: GetPharmacyHolidayByIdInput!) {
    getPharmacyHolidayById(input: $input) {
      pharmacyHolidayId
      hospitalId
      holidayStartDate
      holidayEndDate
    }
  }
`;

/**
 * __useGetPharmacyHolidayByIdQuery__
 *
 * To run a query within a React component, call `useGetPharmacyHolidayByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPharmacyHolidayByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPharmacyHolidayByIdQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPharmacyHolidayByIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPharmacyHolidayByIdQuery,
    GetPharmacyHolidayByIdQueryVariables
  > &
    (
      | { variables: GetPharmacyHolidayByIdQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPharmacyHolidayByIdQuery,
    GetPharmacyHolidayByIdQueryVariables
  >(GetPharmacyHolidayByIdDocument, options);
}
export function useGetPharmacyHolidayByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPharmacyHolidayByIdQuery,
    GetPharmacyHolidayByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPharmacyHolidayByIdQuery,
    GetPharmacyHolidayByIdQueryVariables
  >(GetPharmacyHolidayByIdDocument, options);
}
export function useGetPharmacyHolidayByIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPharmacyHolidayByIdQuery,
    GetPharmacyHolidayByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPharmacyHolidayByIdQuery,
    GetPharmacyHolidayByIdQueryVariables
  >(GetPharmacyHolidayByIdDocument, options);
}
export type GetPharmacyHolidayByIdQueryHookResult = ReturnType<
  typeof useGetPharmacyHolidayByIdQuery
>;
export type GetPharmacyHolidayByIdLazyQueryHookResult = ReturnType<
  typeof useGetPharmacyHolidayByIdLazyQuery
>;
export type GetPharmacyHolidayByIdSuspenseQueryHookResult = ReturnType<
  typeof useGetPharmacyHolidayByIdSuspenseQuery
>;
export type GetPharmacyHolidayByIdQueryResult = Apollo.QueryResult<
  GetPharmacyHolidayByIdQuery,
  GetPharmacyHolidayByIdQueryVariables
>;
export const IsPharmacyHolidaysDocument = gql`
  query isPharmacyHolidays($input: [DateTime!]!) {
    isPharmacyHolidays(input: $input) {
      date
      isHoliday
    }
  }
`;

/**
 * __useIsPharmacyHolidaysQuery__
 *
 * To run a query within a React component, call `useIsPharmacyHolidaysQuery` and pass it any options that fit your needs.
 * When your component renders, `useIsPharmacyHolidaysQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useIsPharmacyHolidaysQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useIsPharmacyHolidaysQuery(
  baseOptions: Apollo.QueryHookOptions<
    IsPharmacyHolidaysQuery,
    IsPharmacyHolidaysQueryVariables
  > &
    (
      | { variables: IsPharmacyHolidaysQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    IsPharmacyHolidaysQuery,
    IsPharmacyHolidaysQueryVariables
  >(IsPharmacyHolidaysDocument, options);
}
export function useIsPharmacyHolidaysLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    IsPharmacyHolidaysQuery,
    IsPharmacyHolidaysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    IsPharmacyHolidaysQuery,
    IsPharmacyHolidaysQueryVariables
  >(IsPharmacyHolidaysDocument, options);
}
export function useIsPharmacyHolidaysSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    IsPharmacyHolidaysQuery,
    IsPharmacyHolidaysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    IsPharmacyHolidaysQuery,
    IsPharmacyHolidaysQueryVariables
  >(IsPharmacyHolidaysDocument, options);
}
export type IsPharmacyHolidaysQueryHookResult = ReturnType<
  typeof useIsPharmacyHolidaysQuery
>;
export type IsPharmacyHolidaysLazyQueryHookResult = ReturnType<
  typeof useIsPharmacyHolidaysLazyQuery
>;
export type IsPharmacyHolidaysSuspenseQueryHookResult = ReturnType<
  typeof useIsPharmacyHolidaysSuspenseQuery
>;
export type IsPharmacyHolidaysQueryResult = Apollo.QueryResult<
  IsPharmacyHolidaysQuery,
  IsPharmacyHolidaysQueryVariables
>;
export const GetPharmacyBusinessDaysDocument = gql`
  query getPharmacyBusinessDays($input: GetPharmacyBusinessDaysInput!) {
    getPharmacyBusinessDays(input: $input) {
      date
      isHoliday
    }
  }
`;

/**
 * __useGetPharmacyBusinessDaysQuery__
 *
 * To run a query within a React component, call `useGetPharmacyBusinessDaysQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPharmacyBusinessDaysQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPharmacyBusinessDaysQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetPharmacyBusinessDaysQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetPharmacyBusinessDaysQuery,
    GetPharmacyBusinessDaysQueryVariables
  > &
    (
      | { variables: GetPharmacyBusinessDaysQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPharmacyBusinessDaysQuery,
    GetPharmacyBusinessDaysQueryVariables
  >(GetPharmacyBusinessDaysDocument, options);
}
export function useGetPharmacyBusinessDaysLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPharmacyBusinessDaysQuery,
    GetPharmacyBusinessDaysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPharmacyBusinessDaysQuery,
    GetPharmacyBusinessDaysQueryVariables
  >(GetPharmacyBusinessDaysDocument, options);
}
export function useGetPharmacyBusinessDaysSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPharmacyBusinessDaysQuery,
    GetPharmacyBusinessDaysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPharmacyBusinessDaysQuery,
    GetPharmacyBusinessDaysQueryVariables
  >(GetPharmacyBusinessDaysDocument, options);
}
export type GetPharmacyBusinessDaysQueryHookResult = ReturnType<
  typeof useGetPharmacyBusinessDaysQuery
>;
export type GetPharmacyBusinessDaysLazyQueryHookResult = ReturnType<
  typeof useGetPharmacyBusinessDaysLazyQuery
>;
export type GetPharmacyBusinessDaysSuspenseQueryHookResult = ReturnType<
  typeof useGetPharmacyBusinessDaysSuspenseQuery
>;
export type GetPharmacyBusinessDaysQueryResult = Apollo.QueryResult<
  GetPharmacyBusinessDaysQuery,
  GetPharmacyBusinessDaysQueryVariables
>;
export const CreatePharmacyHolidayDocument = gql`
  mutation createPharmacyHoliday($input: CreatePharmacyHolidayInput!) {
    createPharmacyHoliday(input: $input) {
      pharmacyHolidayId
      hospitalId
      holidayStartDate
      holidayEndDate
    }
  }
`;
export type CreatePharmacyHolidayMutationFn = Apollo.MutationFunction<
  CreatePharmacyHolidayMutation,
  CreatePharmacyHolidayMutationVariables
>;

/**
 * __useCreatePharmacyHolidayMutation__
 *
 * To run a mutation, you first call `useCreatePharmacyHolidayMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreatePharmacyHolidayMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createPharmacyHolidayMutation, { data, loading, error }] = useCreatePharmacyHolidayMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreatePharmacyHolidayMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreatePharmacyHolidayMutation,
    CreatePharmacyHolidayMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreatePharmacyHolidayMutation,
    CreatePharmacyHolidayMutationVariables
  >(CreatePharmacyHolidayDocument, options);
}
export type CreatePharmacyHolidayMutationHookResult = ReturnType<
  typeof useCreatePharmacyHolidayMutation
>;
export type CreatePharmacyHolidayMutationResult =
  Apollo.MutationResult<CreatePharmacyHolidayMutation>;
export type CreatePharmacyHolidayMutationOptions = Apollo.BaseMutationOptions<
  CreatePharmacyHolidayMutation,
  CreatePharmacyHolidayMutationVariables
>;
export const UpdatePharmacyHolidayDocument = gql`
  mutation updatePharmacyHoliday($input: UpdatePharmacyHolidayInput!) {
    updatePharmacyHoliday(input: $input) {
      pharmacyHolidayId
      hospitalId
      holidayStartDate
      holidayEndDate
    }
  }
`;
export type UpdatePharmacyHolidayMutationFn = Apollo.MutationFunction<
  UpdatePharmacyHolidayMutation,
  UpdatePharmacyHolidayMutationVariables
>;

/**
 * __useUpdatePharmacyHolidayMutation__
 *
 * To run a mutation, you first call `useUpdatePharmacyHolidayMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdatePharmacyHolidayMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updatePharmacyHolidayMutation, { data, loading, error }] = useUpdatePharmacyHolidayMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdatePharmacyHolidayMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdatePharmacyHolidayMutation,
    UpdatePharmacyHolidayMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdatePharmacyHolidayMutation,
    UpdatePharmacyHolidayMutationVariables
  >(UpdatePharmacyHolidayDocument, options);
}
export type UpdatePharmacyHolidayMutationHookResult = ReturnType<
  typeof useUpdatePharmacyHolidayMutation
>;
export type UpdatePharmacyHolidayMutationResult =
  Apollo.MutationResult<UpdatePharmacyHolidayMutation>;
export type UpdatePharmacyHolidayMutationOptions = Apollo.BaseMutationOptions<
  UpdatePharmacyHolidayMutation,
  UpdatePharmacyHolidayMutationVariables
>;
export const DeletePharmacyHolidayDocument = gql`
  mutation deletePharmacyHoliday($input: DeletePharmacyHolidayInput!) {
    deletePharmacyHoliday(input: $input)
  }
`;
export type DeletePharmacyHolidayMutationFn = Apollo.MutationFunction<
  DeletePharmacyHolidayMutation,
  DeletePharmacyHolidayMutationVariables
>;

/**
 * __useDeletePharmacyHolidayMutation__
 *
 * To run a mutation, you first call `useDeletePharmacyHolidayMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeletePharmacyHolidayMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deletePharmacyHolidayMutation, { data, loading, error }] = useDeletePharmacyHolidayMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useDeletePharmacyHolidayMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeletePharmacyHolidayMutation,
    DeletePharmacyHolidayMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeletePharmacyHolidayMutation,
    DeletePharmacyHolidayMutationVariables
  >(DeletePharmacyHolidayDocument, options);
}
export type DeletePharmacyHolidayMutationHookResult = ReturnType<
  typeof useDeletePharmacyHolidayMutation
>;
export type DeletePharmacyHolidayMutationResult =
  Apollo.MutationResult<DeletePharmacyHolidayMutation>;
export type DeletePharmacyHolidayMutationOptions = Apollo.BaseMutationOptions<
  DeletePharmacyHolidayMutation,
  DeletePharmacyHolidayMutationVariables
>;
