import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type SearchMstItemMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsMstItemSearchTenMstItemRequestInput;
}>;

export type SearchMstItemMutation = {
  __typename?: "mutation_root";
  postApiMstItemSearchTenMstItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
      totalCount?: number;
      tenMsts?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemTenItemDto";
        kouiName?: string;
        itemCd?: string;
        name?: string;
        odrUnitName?: string;
        sinKouiKbn?: number;
        drugKbn?: number;
        masterSbt?: string;
        centerName?: string;
        yohoKbn?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
        isAdopted?: number;
        buiKbn?: number;
        senteiRyoyoKbn?: number;
        centerCd?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cnvTermVal?: number;
        cnvUnitName?: string;
        defaultValue?: number;
        endDate?: number;
        handanGrpKbn?: number;
        hpId?: number;
        ipnCD?: string;
        ipnName?: string;
        ipnNameCd?: string;
        isKensaMstEmpty?: boolean;
        isGetPriceInYakka?: boolean;
        kanaName1?: string;
        kanaName2?: string;
        kanaName3?: string;
        kanaName4?: string;
        kanaName5?: string;
        kanaName6?: string;
        kanaName7?: string;
        kasan1?: number;
        kasan2?: number;
        kensaCenterItemCDDisplay?: string;
        kensaMstCenterItemCd1?: string;
        kensaMstCenterItemCd2?: string;
        kohatuKbn?: number;
        kohatuKbnDisplay?: string;
        kokuji1?: string;
        kokuji2?: string;
        kouseisinKbn?: number;
        kouseisinKbnDisplay?: string;
        kubunToDisplay?: string;
        madokuKbn?: number;
        modeStatus?: number;
        odrTermVal?: number;
        receName?: string;
        rousaiKbn?: number;
        rousaiKbnDisplay?: string;
        senteiRyoyoYakka?: number;
        startDate?: number;
        ten?: number;
        tenDisplay?: string;
        tenId?: number;
        yakka?: number;
        yjCd?: string;
      }>;
    };
  };
};

export type GetApiMstItemSearchTenMstItemQueryVariables = Types.Exact<{
  pageCount: Types.Scalars["Int"]["input"];
  pageIndex: Types.Scalars["Int"]["input"];
  keyword?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  kouiKbn?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  kouiKbns?: Types.InputMaybe<
    Array<Types.Scalars["Int"]["input"]> | Types.Scalars["Int"]["input"]
  >;
  isIncludeUsageSupplement?: Types.InputMaybe<
    Types.Scalars["Boolean"]["input"]
  >;
  sinDate: Types.Scalars["Int"]["input"];
  yjCode?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  genericOrSameItem?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  includeRosai?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  includeMisai?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isIncludeItemExam?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isSearch831SuffixOnly?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isSearchGazoDensibaitaiHozon?: Types.InputMaybe<
    Types.Scalars["Boolean"]["input"]
  >;
}>;

export type GetApiMstItemSearchTenMstItemQuery = {
  __typename?: "query_root";
  getApiMstItemSearchTenMstItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSearchTenMstItemResponse";
      totalCount?: number;
      tenMsts?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemTenItemDto";
        kouiName?: string;
        itemCd?: string;
        name?: string;
        odrUnitName?: string;
        sinKouiKbn?: number;
        drugKbn?: number;
        masterSbt?: string;
        centerName?: string;
        yohoKbn?: number;
        cmtColKeta1?: number;
        cmtColKeta2?: number;
        cmtColKeta3?: number;
        cmtColKeta4?: number;
        isAdopted?: number;
        buiKbn?: number;
        senteiRyoyoKbn?: number;
        centerCd?: string;
        cmtCol1?: number;
        cmtCol2?: number;
        cmtCol3?: number;
        cmtCol4?: number;
        cnvTermVal?: number;
        cnvUnitName?: string;
        defaultValue?: number;
        endDate?: number;
        handanGrpKbn?: number;
        hpId?: number;
        ipnCD?: string;
        ipnName?: string;
        ipnNameCd?: string;
        isKensaMstEmpty?: boolean;
        isGetPriceInYakka?: boolean;
        kanaName1?: string;
        kanaName2?: string;
        kanaName3?: string;
        kanaName4?: string;
        kanaName5?: string;
        kanaName6?: string;
        kanaName7?: string;
        kasan1?: number;
        kasan2?: number;
        kensaCenterItemCDDisplay?: string;
        kensaMstCenterItemCd1?: string;
        kensaMstCenterItemCd2?: string;
        kohatuKbn?: number;
        kohatuKbnDisplay?: string;
        kokuji1?: string;
        kokuji2?: string;
        kouseisinKbn?: number;
        kouseisinKbnDisplay?: string;
        kubunToDisplay?: string;
        madokuKbn?: number;
        modeStatus?: number;
        odrTermVal?: number;
        receName?: string;
        rousaiKbn?: number;
        rousaiKbnDisplay?: string;
        senteiRyoyoYakka?: number;
        startDate?: number;
        ten?: number;
        tenDisplay?: string;
        tenId?: number;
        yakka?: number;
        yjCd?: string;
        isSelectiveComment?: boolean;
      }>;
    };
  };
};

export type GetOrdInfHeaderInfQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  raiinNo: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
}>;

export type GetOrdInfHeaderInfQuery = {
  __typename?: "query_root";
  getOrdInfHeaderInf?: {
    __typename?: "GetHeaderInfResponse";
    status?: number;
    data?: {
      __typename?: "Data";
      hokenPid?: number;
      tantoId?: number;
      tantoName?: string;
      santeiKbn?: number;
      kaSname?: string;
      kaId?: number;
      sinEndTime?: string;
      sinStartTime?: string;
      raiinInfStatus?: number;
      uketukeNo?: number;
      reserveDetailId?: number;
      syosaiKbn?: number;
      jikanKbn?: number;
      uketukeTime?: string;
      tagNo?: number;
      odrInfs?: {
        __typename?: "OrdInfModel";
        inoutKbn?: number;
        odrKouiKbn?: number;
        daysCnt?: number;
        hokenPid?: number;
        hpId?: number;
        isDeleted?: number;
        ptId?: string;
        raiinNo?: string;
        rpEdaNo?: string;
        rpNo?: string;
        santeiKbn?: number;
        sikyuKbn?: number;
        sinDate?: number;
        sortNo?: number;
        syohoSbt?: number;
        ordInfDetails?: Array<{
          __typename?: "OrdInfDetail";
          itemCd?: string;
          ptId?: string;
          sinDate?: number;
          raiinNo?: string;
          rpNo?: string;
          rpEdaNo?: string;
          rowNo?: number;
          sinKouiKbn?: number;
          suryo?: number;
          unitName?: string;
          termVal?: number;
          syohoKbn?: number;
          drugKbn?: number;
          yohoKbn?: number;
          kokuji1?: string;
          kokuji2?: string;
          isNodspRece?: number;
          ipnCd?: string;
          ipnName?: string;
          cmtOpt?: string;
          itemName?: string;
          isDummy?: boolean;
        }>;
      };
    };
  };
};

export type PutApiVisitingUpdateStaticCellKarteMutationVariables = Types.Exact<{
  emrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput: Types.EmrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput;
}>;

export type PutApiVisitingUpdateStaticCellKarteMutation = {
  __typename?: "mutation_root";
  putApiVisitingUpdateStaticCell?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionUpdateReceptionStaticCellResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionUpdateReceptionStaticCellResponse";
      success?: boolean;
    };
  };
};

export type PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutationVariables =
  Types.Exact<{
    emrCloudApiRequestsMedicalExaminationSaveTreatmentDepartmentAndDoctorRequestInput: Types.EmrCloudApiRequestsMedicalExaminationSaveTreatmentDepartmentAndDoctorRequestInput;
  }>;

export type PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation = {
  __typename?: "mutation_root";
  putApiMedicalExaminationSaveTreatmentDepartmentAndDoctor?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationSaveTreatmentDepartmentAndDoctorResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationSaveTreatmentDepartmentAndDoctorResponse";
      success?: boolean;
    };
  };
};

export type GetApiInsuranceMstFindHokenInfByPtIdQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
}>;

export type GetApiInsuranceMstFindHokenInfByPtIdQuery = {
  __typename?: "query_root";
  getApiInsuranceMstFindHokenInfByPtId?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceFindHokenInfByPtIdResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceFindHokenInfByPtIdResponse";
      hokenInfList?: Array<{
        __typename?: "EmrCloudApiResponsesInsuranceHokenInfDto";
        isExpirated?: boolean;
        bango?: string;
        confirmDate?: number;
        hokenId?: number;
        hokenKbn?: number;
        seqNo?: string;
        isDeleted?: number;
        hokenMst?: {
          __typename?: "EmrCloudApiResponsesInsuranceHokenMstDto";
          hokenName?: string;
          hokenSbtKbn?: number;
          houbetu?: string;
        };
      }>;
    };
  };
};

export type GetApiInsuranceMstGetListHokenFundQueryVariables = Types.Exact<{
  ptId: Types.Scalars["BigInt"]["input"];
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiInsuranceMstGetListHokenFundQuery = {
  __typename?: "query_root";
  getApiInsuranceMstGetListHokenFund?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesInsuranceMstGetListHokenFundResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesInsuranceMstGetListHokenFundResponse";
      kohiInfModels?: Array<{
        __typename?: "DomainModelsInsuranceKohiInfModel";
        hokenId?: number;
        hokenName?: string;
        seqNo?: string;
        isExpirated?: boolean;
        isDeleted?: number;
      }>;
    };
  };
};

export type PostApiTodayOrdUpsertPtHokenPatternMutationVariables = Types.Exact<{
  emrCloudApiRequestsMedicalExaminationUpsertHokenPatternRequestInput: Types.EmrCloudApiRequestsMedicalExaminationUpsertHokenPatternRequestInput;
}>;

export type PostApiTodayOrdUpsertPtHokenPatternMutation = {
  __typename?: "mutation_root";
  postApiTodayOrdUpsertPtHokenPattern?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationUpsertHokenPatternResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationUpsertHokenPatternResponse";
      hokenName?: string;
      hokenSName?: string;
      hokenPid?: number;
      isExpired?: boolean;
      status?: number;
      validationPtHokenInf?: {
        __typename?: "EmrCloudApiResponsesMedicalExaminationPtHokenInfResponse";
        status?: number;
        validationMessage?: string;
      };
    };
  };
};

export type GetApiInputItemGetListUsageTreeSetQueryVariables = Types.Exact<{
  kouiKbn: Types.Scalars["Int"]["input"];
  sinDate: Types.Scalars["Int"]["input"];
}>;

export type GetApiInputItemGetListUsageTreeSetQuery = {
  __typename?: "query_root";
  getApiInputItemGetListUsageTreeSet?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesUsageTreeSetResponseGetUsageTreeSetListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesUsageTreeSetResponseGetUsageTreeSetListResponse";
      data?: Array<{
        __typename?: "DomainModelsUsageTreeSetListSetMstModel";
        cmtName?: string;
        cmtOpt?: string;
        generationId?: number;
        defaultValue?: number;
        hasChildItems?: boolean;
        isTitle?: number;
        itemCd?: string;
        selectType?: number;
        setId?: number;
        setKbn?: number;
        setName?: string;
        sinKouiKbn?: number;
        startDate?: number;
        suryo?: number;
        unitName?: string;
        yohoKbn?: number;
        childrens?: Array<{
          __typename?: "DomainModelsUsageTreeSetListSetMstModel";
          cmtName?: string;
          cmtOpt?: string;
          defaultValue?: number;
          generationId?: number;
          hasChildItems?: boolean;
          isTitle?: number;
          itemCd?: string;
          selectType?: number;
          setId?: number;
          setKbn?: number;
          setName?: string;
          sinKouiKbn?: number;
          startDate?: number;
          suryo?: number;
          unitName?: string;
          yohoKbn?: number;
        }>;
      }>;
    };
  };
};

export type PostApiMstItemUpdateAdoptedInputItemMutationVariables =
  Types.Exact<{
    startDateInputItem?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    itemCdInputItem?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
    valueAdopted?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  }>;

export type PostApiMstItemUpdateAdoptedInputItemMutation = {
  __typename?: "mutation_root";
  postApiMstItemUpdateAdoptedInputItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemUpdateAdoptedTenItemResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemUpdateAdoptedTenItemResponse";
      data?: boolean;
    };
  };
};

export type GetApiMstItemSearchUsageItemQueryVariables = Types.Exact<{
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiMstItemSearchUsageItemQuery = {
  __typename?: "query_root";
  getApiMstItemSearchUsageItem?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSearchUsageItemResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSearchUsageItemResponse";
      totalCount?: number;
      usageItems?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemUsageItemDto";
        centerName?: string;
        centerCd?: string;
        buiKbn?: number;
        defaultValue?: number;
        drugKbn?: number;
        endDate?: number;
        fukuyoDaytime?: number;
        fukuyoMorning?: number;
        fukuyoNight?: number;
        fukuyoRise?: number;
        hpId?: number;
        itemCd?: string;
        name?: string;
        odrUnitName?: string;
        odrTermVal?: number;
        sinKouiKbn?: number;
        startDate?: number;
        yjCd?: string;
        yohoKbn?: number;
      }>;
    };
  };
};

export type PostApiMedicalExaminationCalculateMutationVariables = Types.Exact<{
  fromRcCheck?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isSagaku?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  prefix?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  seikyuUp?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type PostApiMedicalExaminationCalculateMutation = {
  __typename?: "mutation_root";
  postApiMedicalExaminationCalculate?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMedicalExaminationCalculateResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMedicalExaminationCalculateResponse";
      ptId?: string;
      sinDate?: number;
    };
  };
};

export const SearchMstItemDocument = gql`
  mutation searchMstItem(
    $input: EmrCloudApiRequestsMstItemSearchTenMstItemRequestInput!
  ) {
    postApiMstItemSearchTenMstItem(
      emrCloudApiRequestsMstItemSearchTenMstItemRequestInput: $input
    ) {
      message
      status
      data {
        totalCount
        tenMsts {
          kouiName
          itemCd
          name
          odrUnitName
          sinKouiKbn
          drugKbn
          masterSbt
          centerName
          yohoKbn
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
          isAdopted
          buiKbn
          senteiRyoyoKbn
          centerCd
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cnvTermVal
          cnvUnitName
          defaultValue
          endDate
          handanGrpKbn
          hpId
          ipnCD
          ipnName
          ipnNameCd
          isKensaMstEmpty
          isGetPriceInYakka
          kanaName1
          kanaName2
          kanaName3
          kanaName4
          kanaName5
          kanaName6
          kanaName7
          kasan1
          kasan2
          kensaCenterItemCDDisplay
          kensaMstCenterItemCd1
          kensaMstCenterItemCd2
          kohatuKbn
          kohatuKbnDisplay
          kokuji1
          kokuji2
          kouseisinKbn
          kouseisinKbnDisplay
          kubunToDisplay
          madokuKbn
          modeStatus
          odrTermVal
          receName
          rousaiKbn
          rousaiKbnDisplay
          senteiRyoyoYakka
          startDate
          ten
          tenDisplay
          tenId
          yakka
          yjCd
          rousaiKbn
          centerCd
        }
      }
    }
  }
`;
export type SearchMstItemMutationFn = Apollo.MutationFunction<
  SearchMstItemMutation,
  SearchMstItemMutationVariables
>;

/**
 * __useSearchMstItemMutation__
 *
 * To run a mutation, you first call `useSearchMstItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSearchMstItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [searchMstItemMutation, { data, loading, error }] = useSearchMstItemMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSearchMstItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SearchMstItemMutation,
    SearchMstItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SearchMstItemMutation,
    SearchMstItemMutationVariables
  >(SearchMstItemDocument, options);
}
export type SearchMstItemMutationHookResult = ReturnType<
  typeof useSearchMstItemMutation
>;
export type SearchMstItemMutationResult =
  Apollo.MutationResult<SearchMstItemMutation>;
export type SearchMstItemMutationOptions = Apollo.BaseMutationOptions<
  SearchMstItemMutation,
  SearchMstItemMutationVariables
>;
export const GetApiMstItemSearchTenMstItemDocument = gql`
  query getApiMstItemSearchTenMstItem(
    $pageCount: Int!
    $pageIndex: Int!
    $keyword: String
    $kouiKbn: Int
    $kouiKbns: [Int!]
    $isIncludeUsageSupplement: Boolean
    $sinDate: Int!
    $yjCode: String
    $genericOrSameItem: Int
    $includeRosai: Boolean
    $includeMisai: Boolean
    $isIncludeItemExam: Boolean
    $isSearch831SuffixOnly: Boolean
    $isSearchGazoDensibaitaiHozon: Boolean
  ) {
    getApiMstItemSearchTenMstItem(
      pageCount: $pageCount
      pageIndex: $pageIndex
      keyword: $keyword
      kouiKbn: $kouiKbn
      kouiKbns: $kouiKbns
      isIncludeUsageSupplement: $isIncludeUsageSupplement
      sinDate: $sinDate
      yjCode: $yjCode
      genericOrSameItem: $genericOrSameItem
      includeRosai: $includeRosai
      includeMisai: $includeMisai
      isIncludeItemExam: $isIncludeItemExam
      isSearch831SuffixOnly: $isSearch831SuffixOnly
      isSearchGazoDensibaitaiHozon: $isSearchGazoDensibaitaiHozon
    ) {
      message
      status
      data {
        totalCount
        tenMsts {
          kouiName
          itemCd
          name
          odrUnitName
          sinKouiKbn
          drugKbn
          masterSbt
          centerName
          yohoKbn
          cmtColKeta1
          cmtColKeta2
          cmtColKeta3
          cmtColKeta4
          isAdopted
          buiKbn
          senteiRyoyoKbn
          centerCd
          cmtCol1
          cmtCol2
          cmtCol3
          cmtCol4
          cnvTermVal
          cnvUnitName
          defaultValue
          endDate
          handanGrpKbn
          hpId
          ipnCD
          ipnName
          ipnNameCd
          isKensaMstEmpty
          isGetPriceInYakka
          kanaName1
          kanaName2
          kanaName3
          kanaName4
          kanaName5
          kanaName6
          kanaName7
          kasan1
          kasan2
          kensaCenterItemCDDisplay
          kensaMstCenterItemCd1
          kensaMstCenterItemCd2
          kohatuKbn
          kohatuKbnDisplay
          kokuji1
          kokuji2
          kouseisinKbn
          kouseisinKbnDisplay
          kubunToDisplay
          madokuKbn
          modeStatus
          odrTermVal
          receName
          rousaiKbn
          rousaiKbnDisplay
          senteiRyoyoYakka
          startDate
          ten
          tenDisplay
          tenId
          yakka
          yjCd
          rousaiKbn
          centerCd
          isSelectiveComment
        }
      }
    }
  }
`;

/**
 * __useGetApiMstItemSearchTenMstItemQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemSearchTenMstItemQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemSearchTenMstItemQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemSearchTenMstItemQuery({
 *   variables: {
 *      pageCount: // value for 'pageCount'
 *      pageIndex: // value for 'pageIndex'
 *      keyword: // value for 'keyword'
 *      kouiKbn: // value for 'kouiKbn'
 *      kouiKbns: // value for 'kouiKbns'
 *      isIncludeUsageSupplement: // value for 'isIncludeUsageSupplement'
 *      sinDate: // value for 'sinDate'
 *      yjCode: // value for 'yjCode'
 *      genericOrSameItem: // value for 'genericOrSameItem'
 *      includeRosai: // value for 'includeRosai'
 *      includeMisai: // value for 'includeMisai'
 *      isIncludeItemExam: // value for 'isIncludeItemExam'
 *      isSearch831SuffixOnly: // value for 'isSearch831SuffixOnly'
 *      isSearchGazoDensibaitaiHozon: // value for 'isSearchGazoDensibaitaiHozon'
 *   },
 * });
 */
export function useGetApiMstItemSearchTenMstItemQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiMstItemSearchTenMstItemQuery,
    GetApiMstItemSearchTenMstItemQueryVariables
  > &
    (
      | {
          variables: GetApiMstItemSearchTenMstItemQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemSearchTenMstItemQuery,
    GetApiMstItemSearchTenMstItemQueryVariables
  >(GetApiMstItemSearchTenMstItemDocument, options);
}
export function useGetApiMstItemSearchTenMstItemLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemSearchTenMstItemQuery,
    GetApiMstItemSearchTenMstItemQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemSearchTenMstItemQuery,
    GetApiMstItemSearchTenMstItemQueryVariables
  >(GetApiMstItemSearchTenMstItemDocument, options);
}
export function useGetApiMstItemSearchTenMstItemSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemSearchTenMstItemQuery,
    GetApiMstItemSearchTenMstItemQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemSearchTenMstItemQuery,
    GetApiMstItemSearchTenMstItemQueryVariables
  >(GetApiMstItemSearchTenMstItemDocument, options);
}
export type GetApiMstItemSearchTenMstItemQueryHookResult = ReturnType<
  typeof useGetApiMstItemSearchTenMstItemQuery
>;
export type GetApiMstItemSearchTenMstItemLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemSearchTenMstItemLazyQuery
>;
export type GetApiMstItemSearchTenMstItemSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemSearchTenMstItemSuspenseQuery
>;
export type GetApiMstItemSearchTenMstItemQueryResult = Apollo.QueryResult<
  GetApiMstItemSearchTenMstItemQuery,
  GetApiMstItemSearchTenMstItemQueryVariables
>;
export const GetOrdInfHeaderInfDocument = gql`
  query getOrdInfHeaderInf($ptId: BigInt!, $raiinNo: BigInt!, $sinDate: Int!) {
    getOrdInfHeaderInf(ptId: $ptId, raiinNo: $raiinNo, sinDate: $sinDate) {
      status
      data {
        hokenPid
        tantoId
        tantoName
        santeiKbn
        kaSname
        kaId
        sinEndTime
        sinStartTime
        raiinInfStatus
        uketukeNo
        reserveDetailId
        syosaiKbn
        jikanKbn
        uketukeTime
        tagNo
        sinEndTime
        sinStartTime
        odrInfs {
          inoutKbn
          odrKouiKbn
          daysCnt
          hokenPid
          hpId
          isDeleted
          ptId
          raiinNo
          rpEdaNo
          rpNo
          santeiKbn
          sikyuKbn
          sinDate
          sortNo
          syohoSbt
          ordInfDetails {
            itemCd
            ptId
            sinDate
            raiinNo
            rpNo
            rpEdaNo
            rowNo
            sinKouiKbn
            suryo
            unitName
            termVal
            syohoKbn
            drugKbn
            yohoKbn
            kokuji1
            kokuji2
            isNodspRece
            ipnCd
            ipnName
            cmtOpt
            itemName
            isDummy
          }
        }
      }
    }
  }
`;

/**
 * __useGetOrdInfHeaderInfQuery__
 *
 * To run a query within a React component, call `useGetOrdInfHeaderInfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetOrdInfHeaderInfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetOrdInfHeaderInfQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetOrdInfHeaderInfQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetOrdInfHeaderInfQuery,
    GetOrdInfHeaderInfQueryVariables
  > &
    (
      | { variables: GetOrdInfHeaderInfQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetOrdInfHeaderInfQuery,
    GetOrdInfHeaderInfQueryVariables
  >(GetOrdInfHeaderInfDocument, options);
}
export function useGetOrdInfHeaderInfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetOrdInfHeaderInfQuery,
    GetOrdInfHeaderInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetOrdInfHeaderInfQuery,
    GetOrdInfHeaderInfQueryVariables
  >(GetOrdInfHeaderInfDocument, options);
}
export function useGetOrdInfHeaderInfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetOrdInfHeaderInfQuery,
    GetOrdInfHeaderInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetOrdInfHeaderInfQuery,
    GetOrdInfHeaderInfQueryVariables
  >(GetOrdInfHeaderInfDocument, options);
}
export type GetOrdInfHeaderInfQueryHookResult = ReturnType<
  typeof useGetOrdInfHeaderInfQuery
>;
export type GetOrdInfHeaderInfLazyQueryHookResult = ReturnType<
  typeof useGetOrdInfHeaderInfLazyQuery
>;
export type GetOrdInfHeaderInfSuspenseQueryHookResult = ReturnType<
  typeof useGetOrdInfHeaderInfSuspenseQuery
>;
export type GetOrdInfHeaderInfQueryResult = Apollo.QueryResult<
  GetOrdInfHeaderInfQuery,
  GetOrdInfHeaderInfQueryVariables
>;
export const PutApiVisitingUpdateStaticCellKarteDocument = gql`
  mutation putApiVisitingUpdateStaticCellKarte(
    $emrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput: EmrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput!
  ) {
    putApiVisitingUpdateStaticCell(
      emrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput: $emrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput
    ) {
      data {
        success
      }
      message
      status
    }
  }
`;
export type PutApiVisitingUpdateStaticCellKarteMutationFn =
  Apollo.MutationFunction<
    PutApiVisitingUpdateStaticCellKarteMutation,
    PutApiVisitingUpdateStaticCellKarteMutationVariables
  >;

/**
 * __usePutApiVisitingUpdateStaticCellKarteMutation__
 *
 * To run a mutation, you first call `usePutApiVisitingUpdateStaticCellKarteMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePutApiVisitingUpdateStaticCellKarteMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [putApiVisitingUpdateStaticCellKarteMutation, { data, loading, error }] = usePutApiVisitingUpdateStaticCellKarteMutation({
 *   variables: {
 *      emrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput: // value for 'emrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput'
 *   },
 * });
 */
export function usePutApiVisitingUpdateStaticCellKarteMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PutApiVisitingUpdateStaticCellKarteMutation,
    PutApiVisitingUpdateStaticCellKarteMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PutApiVisitingUpdateStaticCellKarteMutation,
    PutApiVisitingUpdateStaticCellKarteMutationVariables
  >(PutApiVisitingUpdateStaticCellKarteDocument, options);
}
export type PutApiVisitingUpdateStaticCellKarteMutationHookResult = ReturnType<
  typeof usePutApiVisitingUpdateStaticCellKarteMutation
>;
export type PutApiVisitingUpdateStaticCellKarteMutationResult =
  Apollo.MutationResult<PutApiVisitingUpdateStaticCellKarteMutation>;
export type PutApiVisitingUpdateStaticCellKarteMutationOptions =
  Apollo.BaseMutationOptions<
    PutApiVisitingUpdateStaticCellKarteMutation,
    PutApiVisitingUpdateStaticCellKarteMutationVariables
  >;
export const PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorDocument = gql`
  mutation putApiMedicalExaminationSaveTreatmentDepartmentAndDoctor(
    $emrCloudApiRequestsMedicalExaminationSaveTreatmentDepartmentAndDoctorRequestInput: EmrCloudApiRequestsMedicalExaminationSaveTreatmentDepartmentAndDoctorRequestInput!
  ) {
    putApiMedicalExaminationSaveTreatmentDepartmentAndDoctor(
      emrCloudApiRequestsMedicalExaminationSaveTreatmentDepartmentAndDoctorRequestInput: $emrCloudApiRequestsMedicalExaminationSaveTreatmentDepartmentAndDoctorRequestInput
    ) {
      data {
        success
      }
      message
      status
    }
  }
`;
export type PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutationFn =
  Apollo.MutationFunction<
    PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation,
    PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutationVariables
  >;

/**
 * __usePutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation__
 *
 * To run a mutation, you first call `usePutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [putApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation, { data, loading, error }] = usePutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationSaveTreatmentDepartmentAndDoctorRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationSaveTreatmentDepartmentAndDoctorRequestInput'
 *   },
 * });
 */
export function usePutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation,
    PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation,
    PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutationVariables
  >(PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorDocument, options);
}
export type PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutationHookResult =
  ReturnType<
    typeof usePutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation
  >;
export type PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutationResult =
  Apollo.MutationResult<PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation>;
export type PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutationOptions =
  Apollo.BaseMutationOptions<
    PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutation,
    PutApiMedicalExaminationSaveTreatmentDepartmentAndDoctorMutationVariables
  >;
export const GetApiInsuranceMstFindHokenInfByPtIdDocument = gql`
  query getApiInsuranceMstFindHokenInfByPtId($ptId: BigInt!, $sinDate: Int!) {
    getApiInsuranceMstFindHokenInfByPtId(ptId: $ptId, sinDate: $sinDate) {
      message
      status
      data {
        hokenInfList {
          isExpirated
          bango
          confirmDate
          hokenMst {
            hokenName
            hokenSbtKbn
            houbetu
          }
          hokenId
          hokenKbn
          seqNo
          isDeleted
        }
      }
    }
  }
`;

/**
 * __useGetApiInsuranceMstFindHokenInfByPtIdQuery__
 *
 * To run a query within a React component, call `useGetApiInsuranceMstFindHokenInfByPtIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiInsuranceMstFindHokenInfByPtIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiInsuranceMstFindHokenInfByPtIdQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiInsuranceMstFindHokenInfByPtIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiInsuranceMstFindHokenInfByPtIdQuery,
    GetApiInsuranceMstFindHokenInfByPtIdQueryVariables
  > &
    (
      | {
          variables: GetApiInsuranceMstFindHokenInfByPtIdQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiInsuranceMstFindHokenInfByPtIdQuery,
    GetApiInsuranceMstFindHokenInfByPtIdQueryVariables
  >(GetApiInsuranceMstFindHokenInfByPtIdDocument, options);
}
export function useGetApiInsuranceMstFindHokenInfByPtIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiInsuranceMstFindHokenInfByPtIdQuery,
    GetApiInsuranceMstFindHokenInfByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiInsuranceMstFindHokenInfByPtIdQuery,
    GetApiInsuranceMstFindHokenInfByPtIdQueryVariables
  >(GetApiInsuranceMstFindHokenInfByPtIdDocument, options);
}
export function useGetApiInsuranceMstFindHokenInfByPtIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiInsuranceMstFindHokenInfByPtIdQuery,
    GetApiInsuranceMstFindHokenInfByPtIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiInsuranceMstFindHokenInfByPtIdQuery,
    GetApiInsuranceMstFindHokenInfByPtIdQueryVariables
  >(GetApiInsuranceMstFindHokenInfByPtIdDocument, options);
}
export type GetApiInsuranceMstFindHokenInfByPtIdQueryHookResult = ReturnType<
  typeof useGetApiInsuranceMstFindHokenInfByPtIdQuery
>;
export type GetApiInsuranceMstFindHokenInfByPtIdLazyQueryHookResult =
  ReturnType<typeof useGetApiInsuranceMstFindHokenInfByPtIdLazyQuery>;
export type GetApiInsuranceMstFindHokenInfByPtIdSuspenseQueryHookResult =
  ReturnType<typeof useGetApiInsuranceMstFindHokenInfByPtIdSuspenseQuery>;
export type GetApiInsuranceMstFindHokenInfByPtIdQueryResult =
  Apollo.QueryResult<
    GetApiInsuranceMstFindHokenInfByPtIdQuery,
    GetApiInsuranceMstFindHokenInfByPtIdQueryVariables
  >;
export const GetApiInsuranceMstGetListHokenFundDocument = gql`
  query getApiInsuranceMstGetListHokenFund($ptId: BigInt!, $sinDate: Int) {
    getApiInsuranceMstGetListHokenFund(ptId: $ptId, sinDate: $sinDate) {
      message
      status
      data {
        kohiInfModels {
          hokenId
          hokenName
          seqNo
          isExpirated
          isDeleted
        }
      }
    }
  }
`;

/**
 * __useGetApiInsuranceMstGetListHokenFundQuery__
 *
 * To run a query within a React component, call `useGetApiInsuranceMstGetListHokenFundQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiInsuranceMstGetListHokenFundQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiInsuranceMstGetListHokenFundQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiInsuranceMstGetListHokenFundQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiInsuranceMstGetListHokenFundQuery,
    GetApiInsuranceMstGetListHokenFundQueryVariables
  > &
    (
      | {
          variables: GetApiInsuranceMstGetListHokenFundQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiInsuranceMstGetListHokenFundQuery,
    GetApiInsuranceMstGetListHokenFundQueryVariables
  >(GetApiInsuranceMstGetListHokenFundDocument, options);
}
export function useGetApiInsuranceMstGetListHokenFundLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiInsuranceMstGetListHokenFundQuery,
    GetApiInsuranceMstGetListHokenFundQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiInsuranceMstGetListHokenFundQuery,
    GetApiInsuranceMstGetListHokenFundQueryVariables
  >(GetApiInsuranceMstGetListHokenFundDocument, options);
}
export function useGetApiInsuranceMstGetListHokenFundSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiInsuranceMstGetListHokenFundQuery,
    GetApiInsuranceMstGetListHokenFundQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiInsuranceMstGetListHokenFundQuery,
    GetApiInsuranceMstGetListHokenFundQueryVariables
  >(GetApiInsuranceMstGetListHokenFundDocument, options);
}
export type GetApiInsuranceMstGetListHokenFundQueryHookResult = ReturnType<
  typeof useGetApiInsuranceMstGetListHokenFundQuery
>;
export type GetApiInsuranceMstGetListHokenFundLazyQueryHookResult = ReturnType<
  typeof useGetApiInsuranceMstGetListHokenFundLazyQuery
>;
export type GetApiInsuranceMstGetListHokenFundSuspenseQueryHookResult =
  ReturnType<typeof useGetApiInsuranceMstGetListHokenFundSuspenseQuery>;
export type GetApiInsuranceMstGetListHokenFundQueryResult = Apollo.QueryResult<
  GetApiInsuranceMstGetListHokenFundQuery,
  GetApiInsuranceMstGetListHokenFundQueryVariables
>;
export const PostApiTodayOrdUpsertPtHokenPatternDocument = gql`
  mutation postApiTodayOrdUpsertPtHokenPattern(
    $emrCloudApiRequestsMedicalExaminationUpsertHokenPatternRequestInput: EmrCloudApiRequestsMedicalExaminationUpsertHokenPatternRequestInput!
  ) {
    postApiTodayOrdUpsertPtHokenPattern(
      emrCloudApiRequestsMedicalExaminationUpsertHokenPatternRequestInput: $emrCloudApiRequestsMedicalExaminationUpsertHokenPatternRequestInput
    ) {
      data {
        validationPtHokenInf {
          status
          validationMessage
        }
        hokenName
        hokenSName
        hokenPid
        isExpired
        status
      }
      message
      status
    }
  }
`;
export type PostApiTodayOrdUpsertPtHokenPatternMutationFn =
  Apollo.MutationFunction<
    PostApiTodayOrdUpsertPtHokenPatternMutation,
    PostApiTodayOrdUpsertPtHokenPatternMutationVariables
  >;

/**
 * __usePostApiTodayOrdUpsertPtHokenPatternMutation__
 *
 * To run a mutation, you first call `usePostApiTodayOrdUpsertPtHokenPatternMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiTodayOrdUpsertPtHokenPatternMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiTodayOrdUpsertPtHokenPatternMutation, { data, loading, error }] = usePostApiTodayOrdUpsertPtHokenPatternMutation({
 *   variables: {
 *      emrCloudApiRequestsMedicalExaminationUpsertHokenPatternRequestInput: // value for 'emrCloudApiRequestsMedicalExaminationUpsertHokenPatternRequestInput'
 *   },
 * });
 */
export function usePostApiTodayOrdUpsertPtHokenPatternMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiTodayOrdUpsertPtHokenPatternMutation,
    PostApiTodayOrdUpsertPtHokenPatternMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiTodayOrdUpsertPtHokenPatternMutation,
    PostApiTodayOrdUpsertPtHokenPatternMutationVariables
  >(PostApiTodayOrdUpsertPtHokenPatternDocument, options);
}
export type PostApiTodayOrdUpsertPtHokenPatternMutationHookResult = ReturnType<
  typeof usePostApiTodayOrdUpsertPtHokenPatternMutation
>;
export type PostApiTodayOrdUpsertPtHokenPatternMutationResult =
  Apollo.MutationResult<PostApiTodayOrdUpsertPtHokenPatternMutation>;
export type PostApiTodayOrdUpsertPtHokenPatternMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiTodayOrdUpsertPtHokenPatternMutation,
    PostApiTodayOrdUpsertPtHokenPatternMutationVariables
  >;
export const GetApiInputItemGetListUsageTreeSetDocument = gql`
  query getApiInputItemGetListUsageTreeSet($kouiKbn: Int!, $sinDate: Int!) {
    getApiInputItemGetListUsageTreeSet(sinDate: $sinDate, kouiKbn: $kouiKbn) {
      data {
        data {
          cmtName
          cmtOpt
          generationId
          defaultValue
          hasChildItems
          isTitle
          itemCd
          selectType
          setId
          setKbn
          setName
          sinKouiKbn
          startDate
          suryo
          unitName
          yohoKbn
          childrens {
            cmtName
            cmtOpt
            defaultValue
            generationId
            hasChildItems
            isTitle
            itemCd
            selectType
            setId
            setKbn
            setName
            sinKouiKbn
            startDate
            suryo
            unitName
            yohoKbn
          }
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiInputItemGetListUsageTreeSetQuery__
 *
 * To run a query within a React component, call `useGetApiInputItemGetListUsageTreeSetQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiInputItemGetListUsageTreeSetQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiInputItemGetListUsageTreeSetQuery({
 *   variables: {
 *      kouiKbn: // value for 'kouiKbn'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiInputItemGetListUsageTreeSetQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiInputItemGetListUsageTreeSetQuery,
    GetApiInputItemGetListUsageTreeSetQueryVariables
  > &
    (
      | {
          variables: GetApiInputItemGetListUsageTreeSetQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiInputItemGetListUsageTreeSetQuery,
    GetApiInputItemGetListUsageTreeSetQueryVariables
  >(GetApiInputItemGetListUsageTreeSetDocument, options);
}
export function useGetApiInputItemGetListUsageTreeSetLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiInputItemGetListUsageTreeSetQuery,
    GetApiInputItemGetListUsageTreeSetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiInputItemGetListUsageTreeSetQuery,
    GetApiInputItemGetListUsageTreeSetQueryVariables
  >(GetApiInputItemGetListUsageTreeSetDocument, options);
}
export function useGetApiInputItemGetListUsageTreeSetSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiInputItemGetListUsageTreeSetQuery,
    GetApiInputItemGetListUsageTreeSetQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiInputItemGetListUsageTreeSetQuery,
    GetApiInputItemGetListUsageTreeSetQueryVariables
  >(GetApiInputItemGetListUsageTreeSetDocument, options);
}
export type GetApiInputItemGetListUsageTreeSetQueryHookResult = ReturnType<
  typeof useGetApiInputItemGetListUsageTreeSetQuery
>;
export type GetApiInputItemGetListUsageTreeSetLazyQueryHookResult = ReturnType<
  typeof useGetApiInputItemGetListUsageTreeSetLazyQuery
>;
export type GetApiInputItemGetListUsageTreeSetSuspenseQueryHookResult =
  ReturnType<typeof useGetApiInputItemGetListUsageTreeSetSuspenseQuery>;
export type GetApiInputItemGetListUsageTreeSetQueryResult = Apollo.QueryResult<
  GetApiInputItemGetListUsageTreeSetQuery,
  GetApiInputItemGetListUsageTreeSetQueryVariables
>;
export const PostApiMstItemUpdateAdoptedInputItemDocument = gql`
  mutation postApiMstItemUpdateAdoptedInputItem(
    $startDateInputItem: Int
    $itemCdInputItem: String
    $valueAdopted: Int
  ) {
    postApiMstItemUpdateAdoptedInputItem(
      emrCloudApiRequestsMstItemUpdateAdoptedTenItemRequestInput: {
        itemCdInputItem: $itemCdInputItem
        startDateInputItem: $startDateInputItem
        valueAdopted: $valueAdopted
      }
    ) {
      data {
        data
      }
      message
      status
    }
  }
`;
export type PostApiMstItemUpdateAdoptedInputItemMutationFn =
  Apollo.MutationFunction<
    PostApiMstItemUpdateAdoptedInputItemMutation,
    PostApiMstItemUpdateAdoptedInputItemMutationVariables
  >;

/**
 * __usePostApiMstItemUpdateAdoptedInputItemMutation__
 *
 * To run a mutation, you first call `usePostApiMstItemUpdateAdoptedInputItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMstItemUpdateAdoptedInputItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMstItemUpdateAdoptedInputItemMutation, { data, loading, error }] = usePostApiMstItemUpdateAdoptedInputItemMutation({
 *   variables: {
 *      startDateInputItem: // value for 'startDateInputItem'
 *      itemCdInputItem: // value for 'itemCdInputItem'
 *      valueAdopted: // value for 'valueAdopted'
 *   },
 * });
 */
export function usePostApiMstItemUpdateAdoptedInputItemMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMstItemUpdateAdoptedInputItemMutation,
    PostApiMstItemUpdateAdoptedInputItemMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMstItemUpdateAdoptedInputItemMutation,
    PostApiMstItemUpdateAdoptedInputItemMutationVariables
  >(PostApiMstItemUpdateAdoptedInputItemDocument, options);
}
export type PostApiMstItemUpdateAdoptedInputItemMutationHookResult = ReturnType<
  typeof usePostApiMstItemUpdateAdoptedInputItemMutation
>;
export type PostApiMstItemUpdateAdoptedInputItemMutationResult =
  Apollo.MutationResult<PostApiMstItemUpdateAdoptedInputItemMutation>;
export type PostApiMstItemUpdateAdoptedInputItemMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMstItemUpdateAdoptedInputItemMutation,
    PostApiMstItemUpdateAdoptedInputItemMutationVariables
  >;
export const GetApiMstItemSearchUsageItemDocument = gql`
  query getApiMstItemSearchUsageItem($sinDate: Int) {
    getApiMstItemSearchUsageItem(sinDate: $sinDate) {
      data {
        totalCount
        usageItems {
          centerName
          centerCd
          buiKbn
          defaultValue
          drugKbn
          endDate
          fukuyoDaytime
          fukuyoMorning
          fukuyoNight
          fukuyoRise
          hpId
          itemCd
          name
          odrUnitName
          odrTermVal
          sinKouiKbn
          startDate
          yjCd
          yohoKbn
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiMstItemSearchUsageItemQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemSearchUsageItemQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemSearchUsageItemQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemSearchUsageItemQuery({
 *   variables: {
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function useGetApiMstItemSearchUsageItemQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemSearchUsageItemQuery,
    GetApiMstItemSearchUsageItemQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemSearchUsageItemQuery,
    GetApiMstItemSearchUsageItemQueryVariables
  >(GetApiMstItemSearchUsageItemDocument, options);
}
export function useGetApiMstItemSearchUsageItemLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemSearchUsageItemQuery,
    GetApiMstItemSearchUsageItemQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemSearchUsageItemQuery,
    GetApiMstItemSearchUsageItemQueryVariables
  >(GetApiMstItemSearchUsageItemDocument, options);
}
export function useGetApiMstItemSearchUsageItemSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemSearchUsageItemQuery,
    GetApiMstItemSearchUsageItemQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemSearchUsageItemQuery,
    GetApiMstItemSearchUsageItemQueryVariables
  >(GetApiMstItemSearchUsageItemDocument, options);
}
export type GetApiMstItemSearchUsageItemQueryHookResult = ReturnType<
  typeof useGetApiMstItemSearchUsageItemQuery
>;
export type GetApiMstItemSearchUsageItemLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemSearchUsageItemLazyQuery
>;
export type GetApiMstItemSearchUsageItemSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemSearchUsageItemSuspenseQuery
>;
export type GetApiMstItemSearchUsageItemQueryResult = Apollo.QueryResult<
  GetApiMstItemSearchUsageItemQuery,
  GetApiMstItemSearchUsageItemQueryVariables
>;
export const PostApiMedicalExaminationCalculateDocument = gql`
  mutation postApiMedicalExaminationCalculate(
    $fromRcCheck: Boolean
    $isSagaku: Boolean
    $prefix: String
    $ptId: BigInt
    $raiinNo: BigInt
    $seikyuUp: Int
    $sinDate: Int
  ) {
    postApiMedicalExaminationCalculate(
      emrCloudApiRequestsMedicalExaminationCalculateRequestInput: {
        fromRcCheck: $fromRcCheck
        isSagaku: $isSagaku
        prefix: $prefix
        ptId: $ptId
        raiinNo: $raiinNo
        seikyuUp: $seikyuUp
        sinDate: $sinDate
      }
    ) {
      message
      data {
        ptId
        sinDate
      }
      status
    }
  }
`;
export type PostApiMedicalExaminationCalculateMutationFn =
  Apollo.MutationFunction<
    PostApiMedicalExaminationCalculateMutation,
    PostApiMedicalExaminationCalculateMutationVariables
  >;

/**
 * __usePostApiMedicalExaminationCalculateMutation__
 *
 * To run a mutation, you first call `usePostApiMedicalExaminationCalculateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMedicalExaminationCalculateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMedicalExaminationCalculateMutation, { data, loading, error }] = usePostApiMedicalExaminationCalculateMutation({
 *   variables: {
 *      fromRcCheck: // value for 'fromRcCheck'
 *      isSagaku: // value for 'isSagaku'
 *      prefix: // value for 'prefix'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      seikyuUp: // value for 'seikyuUp'
 *      sinDate: // value for 'sinDate'
 *   },
 * });
 */
export function usePostApiMedicalExaminationCalculateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMedicalExaminationCalculateMutation,
    PostApiMedicalExaminationCalculateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMedicalExaminationCalculateMutation,
    PostApiMedicalExaminationCalculateMutationVariables
  >(PostApiMedicalExaminationCalculateDocument, options);
}
export type PostApiMedicalExaminationCalculateMutationHookResult = ReturnType<
  typeof usePostApiMedicalExaminationCalculateMutation
>;
export type PostApiMedicalExaminationCalculateMutationResult =
  Apollo.MutationResult<PostApiMedicalExaminationCalculateMutation>;
export type PostApiMedicalExaminationCalculateMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMedicalExaminationCalculateMutation,
    PostApiMedicalExaminationCalculateMutationVariables
  >;
