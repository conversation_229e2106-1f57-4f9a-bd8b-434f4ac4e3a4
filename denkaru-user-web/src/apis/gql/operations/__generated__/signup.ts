import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type SignupMutationVariables = Types.Exact<{
  clinicStatus: Types.ClinicStatus;
  doctor: Types.DoctorInput;
  clinic?: Types.InputMaybe<Types.ClinicInput>;
  agreement: Types.Agreement;
  aid?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  bid?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  cid?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type SignupMutation = {
  __typename?: "mutation_root";
  signup: { __typename?: "signupRes"; userId: number };
};

export type UpdateHpStatusFromIvrMutationVariables = Types.Exact<{
  input: Types.UpdateHpStatusIvrInput;
}>;

export type UpdateHpStatusFromIvrMutation = {
  __typename?: "mutation_root";
  updateHpStatusFromIVR: boolean;
};

export type AuthenticateSignupUserMutationVariables = Types.Exact<{
  input: Types.AuthenticateSignupUserInput;
}>;

export type AuthenticateSignupUserMutation = {
  __typename?: "mutation_root";
  authenticateSignupUser: {
    __typename?: "authenticateSignupUserRes";
    status: string;
  };
};

export type FindSignupClinicInfoQueryVariables = Types.Exact<{
  input: Types.FindSignupClinicInfoInput;
}>;

export type FindSignupClinicInfoQuery = {
  __typename?: "query_root";
  findSignupClinicInfo?: {
    __typename?: "signupClinicInfoRes";
    clinicName: string;
    phoneNumber: string;
  };
};

export type FindClinicCodeSuggestionsQueryVariables = Types.Exact<{
  input: Types.ClinicCodeInput;
}>;

export type FindClinicCodeSuggestionsQuery = {
  __typename?: "query_root";
  findClinicCodeSuggestions?: Array<{
    __typename?: "clinicCodeSuggestion";
    clinicCode: string;
  }>;
};

export type GetPrefectureCodesQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetPrefectureCodesQuery = {
  __typename?: "query_root";
  getPrefectureCodes?: Array<{
    __typename?: "prefectureCode";
    name: string;
    code: string;
  }>;
};

export const SignupDocument = gql`
  mutation signup(
    $clinicStatus: ClinicStatus!
    $doctor: DoctorInput!
    $clinic: ClinicInput
    $agreement: Agreement!
    $aid: String
    $bid: String
    $cid: String
  ) {
    signup(
      input: {
        clinicStatus: $clinicStatus
        doctor: $doctor
        clinic: $clinic
        agreement: $agreement
        aid: $aid
        bid: $bid
        cid: $cid
      }
    ) {
      userId
    }
  }
`;
export type SignupMutationFn = Apollo.MutationFunction<
  SignupMutation,
  SignupMutationVariables
>;

/**
 * __useSignupMutation__
 *
 * To run a mutation, you first call `useSignupMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSignupMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [signupMutation, { data, loading, error }] = useSignupMutation({
 *   variables: {
 *      clinicStatus: // value for 'clinicStatus'
 *      doctor: // value for 'doctor'
 *      clinic: // value for 'clinic'
 *      agreement: // value for 'agreement'
 *      aid: // value for 'aid'
 *      bid: // value for 'bid'
 *      cid: // value for 'cid'
 *   },
 * });
 */
export function useSignupMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SignupMutation,
    SignupMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<SignupMutation, SignupMutationVariables>(
    SignupDocument,
    options,
  );
}
export type SignupMutationHookResult = ReturnType<typeof useSignupMutation>;
export type SignupMutationResult = Apollo.MutationResult<SignupMutation>;
export type SignupMutationOptions = Apollo.BaseMutationOptions<
  SignupMutation,
  SignupMutationVariables
>;
export const UpdateHpStatusFromIvrDocument = gql`
  mutation updateHpStatusFromIVR($input: UpdateHpStatusIVRInput!) {
    updateHpStatusFromIVR(input: $input)
  }
`;
export type UpdateHpStatusFromIvrMutationFn = Apollo.MutationFunction<
  UpdateHpStatusFromIvrMutation,
  UpdateHpStatusFromIvrMutationVariables
>;

/**
 * __useUpdateHpStatusFromIvrMutation__
 *
 * To run a mutation, you first call `useUpdateHpStatusFromIvrMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateHpStatusFromIvrMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateHpStatusFromIvrMutation, { data, loading, error }] = useUpdateHpStatusFromIvrMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateHpStatusFromIvrMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateHpStatusFromIvrMutation,
    UpdateHpStatusFromIvrMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateHpStatusFromIvrMutation,
    UpdateHpStatusFromIvrMutationVariables
  >(UpdateHpStatusFromIvrDocument, options);
}
export type UpdateHpStatusFromIvrMutationHookResult = ReturnType<
  typeof useUpdateHpStatusFromIvrMutation
>;
export type UpdateHpStatusFromIvrMutationResult =
  Apollo.MutationResult<UpdateHpStatusFromIvrMutation>;
export type UpdateHpStatusFromIvrMutationOptions = Apollo.BaseMutationOptions<
  UpdateHpStatusFromIvrMutation,
  UpdateHpStatusFromIvrMutationVariables
>;
export const AuthenticateSignupUserDocument = gql`
  mutation authenticateSignupUser($input: authenticateSignupUserInput!) {
    authenticateSignupUser(input: $input) {
      status
    }
  }
`;
export type AuthenticateSignupUserMutationFn = Apollo.MutationFunction<
  AuthenticateSignupUserMutation,
  AuthenticateSignupUserMutationVariables
>;

/**
 * __useAuthenticateSignupUserMutation__
 *
 * To run a mutation, you first call `useAuthenticateSignupUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAuthenticateSignupUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [authenticateSignupUserMutation, { data, loading, error }] = useAuthenticateSignupUserMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAuthenticateSignupUserMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AuthenticateSignupUserMutation,
    AuthenticateSignupUserMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AuthenticateSignupUserMutation,
    AuthenticateSignupUserMutationVariables
  >(AuthenticateSignupUserDocument, options);
}
export type AuthenticateSignupUserMutationHookResult = ReturnType<
  typeof useAuthenticateSignupUserMutation
>;
export type AuthenticateSignupUserMutationResult =
  Apollo.MutationResult<AuthenticateSignupUserMutation>;
export type AuthenticateSignupUserMutationOptions = Apollo.BaseMutationOptions<
  AuthenticateSignupUserMutation,
  AuthenticateSignupUserMutationVariables
>;
export const FindSignupClinicInfoDocument = gql`
  query findSignupClinicInfo($input: findSignupClinicInfoInput!) {
    findSignupClinicInfo(input: $input) {
      clinicName
      phoneNumber
    }
  }
`;

/**
 * __useFindSignupClinicInfoQuery__
 *
 * To run a query within a React component, call `useFindSignupClinicInfoQuery` and pass it any options that fit your needs.
 * When your component renders, `useFindSignupClinicInfoQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFindSignupClinicInfoQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useFindSignupClinicInfoQuery(
  baseOptions: Apollo.QueryHookOptions<
    FindSignupClinicInfoQuery,
    FindSignupClinicInfoQueryVariables
  > &
    (
      | { variables: FindSignupClinicInfoQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    FindSignupClinicInfoQuery,
    FindSignupClinicInfoQueryVariables
  >(FindSignupClinicInfoDocument, options);
}
export function useFindSignupClinicInfoLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FindSignupClinicInfoQuery,
    FindSignupClinicInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    FindSignupClinicInfoQuery,
    FindSignupClinicInfoQueryVariables
  >(FindSignupClinicInfoDocument, options);
}
export function useFindSignupClinicInfoSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    FindSignupClinicInfoQuery,
    FindSignupClinicInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    FindSignupClinicInfoQuery,
    FindSignupClinicInfoQueryVariables
  >(FindSignupClinicInfoDocument, options);
}
export type FindSignupClinicInfoQueryHookResult = ReturnType<
  typeof useFindSignupClinicInfoQuery
>;
export type FindSignupClinicInfoLazyQueryHookResult = ReturnType<
  typeof useFindSignupClinicInfoLazyQuery
>;
export type FindSignupClinicInfoSuspenseQueryHookResult = ReturnType<
  typeof useFindSignupClinicInfoSuspenseQuery
>;
export type FindSignupClinicInfoQueryResult = Apollo.QueryResult<
  FindSignupClinicInfoQuery,
  FindSignupClinicInfoQueryVariables
>;
export const FindClinicCodeSuggestionsDocument = gql`
  query findClinicCodeSuggestions($input: clinicCodeInput!) {
    findClinicCodeSuggestions(input: $input) {
      clinicCode
    }
  }
`;

/**
 * __useFindClinicCodeSuggestionsQuery__
 *
 * To run a query within a React component, call `useFindClinicCodeSuggestionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useFindClinicCodeSuggestionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFindClinicCodeSuggestionsQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useFindClinicCodeSuggestionsQuery(
  baseOptions: Apollo.QueryHookOptions<
    FindClinicCodeSuggestionsQuery,
    FindClinicCodeSuggestionsQueryVariables
  > &
    (
      | { variables: FindClinicCodeSuggestionsQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    FindClinicCodeSuggestionsQuery,
    FindClinicCodeSuggestionsQueryVariables
  >(FindClinicCodeSuggestionsDocument, options);
}
export function useFindClinicCodeSuggestionsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    FindClinicCodeSuggestionsQuery,
    FindClinicCodeSuggestionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    FindClinicCodeSuggestionsQuery,
    FindClinicCodeSuggestionsQueryVariables
  >(FindClinicCodeSuggestionsDocument, options);
}
export function useFindClinicCodeSuggestionsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    FindClinicCodeSuggestionsQuery,
    FindClinicCodeSuggestionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    FindClinicCodeSuggestionsQuery,
    FindClinicCodeSuggestionsQueryVariables
  >(FindClinicCodeSuggestionsDocument, options);
}
export type FindClinicCodeSuggestionsQueryHookResult = ReturnType<
  typeof useFindClinicCodeSuggestionsQuery
>;
export type FindClinicCodeSuggestionsLazyQueryHookResult = ReturnType<
  typeof useFindClinicCodeSuggestionsLazyQuery
>;
export type FindClinicCodeSuggestionsSuspenseQueryHookResult = ReturnType<
  typeof useFindClinicCodeSuggestionsSuspenseQuery
>;
export type FindClinicCodeSuggestionsQueryResult = Apollo.QueryResult<
  FindClinicCodeSuggestionsQuery,
  FindClinicCodeSuggestionsQueryVariables
>;
export const GetPrefectureCodesDocument = gql`
  query getPrefectureCodes {
    getPrefectureCodes {
      name
      code
    }
  }
`;

/**
 * __useGetPrefectureCodesQuery__
 *
 * To run a query within a React component, call `useGetPrefectureCodesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPrefectureCodesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPrefectureCodesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetPrefectureCodesQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetPrefectureCodesQuery,
    GetPrefectureCodesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetPrefectureCodesQuery,
    GetPrefectureCodesQueryVariables
  >(GetPrefectureCodesDocument, options);
}
export function useGetPrefectureCodesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetPrefectureCodesQuery,
    GetPrefectureCodesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetPrefectureCodesQuery,
    GetPrefectureCodesQueryVariables
  >(GetPrefectureCodesDocument, options);
}
export function useGetPrefectureCodesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetPrefectureCodesQuery,
    GetPrefectureCodesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetPrefectureCodesQuery,
    GetPrefectureCodesQueryVariables
  >(GetPrefectureCodesDocument, options);
}
export type GetPrefectureCodesQueryHookResult = ReturnType<
  typeof useGetPrefectureCodesQuery
>;
export type GetPrefectureCodesLazyQueryHookResult = ReturnType<
  typeof useGetPrefectureCodesLazyQuery
>;
export type GetPrefectureCodesSuspenseQueryHookResult = ReturnType<
  typeof useGetPrefectureCodesSuspenseQuery
>;
export type GetPrefectureCodesQueryResult = Apollo.QueryResult<
  GetPrefectureCodesQuery,
  GetPrefectureCodesQueryVariables
>;
