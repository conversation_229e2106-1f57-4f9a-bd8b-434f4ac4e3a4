import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type PostApiEpsSaveDispensingResultListGetBatchMutationVariables =
  Types.Exact<{
    dispensingResultListEndDate?: Types.InputMaybe<
      Types.Scalars["BigInt"]["input"]
    >;
    dispensingResultListStartDate?: Types.InputMaybe<
      Types.Scalars["BigInt"]["input"]
    >;
    saveDispensingResultList?: Types.InputMaybe<
      | Array<
          Types.InputMaybe<Types.EmrCloudApiRequestsEpsSaveDispensingResultListItemsInput>
        >
      | Types.InputMaybe<Types.EmrCloudApiRequestsEpsSaveDispensingResultListItemsInput>
    >;
  }>;

export type PostApiEpsSaveDispensingResultListGetBatchMutation = {
  __typename?: "mutation_root";
  postApiEpsSaveDispensingResultList?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsSaveDispensingResultListResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsSaveDispensingResultListResponse";
      isSuccess?: boolean;
    };
  };
};

export type PostApiEpsUpdateDispensingByResponseMutationVariables =
  Types.Exact<{
    emrCloudApiRequestsEpsUpdateDispensingByResponseRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsEpsUpdateDispensingByResponseRequestInput>;
  }>;

export type PostApiEpsUpdateDispensingByResponseMutation = {
  __typename?: "mutation_root";
  postApiEpsUpdateDispensingByResponse?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsUpdateEpsDispensingsResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsUpdateEpsDispensingsResponse";
      isSuccess?: boolean;
    };
  };
};

export type GetApiEpsCheckCsvHokenInfQueryVariables = Types.Exact<{
  bango?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  edaNo?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  futansyaNo?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  hokensyaNo?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  jyukyusyaNo?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  kigo?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
  prescriptionId?: Types.InputMaybe<Types.Scalars["String"]["input"]>;
}>;

export type GetApiEpsCheckCsvHokenInfQuery = {
  __typename?: "query_root";
  getApiEpsCheckCsvHokenInf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsCheckCsvHokenInfResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesEpsCheckCsvHokenInfResponse";
      prescriptionMatched?: boolean;
      updateHoken?: boolean;
      updateKohi?: boolean;
      ptId?: string;
    };
  };
};

export const PostApiEpsSaveDispensingResultListGetBatchDocument = gql`
  mutation postApiEpsSaveDispensingResultListGetBatch(
    $dispensingResultListEndDate: BigInt
    $dispensingResultListStartDate: BigInt
    $saveDispensingResultList: [EmrCloudApiRequestsEpsSaveDispensingResultListItemsInput]
  ) {
    postApiEpsSaveDispensingResultList(
      emrCloudApiRequestsEpsSaveDispensingResultListRequestInput: {
        dispensingResultListEndDate: $dispensingResultListEndDate
        dispensingResultListStartDate: $dispensingResultListStartDate
        saveDispensingResultList: $saveDispensingResultList
      }
    ) {
      data {
        isSuccess
      }
      message
      status
    }
  }
`;
export type PostApiEpsSaveDispensingResultListGetBatchMutationFn =
  Apollo.MutationFunction<
    PostApiEpsSaveDispensingResultListGetBatchMutation,
    PostApiEpsSaveDispensingResultListGetBatchMutationVariables
  >;

/**
 * __usePostApiEpsSaveDispensingResultListGetBatchMutation__
 *
 * To run a mutation, you first call `usePostApiEpsSaveDispensingResultListGetBatchMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsSaveDispensingResultListGetBatchMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsSaveDispensingResultListGetBatchMutation, { data, loading, error }] = usePostApiEpsSaveDispensingResultListGetBatchMutation({
 *   variables: {
 *      dispensingResultListEndDate: // value for 'dispensingResultListEndDate'
 *      dispensingResultListStartDate: // value for 'dispensingResultListStartDate'
 *      saveDispensingResultList: // value for 'saveDispensingResultList'
 *   },
 * });
 */
export function usePostApiEpsSaveDispensingResultListGetBatchMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsSaveDispensingResultListGetBatchMutation,
    PostApiEpsSaveDispensingResultListGetBatchMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsSaveDispensingResultListGetBatchMutation,
    PostApiEpsSaveDispensingResultListGetBatchMutationVariables
  >(PostApiEpsSaveDispensingResultListGetBatchDocument, options);
}
export type PostApiEpsSaveDispensingResultListGetBatchMutationHookResult =
  ReturnType<typeof usePostApiEpsSaveDispensingResultListGetBatchMutation>;
export type PostApiEpsSaveDispensingResultListGetBatchMutationResult =
  Apollo.MutationResult<PostApiEpsSaveDispensingResultListGetBatchMutation>;
export type PostApiEpsSaveDispensingResultListGetBatchMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsSaveDispensingResultListGetBatchMutation,
    PostApiEpsSaveDispensingResultListGetBatchMutationVariables
  >;
export const PostApiEpsUpdateDispensingByResponseDocument = gql`
  mutation postApiEpsUpdateDispensingByResponse(
    $emrCloudApiRequestsEpsUpdateDispensingByResponseRequestInput: EmrCloudApiRequestsEpsUpdateDispensingByResponseRequestInput
  ) {
    postApiEpsUpdateDispensingByResponse(
      emrCloudApiRequestsEpsUpdateDispensingByResponseRequestInput: $emrCloudApiRequestsEpsUpdateDispensingByResponseRequestInput
    ) {
      data {
        isSuccess
      }
      message
      status
    }
  }
`;
export type PostApiEpsUpdateDispensingByResponseMutationFn =
  Apollo.MutationFunction<
    PostApiEpsUpdateDispensingByResponseMutation,
    PostApiEpsUpdateDispensingByResponseMutationVariables
  >;

/**
 * __usePostApiEpsUpdateDispensingByResponseMutation__
 *
 * To run a mutation, you first call `usePostApiEpsUpdateDispensingByResponseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsUpdateDispensingByResponseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsUpdateDispensingByResponseMutation, { data, loading, error }] = usePostApiEpsUpdateDispensingByResponseMutation({
 *   variables: {
 *      emrCloudApiRequestsEpsUpdateDispensingByResponseRequestInput: // value for 'emrCloudApiRequestsEpsUpdateDispensingByResponseRequestInput'
 *   },
 * });
 */
export function usePostApiEpsUpdateDispensingByResponseMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsUpdateDispensingByResponseMutation,
    PostApiEpsUpdateDispensingByResponseMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsUpdateDispensingByResponseMutation,
    PostApiEpsUpdateDispensingByResponseMutationVariables
  >(PostApiEpsUpdateDispensingByResponseDocument, options);
}
export type PostApiEpsUpdateDispensingByResponseMutationHookResult = ReturnType<
  typeof usePostApiEpsUpdateDispensingByResponseMutation
>;
export type PostApiEpsUpdateDispensingByResponseMutationResult =
  Apollo.MutationResult<PostApiEpsUpdateDispensingByResponseMutation>;
export type PostApiEpsUpdateDispensingByResponseMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsUpdateDispensingByResponseMutation,
    PostApiEpsUpdateDispensingByResponseMutationVariables
  >;
export const GetApiEpsCheckCsvHokenInfDocument = gql`
  query getApiEpsCheckCsvHokenInf(
    $bango: String
    $edaNo: String
    $futansyaNo: String
    $hokensyaNo: String
    $jyukyusyaNo: String
    $kigo: String
    $prescriptionId: String
  ) {
    getApiEpsCheckCsvHokenInf(
      bango: $bango
      edaNo: $edaNo
      futansyaNo: $futansyaNo
      hokensyaNo: $hokensyaNo
      jyukyusyaNo: $jyukyusyaNo
      kigo: $kigo
      prescriptionId: $prescriptionId
    ) {
      data {
        prescriptionMatched
        updateHoken
        updateKohi
        ptId
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiEpsCheckCsvHokenInfQuery__
 *
 * To run a query within a React component, call `useGetApiEpsCheckCsvHokenInfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiEpsCheckCsvHokenInfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiEpsCheckCsvHokenInfQuery({
 *   variables: {
 *      bango: // value for 'bango'
 *      edaNo: // value for 'edaNo'
 *      futansyaNo: // value for 'futansyaNo'
 *      hokensyaNo: // value for 'hokensyaNo'
 *      jyukyusyaNo: // value for 'jyukyusyaNo'
 *      kigo: // value for 'kigo'
 *      prescriptionId: // value for 'prescriptionId'
 *   },
 * });
 */
export function useGetApiEpsCheckCsvHokenInfQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiEpsCheckCsvHokenInfQuery,
    GetApiEpsCheckCsvHokenInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiEpsCheckCsvHokenInfQuery,
    GetApiEpsCheckCsvHokenInfQueryVariables
  >(GetApiEpsCheckCsvHokenInfDocument, options);
}
export function useGetApiEpsCheckCsvHokenInfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiEpsCheckCsvHokenInfQuery,
    GetApiEpsCheckCsvHokenInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiEpsCheckCsvHokenInfQuery,
    GetApiEpsCheckCsvHokenInfQueryVariables
  >(GetApiEpsCheckCsvHokenInfDocument, options);
}
export function useGetApiEpsCheckCsvHokenInfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiEpsCheckCsvHokenInfQuery,
    GetApiEpsCheckCsvHokenInfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiEpsCheckCsvHokenInfQuery,
    GetApiEpsCheckCsvHokenInfQueryVariables
  >(GetApiEpsCheckCsvHokenInfDocument, options);
}
export type GetApiEpsCheckCsvHokenInfQueryHookResult = ReturnType<
  typeof useGetApiEpsCheckCsvHokenInfQuery
>;
export type GetApiEpsCheckCsvHokenInfLazyQueryHookResult = ReturnType<
  typeof useGetApiEpsCheckCsvHokenInfLazyQuery
>;
export type GetApiEpsCheckCsvHokenInfSuspenseQueryHookResult = ReturnType<
  typeof useGetApiEpsCheckCsvHokenInfSuspenseQuery
>;
export type GetApiEpsCheckCsvHokenInfQueryResult = Apollo.QueryResult<
  GetApiEpsCheckCsvHokenInfQuery,
  GetApiEpsCheckCsvHokenInfQueryVariables
>;
