import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiMstItemGetRenkeiConfQueryVariables = Types.Exact<{
  renkeiSbt: Types.Scalars["Int"]["input"];
  notLoadMst: Types.Scalars["Boolean"]["input"];
}>;

export type GetApiMstItemGetRenkeiConfQuery = {
  __typename?: "query_root";
  getApiMstItemGetRenkeiConf?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetRenkeiConfResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetRenkeiConfResponse";
      renkeiMstModelList?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemDtoRenkeiMstDto";
        isInvalid?: number;
        functionType?: number;
        renkeiId?: number;
        renkeiName?: string;
        renkeiSbt?: number;
        sortNo?: number;
      }>;
      renkeiTemplateMstModellList?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemDtoRenkeiTemplateMstDto";
        file?: string;
        param?: string;
        templateId?: number;
        templateName?: string;
        sortNo?: number;
      }>;
      renkeiConfModelList?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemDtoRenkeiConfDto";
        biko?: string;
        id?: string;
        isDeleted?: boolean;
        isInvalid?: number;
        param?: string;
        ptNumLength?: number;
        renkeiId?: number;
        renkeiMstName?: string;
        seqNo?: number;
        sortNo?: number;
        templateId?: number;
        renkeiPathConfModelList?: Array<{
          __typename?: "EmrCloudApiResponsesMstItemDtoRenkeiPathConfDto";
          biko?: string;
          charCd?: number;
          edaNo?: number;
          charCdName?: string;
          id?: string;
          interval?: number;
          isDeleted?: boolean;
          isInvalid?: number;
          machine?: string;
          param?: string;
          passWord?: string;
          passWordDisplay?: string;
          path?: string;
          renkeiId?: number;
          seqNo?: number;
          user?: string;
          workPath?: string;
        }>;
        renkeiTimingModelList?: Array<{
          __typename?: "EmrCloudApiResponsesMstItemDtoRenkeiTimingDto";
          eventCd?: string;
          eventName?: string;
          id?: string;
          isDeleted?: boolean;
          isInvalid?: number;
          renkeiId?: number;
          seqNo?: number;
        }>;
      }>;
    };
  };
};

export type PostApiMstItemSaveRenkeiMutationVariables = Types.Exact<{
  input: Types.EmrCloudApiRequestsMstItemSaveRenkeiRequestInput;
}>;

export type PostApiMstItemSaveRenkeiMutation = {
  __typename?: "mutation_root";
  postApiMstItemSaveRenkei?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemSaveRenkeiResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemSaveRenkeiResponse";
      successed?: boolean;
    };
  };
};

export type GetApiMstItemGetRenkeiTimingQueryVariables = Types.Exact<{
  renkeiId: Types.Scalars["Int"]["input"];
}>;

export type GetApiMstItemGetRenkeiTimingQuery = {
  __typename?: "query_root";
  getApiMstItemGetRenkeiTiming?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetRenkeiTimingResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetRenkeiTimingResponse";
      renkeiTimingList?: Array<{
        __typename?: "EmrCloudApiResponsesMstItemDtoRenkeiTimingDto";
        eventCd?: string;
        eventName?: string;
        id?: string;
        isDeleted?: boolean;
        isInvalid?: number;
        renkeiId?: number;
        seqNo?: number;
      }>;
    };
  };
};

export const GetApiMstItemGetRenkeiConfDocument = gql`
  query getApiMstItemGetRenkeiConf($renkeiSbt: Int!, $notLoadMst: Boolean!) {
    getApiMstItemGetRenkeiConf(renkeiSbt: $renkeiSbt, notLoadMst: $notLoadMst) {
      data {
        renkeiMstModelList {
          isInvalid
          functionType
          renkeiId
          renkeiName
          renkeiSbt
          sortNo
        }
        renkeiTemplateMstModellList {
          file
          param
          templateId
          templateName
          sortNo
        }
        renkeiConfModelList {
          biko
          id
          isDeleted
          isInvalid
          param
          ptNumLength
          renkeiId
          renkeiMstName
          seqNo
          sortNo
          templateId
          renkeiPathConfModelList {
            biko
            charCd
            edaNo
            charCdName
            id
            interval
            isDeleted
            isInvalid
            machine
            param
            passWord
            passWordDisplay
            path
            renkeiId
            seqNo
            user
            workPath
          }
          renkeiTimingModelList {
            eventCd
            eventName
            id
            isDeleted
            isInvalid
            renkeiId
            seqNo
          }
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiMstItemGetRenkeiConfQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemGetRenkeiConfQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemGetRenkeiConfQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemGetRenkeiConfQuery({
 *   variables: {
 *      renkeiSbt: // value for 'renkeiSbt'
 *      notLoadMst: // value for 'notLoadMst'
 *   },
 * });
 */
export function useGetApiMstItemGetRenkeiConfQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiMstItemGetRenkeiConfQuery,
    GetApiMstItemGetRenkeiConfQueryVariables
  > &
    (
      | { variables: GetApiMstItemGetRenkeiConfQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemGetRenkeiConfQuery,
    GetApiMstItemGetRenkeiConfQueryVariables
  >(GetApiMstItemGetRenkeiConfDocument, options);
}
export function useGetApiMstItemGetRenkeiConfLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemGetRenkeiConfQuery,
    GetApiMstItemGetRenkeiConfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemGetRenkeiConfQuery,
    GetApiMstItemGetRenkeiConfQueryVariables
  >(GetApiMstItemGetRenkeiConfDocument, options);
}
export function useGetApiMstItemGetRenkeiConfSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemGetRenkeiConfQuery,
    GetApiMstItemGetRenkeiConfQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemGetRenkeiConfQuery,
    GetApiMstItemGetRenkeiConfQueryVariables
  >(GetApiMstItemGetRenkeiConfDocument, options);
}
export type GetApiMstItemGetRenkeiConfQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetRenkeiConfQuery
>;
export type GetApiMstItemGetRenkeiConfLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetRenkeiConfLazyQuery
>;
export type GetApiMstItemGetRenkeiConfSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetRenkeiConfSuspenseQuery
>;
export type GetApiMstItemGetRenkeiConfQueryResult = Apollo.QueryResult<
  GetApiMstItemGetRenkeiConfQuery,
  GetApiMstItemGetRenkeiConfQueryVariables
>;
export const PostApiMstItemSaveRenkeiDocument = gql`
  mutation postApiMstItemSaveRenkei(
    $input: EmrCloudApiRequestsMstItemSaveRenkeiRequestInput!
  ) {
    postApiMstItemSaveRenkei(
      emrCloudApiRequestsMstItemSaveRenkeiRequestInput: $input
    ) {
      data {
        successed
      }
      message
      status
    }
  }
`;
export type PostApiMstItemSaveRenkeiMutationFn = Apollo.MutationFunction<
  PostApiMstItemSaveRenkeiMutation,
  PostApiMstItemSaveRenkeiMutationVariables
>;

/**
 * __usePostApiMstItemSaveRenkeiMutation__
 *
 * To run a mutation, you first call `usePostApiMstItemSaveRenkeiMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiMstItemSaveRenkeiMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiMstItemSaveRenkeiMutation, { data, loading, error }] = usePostApiMstItemSaveRenkeiMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiMstItemSaveRenkeiMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiMstItemSaveRenkeiMutation,
    PostApiMstItemSaveRenkeiMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiMstItemSaveRenkeiMutation,
    PostApiMstItemSaveRenkeiMutationVariables
  >(PostApiMstItemSaveRenkeiDocument, options);
}
export type PostApiMstItemSaveRenkeiMutationHookResult = ReturnType<
  typeof usePostApiMstItemSaveRenkeiMutation
>;
export type PostApiMstItemSaveRenkeiMutationResult =
  Apollo.MutationResult<PostApiMstItemSaveRenkeiMutation>;
export type PostApiMstItemSaveRenkeiMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiMstItemSaveRenkeiMutation,
    PostApiMstItemSaveRenkeiMutationVariables
  >;
export const GetApiMstItemGetRenkeiTimingDocument = gql`
  query getApiMstItemGetRenkeiTiming($renkeiId: Int!) {
    getApiMstItemGetRenkeiTiming(renkeiId: $renkeiId) {
      data {
        renkeiTimingList {
          eventCd
          eventName
          id
          isDeleted
          isInvalid
          renkeiId
          seqNo
        }
      }
      message
      status
    }
  }
`;

/**
 * __useGetApiMstItemGetRenkeiTimingQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemGetRenkeiTimingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemGetRenkeiTimingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemGetRenkeiTimingQuery({
 *   variables: {
 *      renkeiId: // value for 'renkeiId'
 *   },
 * });
 */
export function useGetApiMstItemGetRenkeiTimingQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetApiMstItemGetRenkeiTimingQuery,
    GetApiMstItemGetRenkeiTimingQueryVariables
  > &
    (
      | {
          variables: GetApiMstItemGetRenkeiTimingQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemGetRenkeiTimingQuery,
    GetApiMstItemGetRenkeiTimingQueryVariables
  >(GetApiMstItemGetRenkeiTimingDocument, options);
}
export function useGetApiMstItemGetRenkeiTimingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemGetRenkeiTimingQuery,
    GetApiMstItemGetRenkeiTimingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemGetRenkeiTimingQuery,
    GetApiMstItemGetRenkeiTimingQueryVariables
  >(GetApiMstItemGetRenkeiTimingDocument, options);
}
export function useGetApiMstItemGetRenkeiTimingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemGetRenkeiTimingQuery,
    GetApiMstItemGetRenkeiTimingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemGetRenkeiTimingQuery,
    GetApiMstItemGetRenkeiTimingQueryVariables
  >(GetApiMstItemGetRenkeiTimingDocument, options);
}
export type GetApiMstItemGetRenkeiTimingQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetRenkeiTimingQuery
>;
export type GetApiMstItemGetRenkeiTimingLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetRenkeiTimingLazyQuery
>;
export type GetApiMstItemGetRenkeiTimingSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetRenkeiTimingSuspenseQuery
>;
export type GetApiMstItemGetRenkeiTimingQueryResult = Apollo.QueryResult<
  GetApiMstItemGetRenkeiTimingQuery,
  GetApiMstItemGetRenkeiTimingQueryVariables
>;
