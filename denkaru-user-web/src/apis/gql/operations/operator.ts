import { gql } from "@/apis/gql/apollo-client";

export const OPERATOR_LOGIN = gql`
  mutation operatorLogin($input: OperatorLoginReq!) {
    operatorLogin(input: $input) {
      challengeName
      sessionValue
      pharmacyFlg
      karteStatus
    }
  }
`;

export const OPERATOR_VERIFY_MFA_CODE = gql`
  mutation operatorVerifyMFACode($input: OperatorVerifyMFACodeReq!) {
    operatorVerifyMFACode(input: $input) {
      challengeName
      sessionValue
      pharmacyFlg
    }
  }
`;

export const OPERATOR_CHANGE_PASSWORD = gql`
  mutation operatorChangePassword($input: OperatorChangePasswordReq!) {
    operatorChangePassword(input: $input) {
      challengeName
      sessionValue
      pharmacyFlg
    }
  }
`;
