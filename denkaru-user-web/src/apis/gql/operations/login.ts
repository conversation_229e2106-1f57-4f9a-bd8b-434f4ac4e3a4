import { gql } from "@/apis/gql/apollo-client";

export const LOGIN = gql`
  mutation login($input: LoginReq!) {
    login(input: $input) {
      hospitalId
      karteStatus
      success
      isLoginIdInitialized
      isPasswordInitialized
      pharmacyFlg
      staffInfo {
        hospitalID
        loginId
        managerKbn
        permissions {
          functionCd
          permission
        }
        staffId
        staffKana
        staffName
        staffType
        status
      }
    }
  }
`;

export const LOGOUT = gql`
  mutation logout {
    logout {
      success
      isSessionRemaining
    }
  }
`;

export const LOGIN_INITIAL = gql`
  mutation loginInitial($input: LoginInitialReq!) {
    loginInitial(input: $input) {
      success
    }
  }
`;
